package com.baidu.keyue.deepsight.mysqldb.entity;

import static org.junit.Assert.assertEquals;

import org.junit.Before;
import org.junit.Test;

public class TableFieldMetaInfoTest{

    private TableFieldMetaInfo tableFieldMetaInfo;

    @Before
    public void setUp() {
        tableFieldMetaInfo = new TableFieldMetaInfo();
    }

    // testSetDataTableId 用于测试 setDataTableId
    // generated by Comate
    @Test
    public void testSetDataTableId() {
        Long dataTableId = 12345L;
        tableFieldMetaInfo.setDataTableId(dataTableId);
        assertEquals(dataTableId, tableFieldMetaInfo.getDataTableId());
    }

}