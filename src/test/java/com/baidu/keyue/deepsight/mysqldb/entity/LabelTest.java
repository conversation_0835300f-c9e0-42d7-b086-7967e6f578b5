package com.baidu.keyue.deepsight.mysqldb.entity;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import org.junit.Before;
import org.junit.Test;

import java.util.Date;

public class LabelTest{

    private Label label;

    @Before
    public void setUp() {
        label = new Label();
    }

    // testGetExecModNull 用于测试 getExecMod
    // generated by Comate
    @Test
    public void testGetExecModNull() {
        // 测试 execMod 为 null 的情况
        label.setExecMod(null);
        assertNull(label.getExecMod());
    }

    // testGetFieldWhenFieldIsNull 用于测试 getField
    // generated by Comate
    @Test
    public void testGetFieldWhenFieldIsNull() {
        // 测试field为null的情况
        assertNull("Field should be null", label.getField());
    }

    // testGetFieldWhenFieldIsNotNull 用于测试 getField
    // generated by Comate
    @Test
    public void testGetFieldWhenFieldIsNotNull() {
        // 测试field不为null的情况
        Long expectedField = 12345L;
        label.setField(expectedField);
        assertEquals("Field should match the set value", expectedField, label.getField());
    }

    // testGetLabelCalStatusNull 用于测试 getLabelCalStatus
    // generated by Comate
    @Test
    public void testGetLabelCalStatusNull() {
        // 测试labelCalStatus为null的情况
        label.setLabelCalStatus(null);
        assertNull(label.getLabelCalStatus());
    }

    // testGetLastCalDateNull 用于测试 getLastCalDate
    // generated by Comate
    @Test
    public void testGetLastCalDateNull() {
        // 测试lastCalDate为null的情况
        assertNull(label.getLastCalDate());
    }

    // testGetLastCalDateNotNull 用于测试 getLastCalDate
    // generated by Comate
    @Test
    public void testGetLastCalDateNotNull() {
        // 测试lastCalDate不为null的情况
        Date expectedDate = new Date();
        label.setLastCalDate(expectedDate);
        assertEquals(expectedDate, label.getLastCalDate());
    }

}