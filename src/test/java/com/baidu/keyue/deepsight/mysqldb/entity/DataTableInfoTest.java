package com.baidu.keyue.deepsight.mysqldb.entity;

import static org.junit.Assert.assertEquals;

import org.junit.Before;
import org.junit.Test;

public class DataTableInfoTest{

    private DataTableInfo dataTableInfo;

    @Before
    public void setUp() {
        dataTableInfo = new DataTableInfo();
    }

    // testGetTableName 用于测试 getTableName
    // generated by Comate
    @Test
    public void testGetTableName() {
        // Arrange
        String expectedTableName = "test_table";
        dataTableInfo.setTableName(expectedTableName);
    
        // Act
        String actualTableName = dataTableInfo.getTableName();
    
        // Assert
        assertEquals(expectedTableName, actualTableName);
    }

}