package com.baidu.keyue.deepsight.mysqldb.entity;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;

import org.junit.Before;
import org.junit.Test;

public class FieldEncryConfigTest{

    private FieldEncryConfig fieldEncryConfig;

    @Before
    public void setUp() {
        fieldEncryConfig = new FieldEncryConfig();
    }

    // testGetDataTableIdDefaultValue 用于测试 getDataTableId
    // generated by Comate
    @Test
    public void testGetDataTableIdDefaultValue() {
        // Test the default value
        assertNull("Default dataTableId should be null", fieldEncryConfig.getDataTableId());
    }

    // testGetDataTableIdAfterSettingValue 用于测试 getDataTableId
    // generated by Comate
    @Test
    public void testGetDataTableIdAfterSettingValue() {
        // Test setting a value and getting it back
        String expectedDataTableId = "testDataTableId";
        fieldEncryConfig.setDataTableId(expectedDataTableId);
        assertEquals("getDataTableId should return the set value", expectedDataTableId, fieldEncryConfig.getDataTableId());
    }

}