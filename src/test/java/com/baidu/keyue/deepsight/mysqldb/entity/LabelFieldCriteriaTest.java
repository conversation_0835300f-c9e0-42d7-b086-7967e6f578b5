package com.baidu.keyue.deepsight.mysqldb.entity;

import static org.junit.Assert.*;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import org.junit.Before;
import org.junit.Test;

import java.util.List;

public class LabelFieldCriteriaTest{

    private LabelFieldCriteria labelFieldCriteria;

    @Before
    public void setUp() {
        labelFieldCriteria = new LabelFieldCriteria();
    }

    // testGetOrderByClauseDefault 用于测试 getOrderByClause
    // generated by Comate
    @Test
    public void testGetOrderByClauseDefault() {
        // Test the default value
        assertNull("Default orderByClause should be null", labelFieldCriteria.getOrderByClause());
    }

    // testGetOrderByClauseSet 用于测试 getOrderByClause
    // generated by Comate
    @Test
    public void testGetOrderByClauseSet() {
        // Test setting and getting orderByClause
        String orderByClause = "id ASC";
        labelFieldCriteria.setOrderByClause(orderByClause);
        assertEquals("OrderByClause should match the set value", orderByClause, labelFieldCriteria.getOrderByClause());
    }

    // testSetDistinctTrue 用于测试 setDistinct
    // generated by Comate
    @Test
    public void testSetDistinctTrue() {
        labelFieldCriteria.setDistinct(true);
        assertTrue(labelFieldCriteria.isDistinct());
    }

    // testOr 用于测试 or
    // generated by Comate
    @Test
    public void testOr() {
        // Create a new Criteria object
        LabelFieldCriteria.Criteria criteria = new LabelFieldCriteria.Criteria();
    
        // Call the or method
        labelFieldCriteria.or(criteria);
    
        // Retrieve the oredCriteria list
        List<LabelFieldCriteria.Criteria> oredCriteria = labelFieldCriteria.getOredCriteria();
    
        // Verify that the criteria was added to the list
        assertNotNull(oredCriteria);
        assertEquals(1, oredCriteria.size());
        assertEquals(criteria, oredCriteria.get(0));
    }

    // testOrMultipleTimes 用于测试 or
    // generated by Comate
    @Test
    public void testOrMultipleTimes() {
        // Test that or() can be called multiple times
        labelFieldCriteria.or();
        labelFieldCriteria.or();
        assertEquals(2, labelFieldCriteria.getOredCriteria().size());
    }

    // testCreateCriteriaInitialState 用于测试 createCriteria
    // generated by Comate
    @Test
    public void testCreateCriteriaInitialState() {
        // Test initial state
        assertEquals(0, labelFieldCriteria.getOredCriteria().size());
    
        // Call the method under test
        LabelFieldCriteria.Criteria criteria = labelFieldCriteria.createCriteria();
    
        // Verify the result
        assertNotNull(criteria);
        assertEquals(1, labelFieldCriteria.getOredCriteria().size());
        assertEquals(criteria, labelFieldCriteria.getOredCriteria().get(0));
    }

}