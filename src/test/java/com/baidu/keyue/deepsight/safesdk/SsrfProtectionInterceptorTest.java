package com.baidu.keyue.deepsight.safesdk;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpResponse;

import java.io.IOException;
import java.net.InetAddress;
import java.net.URI;
import java.net.UnknownHostException;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SsrfProtectionInterceptorTest{

    @Mock
    private ClientHttpRequestExecution execution;

    @Mock
    private ClientHttpResponse response;

    @Mock
    private InetAddress inetAddress;

    @InjectMocks
    private SsrfProtectionInterceptor interceptor;

    @Mock
    private SafeWhitelistConfig safeWhitelistConfig;

    @Mock
    private HttpRequest request;

    @Test
    void interceptShouldAllowRequestWhenPublicIpNotInWhitelist() throws IOException, UnknownHostException {
        URI uri = URI.create("http://*******");
        when(request.getURI()).thenReturn(uri);
        when(safeWhitelistConfig.getIpWhitelist()).thenReturn(Set.of());
        when(safeWhitelistConfig.getHostWhitelist()).thenReturn(Set.of());
        when(execution.execute(request, new byte[0])).thenReturn(response);
    
        ClientHttpResponse result = interceptor.intercept(request, new byte[0], execution);
    
        assertEquals(response, result);
        verify(execution).execute(request, new byte[0]);
    }

    @Test
    void interceptShouldHandleUnknownHostException() throws IOException, UnknownHostException {
        URI uri = URI.create("http://unknownhost");
        when(request.getURI()).thenReturn(uri);
        when(execution.execute(request, new byte[0])).thenReturn(response);
    
        ClientHttpResponse result = interceptor.intercept(request, new byte[0], execution);
    
        assertEquals(response, result);
        verify(execution).execute(request, new byte[0]);
    }

    @Test
    void interceptShouldHandleNullInetAddress() throws IOException {
        URI uri = URI.create("http://nullhost");
        when(request.getURI()).thenReturn(uri);
        interceptor.intercept(request, new byte[0], execution);
    }

    @Test
    void interceptShouldAllowRequestWhenHostInWhitelist() throws IOException {
        URI uri = URI.create("http://example.com");
        when(request.getURI()).thenReturn(uri);
        when(safeWhitelistConfig.getHostWhitelist()).thenReturn(Set.of("example.com"));
        when(execution.execute(request, new byte[0])).thenReturn(response);
    
        ClientHttpResponse result = interceptor.intercept(request, new byte[0], execution);
    
        assertEquals(response, result);
        verify(execution).execute(request, new byte[0]);
    }

    @Test
    void interceptShouldAllowRequestWhenIpInWhitelist() throws IOException, UnknownHostException {
        URI uri = URI.create("http://***********");
        when(request.getURI()).thenReturn(uri);
        when(safeWhitelistConfig.getIpWhitelist()).thenReturn(Set.of("***********"));
        when(execution.execute(request, new byte[0])).thenReturn(response);
    
        ClientHttpResponse result = interceptor.intercept(request, new byte[0], execution);
    
        assertEquals(response, result);
        verify(execution).execute(request, new byte[0]);
    }

    @Test
    void interceptShouldBlockRequestWhenPrivateIpNotInWhitelist() throws IOException, UnknownHostException {
        URI uri = URI.create("http://********");
        when(request.getURI()).thenReturn(uri);
        when(safeWhitelistConfig.getIpWhitelist()).thenReturn(Set.of());
        when(safeWhitelistConfig.getHostWhitelist()).thenReturn(Set.of());
    
        assertThrows(SecurityException.class, () -> 
            interceptor.intercept(request, new byte[0], execution)
        );
    }

}