package com.baidu.keyue.deepsight.safesdk;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.Set;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * @ClassName SafeHttpRequestFactoryTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2025/6/25 10:36 AM
 */
@ExtendWith(MockitoExtension.class)
public class SafeHttpRequestFactoryTest {

    @InjectMocks
    private SafeHttpRequestFactory safeHttpRequestFactory;
    @Mock
    SafeWhitelistConfig safeWhitelistConfig;

    @Test
    void testPrepareConnectionWithBlockedIp() throws UnknownHostException, MalformedURLException {
        HttpURLConnection connection = Mockito.mock(HttpURLConnection.class);
        URL url = new URL("http://***********");
        Mockito.doReturn(url).when(connection).getURL();
        Mockito.when(safeWhitelistConfig.getIpWhitelist()).thenReturn(Set.of());
        SecurityException exception = assertThrows(SecurityException.class, () -> {
            safeHttpRequestFactory.prepareConnection(connection, "GET");
        });

        assertEquals("Blocked unsafe request to private IP: ***********", exception.getMessage());
    }

    @Test
    void testPrepareConnectionWithUnKnownHost() throws IOException {
        HttpURLConnection connection = Mockito.mock(HttpURLConnection.class);
        URL url = new URL("http://unknownhost");
        Mockito.doReturn(url).when(connection).getURL();
        safeHttpRequestFactory.prepareConnection(connection, "GET");
    }
}
