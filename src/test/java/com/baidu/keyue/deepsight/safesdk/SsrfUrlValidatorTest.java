package com.baidu.keyue.deepsight.safesdk;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class SsrfUrlValidatorTest {


    @Mock
    private SafeWhitelistConfig safeWhitelistConfig;

    @InjectMocks
    private SsrfUrlValidator ssrfUrlValidator;

    @Test
    void validateUrlShouldPassForWhitelistedHost() throws Exception {
        String url = "http://example.com";
        when(safeWhitelistConfig.getHostWhitelist()).thenReturn(Set.of("example.com"));

        assertDoesNotThrow(() -> ssrfUrlValidator.validateUrl(url));
    }

    @Test
    void validateUrlShouldPassForWhitelistedIp() throws Exception {
        String url = "http://127.0.0.1";
        when(safeWhitelistConfig.getIpWhitelist()).thenReturn(Set.of("127.0.0.1"));

        assertDoesNotThrow(() -> ssrfUrlValidator.validateUrl(url));
    }

    @Test
    void validateUrlShouldNotPassForWhitelistedIp() throws Exception {
        String url = "http://127.0.0.1";
        when(safeWhitelistConfig.getIpWhitelist()).thenReturn(Set.of("*********"));

        assertThrows(RuntimeException.class, () -> ssrfUrlValidator.validateUrl(url));
    }


    @Test
    void validateUrlShouldHandleMalformedUrl() {
        String invalidUrl = "invalid_url";
        ssrfUrlValidator.validateUrl(invalidUrl);
    }
}