package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.kybase.commons.utils.HttpUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {
    "aiob.taskList=/task/list",
    "aiob.url=http://aiob.test",
    "switch.aiobTaskInfoSync=true"
})
public class AiobTaskInfoSyncSchedulerTest{

    @InjectMocks
    private AiobTaskInfoSyncScheduler scheduler;

    private TenantInfo tenantInfo;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private TenantInfoMapper tenantInfoMapper;

    @Mock
    private DorisService dorisService;

    @Mock
    private RLock lock;

    @BeforeEach
    void setUp() {
        tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("test-tenant");
        tenantInfo.setUserId("test-user");
        ReflectionTestUtils.setField(scheduler, "queryTaskInfoMax", 300);
    }

    @Test
    void syncTaskInfoRealShouldHandleNullResponse() {
        // Arrange
        Map<String, Object> taskIdMap = new HashMap<>();
        taskIdMap.put("task_id", "123");
        List<Map<String, Object>> taskIds = List.of(taskIdMap);
    
        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(dorisService.selectList(anyString())).thenReturn(taskIds, Collections.emptyList());
    
        try (MockedStatic<HttpUtil> httpUtilMock = mockStatic(HttpUtil.class)) {
            httpUtilMock.when(() -> HttpUtil.postJson(anyString(), anyString(), anyMap()))
                .thenReturn(null);
    
            // Act
            scheduler.syncTaskInfoReal(tenantInfo);
    
            // Assert
            verify(dorisService, atLeastOnce()).selectList(anyString());
            verify(dorisService, never()).execSql(anyString());
        }
    }

    @Test
    void syncTaskInfoRealShouldHandleEmptyTaskListInResponse() {
        // Arrange
        Map<String, Object> taskIdMap = new HashMap<>();
        taskIdMap.put("task_id", "123");
        List<Map<String, Object>> taskIds = List.of(taskIdMap);
    
        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(dorisService.selectList(anyString())).thenReturn(taskIds, Collections.emptyList());
    
        try (MockedStatic<HttpUtil> httpUtilMock = mockStatic(HttpUtil.class)) {
            String responseJson = "{\"code\":\"0\",\"data\":{\"list\":[]}}";
            httpUtilMock.when(() -> HttpUtil.postJson(anyString(), anyString(), anyMap()))
                .thenReturn(responseJson);
    
            // Act
            scheduler.syncTaskInfoReal(tenantInfo);
    
            // Assert
            verify(dorisService, atLeastOnce()).selectList(anyString());
            verify(dorisService, never()).execSql(anyString());
        }
    }

    @Test
    void syncTaskInfoShouldNotExecuteWhenTaskInfoDisabled() {
        // Arrange
        scheduler.taskInfoEnable = false;
    
        // Act
        scheduler.syncTaskInfo();
    
        // Assert
        verifyNoInteractions(redissonClient, tenantInfoMapper, dorisService);
    }

    @Test
    void syncTaskInfoRealShouldHandleEmptyTaskIds() {
        // Arrange
        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(dorisService.selectList(anyString())).thenReturn(Collections.emptyList());
    
        // Act
        scheduler.syncTaskInfoReal(tenantInfo);
    
        // Assert
        verify(dorisService).getCount(anyString());
        verify(dorisService).selectList(anyString());
        verifyNoMoreInteractions(dorisService);
    }

    @Test
    void syncTaskInfoRealShouldProcessTasksSuccessfully() {
        // Arrange
        Map<String, Object> taskIdMap = new HashMap<>();
        taskIdMap.put("task_id", "123");
        List<Map<String, Object>> taskIds = List.of(taskIdMap);
    
        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(dorisService.selectList(anyString())).thenReturn(taskIds, Collections.emptyList());
    
        try (MockedStatic<HttpUtil> httpUtilMock = mockStatic(HttpUtil.class)) {
            String responseJson = "{\"code\":\"0\",\"data\":{\"list\":[{\"taskId\":123,\"status\":1}]}}";
            httpUtilMock.when(() -> HttpUtil.postJson(anyString(), anyString(), anyMap()))
                .thenReturn(responseJson);
    
            // Act
            scheduler.syncTaskInfoReal(tenantInfo);
    
            // Assert
            verify(dorisService, atLeastOnce()).selectList(anyString());
            verify(dorisService, atLeastOnce()).execSql(anyString());
        }
    }

    @Test
    void syncTaskInfoRealShouldNotExecuteWhenCountIsZero() {
        // Arrange
        when(dorisService.getCount(anyString())).thenReturn(0L);
    
        // Act
        scheduler.syncTaskInfoReal(tenantInfo);
    
        // Assert
        verify(dorisService).getCount(anyString());
        verifyNoMoreInteractions(dorisService);
    }

    @Test
    void syncTaskInfoShouldNotExecuteWhenLockNotAcquired() throws Exception {
        // Arrange
        scheduler.taskInfoEnable = true;
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(false);
    
        // Act
        scheduler.syncTaskInfo();
    
        // Assert
        verify(redissonClient).getLock("AIOB_TASK_STATUS_UPDATE");
        verify(lock).tryLock();
        verifyNoMoreInteractions(lock, tenantInfoMapper, dorisService);
    }

    @Test
    void syncTaskInfoShouldExecuteSuccessfully() throws Exception {
        // Arrange
        scheduler.taskInfoEnable = true;
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(tenantInfoMapper.selectByExample(any())).thenReturn(List.of(tenantInfo));
    
        try (MockedStatic<HttpUtil> httpUtilMock = mockStatic(HttpUtil.class)) {
            httpUtilMock.when(() -> HttpUtil.postJson(anyString(), anyString(), anyMap()))
                .thenReturn("{\"code\":\"0\",\"data\":{\"list\":[{\"taskId\":1,\"status\":1}]}}");
    
            // Act
            scheduler.syncTaskInfo();
    
            // Assert
            verify(lock).unlock();
            verify(tenantInfoMapper).selectByExample(any());
        }
    }

}