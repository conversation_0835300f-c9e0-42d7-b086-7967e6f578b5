package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.AlertConfigTypeEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.AlertConfigMapper;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.service.tool.MessageService;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.kybase.sdk.message.vo.MessageBody;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {"switch.aiobConnectRateAlert=true"})
public class AiobConnectRateAlertScheduleTest{

    @Mock
    private RedissonClient redissonClient;

    @InjectMocks
    private AiobConnectRateAlertSchedule aiobConnectRateAlertSchedule;

    private AlertConfig config;

    private TenantInfo tenantInfo;

    @Mock
    private MessageService messageService;

    @Mock
    private TenantInfoService tenantInfoService;

    @Mock
    private AlertConfigMapper alertConfigMapper;

    @Mock
    private DorisService dorisService;

    @Mock
    private RLock lock;

    @Mock
    private RBucket bucket;

    private Date now;

    @BeforeEach
    void setUp() {
        now = new Date();
        config = new AlertConfig();
        config.setId(1);
        config.setTenantId("testTenant");
        config.setDialCount(100);
        config.setThresholdRate(new BigDecimal("0.5"));
        config.setAlertFreq(1);
        config.setAlertTime("24H");
        config.setConfigType(AlertConfigTypeEnum.LINE.getValue());
        config.setConfigTarget("testTarget");
    
        tenantInfo = new TenantInfo();
        tenantInfo.setAccountid("testAccount");
        ReflectionTestUtils.setField(aiobConnectRateAlertSchedule, "connectRateAlertEnable", true);
    }

    @Test
    void testAlertTaskWhenLockNotAcquired() {
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(false);
    
        aiobConnectRateAlertSchedule.alertTask();
    
        verify(lock, never()).unlock();
    }

    @Test
    void testAlertTaskWhenNoActiveConfigs() {
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(alertConfigMapper.countByExample(any(AlertConfigCriteria.class))).thenReturn(0L);
    
        aiobConnectRateAlertSchedule.alertTask();
    
        verify(alertConfigMapper, never()).selectByExample(any(AlertConfigCriteria.class));
        verify(lock).unlock();
    }

    @Test
    void testAlertTaskWithActiveConfigs() {
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        when(bucket.isExists()).thenReturn(false);
        when(alertConfigMapper.countByExample(any(AlertConfigCriteria.class))).thenReturn(150L);
        when(alertConfigMapper.selectByExample(any(AlertConfigCriteria.class))).thenReturn(List.of(config));
        when(dorisService.selectList(anyString())).thenReturn(List.of(new HashMap<>()));
        aiobConnectRateAlertSchedule.alertTask();
    
        verify(lock).unlock();
    }

    @Test
    void testCheckAlertDataWhenConditionsMet() {
        Map<String, Object> countMap = Map.of(
                "dial_count", 150L,
                "connect_rate", 0.4
        );
        try (MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {
            mockedORMUtils.when(() -> ORMUtils.generateConnectionRateQuery(any(), any()))
                    .thenReturn("testQuery");
            mockedORMUtils.when(() -> ORMUtils.generateConnectRateAlertSave(any()))
                    .thenReturn("testSave");
            when(redissonClient.getBucket(anyString())).thenReturn(bucket);
            when(bucket.isExists()).thenReturn(false);
            when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(tenantInfo);
            when(dorisService.selectList(anyString())).thenReturn(List.of(countMap));
            config.setThresholdRate(new BigDecimal(50));
            aiobConnectRateAlertSchedule.checkAlertData(now, config);
    
            verify(dorisService).execSql("testSave");
            verify(messageService).pushMessage(eq("testAccount"), any(MessageBody.class));
            verify(alertConfigMapper).updateByPrimaryKeySelective(any(AlertConfig.class));
        }
    }

    @Test
    void testCheckAlertDataWhenConditionsNotMet() {
        Map<String, Object> countMap = Map.of(
                "dial_count", 50L,
                "connect_rate", 0.6
        );
        when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        when(bucket.isExists()).thenReturn(false);
        try (MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {
            mockedORMUtils.when(() -> ORMUtils.generateConnectionRateQuery(any(), any()))
                    .thenReturn("testQuery");
    
            when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(tenantInfo);
            when(dorisService.selectList(anyString())).thenReturn(List.of(countMap));
    
            aiobConnectRateAlertSchedule.checkAlertData(now, config);
    
            verify(dorisService, never()).execSql(anyString());
            verify(messageService, never()).pushMessage(anyString(), any());
            verify(alertConfigMapper).updateByPrimaryKeySelective(any(AlertConfig.class));
        }
    }

    @Test
    void testCheckAlertDataWhenConditionsNotMet2() {
        Map<String, Object> countMap = Map.of(
                "dial_count", 50L,
                "connect_rate", 0.6
        );
        Map<String, Object> detailMap = Map.of(
                "taskName", "测试task名",
                "robotName", "测试名robot",
                "taskStatus", 1
        );
        config.setConfigType(AlertConfigTypeEnum.TASK.getValue());
        when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        when(bucket.isExists()).thenReturn(false);
        try (MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {
            mockedORMUtils.when(() -> ORMUtils.generateConnectionRateQuery(any(), any()))
                    .thenReturn("testQuery");

            when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(tenantInfo);
            when(dorisService.selectList(anyString())).thenReturn(List.of(countMap));
            when(dorisService.selectList(null)).thenReturn(List.of(detailMap));

            aiobConnectRateAlertSchedule.checkAlertData(now, config);

            verify(dorisService, never()).execSql(anyString());
            verify(messageService, never()).pushMessage(anyString(), any());
            verify(alertConfigMapper).updateByPrimaryKeySelective(any(AlertConfig.class));
        }
    }

    @Test
    void testCheckAlertDataWhenConditionsNotMet3() {
        Map<String, Object> countMap = Map.of(
                "dial_count", 50L,
                "connect_rate", 0.6
        );
        Map<String, Object> detailMap = Map.of(
                "taskName", "测试task名",
                "robotName", "测试名robot",
                "taskStatus", 1
        );
        config.setConfigType(AlertConfigTypeEnum.ROBOT.getValue());
        when(redissonClient.getBucket(anyString())).thenReturn(bucket);
        when(bucket.isExists()).thenReturn(false);
        try (MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {
            mockedORMUtils.when(() -> ORMUtils.generateConnectionRateQuery(any(), any()))
                    .thenReturn("testQuery");

            when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(tenantInfo);
            when(dorisService.selectList(anyString())).thenReturn(List.of(countMap));
            when(dorisService.selectList(null)).thenReturn(List.of(detailMap));

            aiobConnectRateAlertSchedule.checkAlertData(now, config);

            verify(dorisService, never()).execSql(anyString());
            verify(messageService, never()).pushMessage(anyString(), any());
            verify(alertConfigMapper).updateByPrimaryKeySelective(any(AlertConfig.class));
        }
    }

    @Test
    void testCheckAlertDataWhenExceptionOccurs() {
        try (MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {
            mockedORMUtils.when(() -> ORMUtils.generateConnectionRateQuery(any(), any()))
                    .thenThrow(new RuntimeException("Test exception"));
    
            aiobConnectRateAlertSchedule.checkAlertData(now, config);
    
            verify(dorisService, never()).execSql(anyString());
            verify(messageService, never()).pushMessage(anyString(), any());
            verify(alertConfigMapper, never()).updateByPrimaryKeySelective(any(AlertConfig.class));
        }
    }

    @Test
    void testGetAlertConfig() {
        int offset = 0;
        int pageSize = 100;
        String order = String.format("id ASC limit %d, %d", offset, pageSize);
        AlertConfigCriteria criteria = new AlertConfigCriteria();
        criteria.createCriteria()
                .andIsActiveEqualTo(Boolean.TRUE)
                .andIsActiveEqualTo(true)
                .andNextCheckTimeLessThanOrEqualTo(now);
        criteria.setOrderByClause(order);
    
    
        List<AlertConfig> result = aiobConnectRateAlertSchedule.getAlertConfig(offset, pageSize, now);
    }

}