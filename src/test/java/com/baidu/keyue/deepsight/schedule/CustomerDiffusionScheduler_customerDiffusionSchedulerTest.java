package com.baidu.keyue.deepsight.schedule;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class CustomerDiffusionScheduler_customerDiffusionSchedulerTest{

    @InjectMocks
    private CustomerDiffusionScheduler customerDiffusionScheduler;

    @InjectMocks
    private CustomerDiffusionScheduler scheduler;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(scheduler, "customerDiffusion", true);
    }

    // testCustomerDiffusionSchedulerDisabled 用于测试 customerDiffusionScheduler
    // generated by Comate
    @Test
    void testCustomerDiffusionSchedulerDisabled() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(customerDiffusionScheduler, "customerDiffusion", false);
        customerDiffusionScheduler.customerDiffusionScheduler();
    }

}