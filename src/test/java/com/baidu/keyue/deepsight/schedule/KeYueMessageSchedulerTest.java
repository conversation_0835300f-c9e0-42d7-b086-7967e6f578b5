package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.SOPStatusEnum;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordViewResp;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.AiobSopMetaMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.SopUserConfigMapper;
import com.baidu.keyue.deepsight.service.sop.impl.AiobSOPServiceImpl;
import com.baidu.keyue.deepsight.service.sop.impl.AiobSopMetricService;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.service.tool.MessageService;
import com.baidu.kybase.sdk.message.vo.MessageBody;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.test.context.TestPropertySource;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@TestPropertySource(properties = {
    "aiob.debugPath=aaa",
    "aiob.url=http://127.0.0.1:8080"
})
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class KeYueMessageSchedulerTest{


    private AiobSopMeta aiobSopMeta;

    @Mock
    private RedissonClient redisson;

    @Mock
    private RLock lock;

    private TenantInfo tenantInfo;

    private SopUserConfig sopUserConfig;

    @InjectMocks
    private KeYueMessageScheduler keYueMessageScheduler;

    private Date now;

    private Date oneHourAgo;
    @InjectMocks
    private KeYueMessageScheduler scheduler;

    @Mock
    private MessageService messageService;

    @Mock
    private TenantInfoService tenantInfoService;

    @Mock
    private AiobSOPServiceImpl aiobSOPService;

    @Mock
    private AiobSopMetricService aiobSopMetricService;

    @Mock
    private SopUserConfigMapper sopUserConfigMapper;

    @Mock
    private AiobSopMetaMapper aiobSopMetaMapper;

    @Mock
    private DorisService dorisService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }





    @Test
    void sendMessageShouldNotSendMessageWhenNoSopConfigs() {
        MockitoAnnotations.openMocks(this);
        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("testTenant");
        tenantInfo.setAccountid("testAccount");

        when(tenantInfoService.getAllTenantInfo()).thenReturn(Collections.singletonList(tenantInfo));
        when(aiobSOPService.getSopConfigByTenantId(anyString())).thenReturn(Collections.emptyList());

        keYueMessageScheduler.sendMessage();
        verify(messageService, never()).pushMessage(anyString(), any(MessageBody.class));
    }

    @Test
    void sendMessageShouldNotSendMessageWhenLockFailed() throws Exception{
        MockitoAnnotations.openMocks(this);
        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(false);

        tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("testTenant");
        tenantInfo.setAccountid("testAccount");

        when(tenantInfoService.getAllTenantInfo()).thenReturn(Collections.singletonList(tenantInfo));
        when(aiobSOPService.getSopConfigByTenantId(anyString())).thenReturn(Collections.emptyList());
        try {
            keYueMessageScheduler.sendMessage();
        } catch (Exception e) {
            verify(messageService, never()).pushMessage(anyString(), any(MessageBody.class));
        }
    }

    @Test
    void sendMessageShouldNotSendMessageWhenEmptyUvOrHangUpData() {
        MockitoAnnotations.openMocks(this);
        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("testTenant");
        tenantInfo.setAccountid("testAccount");

        sopUserConfig = new SopUserConfig();
        sopUserConfig.setTaskId("testTask");
        sopUserConfig.setWarningThreshold(50);

        when(tenantInfoService.getAllTenantInfo()).thenReturn(Collections.singletonList(tenantInfo));
        when(aiobSOPService.getSopConfigByTenantId(anyString())).thenReturn(Collections.singletonList(sopUserConfig));
        when(aiobSopMetricService.getNodeUvByTaskIdSQL(anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(Collections.emptyList());
        when(aiobSopMetricService.getNodeHangUpByTaskIdSQL(anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(Collections.emptyList());

        keYueMessageScheduler.sendMessage();
        verify(messageService, never()).pushMessage(anyString(), any(MessageBody.class));
    }

    @Test
    void sendMessageShouldNotSendMessageWhenHangUpRateBelowThreshold1() {
        MockitoAnnotations.openMocks(this);
        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("testTenant");
        tenantInfo.setAccountid("testAccount");

        sopUserConfig = new SopUserConfig();
        sopUserConfig.setTaskId("testTask");
        sopUserConfig.setWarningThreshold(50);

        Map<String, Object> uvMapEntry = new HashMap<>();
        uvMapEntry.put("node_id", "node1");
        uvMapEntry.put("node_uv", "100");
        List<Map<String, Object>> uvMap = Collections.singletonList(uvMapEntry);

        Map<String, Object> hangUpMapEntry = new HashMap<>();
        hangUpMapEntry.put("node_id", "node1");
        hangUpMapEntry.put("node_uv", "40");
        List<Map<String, Object>> hangUpCntMap = Collections.singletonList(hangUpMapEntry);

        when(tenantInfoService.getAllTenantInfo()).thenReturn(Collections.singletonList(tenantInfo));
        when(aiobSOPService.getSopConfigByTenantId(anyString())).thenReturn(Collections.singletonList(sopUserConfig));
        when(aiobSopMetricService.getNodeUvByTaskIdSQL(anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(uvMap);
        when(aiobSopMetricService.getNodeHangUpByTaskIdSQL(anyString(), anyString(), any(Date.class), any(Date.class)))
                .thenReturn(hangUpCntMap);

        keYueMessageScheduler.sendMessage();
        verify(messageService, never()).pushMessage(anyString(), any(MessageBody.class));
    }


    @Test
    void sendMessageShouldHandleExceptionGracefully() {
        MockitoAnnotations.openMocks(this);
        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("testTenant");
        tenantInfo.setAccountid("testAccount");

        when(tenantInfoService.getAllTenantInfo()).thenThrow(new RuntimeException("Test Exception"));

        keYueMessageScheduler.sendMessage();
        verify(messageService, never()).pushMessage(anyString(), any(MessageBody.class));
    }



    @Test
    void sendMessageShouldNotSendMessageWhenNoTenants() {
        MockitoAnnotations.openMocks(this);
        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        when(tenantInfoService.getAllTenantInfo()).thenReturn(Collections.emptyList());
        keYueMessageScheduler.sendMessage();
        verify(messageService, never()).pushMessage(anyString(), any(MessageBody.class));
    }

    @Test
    void sendMessageShouldNotSendMessage() {
        MockitoAnnotations.openMocks(this);

        tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("testTenant");
        tenantInfo.setAccountid("testAccount");

        sopUserConfig = new SopUserConfig();
        sopUserConfig.setTenantId("testTenant");
        sopUserConfig.setTaskId("testTask");
        sopUserConfig.setWarningThreshold(5);

        aiobSopMeta = new AiobSopMeta();
        aiobSopMeta.setNodeId("nodeId");
        aiobSopMeta.setNodeName("testNodeName");

        now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.HOUR_OF_DAY, -1);
        oneHourAgo = calendar.getTime();

        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(tenantInfoService.getAllTenantInfo()).thenReturn(Collections.singletonList(tenantInfo));
        when(aiobSOPService.getSopConfigByTenantId(anyString())).thenReturn(Collections.singletonList(sopUserConfig));

        Map<String, Object> uvMap = new HashMap<>();
        uvMap.put("node_id", "nodeId");
        uvMap.put("node_uv", "100");
        Map<String, Object> hangUpMap = new HashMap<>();
        hangUpMap.put("node_id", "");
        hangUpMap.put("node_uv", "40");

        when(aiobSopMetricService.getNodeUvByTaskIdSQL(anyString(), anyString(), any(), any()))
                .thenReturn(Collections.singletonList(uvMap));
        when(aiobSopMetricService.getNodeHangUpByTaskIdSQL(anyString(), anyString(), any(), any()))
                .thenReturn(Collections.singletonList(hangUpMap));

        scheduler.sendMessage();

        verify(messageService, never()).pushMessage(anyString(), any(MessageBody.class));
    }
    @Test
    void sendMessageShouldNotSendMessageWhenHasPushMsg() {
        MockitoAnnotations.openMocks(this);

        tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("testTenant");
        tenantInfo.setAccountid("testAccount");

        sopUserConfig = new SopUserConfig();
        sopUserConfig.setTenantId("testTenant");
        sopUserConfig.setTaskId("testTask");
        sopUserConfig.setWarningThreshold(5);

        aiobSopMeta = new AiobSopMeta();
        aiobSopMeta.setNodeId("testNode");
        aiobSopMeta.setNodeName("testNodeName");

        now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.HOUR_OF_DAY, -1);
        oneHourAgo = calendar.getTime();

        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(tenantInfoService.getAllTenantInfo()).thenReturn(Collections.singletonList(tenantInfo));
        when(aiobSOPService.getSopConfigByTenantId(anyString())).thenReturn(Collections.singletonList(sopUserConfig));

        Map<String, Object> uvMap = new HashMap<>();
        uvMap.put("node_id", "testNode");
        uvMap.put("node_uv", "100");
        Map<String, Object> hangUpMap = new HashMap<>();
        hangUpMap.put("node_id", "testNode");
        hangUpMap.put("node_uv", "40");

        when(aiobSopMetricService.getNodeUvByTaskIdSQL(anyString(), anyString(), any(), any()))
                .thenReturn(Collections.singletonList(uvMap));
        when(aiobSopMetricService.getNodeHangUpByTaskIdSQL(anyString(), anyString(), any(), any()))
                .thenReturn(Collections.singletonList(hangUpMap));
        RBucket bucket = mock(RBucket.class);
        when(redisson.getBucket(anyString())).thenReturn(bucket);
        when(bucket.isExists()).thenReturn(false);
        scheduler.sendMessage();

        verify(messageService, never()).pushMessage(anyString(), any(MessageBody.class));
    }



    @Test
    void sendMessageShouldNotSendMessageWhenThresholdNotExceeded() {
        MockitoAnnotations.openMocks(this);

        tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("testTenant");
        tenantInfo.setAccountid("testAccount");

        sopUserConfig = new SopUserConfig();
        sopUserConfig.setTenantId("testTenant");
        sopUserConfig.setTaskId("testTask");
        sopUserConfig.setWarningThreshold(5);

        aiobSopMeta = new AiobSopMeta();
        aiobSopMeta.setNodeId("testNode");
        aiobSopMeta.setNodeName("testNodeName");

        now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.HOUR_OF_DAY, -1);
        oneHourAgo = calendar.getTime();

        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(tenantInfoService.getAllTenantInfo()).thenReturn(Collections.singletonList(tenantInfo));
        when(aiobSOPService.getSopConfigByTenantId(anyString())).thenReturn(Collections.singletonList(sopUserConfig));

        Map<String, Object> uvMap = new HashMap<>();
        uvMap.put("node_id", "testNode");
        uvMap.put("node_uv", "100");
        Map<String, Object> hangUpMap = new HashMap<>();
        hangUpMap.put("node_id", "testNode");
        hangUpMap.put("node_uv", "40");

        when(aiobSopMetricService.getNodeUvByTaskIdSQL(anyString(), anyString(), any(), any()))
            .thenReturn(Collections.singletonList(uvMap));
        when(aiobSopMetricService.getNodeHangUpByTaskIdSQL(anyString(), anyString(), any(), any()))
            .thenReturn(Collections.singletonList(hangUpMap));
        RBucket bucket = mock(RBucket.class);
        when(redisson.getBucket(anyString())).thenReturn(bucket);
        when(bucket.isExists()).thenReturn(true);
        scheduler.sendMessage();

        verify(messageService, never()).pushMessage(anyString(), any(MessageBody.class));
    }

    @Test
    void sendMessageShouldHandleExceptionGracefully1() {
        MockitoAnnotations.openMocks(this);

        tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("testTenant");
        tenantInfo.setAccountid("testAccount");

        sopUserConfig = new SopUserConfig();
        sopUserConfig.setTenantId("testTenant");
        sopUserConfig.setTaskId("testTask");
        sopUserConfig.setWarningThreshold(50);

        aiobSopMeta = new AiobSopMeta();
        aiobSopMeta.setNodeId("testNode");
        aiobSopMeta.setNodeName("testNodeName");

        now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.HOUR_OF_DAY, -1);
        oneHourAgo = calendar.getTime();

        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(tenantInfoService.getAllTenantInfo()).thenThrow(new RuntimeException("Test Exception"));

        scheduler.sendMessage();

        verify(messageService, never()).pushMessage(anyString(), any(MessageBody.class));
    }

    @Test
    void updateTaskStatusShouldUpdateCompletedTasks() {
    // Prepare test data
    String tenantId = "testTenant";
    SopUserConfig completedTask = new SopUserConfig();
    completedTask.setTaskId("completedTaskId");

    SopUserConfig inProgressTask = new SopUserConfig();
    inProgressTask.setTaskId("inProgressTaskId");

    List<SopUserConfig> sopUserConfigs = List.of(completedTask, inProgressTask);

    // Mock behavior
    when(aiobSOPService.getSopConfigStatus(tenantId, completedTask.getTaskId()))
        .thenReturn(SOPStatusEnum.COMPLETED.getCode());
    when(aiobSOPService.getSopConfigStatus(tenantId, inProgressTask.getTaskId()))
        .thenReturn(SOPStatusEnum.IN_PROCESS.getCode());

    // Execute
    scheduler.updateTaskStatus(tenantId, sopUserConfigs);

    // Verify
    verify(aiobSOPService).updateStatus(tenantId, SOPStatusEnum.COMPLETED, completedTask.getTaskId());
    verify(aiobSOPService, never()).updateStatus(eq(tenantId), eq(SOPStatusEnum.COMPLETED), eq(inProgressTask.getTaskId()));
    }

    @Test
    void updateTaskStatusShouldNotUpdateWhenNoCompletedTasks() {
    // Prepare test data
    String tenantId = "testTenant";
    SopUserConfig inProgressTask = new SopUserConfig();
    inProgressTask.setTaskId("inProgressTaskId");

    SopUserConfig notStartedTask = new SopUserConfig();
    notStartedTask.setTaskId("notStartedTaskId");

    List<SopUserConfig> sopUserConfigs = List.of(inProgressTask, notStartedTask);

    // Mock behavior
    when(aiobSOPService.getSopConfigStatus(tenantId, inProgressTask.getTaskId()))
        .thenReturn(SOPStatusEnum.IN_PROCESS.getCode());
    when(aiobSOPService.getSopConfigStatus(tenantId, notStartedTask.getTaskId()))
        .thenReturn(SOPStatusEnum.NOT_START.getCode());

    // Execute
    scheduler.updateTaskStatus(tenantId, sopUserConfigs);

    // Verify
    verify(aiobSOPService, never()).updateStatus(any(), any(), any());
    }

    @Test
    void updateTaskStatusShouldHandleEmptyConfigList() {
    // Prepare test data
    String tenantId = "testTenant";
    List<SopUserConfig> emptyList = Collections.emptyList();

    // Execute
    scheduler.updateTaskStatus(tenantId, emptyList);

    // Verify
    verify(aiobSOPService, never()).getSopConfigStatus(any(), any());
    verify(aiobSOPService, never()).updateStatus(any(), any(), any());
    }



    @Test
    void getStepNameShouldReturnNullWhenRobotSceneNot5Or6() {
        Map<String, Object> map = new HashMap<>();
        map.put("robotScene", "1");

        String result = scheduler.getStepName("tenant1", map, "node1");
    }

    @Test
    void getStepNameShouldReturnStepNameWhenRobotSceneIs5() {
        Map<String, Object> map = new HashMap<>();
        map.put("robotScene", "5");

        AiobSopMeta mockMeta = new AiobSopMeta();
        mockMeta.setStepName("testStep");

        when(aiobSOPService.getFrontNodeInfoWithNodeId("node1", "tenant1")).thenReturn(mockMeta);

        String result = scheduler.getStepName("tenant1", map, "node1");
    }

    @Test
    void getStepNameShouldReturnNodeNameWhenRobotSceneIs6() {
        Map<String, Object> map = new HashMap<>();
        map.put("robotScene", "6");
        map.put("sessionId", "session1");

        List<Map<String, Object>> agentIdRes = new ArrayList<>();
        Map<String, Object> agentMap = new HashMap<>();
        agentMap.put("agent_id", "agent1");
        agentMap.put("version_id", "version1");
        agentIdRes.add(agentMap);

        AiobDiagramVersionRecordViewResp resp = new AiobDiagramVersionRecordViewResp();
        AiobDiagramVersionRecordViewResp.AiobDiagramRecordData data = new AiobDiagramVersionRecordViewResp.AiobDiagramRecordData();
        AiobDiagramVersionRecordViewResp.AiobDiagramRecordData.AiobDiagramRecordTopic topic = new AiobDiagramVersionRecordViewResp.AiobDiagramRecordData.AiobDiagramRecordTopic();
        AiobDiagramVersionRecordViewResp.AiobDiagramRecordData.AiobDiagramRecordTopic.AiobDiagramRecordContent content = new AiobDiagramVersionRecordViewResp.AiobDiagramRecordData.AiobDiagramRecordTopic.AiobDiagramRecordContent();
        Map<String, AiobDiagramVersionRecordViewResp.AiobDiagramRecordData.AiobDiagramRecordTopic.AiobDiagramRecordContent.AiobDiagramRecordNode> nodes = new HashMap<>();
        AiobDiagramVersionRecordViewResp.AiobDiagramRecordData.AiobDiagramRecordTopic.AiobDiagramRecordContent.AiobDiagramRecordNode node = new AiobDiagramVersionRecordViewResp.AiobDiagramRecordData.AiobDiagramRecordTopic.AiobDiagramRecordContent.AiobDiagramRecordNode();
        node.setName("nodeName");
        AiobDiagramVersionRecordViewResp.AiobDiagramRecordData.AiobDiagramRecordTopic.AiobDiagramRecordContent.AiobDiagramRecordNode.AiobDiagramRecordTopicData d = new AiobDiagramVersionRecordViewResp.AiobDiagramRecordData.AiobDiagramRecordTopic.AiobDiagramRecordContent.AiobDiagramRecordNode.AiobDiagramRecordTopicData();
        d.setLabel("aaa");
        node.setData(d);
        nodes.put("node1", node);
        content.setNodes(nodes);
        topic.setContent(content);
        data.setTopicList(Collections.singletonList(topic));
        resp.setData(data);

        when(aiobSopMetricService.getAgentIdSQL("tenant1", "session1", "node1")).thenReturn(agentIdRes);
        when(aiobSOPService.getDiagramRecords("agent1", "version1")).thenReturn(resp);

        String result = scheduler.getStepName("tenant1", map, "node1");
    }

    @Test
    void sendMessageShouldSendMessageWhenHangUpRateExceedsThreshold() {
        // Arrange
        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("testTenant");
        tenantInfo.setAccountid("testAccount");
        when(tenantInfoService.getAllTenantInfo()).thenReturn(Collections.singletonList(tenantInfo));

        SopUserConfig userConfig = new SopUserConfig();
        userConfig.setTaskId("testTask");
        userConfig.setWarningThreshold(50); // 50%
        when(aiobSOPService.getSopConfigByStatus(anyString(), anyInt()))
                .thenReturn(Collections.singletonList(userConfig))
                .thenReturn(Collections.singletonList(userConfig));

        // Mock UV data
        Map<String, Object> uvMap = new HashMap<>();
        uvMap.put("node_id", "testNode");
        uvMap.put("node_uv", "100");
        when(aiobSopMetricService.getNodeUvByTaskIdSQL(anyString(), anyString(), any(), any()))
                .thenReturn(Collections.singletonList(uvMap));

        // Mock hang up data
        Map<String, Object> hangUpMap = new HashMap<>();
        hangUpMap.put("node_id", "testNode");
        hangUpMap.put("node_uv", "30"); // 60/100 = 60% > 50% threshold
        when(aiobSopMetricService.getNodeHangUpByTaskIdSQL(anyString(), anyString(), any(), any()))
                .thenReturn(Collections.singletonList(hangUpMap));

        // Mock task name
        Map<String, Object> taskNameMap = new HashMap<>();
        taskNameMap.put("taskName", "testTaskName");
        taskNameMap.put("robotScene", "5");
        when(dorisService.selectList(anyString())).thenReturn(Collections.singletonList(taskNameMap));

        // Mock step name
        AiobSopMeta aiobSopMeta = new AiobSopMeta();
        aiobSopMeta.setStepName("testStep");
        when(aiobSOPService.getFrontNodeInfoWithNodeId(anyString(), anyString()))
                .thenReturn(aiobSopMeta);
        scheduler.sendMessage();
    }
}