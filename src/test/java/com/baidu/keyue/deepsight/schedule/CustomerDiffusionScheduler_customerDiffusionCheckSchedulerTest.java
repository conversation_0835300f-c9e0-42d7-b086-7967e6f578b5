package com.baidu.keyue.deepsight.schedule;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.test.util.ReflectionTestUtils;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class CustomerDiffusionScheduler_customerDiffusionCheckSchedulerTest{

    @Value("${switch.customerDiffusion:false}")
    private Boolean customerDiffusion;

    @InjectMocks
    private CustomerDiffusionScheduler customerDiffusionScheduler;

    @InjectMocks
    private CustomerDiffusionScheduler scheduler;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(scheduler, "customerDiffusion", true);
    }

    // testCustomerDiffusionCheckSchedulerDisabled 用于测试 customerDiffusionCheckScheduler
    // generated by Comate
    @Test
    public void testCustomerDiffusionCheckSchedulerDisabled() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(customerDiffusionScheduler, "customerDiffusion", false);
        customerDiffusionScheduler.customerDiffusionCheckScheduler();
    }

}