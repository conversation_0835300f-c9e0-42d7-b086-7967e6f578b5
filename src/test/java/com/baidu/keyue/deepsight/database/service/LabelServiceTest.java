package com.baidu.keyue.deepsight.database.service;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelMapper;
import com.baidu.keyue.deepsight.service.label.LabelService;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.MDC;

@RunWith(MockitoJUnitRunner.class)
public class LabelServiceTest {

    @Mock
    private ExtendLabelMapper labelMapper;

    @InjectMocks
    private LabelService labelService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        MDC.put(Constants.REQUEST_USER_FIELD, "testUser");
    }

}