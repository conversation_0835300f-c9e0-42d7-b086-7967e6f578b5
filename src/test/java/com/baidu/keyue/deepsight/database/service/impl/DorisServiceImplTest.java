package com.baidu.keyue.deepsight.database.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.utils.JsonUtils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class DorisServiceImplTest {

    @InjectMocks
    private DorisServiceImpl dorisService;

    @Test
    void covertDorisValueShouldReturnListWhenTypeIsArray() {
        try (MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            List<Object> mockList = List.of("item1", "item2");
            mockedJsonUtils.when(() -> JsonUtils.readType(anyString(), any()))
                    .thenReturn(mockList);

            assertEquals(mockList, dorisService.covertDorisValue("array", "[\"item1\",\"item2\"]"));
        }
    }

    @Test
    void covertDorisValueShouldHandleJsonConversionException() {
        try (MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toMap(anyString()))
                    .thenThrow(new RuntimeException("JSON parse error"));

            assertEquals("invalid_json", dorisService.covertDorisValue("json", "invalid_json"));
        }
    }

    @Test
    void covertDorisValueShouldHandleArrayConversionException() {
        try (MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.readType(anyString(), any()))
                    .thenThrow(new RuntimeException("Array parse error"));

            assertEquals("invalid_array", dorisService.covertDorisValue("array", "invalid_array"));
        }
    }

    @Test
    void covertDorisValueShouldReturnNullWhenDataTypeOrDataIsNull() {
        assertEquals("test", dorisService.covertDorisValue(null, "test"));
        assertNull(dorisService.covertDorisValue("invalid_type", null));
    }

    @Test
    void covertDorisValueShouldReturnNumberWhenTypeIsNumber() {
        // Test with Number input
        assertEquals(123L, dorisService.covertDorisValue("int", 123L));
        assertEquals(123.45, dorisService.covertDorisValue("double", 123.45));

        // Test with String input that can be parsed to number
        assertEquals(123L, dorisService.covertDorisValue("int", "123"));
        assertEquals(123.45, dorisService.covertDorisValue("double", "123.45"));
    }

    @Test
    void covertDorisValueShouldReturnMapWhenTypeIsJson() {
        try (MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            Map<String, Object> mockMap = Map.of("key", "value");
            mockedJsonUtils.when(() -> JsonUtils.toMap(anyString())).thenReturn(mockMap);

            assertEquals(mockMap, dorisService.covertDorisValue("json", "{\"key\":\"value\"}"));
            mockedJsonUtils.verify(() -> JsonUtils.toMap("{\"key\":\"value\"}"));
        }
    }

    @Test
    void covertDorisValueShouldHandleNumberConversionException() {
        assertEquals(1, dorisService.covertDorisValue("int", 1));
    }

    @Test
    void covertDorisValueShouldReturnStringWhenTypeIsString() {
        // Test with String input
        assertEquals("test", dorisService.covertDorisValue("varchar", "test"));

        // Test with non-String input
        assertEquals("123", dorisService.covertDorisValue("char", 123));
    }

    @Test
    void covertDorisValueShouldReturnBooleanWhenTypeIsBoolean() {
        // Test with Boolean input
        assertTrue((Boolean) dorisService.covertDorisValue("boolean", true));

        // Test with String input that can be parsed to boolean
        assertTrue((Boolean) dorisService.covertDorisValue("boolean", "true"));
        assertFalse((Boolean) dorisService.covertDorisValue("boolean", "false"));
    }

    @Test
    void covertDorisValueShouldReturnOriginalValueWhenTypeIsNotHandled() {
        Object customObject = new Object();
        assertEquals(customObject, dorisService.covertDorisValue("custom_type", customObject));
    }

}