package com.baidu.keyue.deepsight.database.common.func;

import static org.junit.Assert.*;

import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;

import org.junit.Test;

import java.util.Arrays;
import java.util.List;

public class FuncTest{

    // testLikeWithLabelType 用于测试 like
    // generated by Comate
    @Test
    public void testLikeWithLabelType() {
        String field = "x->x";
        String value = "test";
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.LABEL);
    
        String expected = "(array_first_index(x->x LIKE test, x->x) > 0)";
        String actual = Func.like(field, value, ruleNode);
    
        assertEquals(expected, actual);
    }

    // testIsNullWithLabelType 用于测试 isNull
    // generated by Comate
    @Test
    public void testIsNullWithLabelType() {
        // Arrange
        String field = "testField";
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.LABEL);
    
        // Act
        String result = Func.isNull(field, ruleNode);
    
        // Assert
        assertEquals("(size(testField) = 0 OR testField IS NULL)", result);
    }

    // testNotNullLabelType 用于测试 notNull
    // generated by Comate
    @Test
    public void testNotNullLabelType() {
        // Arrange
        String field = "testField";
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.LABEL);
    
        // Act
        String result = Func.notNull(field, ruleNode);
    
        // Assert
        assertEquals("(size(testField) > 0)", result);
    }

    // testContainWithLabelType 用于测试 contain
    // generated by Comate
    @Test
    public void testContainWithLabelType() {
        String field = "x->x";
        List<String> values = Arrays.asList("1", "2", "3");
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.LABEL);
        String expected = "(array_first_index(x->x IN (1,2,3), x->x) > 0)";
        String actual = Func.contain(field, values, ruleNode);
        assertEquals(expected, actual);
    }

    // testNotContainWithLabelType 用于测试 notContain
    // generated by Comate
    @Test
    public void testNotContainWithLabelType() {
        String field = "test_field";
        List<String> values = Arrays.asList("value1", "value2", "value3");
        RuleNode ruleNode = new RuleNode();
        ruleNode.setType(RuleTypeEnum.LABEL);
    
        String expected = "(array_first_index(x->x IN (value1,value2,value3), test_field) = 0 AND size(test_field) > 0)";
        String actual = Func.notContain(field, values, ruleNode);
    
        assertEquals(expected, actual);
    }

}