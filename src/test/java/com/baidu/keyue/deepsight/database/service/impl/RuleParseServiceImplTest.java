package com.baidu.keyue.deepsight.database.service.impl;

import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.LabelFieldMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.rules.impl.RuleParseServiceImpl;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RuleParseServiceImplTest {
    /**
     * 字段信息mapper
     */
    @Mock
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    /**
     * 数据表信息mapper
     */
    @Mock
    private DataTableInfoMapper dataTableInfoMapper;

    /**
     * 标签字段mapper
     */
    @Mock
    private LabelFieldMapper labelFieldMapper;

    @InjectMocks
    private RuleParseServiceImpl ruleParseService;

    @Before
    public void setUp() {
        ruleParseService.setLabelFieldMapper(labelFieldMapper);
        ruleParseService.setDataTableInfoMapper(dataTableInfoMapper);
        ruleParseService.setTableFieldMetaInfoMapper(tableFieldMetaInfoMapper);

        DataTableInfo dataTableInfo_3 = new DataTableInfo();
        dataTableInfo_3.setId(3L);
        dataTableInfo_3.setTableName("aiob_conversation_session_service");
        when(dataTableInfoMapper.selectByPrimaryKey(3L)).thenReturn(dataTableInfo_3);
        DataTableInfo dataTableInfo_999 = new DataTableInfo();
        dataTableInfo_999.setId(999L);
        dataTableInfo_999.setTableName("user");
        when(dataTableInfoMapper.selectByPrimaryKey(999L)).thenReturn(dataTableInfo_999);

        TableFieldMetaInfo tableFieldMetaInfo_2 = new TableFieldMetaInfo();
        tableFieldMetaInfo_2.setId(2L);
        tableFieldMetaInfo_2.setEnField("memberId");
        tableFieldMetaInfo_2.setTableEnName("aiob_conversation_session_service");
        when(tableFieldMetaInfoMapper.selectByPrimaryKey(2L)).thenReturn(tableFieldMetaInfo_2);

        TableFieldMetaInfo tableFieldMetaInfo_3 = new TableFieldMetaInfo();
        tableFieldMetaInfo_3.setId(3L);
        tableFieldMetaInfo_3.setEnField("taskName");
        tableFieldMetaInfo_3.setTableEnName("aiob_conversation_session_service");
        when(tableFieldMetaInfoMapper.selectByPrimaryKey(3L)).thenReturn(tableFieldMetaInfo_3);

        TableFieldMetaInfo tableFieldMetaInfo_99 = new TableFieldMetaInfo();
        tableFieldMetaInfo_99.setId(99L);
        tableFieldMetaInfo_99.setEnField("mobile");
        tableFieldMetaInfo_99.setTableEnName("user");
        when(tableFieldMetaInfoMapper.selectByPrimaryKey(99L)).thenReturn(tableFieldMetaInfo_99);

        when(tableFieldMetaInfoMapper.selectByExample(any())).thenReturn(List.of(tableFieldMetaInfo_2, tableFieldMetaInfo_3, tableFieldMetaInfo_99));
    }

    @Test
    public void parseRuleGroup() {
        String ruleGroupJson = """
                {
                     "relation": "and",
                     "ruleGroups": [
                         {
                             "name": "条件组1",
                             "relation": "and",
                             "ruleNodes": [
                                 {
                                     "dataTableId": 3,
                                     "type": "datasetProperties",
                                     "filters": [
                                         {
                                             "type": "string",
                                             "fieldId": 2,
                                             "aggregator": "count",
                                             "function": "gt",
                                             "params": [
                                                 "1"
                                             ]
                                         }
                                     ]
                                 },
                                 {
                                     "dataTableId": 3,
                                     "type": "datasetProperties",
                                     "filters": [
                                         {
                                             "type": "string",
                                             "fieldId": 3,
                                             "aggregator": "count",
                                             "function": "gt",
                                             "params": [
                                                 "2"
                                             ]
                                         }
                                     ]
                                 }
                             ]
                         },
                         {
                             "name": "条件组2",
                             "relation": "or",
                             "ruleNodes": [
                                 {
                                     "type": "userProperties",
                                     "filters": [
                                         {
                                             "type": "string",
                                             "fieldId": 99,
                                             "function": "notContain",
                                             "params": [
                                                 "3432"
                                             ]
                                         }
                                     ]
                                 },
                                 {
                                     "type": "userProperties",
                                     "filters": [
                                         {
                                             "type": "string",
                                             "fieldId": 99,
                                             "function": "contain",
                                             "params": [
                                                 "32"
                                             ]
                                         }
                                     ]
                                 }
                             ]
                         }
                     ]
                 }""";
        RuleGroup ruleGroup = JsonUtils.toObjectWithoutException(ruleGroupJson, RuleGroup.class);
        DqlParseResult dqlParseResult = ruleParseService.parseRuleGroup(ruleGroup, new AtomicInteger(0));

        dqlParseResult.setSize(10);
        dqlParseResult.setOffset(0);
        String selectSql = dqlParseResult.parseDorisSql();
        assertEquals(selectSql, """
                SELECT `user`.`mobile`
                FROM `user`
                WHERE ((`user`.`mobile` NOT IN ('3432')) OR (`user`.`mobile` IN ('32'))) AND `user`.`mobile` IN (SELECT `aiob_conversation_session_service`.`mobile`
                FROM `aiob_conversation_session_service`
                GROUP BY `aiob_conversation_session_service`.`mobile`
                HAVING ((COUNT(`aiob_conversation_session_service`.`memberId`) > 1) AND (COUNT(`aiob_conversation_session_service`.`taskName`) > 2))
                )              
                LIMIT 0, 10
                """);
    }

    @Test
    public void parseRuleNode() {
    }

    @Test
    public void checkRuleNode() {
    }
}