package com.baidu.keyue.deepsight.database.service;

import static org.junit.Assert.*;

import com.baidu.keyue.deepsight.config.Constants;

import org.junit.Test;

public class DorisUtilsTest{

    @Test
    public void testGenerateCustomerGroupFieldName() {
        // Test with a positive groupId
        long groupId1 = 12345L;
        String expected1 = Constants.DORIS_CUSTOMER_GROUP_FIELD_PREFIX + groupId1;
        assertEquals(expected1, DorisUtils.generateCustomerGroupFieldName(groupId1));
    
        // Test with a zero groupId
        long groupId2 = 0L;
        String expected2 = Constants.DORIS_CUSTOMER_GROUP_FIELD_PREFIX + groupId2;
        assertEquals(expected2, DorisUtils.generateCustomerGroupFieldName(groupId2));
    
        // Test with a negative groupId
        long groupId3 = -6789L;
        String expected3 = Constants.DORIS_CUSTOMER_GROUP_FIELD_PREFIX + groupId3;
        assertEquals(expected3, DorisUtils.generateCustomerGroupFieldName(groupId3));
    }

    @Test
    public void testGenerateLabelFieldName() {
        // Test with a positive labelFieldId
        long labelFieldId1 = 54321L;
        String expected1 = String.format(Constants.DORIS_LABEL_FIELD_TEM, labelFieldId1);
        assertEquals(expected1, DorisUtils.generateLabelFieldName(labelFieldId1));
    
        // Test with a zero labelFieldId
        long labelFieldId2 = 0L;
        String expected2 = String.format(Constants.DORIS_LABEL_FIELD_TEM, labelFieldId2);
        assertEquals(expected2, DorisUtils.generateLabelFieldName(labelFieldId2));
    
        // Test with a negative labelFieldId
        long labelFieldId3 = -12345L;
        String expected3 = String.format(Constants.DORIS_LABEL_FIELD_TEM, labelFieldId3);
        assertEquals(expected3, DorisUtils.generateLabelFieldName(labelFieldId3));
    }

}