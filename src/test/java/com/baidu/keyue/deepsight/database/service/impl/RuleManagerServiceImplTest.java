package com.baidu.keyue.deepsight.database.service.impl;

import com.baidu.keyue.deepsight.service.label.LabelPropertiesService;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.models.rules.response.LabelCatalog;
import com.baidu.keyue.deepsight.models.rules.response.LabelPropertiesResponse;
import com.baidu.keyue.deepsight.service.rules.impl.RuleManagerServiceImpl;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class RuleManagerServiceImplTest{

    @Mock
    private LabelPropertiesService labelPropertiesService;

    @Mock
    private RuleParseService ruleParseService;

    @InjectMocks
    private RuleManagerServiceImpl ruleManagerService;

    private List<LabelCatalog> mockLabelCatalogs;

    @Before
    public void setUp() {
        LabelCatalog catalog1 = new LabelCatalog();
        catalog1.setId(1L);
        catalog1.setName("Category 1");
        LabelCatalog catalog2 = new LabelCatalog();
        catalog2.setId(2L);
        catalog2.setName("Category 2");
        mockLabelCatalogs = Arrays.asList(catalog1, catalog2);
        when(labelPropertiesService.getLabelCatalogs()).thenReturn(mockLabelCatalogs);

        DqlParseResult dqlParseResult = new DqlParseResult();
        dqlParseResult.setSelect("`user`.`mobile`");
        dqlParseResult.setFrom("user");
        dqlParseResult.getWhere().add("((`user`.`mobile` NOT IN ('3432')) OR (`user`.`mobile` IN ('32')))");
        dqlParseResult.getWhere().add("`user`.`mobile` IN (SELECT `aiob_conversation_session_service`.`mobile`\n" +
                "FROM `aiob_conversation_session_service`\n" +
                "WHERE (`aiob_conversation_session_service`.`memberId` IN ('32'))\n" +
                ")");
        when(ruleParseService.parseRuleGroup(Mockito.any(), Mockito.any())).thenReturn(dqlParseResult);
    }

    // testGetLabelPropertiesResp 用于测试 getLabelPropertiesResp
    // generated by Comate
    @Test
    public void testGetLabelPropertiesResp() {
        LabelPropertiesResponse response = ruleManagerService.getLabelPropertiesResp();
        assertEquals(2, response.getList().size());
        assertEquals("Category 1", response.getList().get(0).getName());
        assertEquals("Category 2", response.getList().get(1).getName());
    }

    /**
     * 规则组解析为 sql 同种逻辑的相同表筛选条件不会查询多次表只会加载一次
     * 标签计算、客群计算参考
     */
    @Test
    public void testParseRuleGroup() {
        String ruleGroupJson = """
                {
                	"relation": "and",
                	"ruleGroups": [{
                		"name": "条件组1",
                		"relation": "and",
                		"ruleNodes": [{
                		    "dataTableId": 3,
                			"type": "datasetProperties",
                			"filters": [{
                				"type": "string",
                				"fieldId": 2,
                				"function": "contain",
                				"params": ["32"]
                			}]
                		}]
                	},
                	{
                		"name": "条件组2",
                		"relation": "or",
                		"ruleNodes": [{
                			"type": "userProperties",
                			"filters": [{
                				"type": "string",
                				"fieldId": 99,
                				"function": "notContain",
                				"params": ["3432"]
                			}]
                		},
                		{
                			"type": "userProperties",
                			"filters": [{
                				"type": "string",
                				"fieldId": 99,
                				"function": "contain",
                				"params": ["32"]
                			}]
                		}]
                	}]
                }""";
        RuleGroup ruleGroup = JsonUtils.toObjectWithoutException(ruleGroupJson, RuleGroup.class);
        DqlParseResult dqlParseResult = ruleManagerService.parseRuleGroup(ruleGroup, new AtomicInteger(0));
        String countSql = dqlParseResult.parseCountSql();
        assertEquals(countSql, """
                SELECT COUNT(*)
                FROM `user`
                WHERE ((`user`.`mobile` NOT IN ('3432')) OR (`user`.`mobile` IN ('32'))) AND `user`.`mobile` IN (SELECT `aiob_conversation_session_service`.`mobile`
                FROM `aiob_conversation_session_service`
                WHERE (`aiob_conversation_session_service`.`memberId` IN ('32'))
                )
                """);

        dqlParseResult.setSize(10);
        dqlParseResult.setOffset(0);
        String selectSql = dqlParseResult.parseDorisSql();
        assertEquals(selectSql, """
                SELECT `user`.`mobile`
                FROM `user`
                WHERE ((`user`.`mobile` NOT IN ('3432')) OR (`user`.`mobile` IN ('32'))) AND `user`.`mobile` IN (SELECT `aiob_conversation_session_service`.`mobile`
                FROM `aiob_conversation_session_service`
                WHERE (`aiob_conversation_session_service`.`memberId` IN ('32'))
                )              
                LIMIT 0, 10
                """);
    }
}