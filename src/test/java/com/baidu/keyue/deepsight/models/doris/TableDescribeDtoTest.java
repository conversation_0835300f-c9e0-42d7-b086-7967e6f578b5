package com.baidu.keyue.deepsight.models.doris;


import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

public class TableDescribeDtoTest{

    @Test
    void defaultNullShouldReturnFalseWhenDefaultValueIsEmptyString() {
        TableDescribeDto dto = new TableDescribeDto("",  "varchar", true, false, "test");
        assertFalse(dto.defaultNull());
    }

    @Test
    void defaultNullShouldReturnTrueWhenDefaultValueIsNull() {
        TableDescribeDto dto = new TableDescribeDto(null, "varchar", true, false, "test");
        assertFalse(dto.defaultNull());
    }

    @Test
    void defaultNullShouldReturnTrueWhenDefaultValueIsUpperCaseNULL() {
        TableDescribeDto dto = new TableDescribeDto("NULL", "varchar", true, false, "test");
        assertFalse(dto.defaultNull());
    }

    @Test
    void defaultNullShouldReturnTrueWhenDefaultValueIsLowerCaseNull() {
        TableDescribeDto dto = new TableDescribeDto("null", "varchar", true, false, "test");
        assertFalse(dto.defaultNull());
    }

    @Test
    void defaultNullShouldReturnFalseWhenDefaultValueIsNotNullOrNullString() {
        TableDescribeDto dto = new TableDescribeDto("default", "varchar", true, false, "test");
        assertFalse(dto.defaultNull());
    }

}