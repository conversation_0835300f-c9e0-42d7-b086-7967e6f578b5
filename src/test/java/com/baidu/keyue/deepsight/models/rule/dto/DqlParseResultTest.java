package com.baidu.keyue.deepsight.models.rule.dto;

import static org.junit.Assert.*;

import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import org.junit.Test;

public class DqlParseResultTest{

    // testParseDorisSqlAllFields 用于测试 parseDorisSql
    // generated by Comate
    @Test
    public void testParseDorisSqlAllFields() {
        DqlParseResult result = new DqlParseResult();
        result.setSelect("a, b, c");
        result.setFrom("table1");
        result.getInnerJoin().add("table2");
        result.getJoinOn().add("table1.a = table2.b");
        result.getWhere().add("a > 10");
        result.getHaving().add("COUNT(a) > 1");
    
        String expected = "SELECT a, b, c\n" +
                "FROM table1\n" +
                "INNER JOIN table2 ON table1.a = table2.b\n" +
                "WHERE a > 10\n" +
                "HAVING COUNT(a) > 1\n";
    
        assertEquals(expected, result.parseDorisSql());
    }

}