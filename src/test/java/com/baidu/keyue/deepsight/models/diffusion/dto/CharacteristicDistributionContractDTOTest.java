package com.baidu.keyue.deepsight.models.diffusion.dto;

import static org.junit.jupiter.api.Assertions.*;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.enums.UserFiledEnum;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CharacteristicDistributionContractDTOTest{

    @Test
    @DisplayName("Test convertFrom with empty data list")
    void testConvertFromWithEmptyDataList() {
        UserFiledEnum userFiledEnum = UserFiledEnum.AGE;
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        CharacteristicDistributionContractDTO result = 
            CharacteristicDistributionContractDTO.convertFrom(userFiledEnum.getName(), userFiledEnum.getMergeFiled(), dataList);
        
        assertEquals(userFiledEnum.getName(), result.getCharacteristicName());
        assertTrue(result.getDistributionContractList().isEmpty());
    }

    @Test
    @DisplayName("Test convertFrom with null values in data")
    void testConvertFromWithNullValues() {
        UserFiledEnum userFiledEnum = UserFiledEnum.GENDER;
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        Map<String, Object> data1 = new HashMap<>();
        data1.put(userFiledEnum.getMergeFiled(), "male");
        data1.put(Constants.PREDICT_CHARACTERISTIC_FIELD_NAME, null);
        data1.put(Constants.SAMPLE_CHARACTERISTIC_FIELD_NAME, null);
        
        Map<String, Object> data2 = new HashMap<>();
        data2.put(userFiledEnum.getMergeFiled(), null);
        data2.put(Constants.PREDICT_CHARACTERISTIC_FIELD_NAME, "30");
        data2.put(Constants.SAMPLE_CHARACTERISTIC_FIELD_NAME, "25");
        
        dataList.add(data1);
        dataList.add(data2);
        
        CharacteristicDistributionContractDTO result = 
            CharacteristicDistributionContractDTO.convertFrom(userFiledEnum.getName(), userFiledEnum.getMergeFiled(), dataList);
        
        assertEquals(userFiledEnum.getName(), result.getCharacteristicName());
        assertEquals(1, result.getDistributionContractList().size());
        
        assertEquals("male", result.getDistributionContractList().get(0).getGrade());
        assertEquals(0, result.getDistributionContractList().get(0).getPredictCount());
        assertEquals(0, result.getDistributionContractList().get(0).getSamplingCount());
    }

    @Test
    @DisplayName("Test convertFrom with different UserFiledEnum")
    void testConvertFromWithDifferentUserFiledEnum() {
        UserFiledEnum userFiledEnum = UserFiledEnum.EDUCATION_LEVEL;
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        Map<String, Object> data = new HashMap<>();
        data.put(userFiledEnum.getMergeFiled(), "bachelor");
        data.put(Constants.PREDICT_CHARACTERISTIC_FIELD_NAME, "100");
        data.put(Constants.SAMPLE_CHARACTERISTIC_FIELD_NAME, "80");
        dataList.add(data);
        
        CharacteristicDistributionContractDTO result = 
            CharacteristicDistributionContractDTO.convertFrom(userFiledEnum.getName(), userFiledEnum.getMergeFiled(), dataList);
        
        assertEquals(userFiledEnum.getName(), result.getCharacteristicName());
        assertEquals(1, result.getDistributionContractList().size());
        assertEquals("bachelor", result.getDistributionContractList().get(0).getGrade());
        assertEquals(100, result.getDistributionContractList().get(0).getPredictCount());
        assertEquals(80, result.getDistributionContractList().get(0).getSamplingCount());
    }

    @Test
    @DisplayName("Test convertFrom with valid data list")
    void testConvertFromWithValidDataList() {
        UserFiledEnum userFiledEnum = UserFiledEnum.AGE;
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        Map<String, Object> data1 = new HashMap<>();
        data1.put(userFiledEnum.getMergeFiled(), "0-0.1");
        data1.put(Constants.PREDICT_CHARACTERISTIC_FIELD_NAME, "10");
        data1.put(Constants.SAMPLE_CHARACTERISTIC_FIELD_NAME, "5");
        
        Map<String, Object> data2 = new HashMap<>();
        data2.put(userFiledEnum.getMergeFiled(), "0.1-0.2");
        data2.put(Constants.PREDICT_CHARACTERISTIC_FIELD_NAME, "20");
        data2.put(Constants.SAMPLE_CHARACTERISTIC_FIELD_NAME, "15");
        
        dataList.add(data1);
        dataList.add(data2);
        
        CharacteristicDistributionContractDTO result = 
            CharacteristicDistributionContractDTO.convertFrom(userFiledEnum.getName(), userFiledEnum.getMergeFiled(), dataList);
        
        assertEquals(userFiledEnum.getName(), result.getCharacteristicName());
        assertEquals(2, result.getDistributionContractList().size());
        
        assertEquals("0-0.1", result.getDistributionContractList().get(0).getGrade());
        assertEquals(10, result.getDistributionContractList().get(0).getPredictCount());
        assertEquals(5, result.getDistributionContractList().get(0).getSamplingCount());
        
        assertEquals("0.1-0.2", result.getDistributionContractList().get(1).getGrade());
        assertEquals(20, result.getDistributionContractList().get(1).getPredictCount());
        assertEquals(15, result.getDistributionContractList().get(1).getSamplingCount());
    }

}