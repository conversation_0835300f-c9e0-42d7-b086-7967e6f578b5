package com.baidu.keyue.deepsight.models.rule.response;

import static org.junit.Assert.*;

import com.baidu.keyue.deepsight.models.rules.response.DatasetInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;

import org.junit.Test;

import java.util.Date;

public class DatasetInfoTest{

    // testConvertFrom 用于测试 convertFrom
    // generated by Comate
    @Test
    public void testConvertFrom() {
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        dataTableInfo.setCnName("测试数据集");
        dataTableInfo.setEnName("Test Dataset");
        dataTableInfo.setDataType(1);
        dataTableInfo.setDescription("测试数据源");
        dataTableInfo.setStatus((byte) 1);
        dataTableInfo.setIsVisable(true);
        dataTableInfo.setIsPreset((byte) 0);
        dataTableInfo.setIsDel((byte) 0);
        dataTableInfo.setCreateTime(new Date());
        dataTableInfo.setUpdateTime(new Date());
        DatasetInfo datasetInfo = DatasetInfo.convertFrom(dataTableInfo);
        assertNotNull(datasetInfo);
        assertEquals(Long.valueOf(1L), datasetInfo.getDataTableId());
        assertEquals("测试数据集", datasetInfo.getDatasetName());
    }

}