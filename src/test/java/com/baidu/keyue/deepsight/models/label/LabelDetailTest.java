package com.baidu.keyue.deepsight.models.label;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;

import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.Label;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;

import org.junit.jupiter.api.Test;

import java.util.Date;
import java.util.Map;

public class LabelDetailTest{

    @Test
    void convertFromShouldSetBasicFields() {
        // Arrange
        Label label = new Label();
        label.setId(123L);
        label.setLabelName("testLabel");
        label.setExecMod((byte) 1);
        label.setTriggerMod((byte) 0);
        label.setCreator("testUser");
        Date now = new Date();
        label.setCreateTime(now);
        label.setLabelCalStatus((byte) 2);
    
        // Act
        LabelDetail result = LabelDetail.convertFrom(label, Map.of());
    
        // Assert
        assertEquals(123L, result.getLabelId());
        assertEquals("testLabel", result.getLabelName());
        assertEquals("SQL", result.getExecMod());
        assertEquals("定时更新", result.getTriggerMod());
        assertEquals("testUser", result.getCreator());
        assertEquals(DatetimeUtils.formatDate(now), result.getCreateTime());
        assertEquals(TaskExecStatusEnum.SUCCESS, result.getStatus());
        assertEquals("", result.getErrorMessage());
    }

    @Test
    void convertFromShouldHandleNullSchedulerMap() {
        // Arrange
        Label label = new Label();
        label.setLabelCalStatus((byte) 3);
        label.setTask(456L);
    
        // Act
        LabelDetail result = LabelDetail.convertFrom(label, null);
    
        // Assert
        assertEquals(TaskExecStatusEnum.FAILED, result.getStatus());
        assertEquals("", result.getErrorMessage());
    }

    @Test
    void convertFromShouldHandleMissingSchedulerForFailedStatus() {
        // Arrange
        Label label = new Label();
        label.setLabelCalStatus((byte) 3);
        label.setTask(456L);
    
        // Act
        LabelDetail result = LabelDetail.convertFrom(label, Map.of(789L, mock(TaskSchedulerWithBLOBs.class)));
    
        // Assert
        assertEquals(TaskExecStatusEnum.FAILED, result.getStatus());
        assertEquals("", result.getErrorMessage());
    }

}