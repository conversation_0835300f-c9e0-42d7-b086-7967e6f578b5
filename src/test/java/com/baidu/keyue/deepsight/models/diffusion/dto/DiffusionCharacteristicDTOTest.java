package com.baidu.keyue.deepsight.models.diffusion.dto;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

public class DiffusionCharacteristicDTOTest{

    @Test
    void compareToShouldReturnPositiveWhenOtherScoreIsGreater() {
        DiffusionCharacteristicDTO dto1 = new DiffusionCharacteristicDTO("1.0f", 1.0f);
        DiffusionCharacteristicDTO dto2 = new DiffusionCharacteristicDTO("2.0f", 2.0f);
        assertTrue(dto1.compareTo(dto2) > 0);
    }

    @Test
    void compareToShouldReturnNegativeWhenOtherScoreIsLess() {
        DiffusionCharacteristicDTO dto1 = new DiffusionCharacteristicDTO("2.0f", 2.0f);
        DiffusionCharacteristicDTO dto2 = new DiffusionCharacteristicDTO("1.0f", 1.0f);
        assertTrue(dto1.compareTo(dto2) < 0);
    }

    @Test
    void compareToShouldReturnZeroWhenScoresAreEqual() {
        DiffusionCharacteristicDTO dto1 = new DiffusionCharacteristicDTO("1.0f", 1.0f);
        DiffusionCharacteristicDTO dto2 = new DiffusionCharacteristicDTO("1.0f", 1.0f);
        assertEquals(0, dto1.compareTo(dto2));
    }

}