package com.baidu.keyue.deepsight.models.catalog;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;

import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.google.common.collect.Lists;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ListCatalogResponseTest{

    private ListCatalogResponse listCatalogResponse;
    // testFlattenCatalogIdsWithNullResults 用于测试 flattenCatalogIds

    @Before
    public void setUp() {
        listCatalogResponse = new ListCatalogResponse();
    }
    // testFlattenCatalogIdsWithNullResults 用于测试 flattenCatalogIds
    // generated by Comate
    @Test
    public void testFlattenCatalogIdsWithNullResults() {
        ListCatalogResponse response = new ListCatalogResponse();
        response.setResults(null);
        List<Long> result = response.flattenCatalogIds();
        assertNull(result);
    }

    // testFlattenCatalogIdsWithEmptyResults 用于测试 flattenCatalogIds
    // generated by Comate
    @Test
    public void testFlattenCatalogIdsWithEmptyResults() {
        ListCatalogResponse response = new ListCatalogResponse();
        response.setResults(Arrays.asList());
        List<Long> result = response.flattenCatalogIds();
        assertEquals(0, result.size());
    }

    // testFlattenCatalogIdsWithSingleCatalogDetail 用于测试 flattenCatalogIds
    // generated by Comate
    @Test
    public void testFlattenCatalogIdsWithSingleCatalogDetail() {
        CatalogDetail detail = new CatalogDetail();
        detail.setCatalogId(1L);
        ListCatalogResponse response = new ListCatalogResponse();
        response.setResults(Arrays.asList(detail));
        List<Long> result = response.flattenCatalogIds();
        assertEquals(1, result.size());
        assertEquals(Long.valueOf(1L), result.get(0));
    }

    // testFill 用于测试 fill
    // generated by Comate
    @Test
    public void testFill() {
        Map<Long, List<LabelWithBLOBs>> labelMap = new HashMap<>();
        List<CatalogDetail> results = Lists.newArrayList();
        CatalogDetail parent = new CatalogDetail();
        parent.setCatalogId(1L);
        CatalogDetail child = new CatalogDetail();
        child.setCatalogId(2L);
        parent.setChildren(Lists.newArrayList(child));
        results.add(parent);
        LabelWithBLOBs label1 = new LabelWithBLOBs();
        label1.setId(1L);
        label1.setLabelName("Label 1");
        List<LabelWithBLOBs> labelsForCatalog1 = Lists.newArrayList(label1);
        labelMap.put(1L, labelsForCatalog1);
        LabelWithBLOBs label2 = new LabelWithBLOBs();
        label2.setId(2L);
        label2.setLabelName("Label 2");
        List<LabelWithBLOBs> labelsForCatalog2 = Lists.newArrayList(label2);
        labelMap.put(2L, labelsForCatalog2);
        listCatalogResponse.fill(labelMap, results);
        assertEquals(1, results.size());
        assertEquals(1L, results.get(0).getCatalogId().longValue());
        assertTrue(CollectionUtils.isNotEmpty(results.get(0).getLabels()));
        assertEquals(1, results.get(0).getChildren().size());
        assertEquals(2L, results.get(0).getChildren().get(0).getCatalogId().longValue());
    }

}