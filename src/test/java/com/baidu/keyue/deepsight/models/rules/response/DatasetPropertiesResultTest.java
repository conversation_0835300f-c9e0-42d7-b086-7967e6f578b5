package com.baidu.keyue.deepsight.models.rules.response;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.enums.TableFieldValueEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelField;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;

import com.baidu.keyue.deepsight.mysqldb.mapper.LabelFieldMapper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

public class DatasetPropertiesResultTest{

    private TableFieldMetaInfo tableFieldMetaInfo;

    @Before
    public void setUp() {
        tableFieldMetaInfo = Mockito.mock(TableFieldMetaInfo.class);
        Mockito.when(tableFieldMetaInfo.getDataTableId()).thenReturn(1L);
        Mockito.when(tableFieldMetaInfo.getId()).thenReturn(1L);
        Mockito.when(tableFieldMetaInfo.getCnField()).thenReturn("TestField");
        Mockito.when(tableFieldMetaInfo.getFieldType()).thenReturn("number");
        Mockito.when(tableFieldMetaInfo.getFieldTag()).thenReturn(TableFieldTagEnum.PRIMARY.getCode());
        Mockito.when(tableFieldMetaInfo.getValueType()).thenReturn(TableFieldValueEnum.NUMBER.getValueType());
        Mockito.when(tableFieldMetaInfo.getConfigInfos()).thenReturn(null);
    }

    // testConvertFromWithPrimaryTag 用于测试 convertFrom
    // generated by Comate
    @Test
    public void testConvertFromWithPrimaryTag() {
        DatasetPropertiesResult result = DatasetPropertiesResult.convertFrom(tableFieldMetaInfo);
    
        assertEquals(1L, result.getDataTableId().longValue());
        assertEquals(1L, result.getFieldId().longValue());
        assertEquals("TestField", result.getCname());
        assertEquals(FilterTypeEnum.NUMBER.getType(), result.getDataType());
        assertTrue(result.getIsIdKey());
        assertTrue(!result.getIsMeasure());
        assertTrue(result.getEnums() == null);
    }

}