package com.baidu.keyue.deepsight.models.rules.dto;

import static org.junit.Assert.*;

import org.junit.Test;

import java.util.Arrays;

public class DqlParseResultTest{

    // testParseDorisSqlOnlySelect 用于测试 parseDorisSql
    // generated by Comate
    @Test
    public void testParseDorisSqlOnlySelect() {
        DqlParseResult result = new DqlParseResult();
        result.setSelect("a, b");
    
        String expected = "SELECT a, b\n";
    
        assertEquals(expected, result.parseDorisSql());
    }

    // testParseDorisSqlAllFields 用于测试 parseDorisSql
    // generated by Comate
    @Test
    public void testParseDorisSqlAllFields() {
        DqlParseResult result = new DqlParseResult();
        result.setSelect("a, b");
        result.setFrom("table1");
        result.setInnerJoin(Arrays.asList("table2", "table3"));
        result.setJoinOn(Arrays.asList("table1.a = table2.b", "table1.b = table3.c"));
        result.setWhere(Arrays.asList("a > 10", "b < 20"));
        result.setHaving(Arrays.asList("SUM(a) > 100", "COUNT(b) > 5"));
        String expected = "SELECT a, b\n" +
                "FROM table1\n" +
                "INNER JOIN table2 ON table1.a = table2.b\n" +
                "INNER JOIN table3 ON table1.b = table3.c\n" +
                "WHERE a > 10 and b < 20\n" +
                "HAVING SUM(a) > 100 and COUNT(b) > 5\n";
        assertEquals(expected, result.parseDorisSql());
    }

}