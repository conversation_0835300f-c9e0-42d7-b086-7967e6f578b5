package com.baidu.keyue.deepsight.models.sop;

import static org.junit.jupiter.api.Assertions.*;

import com.baidu.keyue.deepsight.models.sop.nodepredict.NodeProcessSummaryResponse;

import org.junit.jupiter.api.Test;

public class NodeProcessSummaryResponseTest{

    // testSuccessWhenStatusIsSuccess 用于测试 success
    // generated by Comate
    @Test
    void testSuccessWhenStatusIsSuccess() {
        NodeProcessSummaryResponse response = new NodeProcessSummaryResponse();
        response.setStatus("success");
        assertTrue(response.success(), "Expected success() to return true when status is 'success'");
    }

    // testSuccessWhenStatusIsNotSuccess 用于测试 success
    // generated by Comate
    @Test
    void testSuccessWhenStatusIsNotSuccess() {
        NodeProcessSummaryResponse response = new NodeProcessSummaryResponse();
        response.setStatus("failure");
        assertFalse(response.success(), "Expected success() to return false when status is not 'success'");
    }

    // testSuccessWhenStatusIsCaseInsensitive 用于测试 success
    // generated by Comate
    @Test
    void testSuccessWhenStatusIsCaseInsensitive() {
        NodeProcessSummaryResponse response = new NodeProcessSummaryResponse();
        response.setStatus("SUCCESS");
        assertTrue(response.success(), "Expected success() to return true when status is 'SUCCESS'");
    }

    // testSuccessWithStatusSuccess 用于测试 success
    // generated by Comate
    @Test
    public void testSuccessWithStatusSuccess() {
        NodeProcessSummaryResponse response = new NodeProcessSummaryResponse();
        response.setStatus("success");
        assertTrue(response.success());
    }

    // testSuccessWithStatusFailure 用于测试 success
    // generated by Comate
    @Test
    public void testSuccessWithStatusFailure() {
        NodeProcessSummaryResponse response = new NodeProcessSummaryResponse();
        response.setStatus("failure");
        assertFalse(response.success());
    }

    // testSuccessWithStatusNull 用于测试 success
    // generated by Comate
    @Test
    public void testSuccessWithStatusNull() {
        NodeProcessSummaryResponse response = new NodeProcessSummaryResponse();
        response.setStatus(null);
        assertFalse(response.success());
    }
}