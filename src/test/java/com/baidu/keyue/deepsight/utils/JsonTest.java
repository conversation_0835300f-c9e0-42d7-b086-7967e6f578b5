package com.baidu.keyue.deepsight.utils;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordViewResp;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;

public class JsonTest {
    @Test
    public void testRuleParse() {
        String s = "[]";
        List<RuleFilter> dataFilterRule = JsonUtils.toListUnchecked(s, List.class, RuleFilter.class);
        System.out.println(dataFilterRule);
    }

    @Test
    public void testNull() {
        Map<String, Object> record = new HashMap<>();
        System.out.println(record.get("id"));
        String sessionId = (String) record.get("id");
        System.out.println(sessionId);
    }

    @Test
    public void testAiobJson() throws IOException {
        String msg = "{\"time\":1750041062938,\"data\":{\"menu\":{\"id\":\"3e32a8d4-b125-469d-91bb-ae151b613998\",\"version\":1,\"structure\":{\"startTopic\":\"6d5c461d-0114-4d7b-a114-30cb2a429ab5\",\"topicIDs\":[\"6d5c461d-0114-4d7b-a114-30cb2a429ab5\"],\"diagrams\":{\"6d5c461d-0114-4d7b-a114-30cb2a429ab5\":{\"sourceID\":\"6d5c461d-0114-4d7b-a114-30cb2a429ab5\",\"name\":\"起始主题\",\"type\":\"TOPIC\",\"menuItems\":[{\"type\":\"NODE\",\"sourceID\":\"start00000000000000000000\",\"intentNameEn\":null,\"intentNameZh\":null}]}}}},\"topicList\":[{\"id\":\"6d5c461d-0114-4d7b-a114-30cb2a429ab5\",\"content\":{\"sourceID\":\"6d5c461d-0114-4d7b-a114-30cb2a429ab5\",\"edges\":\"{\\\"edges\\\":[{\\\"id\\\":\\\"reactflow__edge-start0000000000000000000063dab0568e48f700079d1e51-dx6QXDSfiEY3KXYp8sOZN6i8uCqNdyZqLMN4dB1_RzAQz3QDuYIuMTYi__uOYOwYB9WMA0Jk\\\",\\\"source\\\":\\\"start00000000000000000000\\\",\\\"sourceHandle\\\":\\\"63dab0568e48f700079d1e51\\\",\\\"target\\\":\\\"dx6QXDSfiEY3KXYp8sOZN6i8uCqNdyZqLMN4\\\",\\\"targetHandle\\\":\\\"dB1_RzAQz3QDuYIuMTYi__uOYOwYB9WMA0Jk\\\",\\\"type\\\":\\\"CustomEdge\\\"},{\\\"id\\\":\\\"reactflow__edge-ebwKgcZJoTeehfzvtDVPXG0uPF3F-BvsydfZHGZGEOFYilEH3jEqLgPyWkCqwkEf_TeKmGlN-VqIsbdK7llO8uFFNO_V0Ty94sIE_GoJQdyoi8LrpwFQWBamfBi9CszATB-Umupk4lujhtP1x\\\",\\\"source\\\":\\\"ebwKgcZJoTeehfzvtDVPXG0uPF3F-BvsydfZ\\\",\\\"sourceHandle\\\":\\\"HGZGEOFYilEH3jEqLgPyWkCqwkEf_TeKmGlN\\\",\\\"target\\\":\\\"VqIsbdK7llO8uFFNO_V0Ty94sIE_GoJQdyoi\\\",\\\"targetHandle\\\":\\\"8LrpwFQWBamfBi9CszATB-Umupk4lujhtP1x\\\",\\\"type\\\":\\\"CustomEdge\\\"},{\\\"id\\\":\\\"reactflow__edge-dx6QXDSfiEY3KXYp8sOZN6i8uCqNdyZqLMN4txVSz_Xr-s41eglBTVl86soGRnjUhEaHe37g-ebwKgcZJoTeehfzvtDVPXG0uPF3F-BvsydfZyN40MVuiJgALIDAL-km2lxzoe8kDbLOEke9p\\\",\\\"source\\\":\\\"dx6QXDSfiEY3KXYp8sOZN6i8uCqNdyZqLMN4\\\",\\\"sourceHandle\\\":\\\"txVSz_Xr-s41eglBTVl86soGRnjUhEaHe37g\\\",\\\"target\\\":\\\"ebwKgcZJoTeehfzvtDVPXG0uPF3F-BvsydfZ\\\",\\\"targetHandle\\\":\\\"yN40MVuiJgALIDAL-km2lxzoe8kDbLOEke9p\\\",\\\"type\\\":\\\"CustomEdge\\\"},{\\\"id\\\":\\\"reactflow__edge-VqIsbdK7llO8uFFNO_V0Ty94sIE_GoJQdyoiTlOjYyWclwxA2dsl3AI13E_ZWAfOirAYGRT9-XbOxmGXddn7P78BgYXRm5iL0wVrPyblRXlGWxeHZTqcQl-_knj2-GJnD25tKgX6Z080Lfv75\\\",\\\"source\\\":\\\"VqIsbdK7llO8uFFNO_V0Ty94sIE_GoJQdyoi\\\",\\\"sourceHandle\\\":\\\"TlOjYyWclwxA2dsl3AI13E_ZWAfOirAYGRT9\\\",\\\"target\\\":\\\"XbOxmGXddn7P78BgYXRm5iL0wVrPyblRXlGW\\\",\\\"targetHandle\\\":\\\"xeHZTqcQl-_knj2-GJnD25tKgX6Z080Lfv75\\\",\\\"type\\\":\\\"CustomEdge\\\"}]}\",\"nodes\":{\"start00000000000000000000\":{\"type\":\"start\",\"nodeID\":\"start00000000000000000000\",\"blockId\":null,\"name\":null,\"topicName\":null,\"coords\":[200.0,136.62340564181125],\"data\":{\"label\":\"开始\",\"portsV2\":{\"builtIn\":{\"next\":{\"type\":\"next\",\"target\":\"dB1_RzAQz3QDuYIuMTYi__uOYOwYB9WMA0Jk\",\"targetTopicId\":\"6d5c461d-0114-4d7b-a114-30cb2a429ab5\",\"id\":\"63dab0568e48f700079d1e51\",\"data\":{\"points\":null}},\"fail\":null},\"dynamic\":[]},\"hubSlot\":false,\"sourceHandle\":null}},\"dx6QXDSfiEY3KXYp8sOZN6i8uCqNdyZqLMN4\":{\"type\":\"block\",\"nodeID\":\"dx6QXDSfiEY3KXYp8sOZN6i8uCqNdyZqLMN4\",\"blockId\":null,\"name\":\"\",\"topicName\":null,\"coords\":[396.0,125.81170282090562],\"data\":{\"label\":\"任务 1\",\"steps\":[\"dB1_RzAQz3QDuYIuMTYi__uOYOwYB9WMA0Jk\"],\"hubSlot\":false,\"sourceHandle\":null}},\"ebwKgcZJoTeehfzvtDVPXG0uPF3F-BvsydfZ\":{\"type\":\"block\",\"nodeID\":\"ebwKgcZJoTeehfzvtDVPXG0uPF3F-BvsydfZ\",\"blockId\":null,\"name\":\"\",\"topicName\":null,\"coords\":[703.0,176.62340564181125],\"data\":{\"label\":\"任务 2\",\"steps\":[\"yN40MVuiJgALIDAL-km2lxzoe8kDbLOEke9p\"],\"hubSlot\":false,\"sourceHandle\":null}},\"VqIsbdK7llO8uFFNO_V0Ty94sIE_GoJQdyoi\":{\"type\":\"block\",\"nodeID\":\"VqIsbdK7llO8uFFNO_V0Ty94sIE_GoJQdyoi\",\"blockId\":null,\"name\":\"\",\"topicName\":null,\"coords\":[1095.0,234.81170282090562],\"data\":{\"label\":\"任务 3\",\"steps\":[\"8LrpwFQWBamfBi9CszATB-Umupk4lujhtP1x\"],\"hubSlot\":false,\"sourceHandle\":null}},\"XbOxmGXddn7P78BgYXRm5iL0wVrPyblRXlGW\":{\"type\":\"block\",\"nodeID\":\"XbOxmGXddn7P78BgYXRm5iL0wVrPyblRXlGW\",\"blockId\":null,\"name\":\"\",\"topicName\":null,\"coords\":[1410.0,276.62340564181125],\"data\":{\"label\":\"任务 4\",\"steps\":[\"xeHZTqcQl-_knj2-GJnD25tKgX6Z080Lfv75\"],\"hubSlot\":false,\"sourceHandle\":null}},\"dB1_RzAQz3QDuYIuMTYi__uOYOwYB9WMA0Jk\":{\"type\":\"text\",\"nodeID\":\"dB1_RzAQz3QDuYIuMTYi__uOYOwYB9WMA0Jk\",\"blockId\":\"dx6QXDSfiEY3KXYp8sOZN6i8uCqNdyZqLMN4\",\"name\":\"任务 1, 步骤1\",\"topicName\":null,\"coords\":null,\"data\":{\"texts\":[{\"id\":\"7KdMyseSNhEbb5DStJtIslgzj6pnI3sQ1XRH\",\"type\":1,\"richText\":\"\",\"text\":[{\"text\":\"你好，我是机器人，你有事吗\",\"id\":null,\"name\":null,\"nameZh\":null,\"type\":null}],\"audioInfo\":{\"id\":null,\"flowDiagramRelId\":\"\",\"audioName\":\"\",\"audioUrl\":\"\",\"audioDuration\":0,\"audioUploadedStatus\":\"\",\"audioUploadedTime\":null,\"audioId\":\"\"}},{\"id\":\"iUYSYhzTegYFhiaV8DaHr5UYJw677HPFGs4-\",\"type\":3,\"richText\":\"\",\"text\":[],\"audioInfo\":{\"id\":null,\"flowDiagramRelId\":\"\",\"audioName\":\"\",\"audioUrl\":\"\",\"audioDuration\":0,\"audioUploadedStatus\":\"\",\"audioUploadedTime\":null,\"audioId\":\"\"}}],\"portsV2\":{\"builtIn\":{\"next\":{\"type\":\"next\",\"target\":\"yN40MVuiJgALIDAL-km2lxzoe8kDbLOEke9p\",\"targetTopicId\":\"6d5c461d-0114-4d7b-a114-30cb2a429ab5\",\"id\":\"V_7zp-WDP19ya1oxyOpEgwtiMt8xNRns9G1p\",\"data\":{\"points\":null}},\"fail\":null},\"dynamic\":[]},\"hubSlot\":false,\"sourceHandle\":\"txVSz_Xr-s41eglBTVl86soGRnjUhEaHe37g\",\"dialogueInterruptConfig\":null}},\"yN40MVuiJgALIDAL-km2lxzoe8kDbLOEke9p\":{\"type\":\"capture\",\"nodeID\":\"yN40MVuiJgALIDAL-km2lxzoe8kDbLOEke9p\",\"blockId\":\"ebwKgcZJoTeehfzvtDVPXG0uPF3F-BvsydfZ\",\"name\":\"任务 2, 步骤1\",\"topicName\":null,\"coords\":null,\"data\":{\"multiType\":false,\"capture\":{\"variable\":\"last_user_response\",\"image\":\"last_user_image\",\"imageGuide\":[]},\"portsV2\":{\"builtIn\":{\"next\":{\"type\":\"next\",\"target\":\"8LrpwFQWBamfBi9CszATB-Umupk4lujhtP1x\",\"targetTopicId\":\"6d5c461d-0114-4d7b-a114-30cb2a429ab5\",\"id\":\"HGZGEOFYilEH3jEqLgPyWkCqwkEf_TeKmGlN\",\"data\":{\"points\":null}},\"fail\":{\"type\":\"fail\",\"target\":null,\"targetTopicId\":\"\",\"id\":\"RGy_kD6ORG9aorDA6eMdU5aUiAiVnhNe3zLr\",\"data\":{\"points\":null}}},\"dynamic\":[]},\"hubSlot\":false,\"sourceHandle\":\"HGZGEOFYilEH3jEqLgPyWkCqwkEf_TeKmGlN\",\"enableGlobalRecognition\":false,\"pausingIdentifyConfig\":null}},\"8LrpwFQWBamfBi9CszATB-Umupk4lujhtP1x\":{\"type\":\"text\",\"nodeID\":\"8LrpwFQWBamfBi9CszATB-Umupk4lujhtP1x\",\"blockId\":\"VqIsbdK7llO8uFFNO_V0Ty94sIE_GoJQdyoi\",\"name\":\"任务 3, 步骤1\",\"topicName\":null,\"coords\":null,\"data\":{\"texts\":[{\"id\":\"Ydx01sjcIuoEx2fCWzsbE5IXqWTTpltqZK2s\",\"type\":1,\"richText\":\"\",\"text\":[{\"text\":\"好嘞，再见\",\"id\":null,\"name\":null,\"nameZh\":null,\"type\":null}],\"audioInfo\":{\"id\":null,\"flowDiagramRelId\":\"\",\"audioName\":\"\",\"audioUrl\":\"\",\"audioDuration\":0,\"audioUploadedStatus\":\"\",\"audioUploadedTime\":null,\"audioId\":\"\"}},{\"id\":\"jQ4mYSiYmC10eyeAPovh4p2z_2BlnnOD8XWF\",\"type\":3,\"richText\":\"\",\"text\":[],\"audioInfo\":{\"id\":null,\"flowDiagramRelId\":\"\",\"audioName\":\"\",\"audioUrl\":\"\",\"audioDuration\":0,\"audioUploadedStatus\":\"\",\"audioUploadedTime\":null,\"audioId\":\"\"}}],\"portsV2\":{\"builtIn\":{\"next\":{\"type\":\"next\",\"target\":\"xeHZTqcQl-_knj2-GJnD25tKgX6Z080Lfv75\",\"targetTopicId\":\"6d5c461d-0114-4d7b-a114-30cb2a429ab5\",\"id\":\"LiciPC6djLOdTGWw-i9E0estOYtG3BHQYs1S\",\"data\":{\"points\":null}},\"fail\":null},\"dynamic\":[]},\"hubSlot\":false,\"sourceHandle\":\"TlOjYyWclwxA2dsl3AI13E_ZWAfOirAYGRT9\",\"dialogueInterruptConfig\":null}},\"xeHZTqcQl-_knj2-GJnD25tKgX6Z080Lfv75\":{\"type\":\"instruction\",\"nodeID\":\"xeHZTqcQl-_knj2-GJnD25tKgX6Z080Lfv75\",\"blockId\":\"XbOxmGXddn7P78BgYXRm5iL0wVrPyblRXlGW\",\"name\":\"任务 4, 步骤1\",\"topicName\":null,\"coords\":null,\"data\":{\"instruction\":{\"nameEn\":\"system_hangup\",\"name\":\"system_hangup\",\"type\":0,\"defaultParameters\":[],\"customParameters\":[]},\"portsV2\":{\"builtIn\":{\"next\":{\"type\":\"next\",\"target\":null,\"targetTopicId\":\"\",\"id\":\"GPi41wGSsb5s7bGpexa3BByz24swwFSi9ItS\",\"data\":{\"points\":null}},\"fail\":null},\"dynamic\":[]},\"hubSlot\":false,\"sourceHandle\":\"IeSE1b8xHHTFM9aWy2ij8c-LgRTK2jajy08V\"}}}}}],\"diagramEditStatus\":null,\"updateReason\":null,\"id\":null,\"agentId\":null,\"tenantId\":null,\"createdUserId\":null,\"editUserId\":null,\"created\":null,\"updated\":null,\"diagram\":null},\"code\":200,\"msg\":\"OK\"}";
        AiobDiagramVersionRecordViewResp resp = JsonUtils.toObject(msg, AiobDiagramVersionRecordViewResp.class);

        Map<String, String> nextBlockMap = new HashMap<>();
        resp.getData().getTopicList().stream()
                .filter(item -> Objects.nonNull(item.getContent()))
                .map(item -> item.getContent().getEdges())
                .map(item -> JsonUtils.toObjectWithoutException(item, AiobDiagramVersionRecordViewResp.AiobDiagramRecordData.AiobDiagramRecordTopic.AiobDiagramRecordContent.AiobDiagramRecordEdgeWrap.class))
                .filter(item -> Objects.nonNull(item) && CollectionUtils.isNotEmpty(item.getEdges()))
                .forEach(item -> item.getEdges().forEach(edge -> {
                    nextBlockMap.put(edge.getSource(), edge.getTarget());
                }));
        System.out.println(nextBlockMap.get("dx6QXDSfiEY3KXYp8sOZN6i8uCqNdyZqLMN4"));
        System.out.println(nextBlockMap.get("XbOxmGXddn7P78BgYXRm5iL0wVrPyblRXlGW"));
    }
}
