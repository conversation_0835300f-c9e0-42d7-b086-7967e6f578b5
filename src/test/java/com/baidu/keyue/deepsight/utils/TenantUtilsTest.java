package com.baidu.keyue.deepsight.utils;

import com.baidu.keyue.deepsight.config.Constants;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class TenantUtilsTest {

    @Test
    void testGenerateMethodsWithNullTenantId() {
        String tenantId = null;
        Assertions.assertEquals(Constants.DORIS_DEFAULT_USER_PROFILE_TABLE + "_null", TenantUtils.generateUserProfileTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_DEFAULT_LABEL_TABLE + "_null", TenantUtils.generateMockUserTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_AIOB_SESSION_TABLE + "_null", TenantUtils.generateAiobSessionTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_MEMORY_EXTRACT_TABLE + "_null", TenantUtils.generateMemoryExtractTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_AIOB_RECORD_TABLE + "_null", TenantUtils.generateAiobRecordTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_KEYUE_RECORD_TABLE + "_null", TenantUtils.generateKeyueRecordTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_VISITOR_MANAGE_TABLE + "_null", TenantUtils.generateVisitorManageTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_ID_MAPPING_TABLE + "_null", TenantUtils.generateIdMappingTableName(tenantId));
    }

    @Test
    void testGenerateUserProfileTableName() {
        String tenantId = "test123";
        String expected = Constants.DORIS_DEFAULT_USER_PROFILE_TABLE + "_" + tenantId;
        Assertions.assertEquals(expected, TenantUtils.generateUserProfileTableName(tenantId));
    }

    @Test
    void testGenerateMockUserTableName() {
        String tenantId = "test456";
        String expected = Constants.DORIS_DEFAULT_LABEL_TABLE + "_" + tenantId;
        Assertions.assertEquals(expected, TenantUtils.generateMockUserTableName(tenantId));
    }

    @Test
    void testGenerateAiobSessionTableName() {
        String tenantId = "test789";
        String expected = Constants.DORIS_AIOB_SESSION_TABLE + "_" + tenantId;
        Assertions.assertEquals(expected, TenantUtils.generateAiobSessionTableName(tenantId));
    }

    @Test
    void testGenerateAiobRecordTableName() {
        String tenantId = "test202";
        String expected = Constants.DORIS_AIOB_RECORD_TABLE + "_" + tenantId;
        Assertions.assertEquals(expected, TenantUtils.generateAiobRecordTableName(tenantId));
    }

    @Test
    void testGenerateIdMappingTableName() {
        String tenantId = "test505";
        String expected = Constants.DORIS_ID_MAPPING_TABLE + "_" + tenantId;
        Assertions.assertEquals(expected, TenantUtils.generateIdMappingTableName(tenantId));
    }

    @Test
    void testGenerateMethodsWithEmptyTenantId() {
        String tenantId = "";
        Assertions.assertEquals(Constants.DORIS_DEFAULT_USER_PROFILE_TABLE + "_", TenantUtils.generateUserProfileTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_DEFAULT_LABEL_TABLE + "_", TenantUtils.generateMockUserTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_AIOB_SESSION_TABLE + "_", TenantUtils.generateAiobSessionTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_MEMORY_EXTRACT_TABLE + "_", TenantUtils.generateMemoryExtractTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_AIOB_RECORD_TABLE + "_", TenantUtils.generateAiobRecordTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_KEYUE_RECORD_TABLE + "_", TenantUtils.generateKeyueRecordTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_VISITOR_MANAGE_TABLE + "_", TenantUtils.generateVisitorManageTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_ID_MAPPING_TABLE + "_", TenantUtils.generateIdMappingTableName(tenantId));
    }

    @Test
    void testGenerateMemoryExtractTableName() {
        String tenantId = "test101";
        String expected = Constants.DORIS_MEMORY_EXTRACT_TABLE + "_" + tenantId;
        Assertions.assertEquals(expected, TenantUtils.generateMemoryExtractTableName(tenantId));
    }

    @Test
    void testGenerateKeyueRecordTableName() {
        String tenantId = "test303";
        String expected = Constants.DORIS_KEYUE_RECORD_TABLE + "_" + tenantId;
        Assertions.assertEquals(expected, TenantUtils.generateKeyueRecordTableName(tenantId));
    }

    @Test
    void testGenerateVisitorManageTableName() {
        String tenantId = "test404";
        String expected = Constants.DORIS_VISITOR_MANAGE_TABLE + "_" + tenantId;
        Assertions.assertEquals(expected, TenantUtils.generateVisitorManageTableName(tenantId));
    }

    @Test
    void testGenerateAiobSOPNodeTableName() {
        String tenantId = "test123";
        Assertions.assertEquals(Constants.DORIS_AIOB_SOP_NODE_METRIC_TABLE_PREFIX + "_" + tenantId, 
            TenantUtils.generateAiobSOPNodeTableName(tenantId));
        
        tenantId = null;
        Assertions.assertEquals(Constants.DORIS_AIOB_SOP_NODE_METRIC_TABLE_PREFIX + "_null", 
            TenantUtils.generateAiobSOPNodeTableName(tenantId));
        
        tenantId = "";
        Assertions.assertEquals(Constants.DORIS_AIOB_SOP_NODE_METRIC_TABLE_PREFIX + "_", 
            TenantUtils.generateAiobSOPNodeTableName(tenantId));
    }

    @Test
    void testGenerateMethodsWithNullTenantId1() {
        String tenantId = null;
        Assertions.assertEquals(Constants.DORIS_DEFAULT_USER_PROFILE_TABLE + "_null", TenantUtils.generateUserProfileTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_DEFAULT_LABEL_TABLE + "_null", TenantUtils.generateMockUserTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_AIOB_SESSION_TABLE + "_null", TenantUtils.generateAiobSessionTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_MEMORY_EXTRACT_TABLE + "_null", TenantUtils.generateMemoryExtractTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_AIOB_RECORD_TABLE + "_null", TenantUtils.generateAiobRecordTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_KEYUE_RECORD_TABLE + "_null", TenantUtils.generateKeyueRecordTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_VISITOR_MANAGE_TABLE + "_null", TenantUtils.generateVisitorManageTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_ID_MAPPING_TABLE + "_null", TenantUtils.generateIdMappingTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_AIOB_SOP_EDGE_METRIC_TABLE_PREFIX + "_null", TenantUtils.generateAiobSOPEdgeTableName(tenantId));
    }

    @Test
    void testGenerateAiobSOPEdgeTableName() {
        String tenantId = "test123";
        Assertions.assertEquals(Constants.DORIS_AIOB_SOP_EDGE_METRIC_TABLE_PREFIX + "_" + tenantId, 
            TenantUtils.generateAiobSOPEdgeTableName(tenantId));
    }

    @Test
    void testGenerateAiobSOPEdgeTableNameWithEmptyString() {
        String tenantId = "";
        Assertions.assertEquals(Constants.DORIS_AIOB_SOP_EDGE_METRIC_TABLE_PREFIX + "_" + tenantId, 
            TenantUtils.generateAiobSOPEdgeTableName(tenantId));
    }

    @Test
    void testGenerateMethodsWithNullTenantId2() {
        String tenantId = null;
        Assertions.assertEquals(Constants.DORIS_DEFAULT_USER_PROFILE_TABLE + "_null", TenantUtils.generateUserProfileTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_DEFAULT_LABEL_TABLE + "_null", TenantUtils.generateMockUserTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_AIOB_SESSION_TABLE + "_null", TenantUtils.generateAiobSessionTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_MEMORY_EXTRACT_TABLE + "_null", TenantUtils.generateMemoryExtractTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_AIOB_RECORD_TABLE + "_null", TenantUtils.generateAiobRecordTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_KEYUE_RECORD_TABLE + "_null", TenantUtils.generateKeyueRecordTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_VISITOR_MANAGE_TABLE + "_null", TenantUtils.generateVisitorManageTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_ID_MAPPING_TABLE + "_null", TenantUtils.generateIdMappingTableName(tenantId));
        Assertions.assertEquals(Constants.DORIS_AIOB_DEBUG_RECORD_TABLE_PREFIX + "_null", TenantUtils.generateAiobDebugTableName(tenantId));
    }

    @Test
    void testGenerateAiobDebugTableName() {
        String tenantId = "test123";
        Assertions.assertEquals(Constants.DORIS_AIOB_DEBUG_RECORD_TABLE_PREFIX + "_" + tenantId, 
            TenantUtils.generateAiobDebugTableName(tenantId));
    }

    @Test
    void testGenerateAiobDebugTableNameWithEmptyTenantId() {
        String tenantId = "";
        Assertions.assertEquals(Constants.DORIS_AIOB_DEBUG_RECORD_TABLE_PREFIX + "_" + tenantId, 
            TenantUtils.generateAiobDebugTableName(tenantId));
    }

}