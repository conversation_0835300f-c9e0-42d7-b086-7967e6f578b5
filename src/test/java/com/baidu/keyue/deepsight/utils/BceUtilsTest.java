package com.baidu.keyue.deepsight.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

import com.baidu.keyue.deepsight.models.datamanage.dto.BosProperty;
import com.baidu.keyue.deepsight.models.datamanage.dto.StsRequestTo;
import com.baidu.keyue.deepsight.models.datamanage.request.BceRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.StsSessionVo;
import com.baidu.kybase.commons.utils.HttpUtil;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class BceUtilsTest{

    @Test
    void testSignature() throws MalformedURLException, NoSuchAlgorithmException, InvalidKeyException, URISyntaxException {
        BceRequest<?> request = mock(BceRequest.class);
        when(request.getUrl()).thenReturn("http://example.com");
        when(request.getHeader()).thenReturn(new HashMap<>());
        when(request.getWaitSignHeader()).thenReturn(Set.of("host"));
        when(request.getRequestDate()).thenReturn(new java.util.Date());
    
        BosProperty bosProperty = new BosProperty();
        bosProperty.setAccessKey("testAccessKey");
        bosProperty.setSecret("testSecret");
    
        String requestId = "testRequestId";
    
        String result = BceUtils.signature(request, bosProperty, requestId);
        assertNotNull(result);
        assertTrue(result.startsWith("bce-auth-v1/testAccessKey/"));
    }

    @Test
    void testGetStsSession() throws MalformedURLException, NoSuchAlgorithmException, InvalidKeyException, URISyntaxException {
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            // Setup test data
            BceRequest<StsRequestTo> request = mock(BceRequest.class);
            when(request.getUrl()).thenReturn("http://example.com");
            when(request.getHeader()).thenReturn(new HashMap<>());
            when(request.getWaitSignHeader()).thenReturn(Set.of("host"));
            when(request.getRequestDate()).thenReturn(new java.util.Date());
            when(request.getRequestBody()).thenReturn(new StsRequestTo());
    
            BosProperty bosProperty = new BosProperty();
            bosProperty.setAccessKey("testAccessKey");
            bosProperty.setSecret("testSecret");
            bosProperty.setBucket("testBucket");
            bosProperty.setEndPoint("testEndpoint");
            bosProperty.setRegion("testRegion");
    
            // Mock HTTP response
            String mockResponse = "{\"accessKeyId\":\"test\",\"secretAccessKey\":\"test\",\"sessionToken\":\"test\"}";
            mockedHttpUtil.when(() -> HttpUtil.postJson(anyString(), anyString(), any(Map.class)))
                    .thenReturn(mockResponse);
    
            // Execute test
            StsSessionVo result = BceUtils.getStsSession(request, bosProperty);
    
            // Verify results
            assertNotNull(result);
            assertEquals("testBucket", result.getBucket());
            assertEquals("testEndpoint", result.getEndPoint());
            assertEquals("testRegion", result.getRegion());
            assertEquals("test", result.getAccessKeyId());
            assertEquals("test", result.getSecretAccessKey());
            assertEquals("test", result.getSessionToken());
        }
    }

    @Test
    void testHmacSha256WithEmptyInput() {
        assertThrows(IllegalArgumentException.class, () -> BceUtils.hmacSha256("data", ""));
    }

    @Test
    void testSignatureWithInvalidUrl() {
        BceRequest<?> request = mock(BceRequest.class);
        when(request.getUrl()).thenReturn("invalid url");
        BosProperty bosProperty = new BosProperty();
        assertThrows(MalformedURLException.class, () -> BceUtils.signature(request, bosProperty, "test"));
    }

    @Test
    void testGetStsSessionWithHttpError() {
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            BceRequest<StsRequestTo> request = mock(BceRequest.class);
            when(request.getUrl()).thenReturn("http://example.com");
            when(request.getHeader()).thenReturn(new HashMap<>());
            when(request.getWaitSignHeader()).thenReturn(Set.of("host"));
            when(request.getRequestDate()).thenReturn(new java.util.Date());
            when(request.getRequestBody()).thenReturn(new StsRequestTo());
    
            BosProperty bosProperty = new BosProperty();
            bosProperty.setAccessKey("testAccessKey");
            bosProperty.setSecret("testSecret");
    
            mockedHttpUtil.when(() -> HttpUtil.postJson(anyString(), anyString(), any(Map.class)))
                    .thenReturn(null);
    
            assertThrows(IllegalArgumentException.class, () -> BceUtils.getStsSession(request, bosProperty));
        }
    }

    @Test
    void testHmacSha256() throws NoSuchAlgorithmException, InvalidKeyException {
        String data = "testData";
        String key = "testKey";
        String result = BceUtils.hmacSha256(data, key);
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(64, result.length()); // SHA-256 produces 64 character hex string
    }

}