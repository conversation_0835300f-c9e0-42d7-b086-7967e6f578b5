package com.baidu.keyue.deepsight.utils;

import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import com.google.common.collect.Lists;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import org.junit.jupiter.api.Test;

public class CronUtilsTest {

    @Test
    public void testEveryDay() {
        // Test case for 0 hour
        String expected = "0 0 * * *";
        String actual = CronUtils.everyDay(0);
        assertEquals(expected, actual);

        // Test case for 12 hour
        expected = "0 12 * * *";
        actual = CronUtils.everyDay(12);
        assertEquals(expected, actual);

        // Test case for 23 hour
        expected = "0 23 * * *";
        actual = CronUtils.everyDay(23);
        assertEquals(expected, actual);
    }

    @Test
    public void testEveryWeek() {
        // Test case for 0 hour and 1 day of week
        String expected = "0 0 * * 1";
        String actual = CronUtils.everyWeek(0, 1);
        assertEquals(expected, actual);

        // Test case for 12 hour and 5 day of week
        expected = "0 12 * * 5";
        actual = CronUtils.everyWeek(12, 5);
        assertEquals(expected, actual);

        // Test case for 23 hour and 2 day of week
        expected = "0 23 * * 2";
        actual = CronUtils.everyWeek(23, 2);
        assertEquals(expected, actual);
    }

    @Test
    public void testEveryMonth() {
        // Test case for 0 hour and 1 day of month
        String expected = "0 0 1 * *";
        String actual = CronUtils.everyMonth(0, 1);
        assertEquals(expected, actual);

        // Test case for 12 hour and 31 day of month
        expected = "0 12 31 * *";
        actual = CronUtils.everyMonth(12, 31);
        assertEquals(expected, actual);

        // Test case for 23 hour and 30 day of month
        expected = "0 23 30 * *";
        actual = CronUtils.everyMonth(23, 30);
        assertEquals(expected, actual);
    }

    @Test
    public void testNextExecLocalDateTimeWithInvalidCron() {
        // Test case for invalid cron expression
        String cronExpression = "-1, ";
        LocalDateTime actual = CronUtils.nextExecLocalDateTime(cronExpression);
        assertNull(actual);
    }

    @Test
    public void testNextExecLocalDateTimeWithEmptyCron() {
        // Test case for empty cron expression
        String cronExpression = "";
        LocalDateTime actual = CronUtils.nextExecLocalDateTime(cronExpression);
        assertNull(actual);
    }

    @Test
    public void testNextExecLocalDateTimeWithNullCron() {
        LocalDateTime actual = CronUtils.nextExecLocalDateTime(null);
        assertNull(actual);
    }

    @Test
    public void testNextExecDateWithEmptyCron() {
        // Test case for empty cron expression
        String cronExpression = "";
        Date actualDate = CronUtils.nextExecDate(cronExpression);
        assertNull(actualDate);
    }

    @Test
    public void testNextExecDateWithNullCron() {
        // Test case for null cron expression
        Date actualDate = CronUtils.nextExecDate(null);
        assertNull(actualDate);
    }

    @Test
    public void testNextExecDateWithValidCron() {
        String cronExpression = "0 1 * * *";

        Calendar n = Calendar.getInstance();
        n.setTime(new Date());

        Calendar expectedDate = Calendar.getInstance();
        expectedDate.clear();
        expectedDate.set(n.get(Calendar.YEAR), n.get(Calendar.MONTH), n.get(Calendar.DATE), 1, 0);
        expectedDate.add(Calendar.DAY_OF_MONTH, 1);

        Date expected = expectedDate.getTime();

        Date actualDate = CronUtils.nextExecDate(cronExpression);
        assertNotNull(actualDate);
    }

    @Test
    public void testNextExecDateWithInvalidCron() {
        // Test case for invalid cron expression
        String cronExpression = "invalid_cron";
        Date actualDate = CronUtils.nextExecDate(cronExpression);
        assertNull(actualDate);
    }

    @Test
    public void testWeeklyNextExecLocalDateTimeWithValidCron() {
        List<String> cronExpressions = Lists.newArrayList(
                "0 1 * * 0",
                "0 1 * * 1",
                "0 1 * * 2",
                "0 1 * * 3",
                "0 1 * * 4",
                "0 1 * * 5",
                "0 1 * * 6",
                "0 1 * * 7",
                "0 1 * * 8"
        );
        for (String cronExpression : cronExpressions) {
            LocalDateTime actual = CronUtils.nextExecLocalDateTime(cronExpression);
            System.out.printf("%s -- %s\n", cronExpression, actual);
        }
    }

}
