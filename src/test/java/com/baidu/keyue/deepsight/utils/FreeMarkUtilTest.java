package com.baidu.keyue.deepsight.utils;


import freemarker.template.TemplateException;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

public class FreeMarkUtilTest{

    @Test
    void testStringReplaceWithSpecialCharacters() throws Exception {
        String template = "Special: ${value}";
        Map<String, Object> params = new HashMap<>();
        params.put("value", "!@#$%^&*()");
        
        String result = FreeMarkUtil.stringReplace(template, params);
        
        assertEquals("Special: !@#$%^&*()", result);
    }

    @Test
    void testStringReplaceWithNumberValues() throws Exception {
        String template = "Number: ${num}";
        Map<String, Object> params = new HashMap<>();
        params.put("num", 42);
        
        String result = FreeMarkUtil.stringReplace(template, params);
        
        assertEquals("Number: 42", result);
    }

    @Test
    void testStringReplaceWithSimpleTemplate() throws Exception {
        String template = "Hello, ${name}!";
        Map<String, Object> params = new HashMap<>();
        params.put("name", "World");
        
        String result = FreeMarkUtil.stringReplace(template, params);
        
        assertEquals("Hello, World!", result);
    }

    @Test
    void testStringReplaceWithMultipleVariables() throws Exception {
        String template = "User: ${user}, Age: ${age}";
        Map<String, Object> params = new HashMap<>();
        params.put("user", "Alice");
        params.put("age", 25);
        
        String result = FreeMarkUtil.stringReplace(template, params);
        
        assertEquals("User: Alice, Age: 25", result);
    }

    @Test
    void testStringReplaceWithEmptyMap() throws Exception {
        String template = "This is a ${test}";
        Map<String, Object> params = new HashMap<>();
        
        assertThrows(TemplateException.class, () -> FreeMarkUtil.stringReplace(template, params));
    }

    @Test
    void testStringReplaceWithNullSource() {
        Map<String, Object> params = new HashMap<>();
        params.put("key", "value");
        
        assertThrows(Exception.class, () -> {
            FreeMarkUtil.stringReplace(null, params);
        });
    }

    @Test
    void testStringReplaceWithNestedExpressions() throws Exception {
        String template = "Nested: ${user.name}";
        Map<String, Object> params = new HashMap<>();
        Map<String, Object> user = new HashMap<>();
        user.put("name", "Bob");
        params.put("user", user);
        
        String result = FreeMarkUtil.stringReplace(template, params);
        
        assertEquals("Nested: Bob", result);
    }

}