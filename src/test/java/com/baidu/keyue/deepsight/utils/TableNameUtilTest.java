package com.baidu.keyue.deepsight.utils;

import com.baidu.keyue.deepsight.config.Constants;
import org.junit.jupiter.api.Assertions;

import org.junit.jupiter.api.Test;

public class TableNameUtilTest{

    @Test
    void generateDiffusionTemporaryTableNameShouldReturnCorrectTableName() {
        // Arrange
        long diffusionId = 12345L;
        String expected = Constants.DORIS_DIFFUSION_PROCESS_TEMPORARY_TABLE_NAME_PREFIX + diffusionId;
    
        // Act
        String actual = TableNameUtil.generateDiffusionTemporaryTableName(diffusionId);
    
        // Assert
        Assertions.assertEquals(expected, actual);
    }

    @Test
    void generateDiffusionTemporaryTableNameShouldHandleZeroId() {
        // Arrange
        long diffusionId = 0L;
        String expected = Constants.DORIS_DIFFUSION_PROCESS_TEMPORARY_TABLE_NAME_PREFIX + diffusionId;
    
        // Act
        String actual = TableNameUtil.generateDiffusionTemporaryTableName(diffusionId);
    
        // Assert
        Assertions.assertEquals(expected, actual);
    }

    @Test
    void generateDiffusionTemporaryTableNameShouldHandleNegativeId() {
        // Arrange
        long diffusionId = -98765L;
        String expected = Constants.DORIS_DIFFUSION_PROCESS_TEMPORARY_TABLE_NAME_PREFIX + diffusionId;
    
        // Act
        String actual = TableNameUtil.generateDiffusionTemporaryTableName(diffusionId);
    
        // Assert
        Assertions.assertEquals(expected, actual);
    }

    @Test
    void generateDiffusionTemporaryTableNameShouldHandleMaxLongValue() {
        // Arrange
        long diffusionId = Long.MAX_VALUE;
        String expected = Constants.DORIS_DIFFUSION_PROCESS_TEMPORARY_TABLE_NAME_PREFIX + diffusionId;
    
        // Act
        String actual = TableNameUtil.generateDiffusionTemporaryTableName(diffusionId);
    
        // Assert
        Assertions.assertEquals(expected, actual);
    }

    @Test
    void generateDiffusionTemporaryTableNameShouldHandleMinLongValue() {
        // Arrange
        long diffusionId = Long.MIN_VALUE;
        String expected = Constants.DORIS_DIFFUSION_PROCESS_TEMPORARY_TABLE_NAME_PREFIX + diffusionId;
    
        // Act
        String actual = TableNameUtil.generateDiffusionTemporaryTableName(diffusionId);
    
        // Assert
        Assertions.assertEquals(expected, actual);
    }

}