package com.baidu.keyue.deepsight.utils;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.Test;

public class GzipUtilsTest{

    @Test
    void testCompressObjWithValidObject() {
        TestObject obj = new TestObject("test", 123);
        byte[] compressed = GzipUtils.compressObj(obj);
        assertNotNull(compressed);
        String decompressed = GzipUtils.decompressToString(compressed);
        assertEquals("{\"name\":\"test\",\"value\":123}", decompressed);
    }

    @Test
    void testCompressStringWithEmptyString() {
        assertNull(GzipUtils.compressString(""));
    }

    @Test
    void testDecompressToStringWithNull() {
        assertNull(GzipUtils.decompressToString(null));
    }

    @Test
    void testDecompressToStringWithValidData() {
        String input = "Test decompression";
        byte[] compressed = GzipUtils.compressString(input);
        String output = GzipUtils.decompressToString(compressed);
        assertEquals(input, output);
    }

    @Test
    void testDecompressToStringWithInvalidData() {
        byte[] invalidData = {1, 2, 3, 4, 5};
        assertThrows(RuntimeException.class, () -> GzipUtils.decompressToString(invalidData));
    }

    @Test
    void testCompressObjWithNull() {
        assertNull(GzipUtils.compressObj(null));
    }

    @Test
    void testCompressStringWithNull() {
        assertNull(GzipUtils.compressString(null));
    }

    @Test
    void testCompressStringWithBlankString() {
        assertNull(GzipUtils.compressString("   "));
    }

    @Test
    void testCompressStringWithValidString() {
        String input = "Hello, World!";
        byte[] compressed = GzipUtils.compressString(input);
        assertNotNull(compressed);
        String decompressed = GzipUtils.decompressToString(compressed);
        assertEquals(input, decompressed);
    }

    @Test
    void testDecompressToStringWithEmptyArray() {
        assertNull(GzipUtils.decompressToString(new byte[0]));
    }

    static class TestObject {
        private String name;
        private int value;

        public TestObject(String name, int value) {
            this.name = name;
            this.value = value;
        }

        public String getName() {
            return name;
        }

        public int getValue() {
            return value;
        }
    }

}