package com.baidu.keyue.deepsight.utils;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import java.util.List;

public class AliasUtilsTest{

    @Test
    void testGetAliasWithPositiveCount() {
        List<String> result = AliasUtils.getAlias(3);
        assertEquals(List.of("A", "B", "C"), result);
    }

    @Test
    void testGetAliasWithLargeCount() {
        List<String> result = AliasUtils.getAlias(26);
        assertEquals(26, result.size());
        assertEquals("A", result.get(0));
        assertEquals("Z", result.get(25));
    }

    @Test
    void testGetAliasNameWithZero() {
        assertEquals("A", AliasUtils.getAliasName(0));
    }

    @Test
    void testGetAliasNameWithPositiveNumber() {
        assertEquals("B", AliasUtils.getAliasName(1));
        assertEquals("Z", AliasUtils.getAliasName(25));
    }

    @Test
    void testGetAliasNameWithLargeNumber() {
        assertEquals("[", AliasUtils.getAliasName(26)); // ASCII after Z
    }

    @Test
    void testGetAliasWithZeroCount() {
        List<String> result = AliasUtils.getAlias(0);
        assertTrue(result.isEmpty());
    }

}