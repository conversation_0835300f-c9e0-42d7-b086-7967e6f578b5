package com.baidu.keyue.deepsight.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DatetimeUtilsTest{

    @Test
    public void testFromDatetimeStrNullDate() {
        // Given
        String datetimeStr = null;
    
        // When
        Date result = DatetimeUtils.fromDatetimeStr(datetimeStr, DatetimeUtils.DATE_TIME_ZONE_FORMATTER);
    
        // Then
        Assertions.assertNull(result);
    }

    @Test
    public void testFromDatetimeStrValidDate() {
        // Given
        String datetimeStr = "2023-10-01 12:34:56";
    
        // When
        Date result = DatetimeUtils.fromDatetimeStr(datetimeStr, DatetimeUtils.DATE_TIME_FORMATTER);
    
        // Then
        Assertions.assertEquals("2023-10-01 12:34:56", result.toString());
    }

    @Test
    public void testFromDatetimeStrInvalidDate() {
        // Given
        String datetimeStr = "invalid-date";
    
        // When
        Date result = DatetimeUtils.fromDatetimeStr(datetimeStr, DatetimeUtils.DATE_TIME_FORMATTER);
    
        // Then
        Assertions.assertNull(result);
    }

    @Test
    public void testFromDatetimeStrEmptyDate() {
        // Given
        String datetimeStr = "";
    
        // When
        Date result = DatetimeUtils.fromDatetimeStr(datetimeStr, DatetimeUtils.DATE_TIME_FORMATTER);
    
        // Then
        Assertions.assertNull(result);
    }

    @Test
    public void testDateSplit() {
        String datetimeStr = "2025-02-24 10:26:25";
        DateTime result = DatetimeUtils.fromDatetimeStr(datetimeStr, DatetimeUtils.DATE_TIME_FORMATTER);

        DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        DateTimeFormatter hourFormatter = DateTimeFormatter.ofPattern("HH:mm");

        Assertions.assertEquals("2025-02-24", DateUtil.format(result, dateFormatter));
        Assertions.assertEquals("10:26", DateUtil.format(result, hourFormatter));
        Assertions.assertEquals("周一", result.dayOfWeekEnum().toChinese("周"));
    }

    @Test
    public void testDateFormatWithSpecificDate() throws Exception {
        // Given
        Date date = new Date(122, 5, 15); // June 15, 2022
        
        // When
        String result = DatetimeUtils.dateFormat(date);
        
        // Then
        Assertions.assertEquals("2022-06-15", result);
    }

    @Test
    public void testDateFormatWithValidDate() {
        // Given
        Date date = new Date();
        
        // When
        String result = DatetimeUtils.dateFormat(date);
        
        // Then
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2}"));
    }

    @Test
    public void testDateFormatWithNullDate() {
        // Given
        Date date = null;
        
        // When
        String result = DatetimeUtils.dateFormat(date);
        
        // Then
        Assertions.assertNull(result);
    }

    @Test
    public void testHourFormatWithDifferentTimes() {
        // Given
        Date midnight = new Date(1640966400000L);
        Date noon = new Date(1641009600000L);
        Date evening = new Date(1641042000000L);
        
        // When & Then
        Assertions.assertEquals("00:00", DatetimeUtils.hourFormat(midnight)); // UTC+8
        Assertions.assertEquals("12:00", DatetimeUtils.hourFormat(noon)); // UTC+8
        Assertions.assertEquals("21:00", DatetimeUtils.hourFormat(evening)); // UTC+8
    }

    @Test
    public void testHourFormatWithNullDate() {
        // Given
        Date date = null;
        
        // When
        String result = DatetimeUtils.hourFormat(date);
        
        // Then
        Assertions.assertNull(result);
    }

    @Test
    public void testHourFormatWithValidDate() {
        // Given
        Date date = new Date(1640995200000L); // 2022-01-01 00:00:00 UTC
        
        // When
        String result = DatetimeUtils.hourFormat(date);
        
        // Then
        Assertions.assertEquals("08:00", result); // Assuming test runs in UTC+8 timezone
    }

    @Test
    public void testYesterdayDateReturnsCorrectFormat() {
        // When
        String result = DatetimeUtils.yesterdayDate();
        
        // Then
        Assertions.assertNotNull(result);
        Assertions.assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2}"));
    }

    @Test
    public void testYesterdayDateReturnsValidDate() {
        // Given
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expectedYesterday = now.minusDays(1);
        String expectedDate = expectedYesterday.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        
        // When
        String result = DatetimeUtils.yesterdayDate();
        
        // Then
        Assertions.assertEquals(expectedDate, result);
    }

    @Test
    public void testPassedDateByDayWithNegativeDays() {
        LocalDateTime now = LocalDateTime.now();
        int days = -3;
        String expected = now.minusDays(days).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String actual = DatetimeUtils.passedDateByDay(days);
        assertEquals(expected, actual);
    }

    @Test
    public void testPassedDateByDayWithZeroDays() {
        LocalDateTime now = LocalDateTime.now();
        String expected = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String actual = DatetimeUtils.passedDateByDay(0);
        assertEquals(expected, actual);
    }

    @Test
    public void testPassedDateByDayWithOneDay() {
        LocalDateTime now = LocalDateTime.now();
        String expected = now.minusDays(1).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String actual = DatetimeUtils.passedDateByDay(1);
        assertEquals(expected, actual);
    }

    @Test
    public void testPassedDateByDayWithMultipleDays() {
        LocalDateTime now = LocalDateTime.now();
        int days = 5;
        String expected = now.minusDays(days).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String actual = DatetimeUtils.passedDateByDay(days);
        assertEquals(expected, actual);
    }

}