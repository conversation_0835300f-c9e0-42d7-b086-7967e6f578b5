package com.baidu.keyue.deepsight.utils;

import lombok.AllArgsConstructor;
import lombok.Data;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class XIDTest {

    @Test
    public void testGenerateRequestID() {
        // Generate a UUID using the method
        String uuid = XID.generateRequestID();

        // Verify that the UUID is not null
        Assertions.assertNotNull(uuid, "UUID should not be null");

        // Verify that the UUID is in the correct format
        Assertions.assertTrue(uuid.matches("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"),
                "UUID should be in the correct format");
    }

    @Test
    public void testPrioritySort() {
        Map<String, Integer> valueProrityMap = new HashMap<>();
        valueProrityMap.put("a", 1);
        valueProrityMap.put("b", 2);
        valueProrityMap.put("c", 3);

        List<TestClass> list = new ArrayList<>();
        list.add(new TestClass("b", -1));
        list.add(new TestClass("a", -1));
        list.add(new TestClass("c", 4));
        list.stream().sorted(Comparator.comparingLong(value -> valueProrityMap.get(value.v)))
                .forEach(System.out::println);
    }

    @Test
    public void testGenerateRequestIDLength() {
        String uuid = XID.generateRequestID();
        Assertions.assertEquals(36, uuid.length(), "UUID should have length of 36 characters");
    }

    @Data
    @AllArgsConstructor
    public static class TestClass {
        private String v;
        private Integer p;
    }

}