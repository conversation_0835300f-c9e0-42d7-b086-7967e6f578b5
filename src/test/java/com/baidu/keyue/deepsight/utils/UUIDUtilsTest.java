package com.baidu.keyue.deepsight.utils;

import org.junit.jupiter.api.Assertions;

import org.junit.jupiter.api.Test;

import java.util.regex.Pattern;

public class UUIDUtilsTest {

    private static final Pattern UUID_PATTERN = Pattern.compile("^[0-9a-f]{32}$");

    @Test
    void testGenUUIDFormat() {
        String uuid = UUIDUtils.genUUID();
        Assertions.assertNotNull(uuid);
        Assertions.assertTrue(UUID_PATTERN.matcher(uuid).matches());
    }

    @Test
    void testGenUUIDUniqueness() {
        String uuid1 = UUIDUtils.genUUID();
        String uuid2 = UUIDUtils.genUUID();
        Assertions.assertNotEquals(uuid1, uuid2);
    }

    @Test
    void testGenUUIDLength() {
        String uuid = UUIDUtils.genUUID();
        Assertions.assertEquals(32, uuid.length());
    }

}