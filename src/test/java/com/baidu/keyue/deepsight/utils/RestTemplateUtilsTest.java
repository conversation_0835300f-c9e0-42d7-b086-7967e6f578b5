package com.baidu.keyue.deepsight.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.models.echopath.EchoPathResp;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.web.WebContextHolder;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.MDC;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class RestTemplateUtilsTest{

    private final String testUserId = "123";

    private final String testTenantId = "456";

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private RestTemplateUtils restTemplateUtils;

    private final String testUrl = "http://test.com";

    private final String testJsonBody = "{\"key\":\"value\"}";

    private final String testRequestId = "test-request-id";

    @BeforeEach
    void setUp() {
        MDC.put(Constants.REQUEST_ID_FIELD, testRequestId);
    }

    @Test
    void postJsonEchoPathNullResponse() {
        try (MockedStatic<WebContextHolder> mockedWebContext = mockStatic(WebContextHolder.class)) {
            // Setup
            Map<String, String> headerMap = new HashMap<>();
            mockedWebContext.when(WebContextHolder::getUserAuthInfo).thenReturn(null);
    
            when(restTemplate.postForEntity(eq(testUrl), any(HttpEntity.class), eq(EchoPathResp.class)))
                    .thenReturn(null);
    
            // Execute
            EchoPathResp result = restTemplateUtils.postJsonEchoPath(testUrl, headerMap, testJsonBody);
    
            // Verify
            assertNull(result);
            verify(restTemplate).postForEntity(eq(testUrl), any(HttpEntity.class), eq(EchoPathResp.class));
        }
    }

    @Test
    void postJsonEchoPathExceptionThrown() {
        try (MockedStatic<WebContextHolder> mockedWebContext = mockStatic(WebContextHolder.class)) {
            // Setup
            Map<String, String> headerMap = new HashMap<>();
            mockedWebContext.when(WebContextHolder::getUserAuthInfo).thenReturn(null);
    
            when(restTemplate.postForEntity(eq(testUrl), any(HttpEntity.class), eq(EchoPathResp.class)))
                    .thenThrow(new RuntimeException("Test exception"));
    
            // Execute & Verify
            DeepSightException.BusinessException exception = assertThrows(
                    DeepSightException.BusinessException.class,
                    () -> restTemplateUtils.postJsonEchoPath(testUrl, headerMap, testJsonBody)
            );
            assertEquals("请求客户洞察异常", exception.getMessage());
    
            verify(restTemplate).postForEntity(eq(testUrl), any(HttpEntity.class), eq(EchoPathResp.class));
        }
    }

    @Test
    void postJsonEchoPathSuccessWithUserAuthInfo() {
        try (MockedStatic<WebContextHolder> mockedWebContext = mockStatic(WebContextHolder.class)) {
            // Setup
            Map<String, String> headerMap = new HashMap<>();
            headerMap.put("custom-header", "value");

            UserAuthInfo userAuthInfo = mock(UserAuthInfo.class);
            when(userAuthInfo.getUserId()).thenReturn(Long.parseLong(testUserId));
            when(userAuthInfo.getTenantId()).thenReturn(Long.parseLong(testTenantId));
            mockedWebContext.when(WebContextHolder::getUserAuthInfo).thenReturn(userAuthInfo);
    
            EchoPathResp expectedResponse = new EchoPathResp();
            ResponseEntity<EchoPathResp> responseEntity = ResponseEntity.ok(expectedResponse);
            when(restTemplate.postForEntity(eq(testUrl), any(HttpEntity.class), eq(EchoPathResp.class)))
                    .thenReturn(responseEntity);
    
            // Execute
            EchoPathResp result = restTemplateUtils.postJsonEchoPath(testUrl, headerMap, testJsonBody);
    
            // Verify
            assertSame(expectedResponse, result);
            assertEquals(testUserId, headerMap.get(Constants.USER_ID_HEADER_KEY));
            assertEquals(testTenantId, headerMap.get(Constants.TENANT_ID_HEADER_KEY));
            assertEquals(Constants.SERVER_NAME, headerMap.get(Constants.SERVER_NAME_HEADER_KEY));
            assertEquals(testRequestId, headerMap.get(Constants.REQUEST_ID_HEADER_KEY));
    
            verify(restTemplate).postForEntity(eq(testUrl), any(HttpEntity.class), eq(EchoPathResp.class));
        }
    }

    @Test
    void postJsonEchoPathSuccessWithoutUserAuthInfo() {
        try (MockedStatic<WebContextHolder> mockedWebContext = mockStatic(WebContextHolder.class)) {
            // Setup
            Map<String, String> headerMap = new HashMap<>();
            mockedWebContext.when(WebContextHolder::getUserAuthInfo).thenReturn(null);
    
            EchoPathResp expectedResponse = new EchoPathResp();
            ResponseEntity<EchoPathResp> responseEntity = ResponseEntity.ok(expectedResponse);
            when(restTemplate.postForEntity(eq(testUrl), any(HttpEntity.class), eq(EchoPathResp.class)))
                    .thenReturn(responseEntity);
    
            // Execute
            EchoPathResp result = restTemplateUtils.postJsonEchoPath(testUrl, headerMap, testJsonBody);
    
            // Verify
            assertSame(expectedResponse, result);
            assertNull(headerMap.get(Constants.USER_ID_HEADER_KEY));
            assertNull(headerMap.get(Constants.TENANT_ID_HEADER_KEY));
            assertEquals(Constants.SERVER_NAME, headerMap.get(Constants.SERVER_NAME_HEADER_KEY));
            assertEquals(testRequestId, headerMap.get(Constants.REQUEST_ID_HEADER_KEY));
    
            verify(restTemplate).postForEntity(eq(testUrl), any(HttpEntity.class), eq(EchoPathResp.class));
        }
    }

}