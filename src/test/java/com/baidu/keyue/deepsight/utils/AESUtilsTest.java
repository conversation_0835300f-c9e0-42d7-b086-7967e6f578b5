package com.baidu.keyue.deepsight.utils;

import java.io.UnsupportedEncodingException;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class AESUtilsTest {

    // testEncryptAESWithEmptyPlainText 用于测试 encryptAES
    // generated by Comate
    @Test
    public void testEncryptAESWithEmptyPlainText() throws UnsupportedEncodingException {
        String plainText = "";
        String password = "password123";
        String cipherText = AESUtils.encryptAES(plainText, password);
        Assertions.assertEquals("", cipherText);
    }

    // testDecryptWithEmptyCipherText 用于测试 decrypt
    // generated by Comate
    @Test
    public void testDecryptWithEmptyCipherText() {
        String cipherText = "";
        String password = "password123";
        String expectedPlainText = "";

        String plainText = AESUtils.decrypt(cipherText, password);
        Assertions.assertEquals(expectedPlainText, plainText);
    }

    // testEncryptWithEmptyPlainText 用于测试 encrypt
    // generated by Comate
    @Test
    public void testEncryptWithEmptyPlainText() {
        String plainText = "";
        String password = "password123";
        String cipherText = AESUtils.encrypt(plainText, password);
        Assertions.assertEquals("", cipherText);
    }

    // testEncryptWithValidInputs 用于测试 encrypt
    // generated by Comate
    @Test
    public void testEncryptWithValidInputs() {
        String plainText = "13416672890";
        String password = "ea437d38025246b6";
        String cipherText = AESUtils.encrypt(plainText, password);
        Assertions.assertEquals("D587E8ADEF6EBA8990FC45391B68634F", cipherText);
    }

    @Test
    public void testDecrypt() {
        String cipherText = "D491C87C1CB19A0B492B9B9E350461E9";
        String password = "ac1pppC3c2JdZD86";

        String plainText = AESUtils.decrypt(cipherText, password);
        Assertions.assertEquals("13567678899", plainText);
    }
}