package com.baidu.keyue.deepsight.utils;

import org.junit.jupiter.api.Assertions;

import org.junit.jupiter.api.Test;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import org.springframework.core.io.ClassPathResource;

import java.io.FileNotFoundException;
import java.io.IOException;

public class FileUtilTest {
    @Test
    void readFileAsStringShouldThrowFileNotFoundExceptionWhenFileNotExists() {
        String nonExistentFile = "nonexistent.txt";
        Assertions.assertThrows(FileNotFoundException.class, () -> FileUtil.readFileAsString(nonExistentFile));
    }

    @Test
    void readFileAsStringShouldThrowIOExceptionWhenReadFails() throws IOException {
        // Create a mock ClassPathResource that throws IOException
        ClassPathResource mockResource = mock(ClassPathResource.class);
        when(mockResource.getInputStream()).thenThrow(new IOException("Simulated IO error"));

        Assertions.assertThrows(IOException.class, () -> FileUtil.readFileAsString("anyfile.txt"));
    }

    @Test
    void readFileAsStringShouldHandleNullInputStreamGracefully() throws IOException {
        // Create a mock ClassPathResource that returns null input stream
        ClassPathResource mockResource = mock(ClassPathResource.class);
        when(mockResource.getInputStream()).thenReturn(null);

        Assertions.assertThrows(FileNotFoundException.class, () -> FileUtil.readFileAsString("anyfile.txt"));
    }

}