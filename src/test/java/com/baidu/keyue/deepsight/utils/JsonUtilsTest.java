package com.baidu.keyue.deepsight.utils;


import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class JsonUtilsTest {

    @Test
    void testToJson() throws IOException {
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        assertEquals("{\"key\":\"value\"}", JsonUtils.toJson(map));
    }

    @Test
    void testToJsonNode() throws IOException {
        JsonNode node = JsonUtils.toJsonNode("{\"key\":\"value\"}");
        assertEquals("value", node.get("key").asText());
        assertThrows(IOException.class, () -> JsonUtils.toJsonNode("invalid json"));
    }

    @Test
    void testToJsonWithOutException() {
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        assertEquals("{\"key\":\"value\"}", JsonUtils.toJsonWithOutException(map));
        assertEquals("", JsonUtils.toJsonWithOutException(null));
    }

    @Test
    void testToMapJsonNode() {
        ObjectNode node = JsonUtils.createNode();
        node.put("name", "John");
        Map<String, Object> result = JsonUtils.toMap(node);
        assertEquals("John", result.get("name"));
    }

    @Test
    void testToMapWithoutExceptionString() {
        String json = "{\"name\":\"John\"}";
        Map<String, Object> result = JsonUtils.toMapWithoutException(json);
        assertEquals("John", result.get("name"));
        assertEquals(Collections.emptyMap(), JsonUtils.toMapWithoutException(null));
        assertEquals(Collections.emptyMap(), JsonUtils.toMapWithoutException("invalid json"));
    }

    @Test
    void testToJsonNodeUnchecked() {
        JsonNode node = JsonUtils.toJsonNodeUnchecked("{\"key\":\"value\"}");
        assertFalse(node.isMissingNode());
        assertTrue(JsonUtils.toJsonNodeUnchecked("invalid json").isMissingNode());
    }

    @Test
    void testToListUnchecked() {
        String json = "[\"item1\",\"item2\"]";
        List<String> result = JsonUtils.toListUnchecked(json, ArrayList.class, String.class);
        assertEquals(2, result.size());
        assertEquals("item1", result.get(0));
        assertEquals(Collections.emptyList(), JsonUtils.toListUnchecked("invalid json", ArrayList.class, String.class));
    }

    @Test
    void testAddArrNodeValue() {
        ObjectNode root = JsonUtils.createNode();
        ObjectNode child1 = JsonUtils.createNode("key1", "value1");
        ObjectNode child2 = JsonUtils.createNode("key2", "value2");

        JsonUtils.addArrNodeValue(root, "path.to.array", child1, child2);

        JsonNode arrayNode = root.at("/path/to/array");
        assertTrue(arrayNode.isArray());
        assertEquals(2, arrayNode.size());
    }

    @Test
    void testToJsonUnchecked() {
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        assertEquals("{\"key\":\"value\"}", JsonUtils.toJsonUnchecked(map));
        assertNull(JsonUtils.toJsonUnchecked(new Object() {
            @SuppressWarnings("unused")
            public String getValue() {
                throw new RuntimeException();
            }
        }));
    }

    @Test
    void testToJsonNonNull() throws IOException {
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        map.put("nullKey", null);
        assertEquals("{\"key\":\"value\"}", JsonUtils.toJsonNonNull(map));
    }

    @Test
    void testToJsonNonNullUncheckedWithDefault() {
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        map.put("nullKey", null);
        assertEquals("{\"key\":\"value\"}", JsonUtils.toJsonNonNullUnchecked(map, "default"));
        assertEquals("default", JsonUtils.toJsonNonNullUnchecked(new Object() {
            @SuppressWarnings("unused")
            public String getValue() {
                throw new RuntimeException();
            }
        }, "default"));
    }

    @Test
    void testToObjectWithTypeReference() {
        String json = "[{\"name\":\"John\"}]";
        List<Map<String, Object>> result = JsonUtils.toObject(json, JsonUtils.LIST_MAP_TYPE_REFERENCE);
        assertEquals("John", result.get(0).get("name"));
        assertNull(JsonUtils.toObject(null, JsonUtils.LIST_MAP_TYPE_REFERENCE));
    }

    @Test
    void testToObjectWithoutException() {
        String json = "{\"name\":\"John\"}";
        Map<?, ?> result = JsonUtils.toObjectWithoutException(json, Map.class);
        assertEquals("John", result.get("name"));
        assertNull(JsonUtils.toObjectWithoutException(null, Map.class));
        assertNull(JsonUtils.toObjectWithoutException("invalid json", Map.class));
    }

    @Test
    void testToObjectThrowException() throws IOException {
        String json = "[{\"name\":\"John\"}]";
        List<Map<String, Object>> result = JsonUtils.toObjectThrowException(json, JsonUtils.LIST_MAP_TYPE_REFERENCE);
        assertEquals("John", result.get(0).get("name"));
        assertThrows(IOException.class, () -> JsonUtils.toObjectThrowException("invalid json", JsonUtils.LIST_MAP_TYPE_REFERENCE));
    }

    @Test
    void testToJsonForDateWithOutException() {
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        assertNotNull(JsonUtils.toJsonForDateWithOutException(map));
        assertEquals("", JsonUtils.toJsonForDateWithOutException(null));
    }

    @Test
    void testToObjectWithClass() throws IOException {
        String json = "{\"name\":\"John\"}";
        Map<?, ?> result = JsonUtils.toObject(json, Map.class);
        assertEquals("John", result.get("name"));
    }

    @Test
    void testToMapWithoutExceptionStringWithTypeReference() {
        String json = "{\"name\":\"John\"}";
        Map<String, String> result = JsonUtils.toMapWithoutException(json, JsonUtils.MAP_STRING_REFERENCE);
        assertEquals("John", result.get("name"));
        assertEquals(Collections.emptyMap(), JsonUtils.toMapWithoutException(null, JsonUtils.MAP_STRING_REFERENCE));
        assertEquals(Collections.emptyMap(), JsonUtils.toMapWithoutException("invalid json", JsonUtils.MAP_STRING_REFERENCE));
    }

    @Test
    void testToMapString() throws IOException {
        String json = "{\"name\":\"John\"}";
        Map<String, Object> result = JsonUtils.toMap(json);
        assertEquals("John", result.get("name"));
    }

    @Test
    void testCreateNode() {
        ObjectNode node = JsonUtils.createNode();
        assertTrue(node.isEmpty());
    }

    @Test
    void testIsJsonValid() {
        assertTrue(JsonUtils.isJsonValid("{\"name\":\"John\"}"));
        assertFalse(JsonUtils.isJsonValid("invalid json"));
        assertThrows(IllegalArgumentException.class, () -> JsonUtils.isJsonValid(null));
    }

    @Test
    void testToList() throws IOException {
        String json = "[\"item1\",\"item2\"]";
        List<String> result = JsonUtils.toList(json, ArrayList.class, String.class);
        assertEquals(2, result.size());
        assertEquals("item1", result.get(0));
    }

    @Test
    void testCreateNodeWithKeyValue() {
        ObjectNode node = JsonUtils.createNode("name", "John");
        assertEquals("John", node.get("name").asText());
    }

    @Test
    void testNewArrayNode() {
        ArrayNode arrayNode = JsonUtils.newArrayNode();
        assertTrue(arrayNode.isEmpty());
    }

    @Test
    void testToJsonUncheckedWithDefault() {
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        assertEquals("{\"key\":\"value\"}", JsonUtils.toJsonUnchecked(map, "default"));
        assertEquals("default", JsonUtils.toJsonUnchecked(new Object() {
            @SuppressWarnings("unused")
            public String getValue() {
                throw new RuntimeException();
            }
        }, "default"));
    }

    @Test
    void testToJsonNonNullUnchecked() {
        Map<String, String> map = new HashMap<>();
        map.put("key", "value");
        map.put("nullKey", null);
        assertEquals("{\"key\":\"value\"}", JsonUtils.toJsonNonNullUnchecked(map));
        assertNull(JsonUtils.toJsonNonNullUnchecked(new Object() {
            @SuppressWarnings("unused")
            public String getValue() {
                throw new RuntimeException();
            }
        }));
    }

    @Test
    void testSetArrNodeValue() {
        ObjectNode root = JsonUtils.createNode();
        ObjectNode child1 = JsonUtils.createNode("key1", "value1");
        ObjectNode child2 = JsonUtils.createNode("key2", "value2");

        JsonUtils.setArrNodeValue(root, "path.to.array", child1, child2);

        JsonNode arrayNode = root.at("/path/to/array");
        assertTrue(arrayNode.isArray());
        assertEquals(2, arrayNode.size());
    }

    @Test
    void testToObjectForDate() throws IOException {
        String json = "{\"date\":\"2023-01-01\"}";
        Map<?, ?> result = JsonUtils.toObjectForDate(json, Map.class);
        assertEquals("2023-01-01", result.get("date"));
    }

    @Test
    void testToMapStringWithTypeReference() throws IOException {
        String json = "{\"name\":\"John\"}";
        Map<String, String> result = JsonUtils.toMap(json, JsonUtils.MAP_STRING_REFERENCE);
        assertEquals("John", result.get("name"));
        assertNull(JsonUtils.toMap(null, JsonUtils.MAP_STRING_REFERENCE));
    }

    @Test
    void testTransferToJsonWithComplexObject() {
        record Person(String name, int age) {}
        Person person = new Person("Alice", 30);
        String result = JsonUtils.transferToJson(person);
        assertEquals("{\"name\":\"Alice\",\"age\":30}", result);
    }

    @Test
    void testTransferToJsonWithMap() {
        Map<String, String> map = new HashMap<>();
        map.put("name", "test");
        map.put("value", "123");
        String result = JsonUtils.transferToJson(map);
        assertEquals("{\"name\":\"test\",\"value\":\"123\"}", result);
    }

    @Test
    void testTransferToJsonWithPrimitive() {
        int number = 42;
        String result = JsonUtils.transferToJson(number);
        assertEquals("42", result);
    }

    @Test
    void testTransferToJsonWithString() {
        String text = "hello world";
        String result = JsonUtils.transferToJson(text);
        assertEquals("\"hello world\"", result);
    }

    @Test
    void testTransferToJsonWithNull() {
        String result = JsonUtils.transferToJson(null);
        assertNull(result);
    }

}