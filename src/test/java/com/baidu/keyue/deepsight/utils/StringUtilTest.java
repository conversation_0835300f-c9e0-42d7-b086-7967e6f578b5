package com.baidu.keyue.deepsight.utils;

import org.junit.jupiter.api.Assertions;

import org.junit.jupiter.api.Test;

public class StringUtilTest{

    @Test
    void testNull2strWithInteger() {
        Assertions.assertEquals("123", StringUtil.null2str(123));
    }

    @Test
    void testUriEncodeWithSafeChars() {
        Assertions.assertEquals("abc123-_.~", StringUtil.uriEncode("abc123-_.~", true));
    }

    @Test
    void testUriEncodeWithSlashEncoding() {
        Assertions.assertEquals("path%2Fto%2Ffile", StringUtil.uriEncode("path/to/file", true));
    }

    @Test
    void testUriEncodeWithEmptyString() {
        Assertions.assertEquals("", StringUtil.uriEncode("", true));
    }

    @Test
    void testGetSqlLike() {
        Assertions.assertEquals("%test%", StringUtil.getSqlLike("test"));
    }

    @Test
    void testGetSqlLikeWithSpecialChars() {
        Assertions.assertEquals("%test_123%", StringUtil.getSqlLike("test_123"));
    }

    @Test
    void testNull2strWithString() {
        Assertions.assertEquals("test", StringUtil.null2str("test"));
    }

    @Test
    void testUriEncodeWithoutSlashEncoding() {
        Assertions.assertEquals("path/to/file", StringUtil.uriEncode("path/to/file", false));
    }

    @Test
    void testUriEncodeWithSpecialChars() {
        Assertions.assertEquals("hello+world%21", StringUtil.uriEncode("hello world!", true));
    }

    @Test
    void testGetSqlLikeWithEmptyString() {
        Assertions.assertEquals("%%", StringUtil.getSqlLike(""));
    }

    @Test
    void testNull2strWithNull() {
        Assertions.assertEquals("", StringUtil.null2str(null));
    }

}