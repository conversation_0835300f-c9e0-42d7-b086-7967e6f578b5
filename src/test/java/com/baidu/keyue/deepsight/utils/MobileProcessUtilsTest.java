package com.baidu.keyue.deepsight.utils;

import org.junit.jupiter.api.Assertions;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

public class MobileProcessUtilsTest{

    @Test
    void decryptMobileWithMapShouldReturnOriginalWhen11Digits() {
        String mobile = "13812345678";
        Map<String, String> encryptInfo = new HashMap<>();
        Assertions.assertEquals(mobile, MobileProcessUtils.decryptMobile(mobile, encryptInfo));
    }

    @Test
    void decryptMobileWithMapShouldTryFieldKeyWhenDefaultKeyFails() {
        String encrypted = "D491C87C1CB19A0B492B9B9E350461E9";
        String key = "ac1pppC3c2JdZD86";
        Map<String, String> encryptInfo = new HashMap<>();
        encryptInfo.put("mobile", key);
        // Mock AESUtils.decrypt to throw exception for default key but succeed for field key
        Assertions.assertEquals("13567678899", MobileProcessUtils.decryptMobile(encrypted, encryptInfo));
    }

    @Test
    void decryptMobileWithKeyShouldReturnOriginalWhen11Digits() {
        String mobile = "13812345678";
        Assertions.assertEquals(mobile, MobileProcessUtils.decryptMobile("anyKey", mobile));
    }

    @Test
    void decryptMobileWithKeyShouldTryDefaultKeyWhenNot11Digits() {
        String encrypted = "encryptedMobile";
        // Mock AESUtils.decrypt to throw exception for default key
        Assertions.assertNull(MobileProcessUtils.decryptMobile(null, encrypted));
    }

    @Test
    void decryptMobileWithKeyShouldReturnNullWhenBothKeysFail() {
        String encrypted = "encryptedMobile";
        Assertions.assertNull(MobileProcessUtils.decryptMobile("invalidKey", encrypted));
    }

    @Test
    void decryptMobileWithKeyShouldUseProvidedKeyWhenDefaultKeyFails() {
        String encrypted = "encryptedMobile";
        String key = "validKey";
        // Mock AESUtils.decrypt to throw exception for default key but succeed for provided key
        Assertions.assertNull(MobileProcessUtils.decryptMobile(key, encrypted));
    }

    @Test
    void maskMobileNumberShouldMaskCorrectlyWhenValidMobile() {
        String mobile = "13812345678";
        String expected = "138****5678";
        Assertions.assertEquals(expected, MobileProcessUtils.maskMobileNumber(mobile));
    }

}