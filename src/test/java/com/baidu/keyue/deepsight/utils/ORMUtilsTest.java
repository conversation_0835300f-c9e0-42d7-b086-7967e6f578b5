package com.baidu.keyue.deepsight.utils;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.enums.AiobFailTypeEnum;
import com.baidu.keyue.deepsight.enums.AiobLineSourceEnum;
import com.baidu.keyue.deepsight.enums.AiobLineStatusEnum;
import com.baidu.keyue.deepsight.enums.AiobRobotTypeEnum;
import com.baidu.keyue.deepsight.enums.AiobSortFieldEnum;
import com.baidu.keyue.deepsight.enums.AiobTaskStatusEnum;
import com.baidu.keyue.deepsight.enums.AlertConfigTypeEnum;
import com.baidu.keyue.deepsight.enums.AlertTimeTypeEnum;
import com.baidu.keyue.deepsight.enums.DateLineTypeEnum;
import com.baidu.keyue.deepsight.enums.DialMetricQueryTypeEnum;
import com.baidu.keyue.deepsight.enums.MemoryTypeEnum;
import com.baidu.keyue.deepsight.enums.OperationModeEnum;
import com.baidu.keyue.deepsight.enums.SortTypeEnum;
import com.baidu.keyue.deepsight.models.agg.AiobAggMetricCal;
import com.baidu.keyue.deepsight.models.agg.AiobAggMetricTimeBucket;
import com.baidu.keyue.deepsight.models.dial.CallCoreMetricsRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendDetailRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendRequest;
import com.baidu.keyue.deepsight.models.dial.ThirtyDayRankingRequest;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionCalculateContext;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionResponseItem;
import com.baidu.keyue.deepsight.models.doris.TableDescribeDto;
import com.baidu.keyue.deepsight.models.meg.MEGBaiduData;
import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.SQLDialect;
import org.jooq.UpdateSetMoreStep;
import org.jooq.UpdateSetStep;
import org.jooq.conf.ParamType;
import org.jooq.conf.Settings;
import org.jooq.conf.StatementType;
import org.jooq.impl.DSL;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.name;
import static org.jooq.impl.DSL.table;
import static org.jooq.impl.DSL.using;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class ORMUtilsTest {

    private final DateTime onlineTime = DateUtil.parse("2025-07-31 23:59:59");



    @Test
    public void testSqlGen() {
        System.setProperty("org.jooq.no-tips", "true");
        System.setProperty("org.jooq.no-logo", "true");

        Settings settings = new Settings()
                .withParamType(ParamType.INLINED); // 关键配置


        Field<?>[] selectFields = new Field[]{
                field(name("t", "roleType")).as("role"),
                field(name("t", "content")),
                field(name("t", "sessionId")),
                field(name("s", "mobile")).as("mobile"),
        };

        // 动态生成 JOIN SQL（不执行）
        String sql = using(SQLDialect.MYSQL, settings)
                .select(selectFields)
                .from(table(name("aiob_conversation_record_38849094270964736")).as("t"))
                .leftJoin(table(name("aiob_conversation_session_service_38849094270964736")).as("s"))
                .on(field(name("t", "sessionId")).eq(field(name("s", "sessionId"))))
                .where(field(name("t", "deepsight_datetime")).gt("2025-03-05 16:58:32"))
                .getSQL();

        Assertions.assertEquals(
                "select `t`.`roleType` as `role`, `t`.`content`, `t`.`sessionId`, `s`.`mobile` as `mobile` " +
                        "from `aiob_conversation_record_38849094270964736` as `t` " +
                        "left outer join `aiob_conversation_session_service_38849094270964736` as `s` " +
                        "on `t`.`sessionId` = `s`.`sessionId` " +
                        "where `t`.`deepsight_datetime` > '2025-03-05 16:58:32'",
                sql
        );
    }

    @Test
    public void testSqlGenWithCondition() {
        String sql = ORMUtils.getMemoryCalculateAiobTalkData(
                "aiob_conversation_record_38849094270964736",
                "aiob_conversation_session_service_38849094270964736",
                "2025-03-05 16:58:32");

        Assertions.assertEquals(
                "select `t`.`roleType` as `role`, `t`.`content`, `t`.`sessionId`, `s`.`mobile` as `mobile` " +
                        "from `aiob_conversation_record_38849094270964736` as `t` " +
                        "left outer join `aiob_conversation_session_service_38849094270964736` as `s` " +
                        "on `t`.`sessionId` = `s`.`sessionId` " +
                        "where `t`.`deepsight_datetime` > '2025-03-05 16:58:32'",
                sql
        );
    }

    @Test
    public void testGetMemoryCalculateAiobSession() {
        Assertions.assertEquals(
                "select `oneId`, `sessionId`, `conversationContent` " +
                        "from `aiob_conversation_session_service_38849094270964736` " +
                        "where (`deepsight_datetime` >= '2025-03-05 16:58:32' and `oneId` <> '' and `conversationContent` <> '')",
                ORMUtils.getMemoryCalculateAiobSession(
                        "aiob_conversation_session_service_38849094270964736",
                        "2025-03-05 16:58:32")
        );
    }

    @Test
    public void testGetDataPredictSql() {
        String sql = "select `m`.`external_id` as `id`, `m`.`oneId`, `m`.`memory_content` as `content`, `m`.`memory_type` as `type`, `u`.`mobile` " +
                "from `memory_extract_info_4109739852038144` as `m` " +
                "left outer join `mock_user_4109739852038144` as `u` " +
                "on `m`.`oneId` = `u`.`oneId` " +
                "where `m`.`deepsight_datetime` > '2025-02-21 16:58:32'";

        Assertions.assertEquals(
                sql,
                ORMUtils.getDataPredictSql(
                        "mock_user_4109739852038144",
                        "memory_extract_info_4109739852038144",
                        "2025-02-21 16:58:32")
        );

    }

    @Test
    public void testUpdate() {
        String tableName = "mock_user_1234567890";
        String sql = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS)
                .update(table(name(tableName)))
                .set(field(name("col_1")), "1")
                .set(field(name("col_2")), "2")
                .set(field(name("col_3")), 3)
                .where(
                        field(name("id")).eq(1),
                        field(name("mobile")).eq("ajhsfgas")
                )
                .getSQL();

        Assertions.assertEquals(
                sql,
                "update `mock_user_1234567890` set `col_1` = '1', `col_2` = '2', `col_3` = 3 where (`id` = 1 and `mobile` = 'ajhsfgas')"
        );
    }

    @Test
    public void testUpdateWithCondition() {
        String tableName = "mock_user_1234567890";
        String mobile = "ajhsfgas";

        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        UpdateSetStep update = dsl.update(table(name(tableName)));

        UpdateSetMoreStep moreStep = update.set(field(name("col_1")), "1");
        moreStep.set(field(name("col_2")), "2");
        moreStep.set(field(name("col_3")), 3);
        moreStep.where(
                field(name("id")).eq(1),
                field(name("mobile")).eq(mobile));

        String sql = moreStep.getSQL();

        Assertions.assertEquals(
                sql,
                "update `mock_user_1234567890` set `col_1` = '1', `col_2` = '2', `col_3` = 3 where (`id` = 1 and `mobile` = 'ajhsfgas')"
        );
    }

    @Test
    public void testUpdateUserMegData() {
        String tableName = "mock_user_1234567890";
        String mobile = "315BA892152FEBBFDABF334DA9887B23";

        String dataJson = "{\"megIds\":{\"idsMap\":{\"CUID\":[\"44A554E40D8463EBA902F9B0F03F2E4FA9CD3CC3\",\"FB292A8B645ADD87F0EBA0E3AD6D809F3298A751COSILKRHMBI\",\"884E68C1DA9E2EBE7836B98091691057FCEED4A61OMQQDCQKMH\"],\"IDFA\":[\"133EB8C0-CC9E-4C2D-A462-8422ED656F70\",\"226C0EAC-129D-4BBC-AF5E-D95341241EE7\"],\"BAIDUID\":[\"FC8CA1ACA5408849B0A5D1F206BDCE1F\",\"F9A0D6C858D66B65BDA8770BFB430AC4\",\"326C44699EB4392BBD897962A44899A7\",\"FACD791431EF952F3538E20A21710B9B\",\"47FD2EF91C7A0D639AC2340B68A37820\"],\"UDWID\":[\"user713182880\"]}},\"attributes\":[{\"description\":{\"name\":\"性别\"},\"item\":[{\"value\":\"男\",\"weight\":65}]},{\"description\":{\"name\":\"年龄\"},\"item\":[{\"value\":\"25-34\",\"weight\":47}]},{\"description\":{\"name\":\"教育水平\"},\"item\":[{\"value\":\"大专\",\"weight\":54}]},{\"description\":{\"name\":\"职业类别\"}},{\"description\":{\"name\":\"所在行业\"},\"item\":[{\"value\":\"IT通信电子\",\"weight\":6}]},{\"description\":{\"name\":\"人生阶段\"}},{\"description\":{\"name\":\"婚姻状况\"}},{\"description\":{\"name\":\"消费水平\"},\"item\":[{\"value\":\"中\",\"weight\":34}]},{\"description\":{\"name\":\"消费意愿\"}},{\"description\":{\"name\":\"兴趣关注\"}}]}";

        MEGBaiduData megData = JsonUtils.toObjectWithoutException(dataJson, MEGBaiduData.class);
        String sql = ORMUtils.updateUserMegData(tableName, mobile, megData);

        Assertions.assertTrue(
                StringUtils.endsWith(sql, "`BAIDUID` = 'FC8CA1ACA5408849B0A5D1F206BDCE1F', `CUID` = '44A554E40D8463EBA902F9B0F03F2E4FA9CD3CC3', `IDFA` = '133EB8C0-CC9E-4C2D-A462-8422ED656F70', `bd_gender` = '男', `bd_age_group` = '25-34', `bd_education_level` = '大专', `bd_industry` = 'IT通信电子', `bd_consume_level` = '中' where `oneId` = '315BA892152FEBBFDABF334DA9887B23'")
        );
    }

    @Test
    public void testGenerateLoadOneIdSetWithTime() {
        String sql = "select distinct `oneId` from `mock_user_4109739852038144` where (`deepsight_update_datetime` > '2025-02-21 16:58:32' and `oneId` <> '')";

        Assertions.assertEquals(
                sql,
                ORMUtils.generateLoadOneIdSetWithTime("mock_user_4109739852038144", "2025-02-21 16:58:32")
        );
    }

    @Test
    public void testGenerateFetchUserDataSql() {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        context.setThreshold(50f);
        context.setTenantId("39274149511549952");

        String mockUserTableName = TenantUtils.generateMockUserTableName("39274149511549952");

        List<TableDescribeDto> tableSchemas = Lists.newArrayList(
                new TableDescribeDto("user_id", "varchar(255)", false, true, "NULL"),
                new TableDescribeDto("user_name", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("age", "int", true, false, "NULL"),
                new TableDescribeDto("age_group", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("city", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("gender", "varchar(128)", true, false, "NULL"),
                new TableDescribeDto("register_time", "datetime", true, false, "NULL"),
                new TableDescribeDto("update_time", "datetime", true, false, "NULL"),
                new TableDescribeDto("tags", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("area", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("country", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("DEVICEID", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("device_model", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("district", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("membership_level", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("os", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("province", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("user_type", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("income_level", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("education_level", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("life_stage", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("industry", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("occupation", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("city_level", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("mobile", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("email_address", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("wechat_id", "varchar(255)", true, false, "NULL"),
                new TableDescribeDto("predict_gender", "varchar(8)", true, false, ""),
                new TableDescribeDto("predict_age_group", "varchar(32)", true, false, ""),
                new TableDescribeDto("predict_life_stage", "varchar(32)", true, false, ""),
                new TableDescribeDto("predict_marriage_status", "varchar(8)", true, false, ""),
                new TableDescribeDto("predict_industry", "varchar(32)", true, false, ""),
                new TableDescribeDto("predict_education_level", "varchar(32)", true, false, ""),
                new TableDescribeDto("predict_occupation", "varchar(32)", true, false, ""),
                new TableDescribeDto("predict_consume_level", "varchar(8)", true, false, ""),
                new TableDescribeDto("predict_consume_intent", "varchar(8)", true, false, ""),
                new TableDescribeDto("predict_geographic_location", "varchar(128)", true, false, ""),
                new TableDescribeDto("predict_interests", "varchar(256)", true, false, ""),
                new TableDescribeDto("merge_gender", "varchar(8)", true, false, ""),
                new TableDescribeDto("merge_age_group", "varchar(32)", true, false, ""),
                new TableDescribeDto("merge_life_stage", "varchar(32)", true, false, ""),
                new TableDescribeDto("merge_marriage_status", "varchar(8)", true, false, ""),
                new TableDescribeDto("merge_industry", "varchar(32)", true, false, ""),
                new TableDescribeDto("merge_education_level", "varchar(32)", true, false, ""),
                new TableDescribeDto("merge_occupation", "varchar(32)", true, false, ""),
                new TableDescribeDto("merge_consume_level", "varchar(8)", true, false, ""),
                new TableDescribeDto("merge_consume_intent", "varchar(8)", true, false, ""),
                new TableDescribeDto("merge_geographic_location", "varchar(128)", true, false, ""),
                new TableDescribeDto("merge_interests", "varchar(256)", true, false, ""),
                new TableDescribeDto("bd_gender", "varchar(8)", true, false, ""),
                new TableDescribeDto("bd_age_group", "varchar(32)", true, false, ""),
                new TableDescribeDto("bd_life_stage", "varchar(32)", true, false, ""),
                new TableDescribeDto("bd_marriage_status", "varchar(8)", true, false, ""),
                new TableDescribeDto("bd_industry", "varchar(32)", true, false, ""),
                new TableDescribeDto("bd_education_level", "varchar(32)", true, false, ""),
                new TableDescribeDto("bd_occupation", "varchar(32)", true, false, ""),
                new TableDescribeDto("bd_consume_level", "varchar(8)", true, false, ""),
                new TableDescribeDto("bd_consume_intent", "varchar(128)", true, false, ""),
                new TableDescribeDto("bd_geographic_location", "varchar(128)", true, false, ""),
                new TableDescribeDto("bd_interests", "varchar(2048)", true, false, ""),
                new TableDescribeDto("deepsight_datetime", "datetime", true, false, "CURRENT_TIMESTAMP"),
                new TableDescribeDto("process_customer_150", "varchar(150)", true, false, "1"),
                new TableDescribeDto("process_customer_151", "varchar(150)", true, false, "0"),
                new TableDescribeDto("deepsight_update_datetime", "datetime", true, false, "NULL"),
                new TableDescribeDto("mobile_list", "array<varchar(128)>", true, false, "[]"),
                new TableDescribeDto("email_list", "array<varchar(128)>", true, false, "[]"),
                new TableDescribeDto("source", "varchar(128)", true, false, "NULL"),
                new TableDescribeDto("idm_baiduid", "varchar(255)", true, false, ""),
                new TableDescribeDto("idm_userid", "array<varchar(255)>", true, false, "[]"),
                new TableDescribeDto("idm_cuid", "array<varchar(255)>", true, false, "[]"),
                new TableDescribeDto("idm_imei", "array<varchar(255)>", true, false, "[]"),
                new TableDescribeDto("idm_mac", "array<varchar(255)>", true, false, "[]"),
                new TableDescribeDto("idm_idfa", "array<varchar(255)>", true, false, "[]"),
                new TableDescribeDto("idm_oaid", "array<varchar(255)>", true, false, "[]"),
                new TableDescribeDto("IMEI", "varchar(255)", true, false, ""),
                new TableDescribeDto("BAIDUID", "varchar(255)", true, false, ""),
                new TableDescribeDto("CUID", "varchar(255)", true, false, ""),
                new TableDescribeDto("USERID", "varchar(255)", true, false, ""),
                new TableDescribeDto("MAC", "varchar(255)", true, false, ""),
                new TableDescribeDto("UNIONID", "varchar(255)", true, false, ""),
                new TableDescribeDto("IDFA", "varchar(255)", true, false, ""),
                new TableDescribeDto("OAID", "varchar(255)", true, false, ""),
                new TableDescribeDto("anonymous_id", "varchar(255)", true, false, ""),
                new TableDescribeDto("oneId", "varchar(64)", true, false, "NULL")
        );

        Pair<String, String> sqlPari = ORMUtils.generateFetchPredictGroupDataSql(mockUserTableName, tableSchemas, Lists.newArrayList(150L, 151L), 0.1f, OperationModeEnum.OPERATION_BY_BAIDU_OP);

        Assertions.assertEquals("SELECT COUNT(*) FROM (\n" +
                "    SELECT oneId,\n" +
                "        ( CASE WHEN `user_id` IS NOT NULL THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `user_name` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `age` IS NOT NULL THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `age_group` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `city` IS NOT NULL THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `gender` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `register_time` IS NOT NULL THEN 1 ELSE 0 END  + " +
                " CASE WHEN `update_time` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `tags` IS NOT NULL THEN 1 ELSE 0 END  + " +
                " CASE WHEN `area` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `country` IS NOT NULL THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `DEVICEID` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `device_model` IS NOT NULL THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `district` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `membership_level` IS NOT NULL THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `os` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `province` IS NOT NULL THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `user_type` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `income_level` IS NOT NULL THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `education_level` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `life_stage` IS NOT NULL THEN 1 ELSE 0 END  + " +
                " CASE WHEN `industry` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `occupation` IS NOT NULL THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `city_level` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `mobile` IS NOT NULL THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `email_address` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `wechat_id` IS NOT NULL THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `merge_gender` != '' THEN 1 ELSE 0 END  +  CASE WHEN `merge_age_group` != '' THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `merge_life_stage` != '' THEN 1 ELSE 0 END  +  CASE WHEN `merge_marriage_status` != '' THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `merge_industry` != '' THEN 1 ELSE 0 END  +  CASE WHEN `merge_education_level` != '' THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `merge_occupation` != '' THEN 1 ELSE 0 END  +  CASE WHEN `merge_consume_level` != '' THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `merge_consume_intent` != '' THEN 1 ELSE 0 END  +  CASE WHEN `merge_geographic_location` != '' THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `merge_interests` != '' THEN 1 ELSE 0 END  +  CASE WHEN array_size(`mobile_list`) > 0 THEN 1 ELSE 0 END  +  " +
                "CASE WHEN array_size(`email_list`) > 0 THEN 1 ELSE 0 END  +  CASE WHEN `source` IS NOT NULL THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `IMEI` != '' THEN 1 ELSE 0 END  +  CASE WHEN `BAIDUID` != '' THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `CUID` != '' THEN 1 ELSE 0 END  +  CASE WHEN `USERID` != '' THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `MAC` != '' THEN 1 ELSE 0 END  +  CASE WHEN `UNIONID` != '' THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `IDFA` != '' THEN 1 ELSE 0 END  +  CASE WHEN `OAID` != '' THEN 1 ELSE 0 END  +  " +
                "CASE WHEN `anonymous_id` != '' THEN 1 ELSE 0 END ) AS non_null_count\n" +
                "    FROM mock_user_39274149511549952\n" +
                "    WHERE `process_customer_150` = '1' OR `process_customer_151` = '1'\n" +
                "    order by deepsight_datetime\n" +
                ") AS subquery\n" +
                "WHERE oneId != '' AND subquery.non_null_count >= 0", sqlPari.getLeft());
    }

    @Test
    public void testGenerateOneIdRetrieveSql() {
        String sql = "select `oneId`, `mobile`, `CUID` from `mock_user_4109739852038144` where `oneId` in ('111', '222', '333')";

        Assertions.assertEquals(
                sql,
                ORMUtils.generateOneIdRetrieveSql("mock_user_4109739852038144", Lists.newArrayList("111", "222", "333"))
        );
    }

    @Test
    public void testGenerateBatchInsertDiffusion() {
        String tableName = "temp_table_diffusion";
        String now = "2025-03-27 10:49:22";
        List<DiffusionResponseItem> batch = Lists.newArrayList(
                new DiffusionResponseItem("111", 0.1F),
                new DiffusionResponseItem("222", 0.2F)
        );
        String sql = ORMUtils.generateBatchInsertDiffusion(tableName, batch, now);

        Assertions.assertEquals(
                "insert into `temp_table_diffusion` (`oneId`, `score`, `deepsight_datetime`, `deepsight_update_datetime`) values " +
                        "('111', '0.100000', '2025-03-27 10:49:22', '2025-03-27 10:49:22'), ('222', '0.20000000', '2025-03-27 10:49:22', '2025-03-27 10:49:22')",
                sql
        );
    }

    @Test
    public void testGenerateBatchUpdateDiffusion() {
        String tableName = "temp_table_diffusion";
        String now = "2025-03-27 10:49:22";
        List<DiffusionResponseItem> batch = Lists.newArrayList(
                new DiffusionResponseItem("111", 0.1F),
                new DiffusionResponseItem("222", 0.2F)
        );
        String sql = ORMUtils.generateBatchUpdateDiffusion(tableName, batch.get(0), now);
        Assertions.assertEquals(
                "update `temp_table_diffusion` set score = '0.10000000', deepsight_update_datetime = '2025-03-27 10:49:22' where oneId = '111'",
                sql
        );
    }

    @Test
    public void testGenerateClearCustomerGroupDataSql() {
        Assertions.assertEquals(
                "update `mock_user_4109739852038144` set process_customer_1 = '0'",
                ORMUtils.generateClearCustomerGroupDataSql("mock_user_4109739852038144", "process_customer_1")
        );
    }

    @Test
    public void testGenerateClearDiffusionTempTableDataSql() {
        Assertions.assertEquals(
                "delete from `process_diffusion_temporary_t_1` where oneId <> ''",
                ORMUtils.generateClearDiffusionTempTableDataSql("process_diffusion_temporary_t_1")
        );
    }

    @Test
    public void testGenerateMigrateDiffusionDataToCustomerGroup() {
        Assertions.assertEquals(
                "update `mock_user_4109739852038144` set process_customer_1 = '1' where oneId in (select oneId from `process_diffusion_temporary_t_1`)",
                ORMUtils.generateMigrateDiffusionDataToCustomerGroup("mock_user_4109739852038144", "process_diffusion_temporary_t_1", "process_customer_1")
        );
    }

    @Test
    public void testGenerateFetchPredictGroupDataSql() {
        // Prepare test data
        String mockUserTableName = "mock_user_table";
        List<Long> customerGroupIds = List.of(123L, 456L);
        float threshold = 20f;

        // Create table schemas with different field types
        List<TableDescribeDto> tableSchemas = Stream.of(
                createTableDescribeDto("field1", "NULL", true),
                createTableDescribeDto("field2", "", false),
                createTableDescribeDto("field3", "[]", false),
                createTableDescribeDto(Constants.DORIS_DEFAULT_DATA_INSERT_DATE_FIELD, "", false),
                createTableDescribeDto(Constants.DORIS_DEFAULT_DATA_UPDATE_DATE_FIELD, "", false),
                createTableDescribeDto(Constants.TABLE_USER_ONE_ID, "", false),
                createTableDescribeDto("idm_field", "", false),
                createTableDescribeDto("bd_field", "", false),
                createTableDescribeDto("predict_field", "", false)
        ).toList();

        // Execute method
        Pair<String, String> result = ORMUtils.generateFetchPredictGroupDataSql(
                mockUserTableName, tableSchemas, customerGroupIds, threshold, OperationModeEnum.OPERATION_BY_BAIDU_OP);
        // Verify results
        String expectedCountSql = """
                SELECT COUNT(*) FROM (
                    SELECT oneId,
                        ( CASE WHEN `field1` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `field2` != '' THEN 1 ELSE 0 END  +  CASE WHEN array_size(`field3`) > 0 THEN 1 ELSE 0 END ) AS non_null_count
                    FROM mock_user_table
                    WHERE `process_customer_123` = '1' OR `process_customer_456` = '1'
                    order by deepsight_datetime
                ) AS subquery
                WHERE oneId != '' AND subquery.non_null_count >= 0""";

        String expectedSelectSql = """
                SELECT * FROM (
                    SELECT oneId,
                        ( CASE WHEN `field1` IS NOT NULL THEN 1 ELSE 0 END  +  CASE WHEN `field2` != '' THEN 1 ELSE 0 END  +  CASE WHEN array_size(`field3`) > 0 THEN 1 ELSE 0 END ) AS non_null_count
                    FROM mock_user_table
                    WHERE `process_customer_123` = '1' OR `process_customer_456` = '1'
                    order by deepsight_datetime
                ) AS subquery
                WHERE oneId != '' AND subquery.non_null_count >= 0""";

        Assertions.assertEquals(expectedCountSql, result.getLeft());
        Assertions.assertEquals(expectedSelectSql, result.getRight());
    }

    private TableDescribeDto createTableDescribeDto(String fieldName, String defaultValue,
                                                    boolean defaultNull) {
        TableDescribeDto dto = new TableDescribeDto();
        dto.setFieldName(fieldName);
        dto.setNullable(defaultNull);
        dto.setDefaultValue(defaultNull ? "NULL" : defaultValue);
        return dto;
    }

    @Test
    void getSOPAiobConnectedSession() {
        String sessionTable = "aiob_conversation_session_1";
        String botVersionId = "botVersionId";
        Integer robotSceneFilter = 6;
        String actual = ORMUtils.getSOPAiobConnectedSession(sessionTable, botVersionId, robotSceneFilter);
        String expected = "select `oneId`, `sessionId`, `taskId`, `robotScene`, `sipCode`, `botVersionId`, `startTime`, `customTagList` from `aiob_conversation_session_1` where (`botVersionId` = 'botVersionId' and `robotScene` = 6 and `sipCode` = '200')";
        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateDiffusionFilterSeedGroupOneIds() {
        String tableName = "test_table";
        String groupField = "group_field";
        List<String> seedOneIds = Arrays.asList("id1", "id2", "id3");

        String expectedSql = "select `oneId` from `test_table` where (`oneId` in ('id1', 'id2', 'id3') and `group_field` = '0')";
        String actualSql = ORMUtils.generateDiffusionFilterSeedGroupOneIds(tableName, groupField, seedOneIds);

        Assertions.assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateDiffusionFilterSeedGroupOneIdsWithEmptyList() {
        String tableName = "empty_test_table";
        String groupField = "group_field";
        List<String> seedOneIds = List.of();

        String expectedSql = "select `oneId` from `empty_test_table` where (false and `group_field` = '0')";
        String actualSql = ORMUtils.generateDiffusionFilterSeedGroupOneIds(tableName, groupField, seedOneIds);

        Assertions.assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateDiffusionFilterSeedGroupOneIdsWithSingleId() {
        String tableName = "single_id_table";
        String groupField = "group_field";
        List<String> seedOneIds = List.of("single_id");

        String expectedSql = "select `oneId` from `single_id_table` where (`oneId` in ('single_id') and `group_field` = '0')";
        String actualSql = ORMUtils.generateDiffusionFilterSeedGroupOneIds(tableName, groupField, seedOneIds);

        Assertions.assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateQueryTaskNameByTaskIdSQL() {
        String tableName = "test_table";
        String taskId = "12345";
        String expectedSQL = "select `taskName` from `test_table` where `taskId` = '12345'";

        String actualSQL = ORMUtils.generateQueryTaskNameByTaskIdSQL(tableName, taskId);

    }


    @Test
    public void testGenerateQueryBasicInfoWithOneIdsSingleId() {
        // Setup
        String mockUserTableName = "mock_user_table";
        List<String> oneIds = List.of("single_id");

        // Execute
        String actualSql = ORMUtils.generateQueryBasicInfoWithOneIds(mockUserTableName, oneIds);

        // Verify
        String expectedSql = "select `" + Constants.TABLE_USER_ONE_ID + "`" +
                Constants.DIFFUSION_BASIC_INFO_WITH_SELF_OPERATION.keySet()
                        .stream()
                        .map(k -> ", `" + k + "`")
                        .reduce("", String::concat) +
                " from `" + mockUserTableName + "` where `" +
                Constants.TABLE_USER_ONE_ID + "` in ('single_id')";
        Assertions.assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateQueryBasicInfoWithOneIds() {
        // Setup
        String mockUserTableName = "mock_user_table";
        List<String> oneIds = Arrays.asList("id1", "id2", "id3");

        // Expected fields
        StringBuilder expectedFields = new StringBuilder();
        expectedFields.append("`").append(Constants.TABLE_USER_ONE_ID).append("`");
        Constants.DIFFUSION_BASIC_INFO_WITH_SELF_OPERATION.keySet()
                .forEach(k -> expectedFields.append(", `").append(k).append("`"));

        // Execute
        String actualSql = ORMUtils.generateQueryBasicInfoWithOneIds(mockUserTableName, oneIds);

        // Verify
        String expectedSql = "select " + expectedFields.toString() +
                " from `" + mockUserTableName + "` where `" +
                Constants.TABLE_USER_ONE_ID + "` in ('id1', 'id2', 'id3')";
        Assertions.assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateQueryBasicInfoWithOneIdsEmptyList() {
        // Setup
        String mockUserTableName = "mock_user_table";
        List<String> oneIds = List.of();

        // Execute
        String actualSql = ORMUtils.generateQueryBasicInfoWithOneIds(mockUserTableName, oneIds);

        // Verify
        String expectedSql = "select `" + Constants.TABLE_USER_ONE_ID + "`" +
                Constants.DIFFUSION_BASIC_INFO_WITH_SELF_OPERATION.keySet()
                        .stream()
                        .map(k -> ", `" + k + "`")
                        .reduce("", String::concat) +
                " from `" + mockUserTableName + "` where false";
        Assertions.assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateNewOneIdsFromSessionAggTableInYesterdayWithEmptyDate() {
        String aggTable = "session_agg_table";
        String yesterday = "";

        String expectedSql = "select distinct oneId from `session_agg_table` where call_date = ''";
        String actualSql = ORMUtils.generateNewOneIdsFromSessionAggTableInYesterday(aggTable, yesterday);

        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateNewOneIdsFromSessionAggTableInYesterday() {
        String aggTable = "session_agg_table";
        String yesterday = "2023-05-15";

        String expectedSql = "select distinct oneId from `session_agg_table` where call_date = '2023-05-15'";
        String actualSql = ORMUtils.generateNewOneIdsFromSessionAggTableInYesterday(aggTable, yesterday);

        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateNewOneIdsFromSessionAggTableInYesterdayWithDifferentTableName() {
        String aggTable = "another_agg_table";
        String yesterday = "2023-05-16";

        String expectedSql = "select distinct oneId from `another_agg_table` where call_date = '2023-05-16'";
        String actualSql = ORMUtils.generateNewOneIdsFromSessionAggTableInYesterday(aggTable, yesterday);

        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateQuerySessionAggRecordsWith() {
        String aggTable = "session_agg_table";
        String oneId = "12345";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        String expectedSql = "select * from `session_agg_table` " +
                "where (oneId = '12345' and call_date <= '2023-01-31' and call_date >= '2023-01-01')";

        String actualSql = ORMUtils.generateQuerySessionAggRecordsWith(aggTable, startDate, endDate, oneId);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateQuerySessionAggRecordsWithSameDate() {
        String aggTable = "session_agg_table";
        String oneId = "12345";
        String date = "2023-01-15";

        String expectedSql = "select * from `session_agg_table` " +
                "where (oneId = '12345' and call_date <= '2023-01-15' and call_date >= '2023-01-15')";

        String actualSql = ORMUtils.generateQuerySessionAggRecordsWith(aggTable, date, date, oneId);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateQuerySessionAggRecordsWithEmptyDates() {
        String aggTable = "session_agg_table";
        String oneId = "12345";
        String emptyDate = "";

        String expectedSql = "select * from `session_agg_table` " +
                "where (oneId = '12345' and call_date <= '' and call_date >= '')";

        String actualSql = ORMUtils.generateQuerySessionAggRecordsWith(aggTable, emptyDate, emptyDate, oneId);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateQuerySessionAggRecordsWithSpecialTableName() {
        String aggTable = "special_table_name_with_123";
        String oneId = "12345";
        String startDate = "2023-01-01";
        String endDate = "2023-01-31";

        String expectedSql = "select * from `special_table_name_with_123` " +
                "where (oneId = '12345' and call_date <= '2023-01-31' and call_date >= '2023-01-01')";

        String actualSql = ORMUtils.generateQuerySessionAggRecordsWith(aggTable, startDate, endDate, oneId);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateInsertUserMetricSqlWithNullValues() {
        // Prepare test data with null values
        String tableName = "test_user_metrics";

        AiobAggMetricCal metricCal = new AiobAggMetricCal();
        metricCal.setOneId(null);
        metricCal.setConnectRate(null);
        metricCal.setTimeBucketStatistics(null);
        metricCal.setFirstRoundHangupRate(null);
        metricCal.setAvgRounds(null);
        metricCal.setAvgDuration(null);

        // Expected SQL
        String expectedSql = "insert into `test_user_metrics` (`oneId`, `connect_rate`, `time_bucket_statistics`, `first_round_hangup_rate`, `avg_rounds`, `avg_duration`) " +
                "values (null, 'null', '', 'null', 'null', 'null')";

        // Execute and verify
        String actualSql = ORMUtils.generateInsertUserMetricSql(tableName, metricCal);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateInsertUserMetricSql() {
        // Prepare test data
        String tableName = "test_user_metrics";
        String oneId = "test_one_id";

        Map<String, AiobAggMetricTimeBucket> timeBucketStats = new HashMap<>();
        timeBucketStats.put("morning", new AiobAggMetricTimeBucket());

        AiobAggMetricCal metricCal = new AiobAggMetricCal();
        metricCal.setOneId(oneId);
        metricCal.setConnectRate(0.85f);
        metricCal.setTimeBucketStatistics(timeBucketStats);
        metricCal.setFirstRoundHangupRate(0.1f);
        metricCal.setAvgRounds(3.5f);
        metricCal.setAvgDuration(120.5f);

        // Expected SQL
        String expectedSql = "insert into `test_user_metrics` (`oneId`, `connect_rate`, `time_bucket_statistics`, `first_round_hangup_rate`, `avg_rounds`, `avg_duration`) " +
                "values ('test_one_id', '0.850000', '{\"morning\":{}}', '0.100000', '3.500000', '120.500000')";

        // Execute and verify
        String actualSql = ORMUtils.generateInsertUserMetricSql(tableName, metricCal);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testMemory() {
        String sql = "SELECT * FROM " + "memory_extract_info_1234567890";
        String oneId = "14545";
//        String queryText = "今天";
        String queryText = "' OR '1'='1'";
        MemoryTypeEnum requestType = MemoryTypeEnum.EVENT;

        List<String> whereConditions = Lists.newArrayList();
        whereConditions.add(" oneId = '" + oneId + "' ");
        whereConditions.add(" memory_content like '%" + queryText + "%' ");
        whereConditions.add(" memory_type = '" + requestType.getCode() + "' ");

        sql += " WHERE ";
        sql += StringUtils.join(whereConditions, " AND ");
        sql += " ORDER BY extract_date DESC";

        DSLContext dsl = using(SQLDialect.MYSQL, ORMUtils.INLINED_SETTINGS);
        String ormSql = dsl
                .select()
                .from(DSL.table(DSL.name("memory_extract_info_1234567890")))
                .where(
                        field(Constants.TABLE_USER_ONE_ID).eq(oneId),
                        field("memory_content").like("%" + queryText + "%"),
                        field("memory_type").eq(requestType.getCode())
                )
                .orderBy(field("extract_date").desc())
                .getSQL();
        System.out.println(ormSql);
    }

    @Test
    public void testClearMemoryExtractResult() {
        Assertions.assertEquals(
                "delete from `memory_extract_info_1234567890` where oneId = 'id1'",
                ORMUtils.clearMemoryExtractResult("memory_extract_info_1234567890", "id1")
        );
    }

    @Test
    public void testSqlInjection() {
        System.setProperty("org.jooq.no-tips", "true");
        System.setProperty("org.jooq.no-logo", "true");
        DSLContext dsl = using(SQLDialect.MYSQL, new Settings()
                .withStatementType(StatementType.STATIC_STATEMENT));
        String tableName = "mock_user_37902964022432768";

        List<String> userIdCases = Lists.newArrayList(
                "1",
                "'1' OR 1=1",
                "' OR 1=1--",
                "'/*comment*/OR/*comment*/1=1--",
                "'--",
                "'#",
                "' OR EXISTS(SELECT * FROM users)--",
                "' AND SLEEP(2)--",
                "\\u0027 OR 1=1--",
                "'/*!OR*/1=1--",
                "'%0AOR%0A1=1--"
        );

        for (String condition : userIdCases) {
            String sql = "select * from " + tableName + " where user_id = " + condition;
            String ormSql = dsl.select().from(DSL.table(DSL.name(tableName)))
                    .where(DSL.field(DSL.name("user_id")).eq(DSL.escape(condition, '\''))).getSQL();
            System.out.println(sql);
            System.out.println(ormSql);
            System.out.println();
        }
    }

    @Test
    public void testGenerateWholeEdgeMetricWithTimeRangeOnly() {
        Date startTime = new Date(1646064000000L); // 2022-03-01 00:00:00
        Date endTime = new Date(1646150400000L);   // 2022-03-02 00:00:00

        String actual = ORMUtils.generateWholeEdgeMetric(
                "test_table",
                "task123",
                null,
                null,
                null,
                startTime,
                endTime
        );

        String expected = "select `from_node`, `end_node`, count(`oneId`) as `edge_count`, count(distinct `oneId`) as `edge_count_uv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `cal_date` >= '2022-03-01 00:00:00' " +
                "and `cal_date` < '2022-03-02 00:00:00') " +
                "group by `from_node`, `end_node`";

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateWholeEdgeMetricAllParams() {
        Date startTime = new Date(1646064000000L); // 2022-03-01 00:00:00
        Date endTime = new Date(1646150400000L);   // 2022-03-02 00:00:00

        String actual = ORMUtils.generateWholeEdgeMetric(
                "test_table",
                "task123",
                "robot456",
                "v1.0",
                "topic789",
                startTime,
                endTime
        );

        String expected = "select `from_node`, `end_node`, count(`oneId`) as `edge_count`, count(distinct `oneId`) as `edge_count_uv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `robot_id` = 'robot456' and `robot_ver` = 'v1.0' " +
                "and `topic_id` = 'topic789' and `cal_date` >= '2022-03-01 00:00:00' " +
                "and `cal_date` < '2022-03-02 00:00:00') " +
                "group by `from_node`, `end_node`";

        
        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateWholeEdgeMetricRequiredParamsOnly() {
        String actual = ORMUtils.generateWholeEdgeMetric(
                "test_table",
                "task123",
                null,
                null,
                null,
                null,
                null
        );

        String expected = "select `from_node`, `end_node`, count(`oneId`) as `edge_count`, count(distinct `oneId`) as `edge_count_uv` " +
                "from `test_table` " +
                "where `task_id` = 'task123' " +
                "group by `from_node`, `end_node`";

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateWholeEdgeMetricWithRobotIdOnly() {
        String actual = ORMUtils.generateWholeEdgeMetric(
                "test_table",
                "task123",
                "robot456",
                null,
                null,
                null,
                null
        );

        String expected = "select `from_node`, `end_node`, count(`oneId`) as `edge_count`, count(distinct `oneId`) as `edge_count_uv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `robot_id` = 'robot456') " +
                "group by `from_node`, `end_node`";

        assertEquals(expected, actual);
    }

    @Test
    void testGenerateWholeIntentMetricSomeOptionalParams() {
        String tableName = "test_table";
        List<String> endNodeIds = List.of("node1");
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = null;
        String topicId = "topic789";
        Date startTime = new Date(1700000000000L);
        Date endTime = null;

        String expectedSql = "select `node_id`, `tag`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` LATERAL VIEW EXPLODE(intent) tag_table AS tag " +
                "where (`task_id` = 'task123' and `node_id` in ('node1') and `robot_id` = 'robot456' and `topic_id` = 'topic789' and `cal_date` >= '2023-11-15 06:13:20') " +
                "group by `node_id`, `tag`";

        String actualSql = ORMUtils.generateWholeIntentMetric(tableName, endNodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    void testGenerateWholeIntentMetricAllParams() {
        String tableName = "test_table";
        List<String> endNodeIds = List.of("node1", "node2");
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "1.0";
        String topicId = "topic789";
        Date startTime = new Date(1700000000000L);
        Date endTime = new Date(1701000000000L);

        String expectedSql = "select `node_id`, `tag`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` LATERAL VIEW EXPLODE(intent) tag_table AS tag " +
                "where (`task_id` = 'task123' and `node_id` in ('node1', 'node2') and `robot_id` = 'robot456' and `robot_ver` = '1.0' and `topic_id` = 'topic789' and `cal_date` >= '2023-11-15 06:13:20' and `cal_date` < '2023-11-26 20:00:00') " +
                "group by `node_id`, `tag`";

        String actualSql = ORMUtils.generateWholeIntentMetric(tableName, endNodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    void testGenerateWholeIntentMetricRequiredParamsOnly() {
        String tableName = "test_table";
        List<String> endNodeIds = List.of();
        String taskId = "task123";
        String robotId = null;
        String robotVer = null;
        String topicId = null;
        Date startTime = null;
        Date endTime = null;

        String expectedSql = "select `node_id`, `tag`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` LATERAL VIEW EXPLODE(intent) tag_table AS tag " +
                "where `task_id` = 'task123' " +
                "group by `node_id`, `tag`";

        String actualSql = ORMUtils.generateWholeIntentMetric(tableName, endNodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateGetAllFromNodesSQLBasicCase() {
        String tableName = "test_table";
        String currNodeId = "node123";
        String taskId = "task456";
        String robotId = "robot789";
        String robotVer = "1.0";
        String topicId = "topic101";
        Date startTime = new Date(1700000000000L);
        Date endTime = new Date(1700086400000L);

        String sql = ORMUtils.generateGetAllFromNodesSQL(tableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

        String expected = "select distinct `from_node` from `test_table` " +
                "where (`end_node` = 'node123' " +
                "and `task_id` = 'task456' " +
                "and `robot_id` = 'robot789' " +
                "and `robot_ver` = '1.0' " +
                "and `topic_id` = 'topic101' " +
                "and `cal_date` >= '2023-11-15 06:13:20' " +
                "and `cal_date` < '2023-11-16 06:13:20')";
        assertEquals(expected, sql);
    }

    @Test
    public void testGenerateGetAllFromNodesSQLNullOptionalFields() {
        String tableName = "test_table";
        String currNodeId = "node123";
        String taskId = "task456";
        String robotId = null;
        String robotVer = null;
        String topicId = null;
        Date startTime = null;
        Date endTime = null;

        String sql = ORMUtils.generateGetAllFromNodesSQL(tableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

        String expected = "select distinct `from_node` from `test_table` " +
                "where (`end_node` = 'node123' " +
                "and `task_id` = 'task456')";
        assertEquals(expected, sql);
    }

    @Test
    public void testGenerateGetAllFromNodesSQLEmptyOptionalFields() {
        String tableName = "test_table";
        String currNodeId = "node123";
        String taskId = "task456";
        String robotId = "";
        String robotVer = "";
        String topicId = "";
        Date startTime = new Date(1700000000000L);
        Date endTime = new Date(1700086400000L);

        String sql = ORMUtils.generateGetAllFromNodesSQL(tableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

        String expected = "select distinct `from_node` from `test_table` " +
                "where (`end_node` = 'node123' " +
                "and `task_id` = 'task456' " +
                "and `cal_date` >= '2023-11-15 06:13:20' " +
                "and `cal_date` < '2023-11-16 06:13:20')";
        assertEquals(expected, sql);
    }

    @Test
    public void testGenerateGetAllFromNodesSQLOnlyStartTime() {
        String tableName = "test_table";
        String currNodeId = "node123";
        String taskId = "task456";
        String robotId = "robot789";
        String robotVer = null;
        String topicId = null;
        Date startTime = new Date(1700000000000L);
        Date endTime = null;

        String sql = ORMUtils.generateGetAllFromNodesSQL(tableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

        String expected = "select distinct `from_node` from `test_table` " +
                "where (`end_node` = 'node123' " +
                "and `task_id` = 'task456' " +
                "and `robot_id` = 'robot789' " +
                "and `cal_date` >= '2023-11-15 06:13:20')";
        assertEquals(expected, sql);
    }

    @Test
    public void testGenerateGetAllFromNodesSQLOnlyEndTime() {
        String tableName = "test_table";
        String currNodeId = "node123";
        String taskId = "task456";
        String robotId = null;
        String robotVer = "1.0";
        String topicId = null;
        Date startTime = null;
        Date endTime = new Date(1700086400000L);

        String sql = ORMUtils.generateGetAllFromNodesSQL(tableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

        String expected = "select distinct `from_node` from `test_table` " +
                "where (`end_node` = 'node123' " +
                "and `task_id` = 'task456' " +
                "and `robot_ver` = '1.0' " +
                "and `cal_date` < '2023-11-16 06:13:20')";
        assertEquals(expected, sql);
    }

    @Test
    public void testGenerateWholeNodeMetricWithIdsSQL() {
        // Prepare test data
        String tableName = "test_table";
        List<String> nodeIds = List.of("node1", "node2", "node3");
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "1.0";
        String topicId = "topic789";
        Date startTime = new Date(1700000000000L);
        Date endTime = new Date(1701000000000L);

        // Expected SQL
        String expectedSQL = "select `node_id`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `node_id` in ('node1', 'node2', 'node3') and `robot_id` = 'robot456' and `robot_ver` = '1.0' " +
                "and `topic_id` = 'topic789' and `cal_date` >= '2023-11-15 06:13:20' and `cal_date` < '2023-11-26 20:00:00') " +
                "group by `node_id`";

        // Execute method
        String actualSQL = ORMUtils.generateWholeNodeMetricWithIdsSQL(
                tableName, nodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);

        // Assert
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateWholeNodeMetricWithIdsSQLWithNullOptionalFields() {
        // Prepare test data with null optional fields
        String tableName = "test_table";
        List<String> nodeIds = List.of("node1");
        String taskId = "task123";
        String robotId = null;
        String robotVer = null;
        String topicId = null;
        Date startTime = null;
        Date endTime = null;

        // Expected SQL
        String expectedSQL = "select `node_id`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `node_id` in ('node1')) " +
                "group by `node_id`";

        // Execute method
        String actualSQL = ORMUtils.generateWholeNodeMetricWithIdsSQL(
                tableName, nodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);

        // Assert
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateWholeNodeMetricWithIdsSQLWithEmptyNodeIds() {
        // Prepare test data with empty nodeIds
        String tableName = "test_table";
        List<String> nodeIds = List.of();
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "1.0";
        String topicId = "topic789";
        Date startTime = new Date(1700000000000L);
        Date endTime = new Date(1701000000000L);

        // Expected SQL
        String expectedSQL = "select `node_id`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and false and `robot_id` = 'robot456' and `robot_ver` = '1.0' " +
                "and `topic_id` = 'topic789' and `cal_date` >= '2023-11-15 06:13:20' and `cal_date` < '2023-11-26 20:00:00') " +
                "group by `node_id`";

        // Execute method
        String actualSQL = ORMUtils.generateWholeNodeMetricWithIdsSQL(
                tableName, nodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);

        // Assert
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateCurrNodeToForwardNodeEdgeMetricSQL() {
        // Test with all parameters
        Date startTime = new Date(1700000000000L);
        Date endTime = new Date(1701000000000L);
        String result1 = ORMUtils.generateCurrNodeToForwardNodeEdgeMetricSQL(
                "test_table", "node123", "task456", "robot789", "1.0", "topic101", startTime, endTime);
        String expected1 = "select `end_node`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task456' and `from_node` = 'node123' and `robot_id` = 'robot789' and `robot_ver` = '1.0' and `topic_id` = 'topic101' " +
                "and `cal_date` >= '2023-11-15 06:13:20' and `cal_date` < '2023-11-26 20:00:00') " +
                "group by `end_node`";
        assertEquals(expected1, result1);

        // Test with null optional parameters
        String result2 = ORMUtils.generateCurrNodeToForwardNodeEdgeMetricSQL(
                "test_table", "node123", "task456", null, null, null, null, null);
        String expected2 = "select `end_node`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task456' and `from_node` = 'node123') " +
                "group by `end_node`";
        assertEquals(expected2, result2);

        // Test with some optional parameters
        String result3 = ORMUtils.generateCurrNodeToForwardNodeEdgeMetricSQL(
                "test_table", "node123", "task456", "robot789", null, "topic101", startTime, null);
        String expected3 = "select `end_node`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task456' and `from_node` = 'node123' and `robot_id` = 'robot789' and `topic_id` = 'topic101' " +
                "and `cal_date` >= '2023-11-15 06:13:20') " +
                "group by `end_node`";
        assertEquals(expected3, result3);
    }

    @Test
    public void testGeneratePreviewNodeToCurrNodeEdgeMetricSQL() {
        // Prepare test data
        String tableName = "test_table";
        List<String> previewNodeIds = Lists.newArrayList("node1", "node2", "node3");
        String currNodeId = "currNode";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "1.0";
        String topicId = "topic789";
        Date startTime = new Date(1700000000000L);
        Date endTime = new Date(1701000000000L);

        // Expected SQL
        String expectedSQL = "select `from_node`, count(`oneId`) as `edge_count`, count(distinct `oneId`) as `edge_count_uv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `from_node` in ('node1', 'node2', 'node3') and `end_node` in ('currNode') " +
                "and `robot_id` = 'robot456' and `robot_ver` = '1.0' and `topic_id` = 'topic789' " +
                "and `cal_date` >= '2023-11-15 06:13:20' and `cal_date` < '2023-11-26 20:00:00') " +
                "group by `from_node`";

        // Execute method
        String actualSQL = ORMUtils.generatePreviewNodeToCurrNodeEdgeMetricSQL(
                tableName, previewNodeIds, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

        // Assert
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGeneratePreviewNodeToCurrNodeEdgeMetricSQLWithOptionalParamsNull() {
        // Prepare test data with optional params null
        String tableName = "test_table";
        List<String> previewNodeIds = Lists.newArrayList("node1", "node2");
        String currNodeId = "currNode";
        String taskId = "task123";

        // Expected SQL without optional conditions
        String expectedSQL = "select `from_node`, count(`oneId`) as `edge_count`, count(distinct `oneId`) as `edge_count_uv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `from_node` in ('node1', 'node2') and `end_node` in ('currNode')) " +
                "group by `from_node`";

        // Execute method with optional params null
        String actualSQL = ORMUtils.generatePreviewNodeToCurrNodeEdgeMetricSQL(
                tableName, previewNodeIds, currNodeId, taskId, null, null, null, null, null);

        // Assert
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGeneratePreviewNodeToCurrNodeEdgeMetricSQLWithPartialOptionalParams() {
        // Prepare test data with some optional params
        String tableName = "test_table";
        List<String> previewNodeIds = Lists.newArrayList("node1");
        String currNodeId = "currNode";
        String taskId = "task123";
        String robotId = "robot456";
        Date endTime = new Date(1701000000000L);

        // Expected SQL with partial optional conditions
        String expectedSQL = "select `from_node`, count(`oneId`) as `edge_count`, count(distinct `oneId`) as `edge_count_uv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `from_node` in ('node1') and `end_node` in ('currNode') " +
                "and `robot_id` = 'robot456' and `cal_date` < '2023-11-26 20:00:00') " +
                "group by `from_node`";

        // Execute method with partial optional params
        String actualSQL = ORMUtils.generatePreviewNodeToCurrNodeEdgeMetricSQL(
                tableName, previewNodeIds, currNodeId, taskId, robotId, null, null, null, endTime);

        // Assert
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateDailyCurrNodeToForwardNodeEdgeMetricSQLOptionalParamsNull() {
        String expected = "select `end_node`, to_date(`cal_date`) as `day`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `from_node` = 'node1') " +
                "group by `end_node`, `day`";

        String actual = ORMUtils.generateDailyCurrNodeToForwardNodeEdgeMetricSQL(
                "test_table", "node1", "task123", null, null, null, null, null);

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateDailyCurrNodeToForwardNodeEdgeMetricSQLOnlyRobotId() {
        String expected = "select `end_node`, to_date(`cal_date`) as `day`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `from_node` = 'node1' and `robot_id` = 'robot1') " +
                "group by `end_node`, `day`";

        String actual = ORMUtils.generateDailyCurrNodeToForwardNodeEdgeMetricSQL(
                "test_table", "node1", "task123", "robot1", null, null, null, null);

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateDailyCurrNodeToForwardNodeEdgeMetricSQLOnlyTimeRange() {
        Date startTime = new Date(1700000000000L);
        Date endTime = new Date(1700086400000L);

        String expected = "select `end_node`, to_date(`cal_date`) as `day`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `from_node` = 'node1' " +
                "and `cal_date` >= '2023-11-15 06:13:20' and `cal_date` < '2023-11-16 06:13:20') " +
                "group by `end_node`, `day`";

        String actual = ORMUtils.generateDailyCurrNodeToForwardNodeEdgeMetricSQL(
                "test_table", "node1", "task123", null, null, null, startTime, endTime);

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateDailyCurrNodeToForwardNodeEdgeMetricSQLAllParams() {
        Date startTime = new Date(1700000000000L);
        Date endTime = new Date(1700086400000L);

        String expected = "select `end_node`, to_date(`cal_date`) as `day`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `from_node` = 'node1' and `robot_id` = 'robot1' and `robot_ver` = 'v1.0' and `topic_id` = 'topic1' " +
                "and `cal_date` >= '2023-11-15 06:13:20' and `cal_date` < '2023-11-16 06:13:20') " +
                "group by `end_node`, `day`";

        String actual = ORMUtils.generateDailyCurrNodeToForwardNodeEdgeMetricSQL(
                "test_table", "node1", "task123", "robot1", "v1.0", "topic1", startTime, endTime);

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateDailyCurrNodeMetric() {
        // Setup
        String tableName = "test_table";
        String currNodeId = "node123";
        String taskId = "task456";
        String robotId = "robot789";
        String robotVer = "1.0";
        String topicId = "topic101";
        Date startTime = new Date(1672531200000L); // 2023-01-01
        Date endTime = new Date(1672617600000L); // 2023-01-02

        // Execute
        String sql = ORMUtils.generateDailyCurrNodeMetric(tableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

        // Verify
        String expectedSql = "select to_date(`cal_date`) as `day`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task456' and `node_id` = 'node123' and `robot_id` = 'robot789' and `robot_ver` = '1.0' " +
                "and `topic_id` = 'topic101' and `cal_date` >= '2023-01-01 08:00:00' and `cal_date` < '2023-01-02 08:00:00') " +
                "group by `day`";
        assertEquals(expectedSql, sql);
    }

    @Test
    public void testGenerateDailyCurrNodeMetricWithNullOptionalFields() {
        // Setup
        String tableName = "test_table";
        String currNodeId = "node123";
        String taskId = "task456";
        Date startTime = new Date(1672531200000L); // 2023-01-01
        Date endTime = new Date(1672617600000L); // 2023-01-02

        // Execute
        String sql = ORMUtils.generateDailyCurrNodeMetric(tableName, currNodeId, taskId, null, null, null, startTime, endTime);

        // Verify
        String expectedSql = "select to_date(`cal_date`) as `day`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task456' and `node_id` = 'node123' and `cal_date` >= '2023-01-01 08:00:00' and `cal_date` < '2023-01-02 08:00:00') " +
                "group by `day`";
        assertEquals(expectedSql, sql);
    }

    @Test
    public void testGenerateDailyCurrNodeMetricWithNullDates() {
        // Setup
        String tableName = "test_table";
        String currNodeId = "node123";
        String taskId = "task456";
        String robotId = "robot789";
        String robotVer = "1.0";
        String topicId = "topic101";

        // Execute
        String sql = ORMUtils.generateDailyCurrNodeMetric(tableName, currNodeId, taskId, robotId, robotVer, topicId, null, null);

        // Verify
        String expectedSql = "select to_date(`cal_date`) as `day`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task456' and `node_id` = 'node123' and `robot_id` = 'robot789' and `robot_ver` = '1.0' " +
                "and `topic_id` = 'topic101') " +
                "group by `day`";
        assertEquals(expectedSql, sql);
    }

    @Test
    public void testGenerateWholeNodeHangupMetricAllParams() {
        Date startTime = new Date(System.currentTimeMillis() - 86400000); // yesterday
        Date endTime = new Date();

        String expectedSql = "select `node_id`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'test_task' and `hangup` = 1 and `robot_id` = 'robot123' and `robot_ver` = 'v1.0' and `topic_id` = 'topic456' " +
                "and `cal_date` >= '" + DatetimeUtils.formatDate(startTime) + "' and `cal_date` < '" + DatetimeUtils.formatDate(endTime) + "') " +
                "group by `node_id`";

        String actualSql = ORMUtils.generateWholeNodeHangupMetric(
                "test_table",
                "test_task",
                "robot123",
                "v1.0",
                "topic456",
                startTime,
                endTime);

        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateWholeNodeHangupMetricRequiredParamsOnly() {
        String expectedSql = "select `node_id`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'test_task' and `hangup` = 1) " +
                "group by `node_id`";

        String actualSql = ORMUtils.generateWholeNodeHangupMetric(
                "test_table",
                "test_task",
                null,
                null,
                null,
                null,
                null);

        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateWholeNodeHangupMetricPartialOptionalParams() {
        Date startTime = new Date();

        String expectedSql = "select `node_id`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'test_task' and `hangup` = 1 and `robot_id` = 'robot123' and `cal_date` >= '" + DatetimeUtils.formatDate(startTime) + "') " +
                "group by `node_id`";

        String actualSql = ORMUtils.generateWholeNodeHangupMetric(
                "test_table",
                "test_task",
                "robot123",
                null,
                null,
                startTime,
                null);

        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateNodeMetricWithIdsSQLWithNullOptionalParams() {
        String tableName = "test_table";
        List<String> nodeIds = List.of("node1");
        String taskId = "task123";

        String expectedSql = "select count(distinct `oneId`) as `node_uv`, count(distinct `sessionId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `node_id` in ('node1'))";

        String actualSql = ORMUtils.generateNodeMetricWithIdsSQL(
                tableName, nodeIds, taskId, null, null, null, null, null
        );

        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateNodeMetricWithIdsSQLWithPartialOptionalParams() {
        String tableName = "test_table";
        List<String> nodeIds = List.of("node1", "node2");
        String taskId = "task123";
        String robotId = "robot456";
        Date startTime = new Date(1700000000000L);

        String expectedSql = "select count(distinct `oneId`) as `node_uv`, count(distinct `sessionId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `node_id` in ('node1', 'node2') and " +
                "`robot_id` = 'robot456' and `cal_date` >= '2023-11-15 06:13:20')";

        String actualSql = ORMUtils.generateNodeMetricWithIdsSQL(
                tableName, nodeIds, taskId, robotId, null, null, startTime, null
        );

        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateNodeMetricWithIdsSQL() {
        String tableName = "test_table";
        List<String> nodeIds = List.of("node1", "node2", "node3");
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "1.0";
        String topicId = "topic789";
        Date startTime = new Date(1700000000000L);
        Date endTime = new Date(1700086400000L);

        String expectedSql = "select count(distinct `oneId`) as `node_uv`, count(distinct `sessionId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `node_id` in ('node1', 'node2', 'node3') and " +
                "`robot_id` = 'robot456' and `robot_ver` = '1.0' and `topic_id` = 'topic789' and " +
                "`cal_date` >= '2023-11-15 06:13:20' and `cal_date` < '2023-11-16 06:13:20')";

        String actualSql = ORMUtils.generateNodeMetricWithIdsSQL(
                tableName, nodeIds, taskId, robotId, robotVer, topicId, startTime, endTime
        );

        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateCheckDuplicateDataSql() {
        // Test case 1: Normal case
        String sql1 = ORMUtils.generateCheckDuplicateDataSql("user_table", "123", "user_id");
        assertEquals("select count(*) from `user_table` where `user_id` = '123' limit 1", sql1);

        // Test case 2: Empty id
        String sql2 = ORMUtils.generateCheckDuplicateDataSql("user_table", "", "user_id");
        assertEquals("select count(*) from `user_table` where `user_id` = '' limit 1", sql2);

        // Test case 3: Special characters in id
        String sql3 = ORMUtils.generateCheckDuplicateDataSql("user_table", "abc@123#", "user_id");
        assertEquals("select count(*) from `user_table` where `user_id` = 'abc@123#' limit 1", sql3);

        // Test case 4: Different field name
        String sql4 = ORMUtils.generateCheckDuplicateDataSql("product_table", "456", "product_code");
        assertEquals("select count(*) from `product_table` where `product_code` = '456' limit 1", sql4);

        // Test case 5: Table with schema
        String sql5 = ORMUtils.generateCheckDuplicateDataSql("db.schema.user_table", "789", "id");
    }

    @Test
    public void testGenerateCheckDuplicateDataSql1() {
        // Test case 1: Normal case
        String sql1 = ORMUtils.generateCheckDuplicateDataSql("user_table", "123", "user_id");
        assertEquals("select count(*) from `user_table` where `user_id` = '123' limit 1", sql1);

        // Test case 2: Empty id
        String sql2 = ORMUtils.generateCheckDuplicateDataSql("user_table", "", "user_id");
        assertEquals("select count(*) from `user_table` where `user_id` = '' limit 1", sql2);

        // Test case 3: Special characters in id
        String sql3 = ORMUtils.generateCheckDuplicateDataSql("user_table", "abc@123#", "user_id");
        assertEquals("select count(*) from `user_table` where `user_id` = 'abc@123#' limit 1", sql3);

        // Test case 4: Different field name
        String sql4 = ORMUtils.generateCheckDuplicateDataSql("product_table", "456", "product_code");
        assertEquals("select count(*) from `product_table` where `product_code` = '456' limit 1", sql4);

        // Test case 5: Table with schema
        String sql5 = ORMUtils.generateCheckDuplicateDataSql("db.schema.user_table", "789", "id");
    }

    @Test
    public void testGenerateClearSOPMetricDataSql() {
        // Test normal case
        String tableName = "aiob_sop_metric_data";
        String robotVer = "v2.3.5";
        String expectedSql = "delete from `aiob_sop_metric_data` where `robot_ver` = 'v2.3.5'";
        String actualSql = ORMUtils.generateClearSOPMetricDataSql(tableName, robotVer);
        assertEquals(expectedSql, actualSql);

        // Test empty robot version
        String emptyVersionSql = "delete from `aiob_sop_metric_data` where `robot_ver` = ''";
        actualSql = ORMUtils.generateClearSOPMetricDataSql(tableName, "");
        assertEquals(emptyVersionSql, actualSql);

        // Test special characters in robot version
        String specialVersion = "v2.3.5-beta!@#";
        String specialVersionSql = "delete from `aiob_sop_metric_data` where `robot_ver` = 'v2.3.5-beta!@#'";
        actualSql = ORMUtils.generateClearSOPMetricDataSql(tableName, specialVersion);
        assertEquals(specialVersionSql, actualSql);

        // Test different table name
        String diffTableName = "another_sop_table";
        String diffTableSql = "delete from `another_sop_table` where `robot_ver` = 'v2.3.5'";
        actualSql = ORMUtils.generateClearSOPMetricDataSql(diffTableName, robotVer);
        assertEquals(diffTableSql, actualSql);
    }

    @Test
    public void testGenerateFlexibleIdTransferSql() {
        // Test normal case
        String tableName = "robot_id_mapping";
        String agentId = "agent123";
        String versionId = "v1.0";

        String expectedSql = "select `robot_id`, `robot_ver` from `robot_id_mapping` " +
                "where (`agent_id` = 'agent123' and `version_id` = 'v1.0') limit 1";

        String actualSql = ORMUtils.generateFlexibleIdTransferSql(tableName, agentId, versionId);
        assertEquals(expectedSql, actualSql);

        // Test with empty strings
        String emptyTableSql = ORMUtils.generateFlexibleIdTransferSql("", agentId, versionId);
        assertEquals("select `robot_id`, `robot_ver` from `` where (`agent_id` = 'agent123' and `version_id` = 'v1.0') limit 1",
                emptyTableSql);

        // Test with null values
        String nullAgentSql = ORMUtils.generateFlexibleIdTransferSql(tableName, null, versionId);
        assertEquals("select `robot_id`, `robot_ver` from `robot_id_mapping` where (`agent_id` = null and `version_id` = 'v1.0') limit 1",
                nullAgentSql);

        String nullVersionSql = ORMUtils.generateFlexibleIdTransferSql(tableName, agentId, null);
        assertEquals("select `robot_id`, `robot_ver` from `robot_id_mapping` where (`agent_id` = 'agent123' and `version_id` = null) limit 1",
                nullVersionSql);
    }

    @Test
    public void testGenerateQueryTaskScene() {
        String tableName = "task_scene_table";
        String taskId = "12345";

        String expectedSql = "select `robotScene` from `task_scene_table` where (`taskId` = '12345' and `sipCode` = '200') limit 1";
        String actualSql = ORMUtils.generateQueryTaskScene(tableName, taskId);

        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateCountSopUserDetailSQLWithOnlyEndTime() {
        String nodeTableName = "sop_user_detail_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "v1.0";
        String nodeId = "node789";
        Date endTime = new Date(1646208000000L);

        String actual = ORMUtils.generateCountSopUserDetailSQL(nodeTableName, taskId, robotId, robotVer, null, endTime, nodeId);

        String expected = "select count(distinct `oneId`) as `count` " +
                "from `sop_user_detail_table` " +
                "where (`node_id` = 'node789' " +
                "and `task_id` = 'task123' " +
                "and `robot_id` = 'robot456' " +
                "and `robot_ver` = 'v1.0' " +
                "and `cal_date` < '2022-03-02 16:00:00')";

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateCountSopUserDetailSQLWithAllParameters() {
        String nodeTableName = "sop_user_detail_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "v1.0";
        String nodeId = "node789";
        Date startTime = new Date(1646121600000L);
        Date endTime = new Date(1646208000000L);

        String actual = ORMUtils.generateCountSopUserDetailSQL(nodeTableName, taskId, robotId, robotVer, startTime, endTime, nodeId);

        String expected = "select count(distinct `oneId`) as `count` " +
                "from `sop_user_detail_table` " +
                "where (`node_id` = 'node789' " +
                "and `task_id` = 'task123' " +
                "and `robot_id` = 'robot456' " +
                "and `robot_ver` = 'v1.0' " +
                "and `cal_date` >= '2022-03-01 16:00:00' and `cal_date` < '2022-03-02 16:00:00')";

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateCountSopUserDetailSQLWithoutTimeParameters() {
        String nodeTableName = "sop_user_detail_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "v1.0";
        String nodeId = "node789";

        String actual = ORMUtils.generateCountSopUserDetailSQL(nodeTableName, taskId, robotId, robotVer, null, null, nodeId);

        String expected = "select count(distinct `oneId`) as `count` " +
                "from `sop_user_detail_table` " +
                "where (`node_id` = 'node789' " +
                "and `task_id` = 'task123' " +
                "and `robot_id` = 'robot456' " +
                "and `robot_ver` = 'v1.0')";

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateCountSopUserDetailSQLWithOnlyStartTime() {
        String nodeTableName = "sop_user_detail_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "v1.0";
        String nodeId = "node789";
        Date startTime = new Date(1646121600000L); // 2022-03-01 00:00:00

        String actual = ORMUtils.generateCountSopUserDetailSQL(nodeTableName, taskId, robotId, robotVer, startTime, null, nodeId);

        String expected = "select count(distinct `oneId`) as `count` " +
                "from `sop_user_detail_table` " +
                "where (`node_id` = 'node789' " +
                "and `task_id` = 'task123' " +
                "and `robot_id` = 'robot456' " +
                "and `robot_ver` = 'v1.0' " +
                "and `cal_date` >= '2022-03-01 16:00:00')";

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateCountSopUserDetailWithIntentSQLAllParams() throws ParseException {
        String nodeTableName = "test_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "v1.0";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startTime = sdf.parse("2023-01-01");
        Date endTime = sdf.parse("2023-01-31");
        String intent = "test_intent";

        String expectedSQL = "select count(distinct `oneId`) as `count` " +
                "from `test_table` " +
                "where (array_contains(`intent`, 'test_intent') " +
                "and `task_id` = 'task123' " +
                "and `robot_id` = 'robot456' " +
                "and `robot_ver` = 'v1.0' " +
                "and `cal_date` >= '2023-01-01 00:00:00' " +
                "and `cal_date` < '2023-01-31 00:00:00')";

        String actualSQL = ORMUtils.generateCountSopUserDetailWithIntentSQL(
                nodeTableName, taskId, robotId, robotVer, startTime, endTime, intent);

        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateCountSopUserDetailWithIntentSQLNoStartTime() throws ParseException {
        String nodeTableName = "test_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "v1.0";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date endTime = sdf.parse("2023-01-31");
        String intent = "test_intent";

        String expectedSQL = "select count(distinct `oneId`) as `count` " +
                "from `test_table` " +
                "where (array_contains(`intent`, 'test_intent') " +
                "and `task_id` = 'task123' " +
                "and `robot_id` = 'robot456' " +
                "and `robot_ver` = 'v1.0' " +
                "and `cal_date` < '2023-01-31 00:00:00')";

        String actualSQL = ORMUtils.generateCountSopUserDetailWithIntentSQL(
                nodeTableName, taskId, robotId, robotVer, null, endTime, intent);

        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateCountSopUserDetailWithIntentSQLNoEndTime() throws ParseException {
        String nodeTableName = "test_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "v1.0";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startTime = sdf.parse("2023-01-01");
        String intent = "test_intent";

        String expectedSQL = "select count(distinct `oneId`) as `count` " +
                "from `test_table` " +
                "where (array_contains(`intent`, 'test_intent') " +
                "and `task_id` = 'task123' " +
                "and `robot_id` = 'robot456' " +
                "and `robot_ver` = 'v1.0' " +
                "and `cal_date` >= '2023-01-01 00:00:00')";

        String actualSQL = ORMUtils.generateCountSopUserDetailWithIntentSQL(
                nodeTableName, taskId, robotId, robotVer, startTime, null, intent);

        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateCountSopUserDetailWithIntentSQLNoTimeRange() {
        String nodeTableName = "test_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "v1.0";
        String intent = "test_intent";

        String expectedSQL = "select count(distinct `oneId`) as `count` " +
                "from `test_table` " +
                "where (array_contains(`intent`, 'test_intent') " +
                "and `task_id` = 'task123' " +
                "and `robot_id` = 'robot456' " +
                "and `robot_ver` = 'v1.0')";

        String actualSQL = ORMUtils.generateCountSopUserDetailWithIntentSQL(
                nodeTableName, taskId, robotId, robotVer, null, null, intent);

        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateQuerySopUserDetailSQLWithOnlyStartTime() {
        // Prepare test data with only start time
        String nodeTableName = "test_node_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "1.0";
        Date startTime = new Date(System.currentTimeMillis() - 86400000); // yesterday
        String nodeId = "node789";
        Integer pageNo = 3;
        Integer pageSize = 5;

        // Expected SQL
        String expectedSQL = "select `oneId`, GROUP_CONCAT(`sessionId`, ',') as `sessionIds` " +
                "from `test_node_table` " +
                "where (`node_id` = 'node789' and `task_id` = 'task123' and `robot_id` = 'robot456' and `robot_ver` = '1.0' " +
                "and `cal_date` >= '" + DatetimeUtils.formatDate(startTime) + "') " +
                "group by `oneId` " +
                "order by `oneId` asc " +
                "limit 5 offset 10";

        // Call method under test
        String actualSQL = ORMUtils.generateQuerySopUserDetailSQL(
                nodeTableName, taskId, robotId, robotVer, startTime, null, nodeId, pageNo, pageSize);

        // Assert
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateQuerySopUserDetailSQLWithOnlyEndTime() {
        // Prepare test data with only end time
        String nodeTableName = "test_node_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "1.0";
        Date endTime = new Date(); // now
        String nodeId = "node789";
        Integer pageNo = 1;
        Integer pageSize = 100;

        // Expected SQL
        String expectedSQL = "select `oneId`, GROUP_CONCAT(`sessionId`, ',') as `sessionIds` " +
                "from `test_node_table` " +
                "where (`node_id` = 'node789' and `task_id` = 'task123' and `robot_id` = 'robot456' and `robot_ver` = '1.0' " +
                "and `cal_date` < '" + DatetimeUtils.formatDate(endTime) + "') " +
                "group by `oneId` " +
                "order by `oneId` asc " +
                "limit 100 offset 0";

        // Call method under test
        String actualSQL = ORMUtils.generateQuerySopUserDetailSQL(
                nodeTableName, taskId, robotId, robotVer, null, endTime, nodeId, pageNo, pageSize);

        // Assert
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateQuerySopUserDetailSQL() {
        // Prepare test data
        String nodeTableName = "test_node_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "1.0";
        Date startTime = new Date(System.currentTimeMillis() - 86400000); // yesterday
        Date endTime = new Date(); // now
        String nodeId = "node789";
        Integer pageNo = 2;
        Integer pageSize = 10;

        // Expected SQL
        String expectedSQL = "select `oneId`, GROUP_CONCAT(`sessionId`, ',') as `sessionIds` " +
                "from `test_node_table` " +
                "where (`node_id` = 'node789' and `task_id` = 'task123' and `robot_id` = 'robot456' and `robot_ver` = '1.0' " +
                "and `cal_date` >= '" + DatetimeUtils.formatDate(startTime) + "' " +
                "and `cal_date` < '" + DatetimeUtils.formatDate(endTime) + "') " +
                "group by `oneId` " +
                "order by `oneId` asc " +
                "limit 10 offset 10";

        // Call method under test
        String actualSQL = ORMUtils.generateQuerySopUserDetailSQL(
                nodeTableName, taskId, robotId, robotVer, startTime, endTime, nodeId, pageNo, pageSize);

        // Assert
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateQuerySopUserDetailSQLWithoutTimeRange() {
        // Prepare test data without time range
        String nodeTableName = "test_node_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "1.0";
        String nodeId = "node789";
        Integer pageNo = 1;
        Integer pageSize = 20;

        // Expected SQL
        String expectedSQL = "select `oneId`, GROUP_CONCAT(`sessionId`, ',') as `sessionIds` " +
                "from `test_node_table` " +
                "where (`node_id` = 'node789' and `task_id` = 'task123' and `robot_id` = 'robot456' and `robot_ver` = '1.0') " +
                "group by `oneId` " +
                "order by `oneId` asc " +
                "limit 20 offset 0";

        // Call method under test
        String actualSQL = ORMUtils.generateQuerySopUserDetailSQL(
                nodeTableName, taskId, robotId, robotVer, null, null, nodeId, pageNo, pageSize);

        // Assert
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateQuerySopUserDetailWithIntentSQLOnlyStartTime() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startTime = sdf.parse("2023-01-01");

        String actual = ORMUtils.generateQuerySopUserDetailWithIntentSQL(
                "test_table",
                "task123",
                "robot456",
                "v1.0",
                startTime,
                null,
                "greeting",
                3,
                30
        );

        String expected = "select `oneId`, GROUP_CONCAT(`sessionId`, ',') as `sessionIds` " +
                "from `test_table` " +
                "where (array_contains(`intent`, 'greeting') and `task_id` = 'task123' and `robot_id` = 'robot456' and `robot_ver` = 'v1.0' " +
                "and `cal_date` >= '2023-01-01 00:00:00') " +
                "group by `oneId` " +
                "order by `oneId` asc " +
                "limit 30 offset 60";

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateQuerySopUserDetailWithIntentSQL() throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date startTime = sdf.parse("2023-01-01");
        Date endTime = sdf.parse("2023-01-31");

        String actual = ORMUtils.generateQuerySopUserDetailWithIntentSQL(
                "test_table",
                "task123",
                "robot456",
                "v1.0",
                startTime,
                endTime,
                "greeting",
                1,
                10
        );

        String expected = "select `oneId`, GROUP_CONCAT(`sessionId`, ',') as `sessionIds` " +
                "from `test_table` " +
                "where (array_contains(`intent`, 'greeting') and `task_id` = 'task123' and `robot_id` = 'robot456' and `robot_ver` = 'v1.0' " +
                "and `cal_date` >= '2023-01-01 00:00:00' and `cal_date` < '2023-01-31 00:00:00') " +
                "group by `oneId` " +
                "order by `oneId` asc " +
                "limit 10 offset 0";

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateQuerySopUserDetailWithIntentSQLNoDateRange() {
        String actual = ORMUtils.generateQuerySopUserDetailWithIntentSQL(
                "test_table",
                "task123",
                "robot456",
                "v1.0",
                null,
                null,
                "greeting",
                2,
                20
        );

        String expected = "select `oneId`, GROUP_CONCAT(`sessionId`, ',') as `sessionIds` " +
                "from `test_table` " +
                "where (array_contains(`intent`, 'greeting') and `task_id` = 'task123' and `robot_id` = 'robot456' and `robot_ver` = 'v1.0') " +
                "group by `oneId` " +
                "order by `oneId` asc " +
                "limit 20 offset 20";

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateQuerySessionStartTimeWithIdsSQLEmptySessionIds() {
        String sessionTable = "aiob_conversation_session";
        List<String> sessionIds = Collections.emptyList();

        String sql = ORMUtils.generateQuerySessionStartTimeWithIdsSQL(sessionTable, sessionIds);

        assertEquals(
                "select `sessionId`, `startTime`, `mobile`, `oneId` from `aiob_conversation_session` where false",
                sql
        );
    }

    @Test
    public void testGenerateQuerySessionStartTimeWithIdsSQLSingleSessionId() {
        String sessionTable = "aiob_conversation_session_123";
        List<String> sessionIds = Collections.singletonList("session123");

        String sql = ORMUtils.generateQuerySessionStartTimeWithIdsSQL(sessionTable, sessionIds);

        assertEquals(
                "select `sessionId`, `startTime`, `mobile`, `oneId` from `aiob_conversation_session_123` where `sessionId` in ('session123')",
                sql
        );
    }

    @Test
    public void testGenerateQuerySessionStartTimeWithIdsSQLMultipleSessionIds() {
        String sessionTable = "aiob_conversation_session_456";
        List<String> sessionIds = Arrays.asList("session1", "session2", "session3");

        String sql = ORMUtils.generateQuerySessionStartTimeWithIdsSQL(sessionTable, sessionIds);

        assertEquals(
                "select `sessionId`, `startTime`, `mobile`, `oneId` from `aiob_conversation_session_456` where `sessionId` in ('session1', 'session2', 'session3')",
                sql
        );
    }

    @Test
    public void testGenerateQueryAgentIdBySQL() {
        String tableName = "agent_table";
        String sessionId = "session123";
        String nodeId = "node456";

        String expectedSQL = "select `agent_id`, `version_id` from `agent_table` " +
                "where `sessionId` = 'session123' and `nodeId` = 'node456' limit 1";

        String actualSQL = ORMUtils.generateQueryAgentIdBySQL(tableName, sessionId, nodeId);

    }

    @Test
    public void testGenerateQueryAgentIdBySQLWithEmptyInput() {
        String tableName = "";
        String sessionId = "";
        String nodeId = "";

        String expectedSQL = "select `agent_id`, `version_id` from `` " +
                "where `sessionId` = '' and `nodeId` = '' limit 1";

        String actualSQL = ORMUtils.generateQueryAgentIdBySQL(tableName, sessionId, nodeId);

    }

    @Test
    public void testGenerateQueryAgentIdBySQLWithSpecialCharacters() {
        String tableName = "agent_table_123";
        String sessionId = "session-123_abc";
        String nodeId = "node@456#";

        String expectedSQL = "select `agent_id`, `version_id` from `agent_table_123` " +
                "where `sessionId` = 'session-123_abc' and `nodeId` = 'node@456#' limit 1";

        String actualSQL = ORMUtils.generateQueryAgentIdBySQL(tableName, sessionId, nodeId);

    }

    @Test
    public void testGenerateWholeNodeMetricFullParams() {
        String tableName = "test_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "1.0";
        String topicId = "topic789";
        Date startTime = new Date(1646064000000L); // 2022-03-01
        Date endTime = new Date(1648742400000L); // 2022-03-31

        String expectedSql = "select `node_id`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `node_id` <> '' and `robot_id` = 'robot456' and `robot_ver` = '1.0' and `topic_id` = 'topic789' " +
                "and `cal_date` >= '2022-03-01 00:00:00' and `cal_date` < '2022-04-01 00:00:00') group by `node_id`";

        String actualSql = ORMUtils.generateWholeNodeMetric(tableName, taskId, robotId, robotVer, topicId, startTime, endTime);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateWholeNodeMetricWithRequiredParamsOnly() {
        String tableName = "test_table";
        String taskId = "task123";
        String robotId = null;
        String robotVer = null;
        String topicId = null;
        Date startTime = null;
        Date endTime = null;

        String expectedSql = "select `node_id`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `node_id` <> '') group by `node_id`";

        String actualSql = ORMUtils.generateWholeNodeMetric(tableName, taskId, robotId, robotVer, topicId, startTime, endTime);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateWholeNodeMetricWithPartialOptionalParams() {
        String tableName = "test_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = null;
        String topicId = "topic789";
        Date startTime = new Date(1646064000000L); // 2022-03-01
        Date endTime = null;

        String expectedSql = "select `node_id`, count(distinct `oneId`) as `node_uv`, count(`oneId`) as `node_pv` " +
                "from `test_table` " +
                "where (`task_id` = 'task123' and `node_id` <> '' and `robot_id` = 'robot456' and `topic_id` = 'topic789' " +
                "and `cal_date` >= '2022-03-01 00:00:00') group by `node_id`";

        String actualSql = ORMUtils.generateWholeNodeMetric(tableName, taskId, robotId, robotVer, topicId, startTime, endTime);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateCountAnalysedMetricSQL() {
        String sessionTableName = "aiob_session_table";
        String nodeMetricTableName = "aiob_node_metric_table";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "1.0.0";

        String actual = ORMUtils.generateCountAnalysedMetricSQL(
                sessionTableName, nodeMetricTableName, taskId, robotId, robotVer);

        String expected = "select count(distinct `n`.`sessionId`) as `count` " +
                "from `aiob_node_metric_table` as `n` " +
                "where (`n`.`task_id` = 'task123' and `n`.`robot_id` = 'robot456' and `n`.`robot_ver` = '1.0.0')";

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateCountAnalysedMetricSQLWithEmptyParams() {
        String sessionTableName = "session_table";
        String nodeMetricTableName = "node_table";
        String taskId = "";
        String robotId = "";
        String robotVer = "";

        String actual = ORMUtils.generateCountAnalysedMetricSQL(
                sessionTableName, nodeMetricTableName, taskId, robotId, robotVer);

        String expected = "select count(distinct `n`.`sessionId`) as `count` " +
                "from `node_table` as `n` " +
                "where (`n`.`task_id` = '' and `n`.`robot_id` = '' and `n`.`robot_ver` = '')";

        assertEquals(expected, actual);
    }

    @Test
    public void testGenerateCountSessionConnectedCallSQLWithoutRobotIdAndVer() {
        String expectedSQL = "select count(distinct `sessionId`) as `count` from `test_table` where (`taskId` = 'task123' and `sipCode` = '200')";
        String actualSQL = ORMUtils.generateCountSessionConnectedCallSQL("test_table", "task123", null, null);
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateCountSessionConnectedCallSQLWithRobotId() {
        String expectedSQL = "select count(distinct `sessionId`) as `count` from `test_table` where (`taskId` = 'task123' and `sipCode` = '200' and `robotId` = 'robot456')";
        String actualSQL = ORMUtils.generateCountSessionConnectedCallSQL("test_table", "task123", "robot456", null);
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateCountSessionConnectedCallSQLWithRobotVer() {
        String expectedSQL = "select count(distinct `sessionId`) as `count` from `test_table` where (`taskId` = 'task123' and `sipCode` = '200' and `botVersionId` = 'ver789')";
        String actualSQL = ORMUtils.generateCountSessionConnectedCallSQL("test_table", "task123", null, "ver789");
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateCountSessionConnectedCallSQLWithRobotIdAndVer() {
        String expectedSQL = "select count(distinct `sessionId`) as `count` from `test_table` where (`taskId` = 'task123' and `sipCode` = '200' and `robotId` = 'robot456' and `botVersionId` = 'ver789')";
        String actualSQL = ORMUtils.generateCountSessionConnectedCallSQL("test_table", "task123", "robot456", "ver789");
        assertEquals(expectedSQL, actualSQL);
    }

    @Test
    public void testGenerateCountFlexibleSessionConnectedCallSQL() {
        // Test case 1: Normal case with all parameters
        String sessionTable = "aiob_conversation_session";
        String debugTable = "aiob_conversation_debug";
        String taskId = "task123";
        String robotId = "robot456";
        String robotVer = "v1.0";

        String expectedSQL = "select count(distinct `s`.`sessionId`) as `count` " +
                "from `aiob_conversation_session` as `s` " +
                "where (`s`.`taskId` = 'task123' and `s`.`sipCode` = '200' and exists (select 1 as `one` " +
                "from `aiob_conversation_debug` as `n` " +
                "where (`n`.`sessionId` = `s`.`sessionId` and `n`.`agent_id` = 'robot456' and `n`.`version_id` = 'v1.0')))";

        String actualSQL = ORMUtils.generateCountFlexibleSessionConnectedCallSQL(
                sessionTable, debugTable, taskId, robotId, robotVer);

        assertEquals(expectedSQL, actualSQL);

        // Test case 2: Empty strings as parameters
        String emptyExpectedSQL = "select count(distinct `s`.`sessionId`) as `count` " +
                "from `` as `s` " +
                "where (`s`.`taskId` = '' and `s`.`sipCode` = '200' and exists (select 1 as `one` " +
                "from `` as `n` " +
                "where (`n`.`sessionId` = `s`.`sessionId` and `n`.`agent_id` = '' and `n`.`version_id` = '')))";

        String emptyActualSQL = ORMUtils.generateCountFlexibleSessionConnectedCallSQL(
                "", "", "", "", "");

        assertEquals(emptyExpectedSQL, emptyActualSQL);

        // Test case 3: Special characters in parameters
        String specialCharExpectedSQL = "select count(distinct `s`.`sessionId`) as `count` " +
                "from `table_with_$pecial` as `s` " +
                "where (`s`.`taskId` = 'task_123' and `s`.`sipCode` = '200' and exists (select 1 as `one` " +
                "from `debug_table_123` as `n` " +
                "where (`n`.`sessionId` = `s`.`sessionId` and `n`.`agent_id` = 'robot@123' and `n`.`version_id` = 'v1.0-beta')))";

        String specialCharActualSQL = ORMUtils.generateCountFlexibleSessionConnectedCallSQL(
                "table_with_$pecial", "debug_table_123", "task_123", "robot@123", "v1.0-beta");

        assertEquals(specialCharExpectedSQL, specialCharActualSQL);
    }

    @Test
    public void testGenerateCountFlexibleProcessedCallSQL() {
        // Setup test data
        String sessionTableName = "aiob_conversation_session_service_123";
        String nodeTableName = "aiob_conversation_node_123";
        String taskId = "task_123";
        String robotId = "robot_123";
        String robotVer = "v1.0";

        // Execute the method
        String actualSql = ORMUtils.generateCountFlexibleProcessedCallSQL(
                sessionTableName, nodeTableName, taskId, robotId, robotVer);

        // Verify the result
        String expectedSql = "select count(distinct `s`.`sessionId`) as `count` " +
                "from `aiob_conversation_session_service_123` as `s` " +
                "join `aiob_conversation_node_123` as `n` " +
                "on `n`.`sessionId` = `s`.`sessionId` " +
                "where (`n`.`task_id` = 'task_123' and `n`.`robot_id` = 'robot_123' and `n`.`robot_ver` = 'v1.0')";

        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateCountFlexibleProcessedCallSQLWithDifferentInputs() {
        // Setup test data with different values
        String sessionTableName = "session_table_456";
        String nodeTableName = "node_table_456";
        String taskId = "task_456";
        String robotId = "robot_456";
        String robotVer = "v2.0";

        // Execute the method
        String actualSql = ORMUtils.generateCountFlexibleProcessedCallSQL(
                sessionTableName, nodeTableName, taskId, robotId, robotVer);

        // Verify the result
        String expectedSql = "select count(distinct `s`.`sessionId`) as `count` " +
                "from `session_table_456` as `s` " +
                "join `node_table_456` as `n` " +
                "on `n`.`sessionId` = `s`.`sessionId` " +
                "where (`n`.`task_id` = 'task_456' and `n`.`robot_id` = 'robot_456' and `n`.`robot_ver` = 'v2.0')";

        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateCallAnalysisCoreMetrics() {
        // Setup test data
        String tableName = "call_record_table";
        Date startTime = new Date(1699971200000L);
        Date endTime = new Date(1700057600000L);

        // Expected SQL
        String expectedSql = "select count(*) as `dial_count`, SUM(IF(sipCode = '200', 1, 0)) as `connected_count`," +
                " ROUND(SUM(IF(sipCode = '200', 1, 0 )) / COUNT(*), 2) as `connect_rate` from `call_record_table` where " +
                "(`deepsight_datetime` >= '2023-11-14 22:13:20' and `deepsight_datetime` < '2023-11-15 22:13:20')";

        // Execute method
        String actualSql = ORMUtils.generateCallAnalysisCoreMetrics(tableName, startTime, endTime);

        // Assert
//        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateConnectionRateTrendHourType() {
        ConnectionRateTrendRequest request = new ConnectionRateTrendRequest();
        request.setType(DateLineTypeEnum.HOUR);

        String tableName = "test_table";
        String actual = ORMUtils.generateConnectionRateTrend(request, tableName);

        assertNotNull(actual);
    }

    @Test
    public void testGenerateConnectionRateTrendDayType() {
        ConnectionRateTrendRequest request = new ConnectionRateTrendRequest();
        request.setType(DateLineTypeEnum.DAY);

        String tableName = "test_table";
        String actual = ORMUtils.generateConnectionRateTrend(request, tableName);
        assertNotNull(actual);
    }

    @Test
    public void testGenerateQueryAiobAlertDaysSql() {
        // Test case 1: All parameters provided
        Date startTime = new Date(1700000000000L);
        Date endTime = new Date(1701000000000L);
        String sql1 = ORMUtils.generateQueryAiobAlertDaysSql("tenant123", AlertConfigTypeEnum.ROBOT, "robot123", startTime, endTime);
        assertEquals("select count(distinct `alarm_date`) as `total` from `alert_record` " +
                        "where (`tenant_id` = 'tenant123' and `create_time` >= 'Wed Nov 15 06:13:20 CST 2023' " +
                        "and `create_time` < 'Sun Nov 26 20:00:00 CST 2023' and `config_type` = 'ROBOT' and `config_target` = 'robot123')",
                sql1);

        // Test case 2: Null configType and configTarget
        String sql2 = ORMUtils.generateQueryAiobAlertDaysSql("tenant123", null, null, startTime, endTime);
        assertEquals("select count(distinct `alarm_date`) as `total` from `alert_record` where (`tenant_id` =" +
                        " 'tenant123' and `create_time` >= 'Wed Nov 15 06:13:20 CST 2023' and `create_time` < 'Sun Nov 26 20:00:00 CST 2023')",
                sql2);

        // Test case 3: Null startTime and endTime
        String sql3 = ORMUtils.generateQueryAiobAlertDaysSql("tenant123", AlertConfigTypeEnum.TASK, "task456", null, null);
        assertEquals("select count(distinct `alarm_date`) as `total` from `alert_record` where (`tenant_id` =" +
                        " 'tenant123' and `config_type` = 'TASK' and `config_target` = 'task456')",
                sql3);

        // Test case 4: Only required tenantId
        String sql4 = ORMUtils.generateQueryAiobAlertDaysSql("tenant123", null, null, null, null);
        assertEquals("select count(distinct `alarm_date`) as `total` from `alert_record` " +
                        "where `tenant_id` = 'tenant123'",
                sql4);
    }

    @Test
    public void testGenerateAiobThirtyDayRankingPageSqlLineType() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.LINE);
        request.setSource(AiobLineSourceEnum.CUSTOMER);
        request.setLineStatus(AiobLineStatusEnum.ENABLED);
        request.setSortField(AiobSortFieldEnum.CALL_COUNT);
        request.setSortOrder(SortTypeEnum.DESC);
        request.setPageSize(10);

        String tableName = "aiob_conversation_session_123";
        String tenantId = "tenant_456";

        String sql = ORMUtils.generateAiobThirtyDayRankingPageSql(request, tableName, tenantId);

        assertNotNull(sql);
        assertTrue(sql.contains("`callerNum`"));
        assertTrue(sql.contains("`didOwner`"));
        assertTrue(sql.contains("`lineStatus`"));
        assertTrue(sql.contains("count(*) as `dial_count`"));
        assertTrue(sql.contains("order by `dial_count` desc"));
    }

    @Test
    public void testGenerateAiobThirtyDayRankingPageSqlTaskType() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.TASK);
        request.setTaskStatus(AiobTaskStatusEnum.RUNNING);
        request.setSortField(AiobSortFieldEnum.CONNECTED_RATE);
        request.setSortOrder(SortTypeEnum.ASC);
        request.setPageSize(20);

        String tableName = "aiob_conversation_session_789";
        String tenantId = "tenant_101";

        String sql = ORMUtils.generateAiobThirtyDayRankingPageSql(request, tableName, tenantId);

        assertNotNull(sql);
        assertTrue(sql.contains("`taskId`"));
        assertTrue(sql.contains("`taskStatus`"));
    }

    @Test
    public void testGenerateAiobThirtyDayRankingPageSqlRobotType() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.ROBOT);
        request.setRobotType(AiobRobotTypeEnum.FLEX_CANVAS);
        request.setSortField(AiobSortFieldEnum.ALARM_DAYS);
        request.setSortOrder(SortTypeEnum.DESC);
        request.setPageSize(15);

        String tableName = "aiob_conversation_session_202";
        String tenantId = "tenant_303";

        String sql = ORMUtils.generateAiobThirtyDayRankingPageSql(request, tableName, tenantId);

        assertNotNull(sql);
        assertTrue(sql.contains("`robotId`"));
        assertTrue(sql.contains("`robotScene`"));
        assertTrue(sql.contains("order by `alarmDays` desc"));
    }

    @Test
    public void testGenerateAiobThirtyDayRankingPageSqlNoSort() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.LINE);
        request.setPageSize(5);
        String tableName = "aiob_conversation_session_404";
        String tenantId = "tenant_505";

        String sql = ORMUtils.generateAiobThirtyDayRankingPageSql(request, tableName, tenantId);

        assertNotNull(sql);
        assertFalse(sql.contains("ORDER BY"));
    }

    @Test
    public void testGenerateAiobThirtyDayRankingPageSqlAllConditions() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.TASK);
        request.setSource(AiobLineSourceEnum.ALL);
        request.setTaskStatus(AiobTaskStatusEnum.COMPLETED);
        request.setLineStatus(AiobLineStatusEnum.ALL);
        request.setRobotType(AiobRobotTypeEnum.QUICK_SCENES);
        request.setSortField(AiobSortFieldEnum.PLATFORM_REASON_RATE);
        request.setSortOrder(SortTypeEnum.DESC);
        request.setPageSize(8);

        String tableName = "aiob_conversation_session_606";
        String tenantId = "tenant_707";

        String sql = ORMUtils.generateAiobThirtyDayRankingPageSql(request, tableName, tenantId);

        assertNotNull(sql);
        assertTrue(sql.contains("`didOwner`"));
        assertTrue(sql.contains("`taskStatus`"));
        assertTrue(sql.contains("`lineStatus`"));
        assertTrue(sql.contains("`robotScene`"));
        assertTrue(sql.contains("order by"));
    }

    @Test
    public void testGenerateAiobThirtyDayRankingCountSqlLineType() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.LINE);
        request.setSource(AiobLineSourceEnum.CUSTOMER);
        request.setLineStatus(AiobLineStatusEnum.ENABLED);
        request.setEndTime(new DateTime(1752119818159L));
        request.setStartTime(DateUtil.offsetDay(request.getEndTime(), -30));
        String tableName = "aiob_session_table";
        String tenantId = "12345";

        String sql = ORMUtils.generateAiobThirtyDayRankingCountSql(request, tableName, tenantId);
    }

    @Test
    public void testGenerateAiobThirtyDayRankingCountSqlTaskType() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.TASK);
        request.setTaskStatus(AiobTaskStatusEnum.RUNNING);
        request.setEndTime(new DateTime(1752119818159L));
        request.setStartTime(DateUtil.offsetDay(request.getEndTime(), -30));
        String tableName = "aiob_session_table";
        String tenantId = "12345";

        String sql = ORMUtils.generateAiobThirtyDayRankingCountSql(request, tableName, tenantId);
    }

    @Test
    public void testGenerateAiobThirtyDayRankingCountSqlRobotType() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.ROBOT);
        request.setRobotType(AiobRobotTypeEnum.FLEX_CANVAS);
        request.setEndTime(new DateTime(1752119818159L));
        request.setStartTime(DateUtil.offsetDay(request.getEndTime(), -30));
        String tableName = "aiob_session_table";
        String tenantId = "12345";

        String sql = ORMUtils.generateAiobThirtyDayRankingCountSql(request, tableName, tenantId);
    }

    @Test
    public void testGenerateAiobThirtyDayRankingCountSqlNoConditions() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setEndTime(new DateTime(1752119818159L));
        request.setStartTime(DateUtil.offsetDay(request.getEndTime(), -30));
        request.setType(AlertConfigTypeEnum.LINE);

        String tableName = "aiob_session_table";
        String tenantId = "12345";

        String sql = ORMUtils.generateAiobThirtyDayRankingCountSql(request, tableName, tenantId);
    }

    @Test
    public void testGenerateAiobThirtyDayRankingCountSqlAllConditions() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.TASK);
        request.setSource(AiobLineSourceEnum.PLATFORM);
        request.setLineStatus(AiobLineStatusEnum.ENABLED);
        request.setTaskStatus(AiobTaskStatusEnum.RUNNING);
        request.setRobotType(AiobRobotTypeEnum.QUICK_SCENES);
        request.setEndTime(new DateTime(1752119818159L));
        request.setStartTime(DateUtil.offsetDay(request.getEndTime(), -30));
        String tableName = "aiob_session_table";
        String tenantId = "12345";

        String sql = ORMUtils.generateAiobThirtyDayRankingCountSql(request, tableName, tenantId);
    }


    @Test
    public void testGenerateQueryGetAiobConnectionRateTrendSql_Hour24() {
        ConnectionRateTrendDetailRequest request = new ConnectionRateTrendDetailRequest();
        request.setLineNum("12345");
        request.setTaskId("task123");
        request.setRobotId("robot456");
        request.setTimeType(AlertTimeTypeEnum.HOUR_24.getValue());

        String tenantId = "tenant_123";
        Timestamp timestamp = Timestamp.valueOf("2023-10-15 12:30:00");
        Date now = new Date(timestamp.getTime());
        String sql = ORMUtils.generateQueryGetAiobConnectionRateTrendSql(request, tenantId, now, onlineTime);

        String expected = "select DATE_FORMAT(createTime, '%Y-%m-%d %H:00:00') AS time, count(*) as `totalCalls`, " +
                "round(((sum(case when sipCode = 200 then 1 else 0 end) * 1E2) / count(*)), 2) as `connectedCallsRate` " +
                "from aiob_conversation_session_service_tenant_123 " +
                "where (createTime >= '2023-10-14 12:00:00' " +
                "and `callerNum` = '12345' and `taskId` = 'task123' and `robotId` = 'robot456') " +
                "group by time";
    }

    @Test
    public void testGenerateQueryGetAiobConnectionRateTrendSql_Day30() {
        ConnectionRateTrendDetailRequest request = new ConnectionRateTrendDetailRequest();
        request.setLineNum("67890");
        request.setTaskId("task456");
        request.setRobotId("robot789");
        request.setTimeType(AlertTimeTypeEnum.DAY_30.getValue());

        String tenantId = "tenant_456";
        Timestamp timestamp = Timestamp.valueOf("2023-10-15 12:20:00");
        Date now = new Date(timestamp.getTime());
        String sql = ORMUtils.generateQueryGetAiobConnectionRateTrendSql(request, tenantId, now, onlineTime);

        String expected = "select DATE_FORMAT(createTime, '%Y-%m-%d') AS time, count(*) as `totalCalls`, " +
                "round(((sum(case when sipCode = 200 then 1 else 0 end) * 1E2) / count(*)), 2) as `connectedCallsRate` " +
                "from aiob_conversation_session_service_tenant_456 " +
                "where (createTime >= '2023-09-16 00:00:00' " +
                "and `callerNum` = '67890' and `taskId` = 'task456' and `robotId` = 'robot789') " +
                "group by time";
    }

    @Test
    public void testGenerateQueryGetAiobConnectionRateTrendSql_EmptyFields() {
        ConnectionRateTrendDetailRequest request = new ConnectionRateTrendDetailRequest();
        request.setTimeType(AlertTimeTypeEnum.HOUR_24.getValue());

        String tenantId = "tenant_789";
        Timestamp timestamp = Timestamp.valueOf("2023-10-15 12:30:00");
        Date now = new Date(timestamp.getTime());
        String sql = ORMUtils.generateQueryGetAiobConnectionRateTrendSql(request, tenantId, now, onlineTime);

        String expected = "select DATE_FORMAT(createTime, '%Y-%m-%d %H:00:00') AS time, count(*) as `totalCalls`, " +
                "round(((sum(case when sipCode = 200 then 1 else 0 end) * 1E2) / count(*)), 2) as `connectedCallsRate` " +
                "from aiob_conversation_session_service_tenant_789 " +
                "where createTime >= '2023-10-14 12:00:00' " +
                "group by time";
    }

    @Test
    public void testGenerateQueryGetRejectRatioReasonsSql_WithTypeAndId() {
        CallCoreMetricsRequest request = new CallCoreMetricsRequest();
        request.setId("123");
        request.setType("TASK");
        String tenantId = "test_tenant";
        AiobFailTypeEnum failType = AiobFailTypeEnum.CALLED_UP;

        Timestamp timestamp = Timestamp.valueOf("2025-07-07 12:00:00");
        Date now = new Date(timestamp.getTime());
        String sql = ORMUtils.generateQueryGetRejectRatioReasonsSql(request, tenantId, failType, now, onlineTime);

        String expected = "select `dicName` as `reason`, count(*) as `count`, " +
                "round(((cast(count(*) as decimal) / cast(sum(count(*)) over () as decimal)) * 1E2), 2) as `ratio` " +
                "from aiob_conversation_session_service_test_tenant " +
                "where (`taskId` = '123' and createTime >= '2025-06-08 00:00:00'" +
                " and `dicCategory` = '被叫原因未接通') " +
                "group by `dicName`";
    }

    @Test
    public void testGenerateQueryGetRejectRatioReasonsSql_WithoutTypeAndId() {
        CallCoreMetricsRequest request = new CallCoreMetricsRequest();
        String tenantId = "test_tenant";
        AiobFailTypeEnum failType = AiobFailTypeEnum.PLATFORM_RULE;

        Timestamp timestamp = Timestamp.valueOf("2025-07-07 12:00:00");
        Date now = new Date(timestamp.getTime());
        String sql = ORMUtils.generateQueryGetRejectRatioReasonsSql(request, tenantId, failType, now, onlineTime);

        String expected = "select `dicName` as `reason`, count(*) as `count`, " +
                "round(((cast(count(*) as decimal) / cast(sum(count(*)) over () as decimal)) * 1E2), 2) as `ratio` " +
                "from aiob_conversation_session_service_test_tenant " +
                "where (createTime >= '2025-06-08 00:00:00'" +
                " and `dicCategory` = '平台规则限制未接通') " +
                "group by `dicName`";
    }

    @Test
    public void testGenerateQueryGetRejectRatioReasonsSql_WithDifferentFailType() {
        CallCoreMetricsRequest request = new CallCoreMetricsRequest();
        request.setId("456");
        request.setType("ROBOT");
        String tenantId = "test_tenant";
        AiobFailTypeEnum failType = AiobFailTypeEnum.LINE;

        Timestamp timestamp = Timestamp.valueOf("2025-07-07 12:00:00");
        Date now = new Date(timestamp.getTime());
        String sql = ORMUtils.generateQueryGetRejectRatioReasonsSql(request, tenantId, failType, now, onlineTime);

        String expected = "select `dicName` as `reason`, count(*) as `count`, " +
                "round(((cast(count(*) as decimal) / cast(sum(count(*)) over () as decimal)) * 1E2), 2) as `ratio` " +
                "from aiob_conversation_session_service_test_tenant " +
                "where (`robotId` = '456' and createTime >= '2025-06-08 00:00:00'" +
                " and `dicCategory` = '号线原因未接通') " +
                "group by `dicName`";
    }

    @Test
    public void testGenerateCountSessionTaskIdsWithSpecialCharacters() {
        String tableName = "aiob_session_task_123!@#";
        String expectedSql = "select count(`taskId`) from `aiob_session_task_123!@#` group by `taskId` order by `taskId`";
        String actualSql = ORMUtils.generateCountSessionTaskIds(tableName);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateCountSessionTaskIds() {
        String tableName = "aiob_session_task";
        String expectedSql = "select count(`taskId`) from `aiob_session_task` group by `taskId` order by `taskId`";
        String actualSql = ORMUtils.generateCountSessionTaskIds(tableName);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateCountSessionTaskIdsWithEmptyTableName() {
        String tableName = "";
        String expectedSql = "select count(`taskId`) from `` group by `taskId` order by `taskId`";
        String actualSql = ORMUtils.generateCountSessionTaskIds(tableName);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateSessionTaskInfoUpdateWithTaskName() {
        String tableName = "task_table";
        Long taskId = 12345L;
        AiobTaskStatusEnum status = AiobTaskStatusEnum.RUNNING;
        String taskName = "test_task";
    
        String expectedSql = "update `task_table` " +
                "set `taskStatus` = 2, `updateTime` = '" + new DateTime() + "', `taskName` = 'test_task' " +
                "where `taskId` = 12345";
    
        String actualSql = ORMUtils.generateSessionTaskInfoUpdate(tableName, taskId, status, taskName);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateSessionTaskInfoUpdateWithoutTaskName() {
        String tableName = "task_table";
        Long taskId = 12345L;
        AiobTaskStatusEnum status = AiobTaskStatusEnum.COMPLETED;
        String taskName = null;
    
        String expectedSql = "update `task_table` " +
                "set `taskStatus` = 4, `updateTime` = '" + new DateTime() + "' " +
                "where `taskId` = 12345";
    
        String actualSql = ORMUtils.generateSessionTaskInfoUpdate(tableName, taskId, status, taskName);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateSessionTaskInfoUpdateEmptyTaskName() {
        String tableName = "task_table";
        Long taskId = 12345L;
        AiobTaskStatusEnum status = AiobTaskStatusEnum.PAUSED;
        String taskName = "";
    
        String expectedSql = "update `task_table` " +
                "set `taskStatus` = 3, `updateTime` = '" + new DateTime() + "' " +
                "where `taskId` = 12345";
    
        String actualSql = ORMUtils.generateSessionTaskInfoUpdate(tableName, taskId, status, taskName);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateSessionDetailQueryForTaskType() {
        AlertConfig config = new AlertConfig();
        config.setTenantId("123456789");
        config.setConfigTarget("task123");
        
        String expectedSql = "select `taskName`, `taskStatus`, `robotName` " +
                "from `aiob_conversation_session_service_123456789` " +
                "where `taskId` = 'task123' " +
                "limit 1";
        
        String actualSql = ORMUtils.generateSessionDetailQuery(config, AlertConfigTypeEnum.TASK);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateSessionDetailQueryForRobotType() {
        AlertConfig config = new AlertConfig();
        config.setTenantId("987654321");
        config.setConfigTarget("robot456");
        
        String expectedSql = "select `taskName`, `taskStatus`, `robotName` " +
                "from `aiob_conversation_session_service_987654321` " +
                "where `robotId` = 'robot456' " +
                "limit 1";
        
        String actualSql = ORMUtils.generateSessionDetailQuery(config, AlertConfigTypeEnum.ROBOT);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateSessionDetailQueryForLineType() {
        AlertConfig config = new AlertConfig();
        config.setTenantId("555555555");
        config.setConfigTarget("line789");
        
        String expectedSql = "select `taskName`, `taskStatus`, `robotName` " +
                "from `aiob_conversation_session_service_555555555` " +
                "where `robotId` = 'line789' " +
                "limit 1";
        
        String actualSql = ORMUtils.generateSessionDetailQuery(config, AlertConfigTypeEnum.LINE);
        assertEquals(expectedSql, actualSql);
    }

    @Test
    public void testGenerateQueryAiobRobotOrTaskOrLineListSqlRobotType() {
        CallCoreMetricsRequest request = new CallCoreMetricsRequest();
        request.setDateType(DateLineTypeEnum.HOUR);
        request.setId("robot123");
        request.setType("ROBOT");
    
        String tenantId = "test_tenant";
        String expectedTableName = "aiob_conversation_session_test_tenant";
    
        String sql = ORMUtils.generateQueryAiobRobotOrTaskOrLineListSql(request, tenantId, DialMetricQueryTypeEnum.ROBOT, onlineTime);
    
        String expectedSql = "select distinct `robotId`, `robotName` from " + expectedTableName + 
                " where `robotId` = 'robot123' and `createTime` >= ? and `createTime` < ?";
    }

    @Test
    public void testGenerateQueryAiobRobotOrTaskOrLineListSqlTaskType() {
        CallCoreMetricsRequest request = new CallCoreMetricsRequest();
        request.setDateType(DateLineTypeEnum.DAY);
        request.setId("task456");
        request.setType("TASK");
    
        String tenantId = "test_tenant";
        String expectedTableName = "aiob_conversation_session_test_tenant";
    
        String sql = ORMUtils.generateQueryAiobRobotOrTaskOrLineListSql(request, tenantId, DialMetricQueryTypeEnum.TASK, onlineTime);
    
        String expectedSql = "select distinct `taskId`, `taskName` from " + expectedTableName + 
                " where `taskId` = 'task456' and `createTime` >= ? and `createTime` < ?";
    }

    @Test
    public void testGenerateQueryAiobRobotOrTaskOrLineListSqlLineType() {
        CallCoreMetricsRequest request = new CallCoreMetricsRequest();
        request.setDateType(DateLineTypeEnum.HOUR);
        request.setId("line789");
        request.setType("LINE");
    
        String tenantId = "test_tenant";
        String expectedTableName = "aiob_conversation_session_test_tenant";
    
        String sql = ORMUtils.generateQueryAiobRobotOrTaskOrLineListSql(request, tenantId, DialMetricQueryTypeEnum.LINE, onlineTime);
    
        String expectedSql = "select distinct `callerNum` from " + expectedTableName + 
                " where `callerNum` = 'line789' and `createTime` >= ? and `createTime` < ?";
    }

    @Test
    public void testGenerateQueryAiobRobotOrTaskOrLineListSqlNoType() {
        CallCoreMetricsRequest request = new CallCoreMetricsRequest();
        request.setDateType(DateLineTypeEnum.DAY);
        request.setId(null);
        request.setType(null);
    
        String tenantId = "test_tenant";
        String expectedTableName = "aiob_conversation_session_test_tenant";
    
        String sql = ORMUtils.generateQueryAiobRobotOrTaskOrLineListSql(request, tenantId, DialMetricQueryTypeEnum.LINE, onlineTime);
    
        String expectedSql = "select distinct `callerNum` from " + expectedTableName + 
                " where `createTime` >= ? and `createTime` < ?";
    }

    @Test
    public void testGenerateSessionSimpleDetailSqlTaskType() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.TASK);
        request.setTaskStatus(AiobTaskStatusEnum.RUNNING);
    
        List<String> configTargets = List.of("task1", "task2");
        String tenantId = "test_tenant";
        String tableName = "aiob_conversation_session_service_test_tenant";
    
        String sql = ORMUtils.generateSessionSimpleDetailSql(configTargets, tenantId, request);
    
        String expectedSql = "select `taskId` as `id`, `taskName`, `taskStatus` " +
                "from (select `taskId`, `taskName`, `taskStatus`, ROW_NUMBER() OVER (PARTITION BY `taskId` ORDER BY `deepsight_datetime` DESC) AS rn " +
                "from `" + tableName + "` " +
                "where `taskStatus` = 2 and `taskId` in ('task1', 'task2') and `taskName` is not null and `taskStatus` is not null) " +
                "where `rn` = 1";
    
    }

    @Test
    public void testGenerateSessionSimpleDetailSqlRobotType() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.ROBOT);
        request.setRobotType(AiobRobotTypeEnum.FLEX_CANVAS);
    
        List<String> configTargets = List.of("robot1", "robot2");
        String tenantId = "test_tenant";
        String tableName = "aiob_conversation_session_service_test_tenant";
    
        String sql = ORMUtils.generateSessionSimpleDetailSql(configTargets, tenantId, request);
    
        String expectedSql = "select `robotId` as `id`, `robotScene` " +
                "from (select `robotId`, `robotScene`, ROW_NUMBER() OVER (PARTITION BY `robotId` ORDER BY `deepsight_datetime` DESC) AS rn " +
                "from `" + tableName + "` " +
                "where `robotScene` = 6 and `robotId` in ('robot1', 'robot2') and `robotScene` is not null) " +
                "where `rn` = 1";
    
    }

    @Test
    public void testGenerateSessionSimpleDetailSqlNoConditions() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.ROBOT);
    
        List<String> configTargets = List.of("robot1", "robot2");
        String tenantId = "test_tenant";
        String tableName = "aiob_conversation_session_service_test_tenant";
    
        String sql = ORMUtils.generateSessionSimpleDetailSql(configTargets, tenantId, request);
    
        String expectedSql = "select `robotId` as `id`, `robotScene` " +
                "from (select `robotId`, `robotScene`, ROW_NUMBER() OVER (PARTITION BY `robotId` ORDER BY `deepsight_datetime` DESC) AS rn " +
                "from `" + tableName + "` " +
                "where `robotId` in ('robot1', 'robot2') and `robotScene` is not null) " +
                "where `rn` = 1";
    
    }

    @Test
    public void testGenerateSessionSimpleDetailSqlLineType() {
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setType(AlertConfigTypeEnum.LINE);
        request.setSource(AiobLineSourceEnum.CUSTOMER);
        request.setLineStatus(AiobLineStatusEnum.ENABLED);
    
        List<String> configTargets = List.of("123", "456");
        String tenantId = "test_tenant";
        String tableName = "aiob_conversation_session_service_test_tenant";
    
        String sql = ORMUtils.generateSessionSimpleDetailSql(configTargets, tenantId, request);
    
        String expectedSql = "select `callerNum` as `id`, `didOwner`, `lineStatus` " +
                "from (select `callerNum`, `didOwner`, `lineStatus`, ROW_NUMBER() OVER (PARTITION BY `callerNum` ORDER BY `deepsight_datetime` DESC) AS rn " +
                "from `" + tableName + "` " +
                "where `didOwner` = 1 and `lineStatus` = 'ENABLED' and `callerNum` in (123, 456) and `didOwner` is not null and `lineStatus` is not null) " +
                "where `rn` = 1";
    
    }
}