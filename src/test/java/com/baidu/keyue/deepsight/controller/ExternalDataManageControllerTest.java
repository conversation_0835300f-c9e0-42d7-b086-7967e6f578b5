package com.baidu.keyue.deepsight.controller;

import static org.hamcrest.Matchers.hasSize;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
import org.mockito.junit.jupiter.MockitoSettings;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.quality.Strictness;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Arrays;
import java.util.List;
@MockitoSettings(strictness = Strictness.LENIENT)
public class ExternalDataManageControllerTest{

    @Mock
    private DataTableManageService dataTableManageService;

    @InjectMocks
    private ExternalDataManageController externalDataManageController;

    private MockMvc mockMvc;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(externalDataManageController).build();
    }

    // testGetFilterFields 用于测试 getFilterFields
    // generated by Comate
    @Test
    public void testGetFilterFields() throws Exception {
        // Mock the service layer
        DatasetPropertiesResult result1 = new DatasetPropertiesResult();
        DatasetPropertiesResult result2 = new DatasetPropertiesResult();
        List<DatasetPropertiesResult> mockResults = Arrays.asList(result1, result2);
        when(dataTableManageService.getTableProperties()).thenReturn(mockResults);
    
        // Perform the GET request and verify the response
        mockMvc.perform(get("/external/deepsight/v1/datatable/field/filterList"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data", hasSize(2)));
    }
    // testGetFilterFields1 用于测试 getFilterFields
    // generated by Comate
    @Test
    void testGetFilterFields1() {
        MockitoAnnotations.initMocks(this);

        // Arrange
        DatasetPropertiesResult result1 = new DatasetPropertiesResult();
        DatasetPropertiesResult result2 = new DatasetPropertiesResult();
        List<DatasetPropertiesResult> mockResults = Arrays.asList(result1, result2);

        when(dataTableManageService.getTableProperties()).thenReturn(mockResults);

        // Act
        BaseResponse<List<DatasetPropertiesResult>> response = externalDataManageController.getFilterFields();

        // Assert
        assertEquals(mockResults, response.getData());
        verify(dataTableManageService, times(1)).getTableProperties();
    }

}