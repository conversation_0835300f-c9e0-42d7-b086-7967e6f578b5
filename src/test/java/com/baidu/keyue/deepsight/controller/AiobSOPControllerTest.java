package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.models.sop.SOPNodePredictRequest;
import com.baidu.keyue.deepsight.models.sop.SOPProgress;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class AiobSOPControllerTest{

    @InjectMocks
    private AiobSOPController aiobSOPController;

    @Mock
    private AiobSOPService aiobSOPService;

    private SOPNodePredictRequest request;

    private SOPProgress response;

    private UserAuthInfo userAuthInfo;

    @BeforeEach
    public void setUp() {
        request = new SOPNodePredictRequest("rule", "taskId", "ver");
        response = new SOPProgress();
        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(123L); // 修改为Long类型
    }

}