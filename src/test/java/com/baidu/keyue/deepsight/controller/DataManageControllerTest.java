package com.baidu.keyue.deepsight.controller;

import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@WebMvcTest(DataManageController.class)
public class DataManageControllerTest{

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private DataTableManageService dataTableManageService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // testGetVisibleFieldsSuccess 用于测试 getVisibleFields
    // generated by Comate
    @Test
    public void testGetVisibleFieldsSuccess() throws Exception {
        List<VisibleFieldResponse> visibleFields = Arrays.asList(new VisibleFieldResponse(), new VisibleFieldResponse());
        when(dataTableManageService.getVisibleFields(Mockito.anyLong(), true)).thenReturn(visibleFields);
    
        mockMvc.perform(get("/datatable/field/visibleList")
                .param("dataTableId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(ErrorCode.SUCCESS.getCode())))
                .andExpect(jsonPath("$.data.length()", is(2)));
    }

    // testGetVisibleFieldsGeneralException 用于测试 getVisibleFields
    // generated by Comate
    @Test
    public void testGetVisibleFieldsGeneralException() throws Exception {
        when(dataTableManageService.getVisibleFields(Mockito.anyLong(), true)).thenThrow(new RuntimeException("General error"));
    
        mockMvc.perform(get("/datatable/field/visibleList")
                .param("dataTableId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(ErrorCode.INTERNAL_ERROR.getCode())))
                .andExpect(jsonPath("$.message", is(ErrorCode.INTERNAL_ERROR.getMessage())));
    }

    // testGetFilterFieldsGeneralException 用于测试 getFilterFields
    // generated by Comate
    @Test
    public void testGetFilterFieldsGeneralException() throws Exception {
        when(dataTableManageService.getTableContentFilterProperties(1L, true)).thenThrow(new RuntimeException("General error"));
    
        mockMvc.perform(get("/datatable/field/filterList")
                .param("dataTableId", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code", is(ErrorCode.INTERNAL_ERROR.getCode())))
                .andExpect(jsonPath("$.message", is(ErrorCode.INTERNAL_ERROR.getMessage())));
    }

}