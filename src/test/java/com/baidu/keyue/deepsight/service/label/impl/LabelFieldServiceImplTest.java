package com.baidu.keyue.deepsight.service.label.impl;

import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.mysqldb.entity.LabelField;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelFieldMapper;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class LabelFieldServiceImplTest{

    @Mock
    private ExtendLabelFieldMapper labelFieldMapper;

    @InjectMocks
    private LabelFieldServiceImpl labelFieldService;

    @Test
    void deleteFieldByIdShouldHandleZeroId() {
        // Arrange
        long testId = 0L;
        LabelField expectedField = new LabelField();
        expectedField.setId(testId);
        expectedField.setDel(true);
    
        // Act
        labelFieldService.deleteFieldById(testId);
    
        // Assert
        verify(labelFieldMapper, times(1)).updateByPrimaryKeySelective(argThat(actualField -> 
            actualField.getId().equals(testId) && 
            Boolean.TRUE.equals(actualField.getDel())
        ));
    }

    @Test
    void deleteFieldByIdShouldHandleNegativeId() {
        // Arrange
        long testId = -1L;
        LabelField expectedField = new LabelField();
        expectedField.setId(testId);
        expectedField.setDel(true);
    
        // Act
        labelFieldService.deleteFieldById(testId);
    
        // Assert
        verify(labelFieldMapper, times(1)).updateByPrimaryKeySelective(argThat(actualField -> 
            actualField.getId().equals(testId) && 
            Boolean.TRUE.equals(actualField.getDel())
        ));
    }

    @Test
    void deleteFieldByIdShouldUpdateFieldWithDelFlagTrue() {
        // Arrange
        long testId = 123L;
        LabelField expectedField = new LabelField();
        expectedField.setId(testId);
        expectedField.setDel(true);
    
        // Act
        labelFieldService.deleteFieldById(testId);
    
        // Assert
        verify(labelFieldMapper, times(1)).updateByPrimaryKeySelective(argThat(actualField -> 
            actualField.getId().equals(testId) && 
            Boolean.TRUE.equals(actualField.getDel())
        ));
    }

}