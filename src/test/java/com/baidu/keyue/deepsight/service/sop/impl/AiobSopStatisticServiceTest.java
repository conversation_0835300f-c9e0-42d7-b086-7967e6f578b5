package com.baidu.keyue.deepsight.service.sop.impl;

import com.baidu.keyue.deepsight.models.sop.SOPAnalyseProgressRequest;
import com.baidu.keyue.deepsight.models.sop.SOPAnalyseProgressResponse;
import com.baidu.keyue.deepsight.models.sop.SOPMetaInternal;
import com.baidu.keyue.deepsight.models.sop.SopEdge;
import com.baidu.keyue.deepsight.models.sop.SopFlexibleWholeRequest;
import com.baidu.keyue.deepsight.models.sop.SopFlexibleWholeResponse;
import com.baidu.keyue.deepsight.models.sop.SopIntent;
import com.baidu.keyue.deepsight.models.sop.SopNode;
import com.baidu.keyue.deepsight.models.sop.SopNodeDailyDistribute;
import com.baidu.keyue.deepsight.models.sop.SopNodeDetailRequest;
import com.baidu.keyue.deepsight.models.sop.SopNodeDetailResponse;
import com.baidu.keyue.deepsight.models.sop.SopNodeWithHangup;
import com.baidu.keyue.deepsight.models.sop.SopSankeyMetaResponse;
import com.baidu.keyue.deepsight.models.sop.SopSankeyStep;
import com.baidu.keyue.deepsight.models.sop.SopSankeyWholeRequest;
import com.baidu.keyue.deepsight.models.sop.SopSankeyWholeResponse;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.keyue.deepsight.utils.AESUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.isNull;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class AiobSopStatisticServiceTest {
    private SopSankeyWholeRequest request1;

    @Mock
    private AiobSOPService aiobSOPService;
    @Mock
    private AiobSopMetricService aiobSopMetricService;

    @InjectMocks
    private AiobSopStatisticService aiobSopStatisticService;

    private String tenantId;

    private SopFlexibleWholeRequest request;

    private List<SopNode> mockNodes;

    private List<SopEdge> mockEdges;

    @BeforeEach
    void setUp() {
        tenantId = "testTenant";
        request = new SopFlexibleWholeRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");
        request.setTopicId("topic1");
        request.setStartTime(new Date());
        request.setEndTime(new Date());

        mockNodes = List.of(new SopNode("node1", 1L, 1L, 0, 0, ""),
                new SopNode("node2", 1L, 1L, 0, 0, ""));
        mockEdges = List.of(new SopEdge("node1", "node2", 1L));

        request1 = new SopSankeyWholeRequest();
        request1.setTaskId("task1");
        request1.setRobotId("robot1");
        request1.setRobotVer("1.0");
        request1.setTopicId("topic1");
        request1.setStartTime(new Date());
        request1.setEndTime(new Date());
    }


    @Test
    void percentShouldReturnCorrectValueWhenBaseNotZero() {
        // Test normal case
        assertEquals(50, aiobSopStatisticService.percent(1, 2));
        assertEquals(33, aiobSopStatisticService.percent(1, 3));
        assertEquals(100, aiobSopStatisticService.percent(3, 3));

        // Test rounding
        assertEquals(67, aiobSopStatisticService.percent(2, 3));
    }

    @Test
    void percentShouldReturnZeroWhenBaseIsZero() {
        assertEquals(0, aiobSopStatisticService.percent(100, 0));
    }

    @Test
    void percentShouldHandleLargeNumbers() {
        assertEquals(10, aiobSopStatisticService.percent(1000000000L, 10000000000L));
    }

    @Test
    void percentShouldReturnZeroWhenNumIsZero() {
        assertEquals(0, aiobSopStatisticService.percent(0, 100));
    }


    @Test
    void intentTagCryptShouldHandleNullInput() {
        try (MockedStatic<AESUtils> mockedAesUtils = mockStatic(AESUtils.class)) {
            mockedAesUtils.when(() -> AESUtils.encrypt(isNull(), anyString()))
                    .thenReturn(null);

            String result = aiobSopStatisticService.intentTagCrypt(null);

            assertNull(result);
            mockedAesUtils.verify(() -> AESUtils.encrypt(isNull(), anyString()));
        }
    }

    @Test
    void intentTagCryptShouldReturnEncryptedValue() {
        try (MockedStatic<AESUtils> mockedAesUtils = mockStatic(AESUtils.class)) {
            String testTag = "testTag";
            String expectedEncrypted = "encryptedValue";

            mockedAesUtils.when(() -> AESUtils.encrypt(eq(testTag), anyString()))
                    .thenReturn(expectedEncrypted);

            String result = aiobSopStatisticService.intentTagCrypt(testTag);

            assertEquals(expectedEncrypted, result);
            mockedAesUtils.verify(() -> AESUtils.encrypt(eq(testTag), anyString()));
        }
    }

    @Test
    void intentTagDecryptShouldReturnNullWhenInputIsNull() {
        try (MockedStatic<AESUtils> mockedAESUtils = mockStatic(AESUtils.class)) {
            mockedAESUtils.when(() -> AESUtils.decrypt(null, "ea437d38025246b6"))
                    .thenReturn(null);

            assertNull(aiobSopStatisticService.intentTagDecrypt(null));

            mockedAESUtils.verify(() ->
                    AESUtils.decrypt(null, "ea437d38025246b6"));
        }
    }

    @Test
    void intentTagDecryptShouldReturnEmptyStringWhenInputIsEmpty() {
        try (MockedStatic<AESUtils> mockedAESUtils = mockStatic(AESUtils.class)) {
            mockedAESUtils.when(() -> AESUtils.decrypt("", "ea437d38025246b6"))
                    .thenReturn("");

            assertEquals("", aiobSopStatisticService.intentTagDecrypt(""));

            mockedAESUtils.verify(() ->
                    AESUtils.decrypt("", "ea437d38025246b6"));
        }
    }

    @Test
    void intentTagDecryptShouldReturnDecryptedTextWhenInputIsValid() {
        try (MockedStatic<AESUtils> mockedAESUtils = mockStatic(AESUtils.class)) {
            String encryptedText = "encryptedText";
            String decryptedText = "decryptedText";

            mockedAESUtils.when(() -> AESUtils.decrypt(encryptedText, "ea437d38025246b6"))
                    .thenReturn(decryptedText);

            String result = aiobSopStatisticService.intentTagDecrypt(encryptedText);
            assertEquals(decryptedText, result);

            mockedAESUtils.verify(() ->
                    AESUtils.decrypt(encryptedText, "ea437d38025246b6"));
        }
    }

    @Test
    void calculateNodeRatiosShouldSetCorrectPercentages() {
        // Arrange
        SopNode node1 = new SopNode();
        node1.setNodeId("node1");
        node1.setUv(40L);
        node1.setPv(40L);

        SopNode node2 = new SopNode();
        node2.setNodeId("node2");
        node2.setUv(60L);
        node2.setPv(60L);

        SopNode node3 = new SopNode();
        node3.setNodeId("node3");
        node3.setUv(30L);
        node3.setPv(30L);

        SopNode node4 = new SopNode();
        node4.setNodeId("node4");
        node4.setUv(30L);
        node4.setPv(30L);

        SopNode node5 = new SopNode();
        node5.setNodeId("node5");
        node5.setUv(40L);
        node5.setPv(40L);

        List<SopNode> nodes = List.of(node1, node2, node3, node4, node5);
        List<SopEdge> edges = List.of(
                new SopEdge("node1", "node3", 0L),
                new SopEdge("node1", "node4", 0L),
                new SopEdge("node2", "node3", 0L),
                new SopEdge("node2", "node4", 0L),
                new SopEdge("node2", "node5", 0L)
        );

        // Act
        aiobSopStatisticService.calculateNodeRatios(nodes, edges);

        // Assert
        assertEquals(30, node3.getUvPercent());
        assertEquals(30, node4.getUvPercent());
        assertEquals(67, node5.getUvPercent());
    }

    @Test
    void calculateNodeRatiosShouldHandleEmptyEdgeList() {
        // Arrange
        SopNode node = new SopNode();
        node.setNodeId("node1");
        node.setUv(100L);
        node.setPv(200L);

        List<SopNode> nodes = List.of(node);
        List<SopEdge> edges = List.of();

        // Act
        aiobSopStatisticService.calculateNodeRatios(nodes, edges);

        // Assert
        assertEquals(0, node.getUvPercent());
        assertEquals(0, node.getPvPercent());
    }

    @Test
    void calculateNodeRatiosShouldHandleMissingFromNode() {
        // Arrange
        SopNode node2 = new SopNode();
        node2.setNodeId("node2");
        node2.setUv(50L);
        node2.setPv(100L);

        SopEdge edge = new SopEdge();
        edge.setFromNodeId("missingNode");
        edge.setEndNodeId("node2");

        List<SopNode> nodes = List.of(node2);
        List<SopEdge> edges = List.of(edge);

        // Act
        aiobSopStatisticService.calculateNodeRatios(nodes, edges);

        // Assert
        assertEquals(0, node2.getUvPercent());
        assertEquals(0, node2.getPvPercent());
    }

    @Test
    void calculateNodeRatiosShouldHandleZeroUvPv() {
        // Arrange
        SopNode node1 = new SopNode();
        node1.setNodeId("node1");
        node1.setUv(0L);
        node1.setPv(0L);

        SopNode node2 = new SopNode();
        node2.setNodeId("node2");
        node2.setUv(50L);
        node2.setPv(100L);

        SopEdge edge = new SopEdge();
        edge.setFromNodeId("node1");
        edge.setEndNodeId("node2");

        List<SopNode> nodes = List.of(node1, node2);
        List<SopEdge> edges = List.of(edge);

        // Act
        aiobSopStatisticService.calculateNodeRatios(nodes, edges);

        // Assert
        assertEquals(0, node2.getUvPercent());
        assertEquals(0, node2.getPvPercent());
    }

    @Test
    void calculateNodeRatiosShouldHandleMultipleFromNodes() {
        // Arrange
        SopNode node1 = new SopNode();
        node1.setNodeId("node1");
        node1.setUv(100L);
        node1.setPv(200L);

        SopNode node2 = new SopNode();
        node2.setNodeId("node2");
        node2.setUv(50L);
        node2.setPv(100L);

        SopNode node3 = new SopNode();
        node3.setNodeId("node3");
        node3.setUv(150L);
        node3.setPv(300L);

        SopEdge edge1 = new SopEdge();
        edge1.setFromNodeId("node1");
        edge1.setEndNodeId("node3");

        SopEdge edge2 = new SopEdge();
        edge2.setFromNodeId("node2");
        edge2.setEndNodeId("node3");

        List<SopNode> nodes = List.of(node1, node2, node3);
        List<SopEdge> edges = List.of(edge1, edge2);

        // Act
        aiobSopStatisticService.calculateNodeRatios(nodes, edges);

        // Assert
        assertEquals(100, node3.getUvPercent()); // 150 / (100 + 50) * 100 = 100
        assertEquals(100, node3.getPvPercent()); // 300 / (200 + 100) * 100 = 100
    }

    @Test
    void nodeDetailStatisticsShouldReturnCorrectResponseWithPreviewNodes() {
        tenantId = "testTenant";
        SopNodeDetailRequest request = new SopNodeDetailRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");
        request.setTopicId("topic1");
        request.setStartTime(new Date());
        request.setEndTime(new Date());
        request.setCurrNodeId("node1");

        // Arrange
        List<String> previewNodeIds = List.of("node0");
        List<SopNode> previewNodes = List.of(new SopNode("node0", 100L, 50L, 0, 0, ""));
        List<SopNode> forwardNodes = List.of(new SopNode("node2", 30L, 15L, 0, 0, ""));
        List<SopEdge> previewToCurrEdges = List.of(new SopEdge("node0", "node1", 50L));
        Map<String, SopNode> dailyMetrics = Maps.newHashMap();
        dailyMetrics.put("2023-01-01", new SopNode("node1", 10L, 5L, 0, 0, ""));
        List<SopNodeDailyDistribute> dailyDistributes = List.of(
                new SopNodeDailyDistribute("2023-01-01", List.of(new SopNode("node2", 3L, 1L, 0, 0, "")))
        );

        when(aiobSopMetricService.getAllPreviewNodes(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(previewNodeIds);
        when(aiobSopMetricService.wholeNodeMetricWithIds(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(previewNodes);
        when(aiobSopMetricService.currNodeToForwardNodeEdgeMetric(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(forwardNodes);
        when(aiobSopMetricService.previewNodeToCurrNodeEdgeMetric(any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(previewToCurrEdges);
        when(aiobSopMetricService.dailyCurrNodeMetric(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(dailyMetrics);
        when(aiobSopMetricService.dailyCurrNodeToForwardNodeEdgeMetric(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(dailyDistributes);

        // Act
        SopNodeDetailResponse response = aiobSopStatisticService.nodeDetailStatistics(tenantId, request);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getNodes().size());
        assertEquals(1 + 1, response.getEdges().size());
        assertEquals(1, response.getDailyDistribute().size());
        assertEquals(100, response.getNodes().stream().filter(n -> n.getNodeId().equals("node0")).findFirst().get().getUvPercent());
    }

    @Test
    void nodeDetailStatisticsShouldHandleEmptyPreviewNodes() {
        tenantId = "testTenant";
        SopNodeDetailRequest request = new SopNodeDetailRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");
        request.setTopicId("topic1");
        request.setStartTime(new Date());
        request.setEndTime(new Date());
        request.setCurrNodeId("node1");

        // Arrange
        when(aiobSopMetricService.getAllPreviewNodes(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(List.of());
        when(aiobSopMetricService.wholeNodeMetricWithIds(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(List.of(new SopNode("node1", 100L, 50L, 0, 0, "")));
        when(aiobSopMetricService.currNodeToForwardNodeEdgeMetric(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(List.of());
        when(aiobSopMetricService.previewNodeToCurrNodeEdgeMetric(any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(List.of());
        when(aiobSopMetricService.dailyCurrNodeMetric(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(Map.of());
        when(aiobSopMetricService.dailyCurrNodeToForwardNodeEdgeMetric(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(List.of());

        // Act
        SopNodeDetailResponse response = aiobSopStatisticService.nodeDetailStatistics(tenantId, request);

        // Assert
        assertNotNull(response);
        assertEquals(1, response.getNodes().size());
        assertEquals(100, response.getNodes().get(0).getUvPercent());
        assertEquals(100, response.getNodes().get(0).getPvPercent());
        assertTrue(response.getEdges().isEmpty());
        assertTrue(response.getDailyDistribute().isEmpty());
    }

    @Test
    void nodeDetailStatisticsShouldHandleEmptyForwardNodes() {
        tenantId = "testTenant";
        SopNodeDetailRequest request = new SopNodeDetailRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");
        request.setTopicId("topic1");
        request.setStartTime(new Date());
        request.setEndTime(new Date());
        request.setCurrNodeId("node1");

        // Arrange
        List<String> previewNodeIds = List.of("node0");
        List<SopNode> previewNodes = List.of(new SopNode("node0", 100L, 50L, 0, 0, ""),
                new SopNode("node1", 50L, 25L, 0, 0, ""));
        List<SopEdge> previewToCurrEdges = List.of(new SopEdge("node0", "node1", 25L));

        when(aiobSopMetricService.getAllPreviewNodes(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(previewNodeIds);
        when(aiobSopMetricService.wholeNodeMetricWithIds(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(previewNodes);
        when(aiobSopMetricService.currNodeToForwardNodeEdgeMetric(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(List.of());
        when(aiobSopMetricService.previewNodeToCurrNodeEdgeMetric(any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(previewToCurrEdges);
        when(aiobSopMetricService.dailyCurrNodeMetric(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(Map.of());
        when(aiobSopMetricService.dailyCurrNodeToForwardNodeEdgeMetric(any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(List.of());

        // Act
        SopNodeDetailResponse response = aiobSopStatisticService.nodeDetailStatistics(tenantId, request);

        // Assert
        assertNotNull(response);
        assertEquals(2, response.getNodes().size());
        assertEquals(1, response.getEdges().size());
        assertEquals(50, response.getNodes().stream().filter(n -> n.getNodeId().equals("node1")).findFirst().get().getUvPercent());
    }

    @Test
    void completePreviewNodeDetailShouldSkipNonPreviewNodes() {
        // Arrange
        AiobSopStatisticService service = new AiobSopStatisticService(null, null);
        List<SopNodeWithHangup> nodes = new ArrayList<>();
        nodes.add(new SopNodeWithHangup("node1", 10L, 20L, 0, 0, ""));
        nodes.add(new SopNodeWithHangup("node2", 30L, 40L, 0, 0, ""));
        List<String> previewNodeIds = List.of("node1");

        // Act
        service.completePreviewNodeDetail(nodes, previewNodeIds);

        // Assert
        SopNode node1 = nodes.get(0);
        assertEquals(100, node1.getUvPercent());
        assertEquals(100, node1.getPvPercent());

        SopNode node2 = nodes.get(1);
        assertEquals(0, node2.getUvPercent());
        assertEquals(0, node2.getPvPercent());
    }

    @Test
    void completePreviewNodeDetailShouldHandleEmptyPreviewNodes() {
        // Arrange
        AiobSopStatisticService service = new AiobSopStatisticService(null, null);
        List<SopNodeWithHangup> nodes = new ArrayList<>();
        nodes.add(new SopNodeWithHangup("node1", 10L, 20L, 0, 0, ""));
        List<String> previewNodeIds = List.of();

        // Act
        service.completePreviewNodeDetail(nodes, previewNodeIds);

        // Assert
        SopNode node1 = nodes.get(0);
        assertEquals(0, node1.getUvPercent());
        assertEquals(0, node1.getPvPercent());
    }

    @Test
    void completePreviewNodeDetailShouldHandleZeroValues() {
        // Arrange
        AiobSopStatisticService service = new AiobSopStatisticService(null, null);
        List<SopNodeWithHangup> nodes = new ArrayList<>();
        nodes.add(new SopNodeWithHangup("node1", 0L, 0L, 0, 0, ""));
        nodes.add(new SopNodeWithHangup("node2", 0L, 0L, 0, 0, ""));
        List<String> previewNodeIds = List.of("node1", "node2");

        // Act
        service.completePreviewNodeDetail(nodes, previewNodeIds);

        // Assert
        SopNode node1 = nodes.get(0);
        assertEquals(0, node1.getUvPercent());
        assertEquals(0, node1.getPvPercent());

        SopNode node2 = nodes.get(1);
        assertEquals(0, node2.getUvPercent());
        assertEquals(0, node2.getPvPercent());
    }

    @Test
    void completePreviewNodeDetailShouldCalculatePercentCorrectly() {
        // Arrange
        AiobSopStatisticService service = new AiobSopStatisticService(null, null);
        List<SopNodeWithHangup> nodes = new ArrayList<>();
        nodes.add(new SopNodeWithHangup("node1", 10L, 20L, 0, 0, ""));
        nodes.add(new SopNodeWithHangup("node2", 30L, 40L, 0, 0, ""));
        List<String> previewNodeIds = List.of("node1", "node2");

        // Act
        service.completePreviewNodeDetail(nodes, previewNodeIds);

        // Assert
        SopNode node1 = nodes.get(0);
        assertEquals(25, node1.getUvPercent());
        assertEquals(33, node1.getPvPercent());

        SopNode node2 = nodes.get(1);
        assertEquals(75, node2.getUvPercent());
        assertEquals(67, node2.getPvPercent());
    }

    @Test
    void completeFirstStepNodePercentShouldDoNothingWhenInputIsNull() {
        // Act
        aiobSopStatisticService.completeFirstStepNodePercent(null, null);

        // No assertion needed, just verify no exception is thrown
    }

    @Test
    void completeFirstStepNodePercentShouldSetPercentWhenNodeIsFirstStep() {
        // Arrange
        SopNode node1 = new SopNode("node1", 0L, 0L, 0, 0, "");
        SopNode node2 = new SopNode("node2", 0L, 0L, 0, 0, "");
        List<SopNode> nodes = List.of(node1, node2);

        SopEdge edge = new SopEdge("node1", "node2", 1L);
        List<SopEdge> edges = List.of(edge);

        // Act
        aiobSopStatisticService.completeFirstStepNodePercent(nodes, edges);

        // Assert
        assertEquals(100, node1.getUvPercent());
        assertEquals(100, node1.getPvPercent());
        assertEquals(0, node2.getUvPercent());
        assertEquals(0, node2.getPvPercent());
    }

    @Test
    void completeFirstStepNodePercentShouldNotSetPercentWhenNodeIsNotFirstStep() {
        // Arrange
        SopNode node1 = new SopNode("node1", 0L, 0L, 0, 0, "");
        SopNode node2 = new SopNode("node2", 0L, 0L, 0, 0, "");
        List<SopNode> nodes = List.of(node1, node2);

        SopEdge edge1 = new SopEdge("node1", "node2", 1L);
        SopEdge edge2 = new SopEdge("node2", "node3", 1L);
        List<SopEdge> edges = List.of(edge1, edge2);

        // Act
        aiobSopStatisticService.completeFirstStepNodePercent(nodes, edges);

        // Assert
        assertEquals(100, node1.getUvPercent());
        assertEquals(100, node1.getPvPercent());
        assertEquals(0, node2.getUvPercent());
        assertEquals(0, node2.getPvPercent());
    }

    @Test
    void completeFirstStepNodePercentShouldNotSetPercentWhenNodeAlreadyHasPercent() {
        // Arrange
        SopNode node1 = new SopNode("node1", 50L, 50L, 0, 0, "");
        SopNode node2 = new SopNode("node2", 0L, 0L, 0, 0, "");
        List<SopNode> nodes = List.of(node1, node2);

        SopEdge edge = new SopEdge("node1", "node2", 1L);
        List<SopEdge> edges = List.of(edge);

        // Act
        aiobSopStatisticService.completeFirstStepNodePercent(nodes, edges);

        // Assert
        assertEquals(100, node1.getUvPercent());
        assertEquals(100, node1.getPvPercent());
        assertEquals(0, node2.getUvPercent());
        assertEquals(0, node2.getPvPercent());
    }

    @Test
    void completeFirstStepNodePercentShouldDoNothingWhenInputIsEmpty() {
        // Arrange
        List<SopNode> emptyNodes = List.of();
        List<SopEdge> emptyEdges = List.of();

        // Act
        aiobSopStatisticService.completeFirstStepNodePercent(emptyNodes, emptyEdges);

        // No assertion needed, just verify no exception is thrown
    }

    @Test
    void flexibleWholeDataShouldReturnCorrectResponseWhenInputValid() {
        // Prepare test data
        String tenantId = "testTenant";
        SopFlexibleWholeRequest request = new SopFlexibleWholeRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");
        request.setTopicId("topic1");
        request.setStartTime(new Date());
        request.setEndTime(new Date());

        when(aiobSopMetricService.flexibleIdTransfer(tenantId, request.getRobotId(), request.getRobotVer()))
                .thenReturn(List.of("newRobotId", "newRobotVer"));

        // Mock node metrics
        SopNode node1 = new SopNode("node1", 100L, 100L, 0, 0, "");
        SopNode node2 = new SopNode("node2", 50L, 60L, 0, 0, "");
        List<SopNode> nodeMetrics = Arrays.asList(node1, node2);
        when(aiobSopMetricService.wholeNodeMetric(eq(tenantId), any(), any(), any(), any(), any(), any()))
                .thenReturn(nodeMetrics);

        // Mock node hangup metrics
        SopNode hangupNode = new SopNode("node1", 10L, 10L, 0, 0, "");
        Map<String, SopNode> hangupMap = new HashMap<>();
        hangupMap.put("node1", hangupNode);
        when(aiobSopMetricService.wholeNodeHangupMetric(eq(tenantId), any(), any(), any(), any(), any(), any()))
                .thenReturn(hangupMap);

        // Mock edge metrics
        SopEdge edge1 = new SopEdge("node1", "node2", 50L);
        List<SopEdge> edgeMetrics = Collections.singletonList(edge1);
        when(aiobSopMetricService.wholeEdgeMetric(eq(tenantId), any(), any(), any(), any(), any(), any()))
                .thenReturn(edgeMetrics);

        // Execute
        SopFlexibleWholeResponse response = aiobSopStatisticService.flexibleWholeData(tenantId, request);

        // Verify
        assertNotNull(response);
        assertEquals(2, response.getNodes().size());
        assertEquals(1, response.getEdges().size());

        // Verify node1 with hangup
        SopNodeWithHangup resultNode1 = response.getNodes().get(0);
        assertEquals(100, resultNode1.getUvPercent());
        assertEquals(100, resultNode1.getPvPercent());
        assertNotNull(resultNode1.getHangup());
        assertEquals(10L, resultNode1.getHangup().getUv());
        assertEquals(10, resultNode1.getHangup().getUvPercent());

        // Verify node2 without hangup
        SopNodeWithHangup resultNode2 = response.getNodes().get(1);
        assertEquals(50, resultNode2.getUvPercent());
        assertEquals(60, resultNode2.getPvPercent());
        assertNotNull(resultNode2.getHangup());
        assertEquals(0, resultNode2.getHangup().getUvPercent());
    }

    @Test
    void flexibleWholeDataShouldHandleEmptyMetrics() {
        // Prepare test data
        String tenantId = "testTenant";
        SopFlexibleWholeRequest request = new SopFlexibleWholeRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");
        request.setTopicId("topic1");
        request.setStartTime(new Date());
        request.setEndTime(new Date());

        // Mock empty metrics
        when(aiobSopMetricService.wholeNodeMetric(eq(tenantId), any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.emptyList());
        when(aiobSopMetricService.wholeNodeHangupMetric(eq(tenantId), any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.emptyMap());
        when(aiobSopMetricService.wholeEdgeMetric(eq(tenantId), any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.emptyList());

        // Execute
        SopFlexibleWholeResponse response = aiobSopStatisticService.flexibleWholeData(tenantId, request);

        // Verify
        assertNotNull(response);
        assertTrue(response.getNodes().isEmpty());
        assertTrue(response.getEdges().isEmpty());
    }

    @Test
    void calculateNodeRatiosV2_ShouldHandleEmptyNodeMetrics() {
        aiobSopStatisticService.calculateNodeRatiosV2(null, mockEdges, tenantId, "task1", "robot1", "1.0", "topic1", new Date(), new Date());
        aiobSopStatisticService.calculateNodeRatiosV2(Collections.emptyList(), mockEdges, tenantId, "task1", "robot1", "1.0", "topic1", new Date(), new Date());
        verify(aiobSopMetricService, never()).nodeMetricWithIds(any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void calculateNodeRatiosV2_ShouldHandleEmptyEdgeMetrics() {
        aiobSopStatisticService.calculateNodeRatiosV2(mockNodes, null, tenantId, "task1", "robot1", "1.0", "topic1", new Date(), new Date());
        aiobSopStatisticService.calculateNodeRatiosV2(mockNodes, Collections.emptyList(), tenantId, "task1", "robot1", "1.0", "topic1", new Date(), new Date());
        verify(aiobSopMetricService, never()).nodeMetricWithIds(any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void calculateNodeRatiosV2_ShouldCalculateSingleEdgeRatio() {
        SopNode node1 = new SopNode("node1", 10L, 20L, 0, 0, "");
        SopNode node2 = new SopNode("node2", 5L, 10L, 0, 0, "");
        SopEdge edge = new SopEdge("node1", "node2", 1L);

        List<SopNode> nodes = List.of(node1, node2);
        List<SopEdge> edges = List.of(edge);

        aiobSopStatisticService.calculateNodeRatiosV2(nodes, edges, tenantId, "task1", "robot1", "1.0", "topic1", new Date(), new Date());

        assertEquals(50, node2.getUvPercent());
        assertEquals(50, node2.getPvPercent());
        verify(aiobSopMetricService, never()).nodeMetricWithIds(any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void calculateNodeRatiosV2_ShouldCalculateMultiEdgeRatio() {
        SopNode node1 = new SopNode("node1", 10L, 20L, 0, 0, "");
        SopNode node2 = new SopNode("node2", 5L, 10L, 0, 0, "");
        SopNode node3 = new SopNode("node3", 5L, 10L, 0, 0, "");
        SopEdge edge1 = new SopEdge("node1", "node3", 1L);
        SopEdge edge2 = new SopEdge("node2", "node3", 1L);

        List<SopNode> nodes = List.of(node1, node2, node3);
        List<SopEdge> edges = List.of(edge1, edge2);

        when(aiobSopMetricService.nodeMetricWithIds(eq(tenantId), eq(List.of("node1", "node2")), eq("task1"),
                eq("robot1"), eq("1.0"), eq("topic1"), any(Date.class), any(Date.class))
        ).thenReturn(new Long[]{15L, 30L});

        aiobSopStatisticService.calculateNodeRatiosV2(nodes, edges, tenantId, "task1", "robot1", "1.0", "topic1", new Date(), new Date());

        assertEquals(33, node3.getUvPercent());
        assertEquals(33, node3.getPvPercent());
        verify(aiobSopMetricService, times(1)).nodeMetricWithIds(any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void calculateNodeRatiosV2_ShouldSkipWhenToNodeNotFound() {
        SopNode node1 = new SopNode("node1", 10L, 20L, 0, 0, "");
        SopEdge edge = new SopEdge("node1", "nonexistent", 1L);

        List<SopNode> nodes = List.of(node1);
        List<SopEdge> edges = List.of(edge);

        aiobSopStatisticService.calculateNodeRatiosV2(nodes, edges, tenantId, "task1", "robot1", "1.0", "topic1", new Date(), new Date());

        assertEquals(0, node1.getUvPercent());
        assertEquals(0, node1.getPvPercent());
        verify(aiobSopMetricService, never()).nodeMetricWithIds(any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void calculateNodeRatiosV2_ShouldHandleZeroBaseCase() {
        SopNode node1 = new SopNode("node1", 0L, 0L, 0, 0, "");
        SopNode node2 = new SopNode("node2", 0L, 0L, 0, 0, "");
        SopEdge edge = new SopEdge("node1", "node2", 1L);

        List<SopNode> nodes = List.of(node1, node2);
        List<SopEdge> edges = List.of(edge);

        aiobSopStatisticService.calculateNodeRatiosV2(nodes, edges, tenantId, "task1", "robot1", "1.0", "topic1", new Date(), new Date());

        assertEquals(0, node2.getUvPercent());
        assertEquals(0, node2.getPvPercent());
        verify(aiobSopMetricService, never()).nodeMetricWithIds(any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void buildDefaultSankeyWholeData_ShouldReturnEmptySteps_WhenInputEmptyList() {
        // Execute method with empty list
        SopSankeyWholeResponse response = aiobSopStatisticService.buildDefaultSankeyWholeData(Collections.emptyList());

        // Verify results
        assertNotNull(response);
        assertTrue(response.getSteps().isEmpty());
        assertTrue(response.getEdges().isEmpty());
    }

    @Test
    void buildDefaultSankeyWholeData_ShouldHandleNullNodes_WhenStepHasNullNodes() {
        // Prepare test data with null nodes
        SOPMetaInternal step = new SOPMetaInternal();
        step.setStepId("step1");
        step.setStepName("Step 1");
        step.setNodes(null);

        List<SOPMetaInternal> sopMetaList = List.of(step);

        // Execute method
        SopSankeyWholeResponse response = aiobSopStatisticService.buildDefaultSankeyWholeData(sopMetaList);

        // Verify results
        assertNotNull(response);
        assertEquals(1, response.getSteps().size());
        assertTrue(response.getEdges().isEmpty());

        SopSankeyStep resultStep = response.getSteps().get(0);
        assertEquals("step1", resultStep.getStepId());
        assertEquals("Step 1", resultStep.getStepName());
        assertEquals(0, resultStep.getNodes().size());
    }

    @Test
    void buildDefaultSankeyWholeData_ShouldReturnCorrectResponse_WhenInputValidSopMetaList() {
        // Prepare test data
        SOPMetaInternal.SopMetaNode node1 = new SOPMetaInternal.SopMetaNode("node1", "nodeId1");
        SOPMetaInternal.SopMetaNode node2 = new SOPMetaInternal.SopMetaNode("node2", "nodeId2");
        SOPMetaInternal step1 = new SOPMetaInternal();
        step1.setStepId("step1");
        step1.setStepName("Step 1");
        step1.setNodes(List.of(node1));

        SOPMetaInternal step2 = new SOPMetaInternal();
        step2.setStepId("step2");
        step2.setStepName("Step 2");
        step2.setNodes(List.of(node2));

        List<SOPMetaInternal> sopMetaList = List.of(step1, step2);

        // Execute method
        SopSankeyWholeResponse response = aiobSopStatisticService.buildDefaultSankeyWholeData(sopMetaList);

        // Verify results
        assertNotNull(response);
        assertEquals(2, response.getSteps().size());
        assertTrue(response.getEdges().isEmpty());

        // Verify first step
        SopSankeyStep firstStep = response.getSteps().get(0);
        assertEquals("step1", firstStep.getStepId());
        assertEquals("Step 1", firstStep.getStepName());
        assertEquals(1, firstStep.getNodes().size());
        assertEquals("node1", firstStep.getNodes().get(0).getNodeId());

        // Verify second step
        SopSankeyStep secondStep = response.getSteps().get(1);
        assertEquals("step2", secondStep.getStepId());
        assertEquals("Step 2", secondStep.getStepName());
        assertEquals(1, secondStep.getNodes().size());
    }

    @Test
    void flexibleWholeDataShouldReturnEmptyResponseWhenIdTransferIsEmpty() {
        when(aiobSopMetricService.flexibleIdTransfer(tenantId, request.getRobotId(), request.getRobotVer()))
                .thenReturn(Collections.emptyList());

        SopFlexibleWholeResponse response = aiobSopStatisticService.flexibleWholeData(tenantId, request);

        assertNotNull(response);
        assertTrue(CollectionUtils.isEmpty(response.getNodes()));
        assertTrue(CollectionUtils.isEmpty(response.getEdges()));
        verify(aiobSopMetricService, times(1)).flexibleIdTransfer(tenantId, request.getRobotId(), request.getRobotVer());
        verify(aiobSopMetricService, never()).wholeNodeMetric(any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    void flexibleWholeDataShouldProcessMetricsCorrectly() {
        SopFlexibleWholeRequest request = new SopFlexibleWholeRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");
        request.setTopicId("topic1");
        request.setStartTime(new Date());
        request.setEndTime(new Date());

        // Mock id transfer
        List<String> idTransferRes = List.of(request.getRobotId(), request.getRobotVer());
        when(aiobSopMetricService.flexibleIdTransfer(tenantId, request.getRobotId(), request.getRobotVer()))
                .thenReturn(idTransferRes);

        // Mock metrics
        SopNode node1 = new SopNode("node1", 10L, 5L, 0, 0, "");
        SopNode node2 = new SopNode("node2", 5L, 3L, 0, 0, "");
        List<SopNode> mockNodes = List.of(node1, node2);
        when(aiobSopMetricService.wholeNodeMetric(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(),
                request.getTopicId(), request.getStartTime(), request.getEndTime()))
                .thenReturn(mockNodes);

        SopNode hangupNode = new SopNode("node1", 2L, 1L, 0, 0, "");
        when(aiobSopMetricService.wholeNodeHangupMetric(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(),
                request.getTopicId(), request.getStartTime(), request.getEndTime()))
                .thenReturn(Map.of("node1", hangupNode));

        SopEdge edge = new SopEdge("node1", "node2", 5L);
        when(aiobSopMetricService.wholeEdgeMetric(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(),
                request.getTopicId(), request.getStartTime(), request.getEndTime()))
                .thenReturn(List.of(edge));

        // Execute
        SopFlexibleWholeResponse response = aiobSopStatisticService.flexibleWholeData(tenantId, request);

        // Verify
        assertNotNull(response);
        assertEquals(2, response.getNodes().size());
        assertEquals(1, response.getEdges().size());

        // Verify node1 with hangup
        SopNodeWithHangup node1WithHangup = response.getNodes().get(0);
        assertEquals(100, node1WithHangup.getUvPercent()); // first node should be 100%
        assertEquals(100, node1WithHangup.getPvPercent());
        assertEquals(20, node1WithHangup.getHangup().getUvPercent()); // 1/5 = 20%
        assertEquals(20, node1WithHangup.getHangup().getPvPercent()); // 2/10 = 20%

        // Verify node2
        SopNodeWithHangup node2WithHangup = response.getNodes().get(1);
        assertEquals(50, node2WithHangup.getUvPercent()); // 5/10 = 50%
        assertEquals(60, node2WithHangup.getPvPercent()); // 3/10 = 30%
        assertNotNull(node2WithHangup.getHangup());

        // Verify edge
        assertEquals(edge, response.getEdges().get(0));

        // Verify interactions
        verify(aiobSopMetricService, times(1)).flexibleIdTransfer(tenantId, request.getRobotId(), request.getRobotVer());
        verify(aiobSopMetricService, times(1)).wholeNodeMetric(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(),
                request.getTopicId(), request.getStartTime(), request.getEndTime());
        verify(aiobSopMetricService, times(1)).wholeNodeHangupMetric(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(),
                request.getTopicId(), request.getStartTime(), request.getEndTime());
        verify(aiobSopMetricService, times(1)).wholeEdgeMetric(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(),
                request.getTopicId(), request.getStartTime(), request.getEndTime());
    }

    @Test
    void flexibleWholeDataShouldHandleEmptyMetrics1() {
        when(aiobSopMetricService.flexibleIdTransfer(tenantId, request.getRobotId(), request.getRobotVer()))
                .thenReturn(List.of("newRobotId", "newRobotVer"));
        when(aiobSopMetricService.wholeNodeMetric(any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.emptyList());
        when(aiobSopMetricService.wholeNodeHangupMetric(any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.emptyMap());
        when(aiobSopMetricService.wholeEdgeMetric(any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(Collections.emptyList());

        SopFlexibleWholeResponse response = aiobSopStatisticService.flexibleWholeData(tenantId, request);

        assertNotNull(response);
        assertTrue(CollectionUtils.isEmpty(response.getNodes()));
        assertTrue(CollectionUtils.isEmpty(response.getEdges()));
    }

    @Test
    void analyseProgressShouldHandleFlexibleSceneCase() {
        // Arrange
        SOPAnalyseProgressRequest request = new SOPAnalyseProgressRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");

        when(aiobSopMetricService.checkTaskSceneType(tenantId, "task1")).thenReturn(6);
        when(aiobSopMetricService.countSessionConnectedCall(tenantId, "task1", null, null)).thenReturn(100L);
        when(aiobSopMetricService.countFlexibleSessionConnectedCall(tenantId, "task1", "robot1", "1.0")).thenReturn(70L);
        when(aiobSopMetricService.flexibleIdTransfer(tenantId, "robot1", "1.0")).thenReturn(List.of("transformedRobotId", "transformedVer"));
        when(aiobSopMetricService.countFlexibleProcessedCall(tenantId, "task1", "transformedRobotId", "transformedVer")).thenReturn(40L);

        // Act
        SOPAnalyseProgressResponse response = aiobSopStatisticService.analyseProgress(tenantId, request);

        // Assert
        assertNotNull(response);
        assertEquals(40L, response.getProcessed());
        assertEquals(70L, response.getConnectedCall());
        assertEquals(100L, response.getTotalConnectedCall());

        verify(aiobSopMetricService).checkTaskSceneType(tenantId, "task1");
        verify(aiobSopMetricService).countSessionConnectedCall(tenantId, "task1", null, null);
        verify(aiobSopMetricService).countFlexibleSessionConnectedCall(tenantId, "task1", "robot1", "1.0");
        verify(aiobSopMetricService).flexibleIdTransfer(tenantId, "robot1", "1.0");
        verify(aiobSopMetricService).countFlexibleProcessedCall(tenantId, "task1", "transformedRobotId", "transformedVer");
    }

    @Test
    void analyseProgressShouldReturnDefaultResponseWhenIdTransferFailsInFlexibleScene() {
        // Arrange
        SOPAnalyseProgressRequest request = new SOPAnalyseProgressRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");

        when(aiobSopMetricService.checkTaskSceneType(tenantId, "task1")).thenReturn(6);
        when(aiobSopMetricService.countSessionConnectedCall(tenantId, "task1", null, null)).thenReturn(100L);
        when(aiobSopMetricService.countFlexibleSessionConnectedCall(tenantId, "task1", "robot1", "1.0")).thenReturn(70L);
        when(aiobSopMetricService.flexibleIdTransfer(tenantId, "robot1", "1.0")).thenReturn(List.of());

        // Act
        SOPAnalyseProgressResponse response = aiobSopStatisticService.analyseProgress(tenantId, request);

        // Assert
        assertNotNull(response);
        assertEquals(0L, response.getProcessed());
        assertEquals(70L, response.getConnectedCall());
        assertEquals(100L, response.getTotalConnectedCall());

        verify(aiobSopMetricService).checkTaskSceneType(tenantId, "task1");
        verify(aiobSopMetricService).countSessionConnectedCall(tenantId, "task1", null, null);
        verify(aiobSopMetricService).countFlexibleSessionConnectedCall(tenantId, "task1", "robot1", "1.0");
        verify(aiobSopMetricService).flexibleIdTransfer(tenantId, "robot1", "1.0");
        verifyNoMoreInteractions(aiobSopMetricService);
    }

    @Test
    void analyseProgressShouldReturnDefaultResponseWhenRobotSceneIsNull() {
        // Arrange
        SOPAnalyseProgressRequest request = new SOPAnalyseProgressRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");

        when(aiobSopMetricService.checkTaskSceneType(tenantId, "task1")).thenReturn(null);

        // Act
        SOPAnalyseProgressResponse response = aiobSopStatisticService.analyseProgress(tenantId, request);

        // Assert
        assertNotNull(response);
        assertEquals(0L, response.getProcessed());
        assertEquals(0L, response.getConnectedCall());
        assertEquals(0L, response.getTotalConnectedCall());
        verify(aiobSopMetricService).checkTaskSceneType(tenantId, "task1");
        verifyNoMoreInteractions(aiobSopMetricService);
    }

    @Test
    void analyseProgressShouldHandleQuickSceneCase() {
        // Arrange
        SOPAnalyseProgressRequest request = new SOPAnalyseProgressRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");

        when(aiobSopMetricService.checkTaskSceneType(tenantId, "task1")).thenReturn(5);
        when(aiobSopMetricService.countSessionConnectedCall(tenantId, "task1", null, null)).thenReturn(100L);
        when(aiobSopMetricService.countSessionConnectedCall(tenantId, "task1", "robot1", "1.0")).thenReturn(80L);
        when(aiobSopMetricService.countAnalysedMetricSQL(tenantId, "task1", "robot1", "1.0")).thenReturn(50L);

        // Act
        SOPAnalyseProgressResponse response = aiobSopStatisticService.analyseProgress(tenantId, request);

        // Assert
        assertNotNull(response);
        assertEquals(50L, response.getProcessed());
        assertEquals(80L, response.getConnectedCall());
        assertEquals(100L, response.getTotalConnectedCall());

        verify(aiobSopMetricService).checkTaskSceneType(tenantId, "task1");
        verify(aiobSopMetricService).countSessionConnectedCall(tenantId, "task1", null, null);
        verify(aiobSopMetricService).countSessionConnectedCall(tenantId, "task1", "robot1", "1.0");
        verify(aiobSopMetricService).countAnalysedMetricSQL(tenantId, "task1", "robot1", "1.0");
    }

    @Test
    void taskIdTransferShouldReturnOriginalIdsWhenRobotSceneIsNull() {
        String tenantId = "tenant1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "1.0";

        when(aiobSopMetricService.checkTaskSceneType(tenantId, taskId)).thenReturn(null);

        List<String> result = aiobSopStatisticService.taskIdTransfer(tenantId, taskId, robotId, robotVer);

        assertEquals(Lists.newArrayList(robotId, robotVer), result);
        verify(aiobSopMetricService).checkTaskSceneType(tenantId, taskId);
        verify(aiobSopMetricService, never()).flexibleIdTransfer(any(), any(), any());
    }

    @Test
    void taskIdTransferShouldReturnOriginalIdsWhenRobotSceneIsNot6() {
        String tenantId = "tenant1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "1.0";

        when(aiobSopMetricService.checkTaskSceneType(tenantId, taskId)).thenReturn(1);

        List<String> result = aiobSopStatisticService.taskIdTransfer(tenantId, taskId, robotId, robotVer);

        assertEquals(Lists.newArrayList(robotId, robotVer), result);
        verify(aiobSopMetricService).checkTaskSceneType(tenantId, taskId);
        verify(aiobSopMetricService, never()).flexibleIdTransfer(any(), any(), any());
    }

    @Test
    void taskIdTransferShouldReturnTransferredIdsWhenRobotSceneIs6AndTransferSuccess() {
        String tenantId = "tenant1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "1.0";
        List<String> transferredIds = Lists.newArrayList("robot2", "2.0");

        when(aiobSopMetricService.checkTaskSceneType(tenantId, taskId)).thenReturn(6);
        when(aiobSopMetricService.flexibleIdTransfer(tenantId, robotId, robotVer)).thenReturn(transferredIds);

        List<String> result = aiobSopStatisticService.taskIdTransfer(tenantId, taskId, robotId, robotVer);

        assertEquals(transferredIds, result);
        verify(aiobSopMetricService).checkTaskSceneType(tenantId, taskId);
        verify(aiobSopMetricService).flexibleIdTransfer(tenantId, robotId, robotVer);
    }

    @Test
    void taskIdTransferShouldReturnOriginalIdsWhenRobotSceneIs6AndTransferFailed() {
        String tenantId = "tenant1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "1.0";

        when(aiobSopMetricService.checkTaskSceneType(tenantId, taskId)).thenReturn(6);
        when(aiobSopMetricService.flexibleIdTransfer(tenantId, robotId, robotVer)).thenReturn(null);

        List<String> result = aiobSopStatisticService.taskIdTransfer(tenantId, taskId, robotId, robotVer);

        assertEquals(Lists.newArrayList(robotId, robotVer), result);
        verify(aiobSopMetricService).checkTaskSceneType(tenantId, taskId);
        verify(aiobSopMetricService).flexibleIdTransfer(tenantId, robotId, robotVer);
    }

    @Test
    void taskIdTransferShouldReturnOriginalIdsWhenRobotSceneIs6AndTransferEmpty() {
        String tenantId = "tenant1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "1.0";

        when(aiobSopMetricService.checkTaskSceneType(tenantId, taskId)).thenReturn(6);
        when(aiobSopMetricService.flexibleIdTransfer(tenantId, robotId, robotVer)).thenReturn(Lists.newArrayList());

        List<String> result = aiobSopStatisticService.taskIdTransfer(tenantId, taskId, robotId, robotVer);

        assertEquals(Lists.newArrayList(robotId, robotVer), result);
        verify(aiobSopMetricService).checkTaskSceneType(tenantId, taskId);
        verify(aiobSopMetricService).flexibleIdTransfer(tenantId, robotId, robotVer);
    }

    @Test
    void getAllNodeIdsWithStep_shouldReturnDistinctNodeIdsWhenInputValid() {
        // Prepare test data
        SOPMetaInternal.SopMetaNode node1 = new SOPMetaInternal.SopMetaNode();
        node1.setNodeId("node1");
        SOPMetaInternal.SopMetaNode node2 = new SOPMetaInternal.SopMetaNode();
        node2.setNodeId("node2");
        SOPMetaInternal.SopMetaNode node3 = new SOPMetaInternal.SopMetaNode();
        node3.setNodeId("node1"); // duplicate nodeId

        SOPMetaInternal meta1 = new SOPMetaInternal();
        meta1.setNodes(List.of(node1, node2));
        SOPMetaInternal meta2 = new SOPMetaInternal();
        meta2.setNodes(List.of(node3));
        SOPMetaInternal meta3 = new SOPMetaInternal();
        meta3.setNodes(null); // empty nodes

        List<SOPMetaInternal> input = List.of(meta1, meta2, meta3);

        // Execute and verify
        List<String> result = aiobSopStatisticService.getAllNodeIdsWithStep(input);
        assertEquals(2, result.size());
        assertTrue(result.contains("node1"));
        assertTrue(result.contains("node2"));
    }

    @Test
    void getAllNodeIdsWithStep_shouldReturnEmptyListWhenInputEmpty() {
        // Empty input
        List<SOPMetaInternal> input = List.of();

        // Execute and verify
        List<String> result = aiobSopStatisticService.getAllNodeIdsWithStep(input);
        assertTrue(result.isEmpty());
    }

    @Test
    void getAllNodeIdsWithStep_shouldFilterBlankNodeIds() {
        // Prepare test data with blank nodeId
        SOPMetaInternal.SopMetaNode node1 = new SOPMetaInternal.SopMetaNode();
        node1.setNodeId("");
        SOPMetaInternal.SopMetaNode node2 = new SOPMetaInternal.SopMetaNode();
        node2.setNodeId("node2");

        SOPMetaInternal meta = new SOPMetaInternal();
        meta.setNodes(List.of(node1, node2));

        // Execute and verify
        List<String> result = aiobSopStatisticService.getAllNodeIdsWithStep(List.of(meta));
        assertEquals(1, result.size());
        assertEquals("node2", result.get(0));
    }

    @Test
    void getAllNodeIdsWithStep_shouldSkipEmptyNodeLists() {
        // Prepare test data with empty nodes list
        SOPMetaInternal meta1 = new SOPMetaInternal();
        meta1.setNodes(List.of());
        SOPMetaInternal meta2 = new SOPMetaInternal();
        meta2.setNodes(null);

        // Execute and verify
        List<String> result = aiobSopStatisticService.getAllNodeIdsWithStep(List.of(meta1, meta2));
        assertTrue(result.isEmpty());
    }

    @Test
    void getAllStartNodeIdsShouldReturnAllNodeIds() {
        // Arrange
        SOPMetaInternal.SopMetaNode node1 = new SOPMetaInternal.SopMetaNode("node1", null);
        SOPMetaInternal.SopMetaNode node2 = new SOPMetaInternal.SopMetaNode("node2", null);

        SOPMetaInternal meta = new SOPMetaInternal(null, null, List.of(node1, node2));
        List<SOPMetaInternal> metaList = List.of(meta);

        // Act
        List<String> result = aiobSopStatisticService.getAllStartNodeIds(metaList);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.containsAll(List.of("node1", "node2")));
    }

    @Test
    void getAllStartNodeIdsShouldReturnEmptyListWhenInputIsEmpty() {
        // Arrange
        List<SOPMetaInternal> emptyList = List.of(new SOPMetaInternal(null, null, null));

        // Act
        List<String> result = aiobSopStatisticService.getAllStartNodeIds(emptyList);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void getAllStartNodeIdsShouldReturnEmptyListWhenNodesIsEmpty() {
        // Arrange
        SOPMetaInternal meta = new SOPMetaInternal(null, null, List.of());
        List<SOPMetaInternal> metaList = List.of(meta);

        // Act
        List<String> result = aiobSopStatisticService.getAllStartNodeIds(metaList);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    void getAllStartNodeIdsShouldReturnDistinctNodeIds() {
        // Arrange
        SOPMetaInternal.SopMetaNode node1 = new SOPMetaInternal.SopMetaNode("node1", null);
        SOPMetaInternal.SopMetaNode node2 = new SOPMetaInternal.SopMetaNode("node2", null);
        SOPMetaInternal.SopMetaNode duplicateNode = new SOPMetaInternal.SopMetaNode("node1", null);

        SOPMetaInternal meta = new SOPMetaInternal(null, null, List.of(node1, node2, duplicateNode));
        List<SOPMetaInternal> metaList = List.of(meta);

        // Act
        List<String> result = aiobSopStatisticService.getAllStartNodeIds(metaList);

        // Assert
        assertEquals(2, result.size());
        assertTrue(result.contains("node1"));
        assertTrue(result.contains("node2"));
    }

    @Test
    void testSankeyWholeDataWithEmptyMeta() {
        // Mock empty SOP meta data
        SopSankeyMetaResponse mockMetaResponse = new SopSankeyMetaResponse();
        mockMetaResponse.setManualChecked(false);
        mockMetaResponse.setSopMeta(Collections.emptyList());

        when(aiobSOPService.listSOPMeta(anyString(), anyString(), anyString()))
                .thenReturn(mockMetaResponse);

        SopSankeyWholeResponse response = aiobSopStatisticService.sankeyWholeData(tenantId, request1);

        assertNotNull(response);
        assertTrue(response.getSteps().isEmpty());
        assertTrue(response.getEdges().isEmpty());
    }

    @Test
    void testSankeyWholeDataWithEmptyNodeMetrics() {
        // Mock SOP meta data
        SopSankeyMetaResponse mockMetaResponse = new SopSankeyMetaResponse();
        mockMetaResponse.setManualChecked(true);

        SOPMetaInternal.SopMetaNode metaNode1 = new SOPMetaInternal.SopMetaNode("node1", "Node 1");
        SOPMetaInternal step1 = new SOPMetaInternal("Step 1", "step1", List.of(metaNode1));
        mockMetaResponse.setSopMeta(List.of(step1));

        when(aiobSOPService.listSOPMeta(anyString(), anyString(), anyString()))
                .thenReturn(mockMetaResponse);

        // Mock empty node metrics
        when(aiobSopMetricService.wholeNodeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Collections.emptyList());

        SopSankeyWholeResponse response = aiobSopStatisticService.sankeyWholeData(tenantId, request1);

        assertNotNull(response);
        assertEquals(1, response.getSteps().size());
        assertEquals(1, response.getSteps().get(0).getNodes().size());
        assertEquals(-1L, response.getSteps().get(0).getNodes().get(0).getUv());
    }

    @Test
    void testSankeyWholeData() {
        // Mock SOP meta data
        SopSankeyMetaResponse mockMetaResponse = new SopSankeyMetaResponse();
        mockMetaResponse.setManualChecked(true);

        SOPMetaInternal.SopMetaNode metaNode1 = new SOPMetaInternal.SopMetaNode("node1", "Node 1");
        SOPMetaInternal.SopMetaNode metaNode2 = new SOPMetaInternal.SopMetaNode("node2", "Node 2");
        SOPMetaInternal step1 = new SOPMetaInternal("Step1", "step1", List.of(metaNode1, metaNode2));
        mockMetaResponse.setSopMeta(List.of(step1));

        when(aiobSOPService.listSOPMeta(anyString(), anyString(), anyString()))
                .thenReturn(mockMetaResponse);

        // Mock node metrics
        SopNode node1 = new SopNode("node1", 100L, 150L, 0, 0, "");
        SopNode node2 = new SopNode("node2", 50L, 75L, 0, 0, "");
        when(aiobSopMetricService.wholeNodeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(List.of(node1, node2));

        // Mock edge metrics
        SopEdge edge1 = new SopEdge("node1", "node2", 50L, 100L);
        when(aiobSopMetricService.wholeEdgeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(List.of(edge1));

        // Mock intent metrics
        SopIntent intent1 = new SopIntent("node2", "positive", 0L, 0L, 20, 10);
        when(aiobSopMetricService.wholeIntentMetric(anyString(), anyList(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(List.of(intent1));

        // Execute test
        SopSankeyWholeResponse response = aiobSopStatisticService.sankeyWholeData(tenantId, request1);

        // Verify results
        assertNotNull(response);
        assertEquals(2, response.getSteps().size());
        assertEquals(2, response.getEdges().size());

        // Verify step data
        SopSankeyStep step = response.getSteps().get(0);
        assertEquals("Step1", step.getStepId());
        assertEquals(2, step.getNodes().size());

        // Verify intent step
        SopSankeyStep intentStep = response.getSteps().get(1);
        assertEquals("意向分析", intentStep.getStepName());
        assertEquals(1, intentStep.getNodes().size());
        assertEquals("098ABDDC09B4EEB172D73B3CB5CBEE65", intentStep.getNodes().get(0).getNodeId());
    }


    @Test
    void testSankeyWholeData_WithValidData_ReturnsCompleteResponse() {
        // Arrange
        SopSankeyMetaResponse metaResponse = new SopSankeyMetaResponse();
        metaResponse.setManualChecked(true);

        SOPMetaInternal meta = new SOPMetaInternal();
        meta.setStepId("step1");
        meta.setStepName("Step 1");
        SOPMetaInternal.SopMetaNode node = new SOPMetaInternal.SopMetaNode();
        node.setNodeId("node1");
        SOPMetaInternal.SopMetaNode node2 = new SOPMetaInternal.SopMetaNode();
        node2.setNodeId("node2");
        meta.setNodes(List.of(node, node2));

        metaResponse.setSopMeta(List.of(meta));

        SopNode nodeMetric = new SopNode("node1", 100L, 200L, 50, 25, "");
        SopEdge edgeMetric = new SopEdge("node1", "node2", 50L, 25L);
        SopIntent intentMetric = new SopIntent();
        intentMetric.setNodeId("node1");
        intentMetric.setIntent("intent1");
        intentMetric.setUv(10L);
        intentMetric.setPv(20L);

        when(aiobSOPService.listSOPMeta(anyString(), anyString(), anyString())).thenReturn(metaResponse);
        when(aiobSopMetricService.wholeNodeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(List.of(nodeMetric));
        when(aiobSopMetricService.wholeEdgeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(List.of(edgeMetric));
        when(aiobSopMetricService.wholeIntentMetric(anyString(), anyList(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(List.of(intentMetric));
        when(aiobSopMetricService.nodeMetricWithIds(anyString(), anyList(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(new Long[]{100L, 200L});

        // Act
        SopSankeyWholeResponse response = aiobSopStatisticService.sankeyWholeData(tenantId, request1);

        // Assert
        assertEquals(2, response.getSteps().size());
        assertEquals(2, response.getEdges().size());
        assertEquals("step1", response.getSteps().get(0).getStepId());
        assertEquals("意向分析", response.getSteps().get(1).getStepName());
    }

    @Test
    void testSankeyWholeData_WithMultipleSteps_ReturnsCompleteResponse() {
        // Arrange
        SopSankeyMetaResponse metaResponse = new SopSankeyMetaResponse();
        metaResponse.setManualChecked(true);

        List<SOPMetaInternal> sopMetas = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            SOPMetaInternal meta = new SOPMetaInternal();
            meta.setStepId("step" + i);
            meta.setStepName("Step " + i);
            SOPMetaInternal.SopMetaNode node = new SOPMetaInternal.SopMetaNode();
            node.setNodeId("node" + i);
            meta.setNodes(List.of(node));
            sopMetas.add(meta);
        }
        metaResponse.setSopMeta(sopMetas);

        List<SopNode> nodeMetrics = new ArrayList<>();
        List<SopEdge> edgeMetrics = new ArrayList<>();
        for (int i = 1; i <= 3; i++) {
            nodeMetrics.add(new SopNode("node" + i, 100L * i, 200L * i, 50 * i, 25 * i, ""));
            if (i > 1) {
                edgeMetrics.add(new SopEdge("node" + (i - 1), "node" + i, 50L * i, 25L * i));
            }
        }

        when(aiobSOPService.listSOPMeta(anyString(), anyString(), anyString())).thenReturn(metaResponse);
        when(aiobSopMetricService.wholeNodeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(nodeMetrics);
        when(aiobSopMetricService.wholeEdgeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(edgeMetrics);
        when(aiobSopMetricService.wholeIntentMetric(anyString(), anyList(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Collections.emptyList());
        when(aiobSopMetricService.nodeMetricWithIds(anyString(), anyList(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(new Long[]{100L, 200L});

        // Act
        SopSankeyWholeResponse response = aiobSopStatisticService.sankeyWholeData(tenantId, request1);

        // Assert
        assertEquals(4, response.getSteps().size()); // 3 steps + 意向分析
        assertEquals(2, response.getEdges().size());
        assertEquals("step1", response.getSteps().get(0).getStepId());
        assertEquals("step3", response.getSteps().get(2).getStepId());
    }

    @Test
    void testSankeyWholeData_WhenManualNotChecked_ReturnsEmptyResponse() {
        // Arrange
        SopSankeyMetaResponse metaResponse = new SopSankeyMetaResponse();
        metaResponse.setManualChecked(false);
        metaResponse.setSopMeta(Collections.emptyList());

        when(aiobSOPService.listSOPMeta(anyString(), anyString(), anyString())).thenReturn(metaResponse);

        // Act
        SopSankeyWholeResponse response = aiobSopStatisticService.sankeyWholeData(tenantId, request1);

        // Assert
        assertTrue(response.getSteps().isEmpty());
        assertTrue(response.getEdges().isEmpty());
        verify(aiobSOPService).listSOPMeta(tenantId, request1.getTaskId(), request1.getRobotVer());
    }

    @Test
    void testSankeyWholeData_WhenNoNodeMetrics_ReturnsDefaultResponse() {
        // Arrange
        SopSankeyMetaResponse metaResponse = new SopSankeyMetaResponse();
        metaResponse.setManualChecked(true);

        SOPMetaInternal meta = new SOPMetaInternal();
        meta.setStepId("step1");
        meta.setStepName("Step 1");
        SOPMetaInternal.SopMetaNode node = new SOPMetaInternal.SopMetaNode();
        node.setNodeId("node1");
        meta.setNodes(List.of(node));

        metaResponse.setSopMeta(List.of(meta));

        when(aiobSOPService.listSOPMeta(anyString(), anyString(), anyString())).thenReturn(metaResponse);
        when(aiobSopMetricService.wholeNodeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Collections.emptyList());

        // Act
        SopSankeyWholeResponse response = aiobSopStatisticService.sankeyWholeData(tenantId, request1);

        // Assert
        assertEquals(1, response.getSteps().size());
        assertEquals("step1", response.getSteps().get(0).getStepId());
        assertTrue(response.getEdges().isEmpty());
    }

    @Test
    void testSankeyWholeData_WithIntentMetrics_ReturnsResponseWithIntentStep() {
        // Arrange
        SopSankeyMetaResponse metaResponse = new SopSankeyMetaResponse();
        metaResponse.setManualChecked(true);

        SOPMetaInternal meta = new SOPMetaInternal();
        meta.setStepId("step1");
        meta.setStepName("Step 1");
        SOPMetaInternal.SopMetaNode node = new SOPMetaInternal.SopMetaNode();
        node.setNodeId("node1");
        meta.setNodes(List.of(node));
        metaResponse.setSopMeta(List.of(meta));

        SopNode nodeMetric = new SopNode("node1", 100L, 200L, 50, 25, "");
        SopEdge edgeMetric = new SopEdge("node1", "node2", 50L, 25L);
        SopIntent intentMetric = new SopIntent();
        intentMetric.setNodeId("node1");
        intentMetric.setIntent("intent1");
        intentMetric.setUv(10L);
        intentMetric.setPv(20L);

        when(aiobSOPService.listSOPMeta(anyString(), anyString(), anyString())).thenReturn(metaResponse);
        when(aiobSopMetricService.wholeNodeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(List.of(nodeMetric));
        when(aiobSopMetricService.wholeEdgeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(List.of(edgeMetric));
        when(aiobSopMetricService.wholeIntentMetric(anyString(), anyList(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(List.of(intentMetric));
        when(aiobSopMetricService.nodeMetricWithIds(anyString(), anyList(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(new Long[]{100L, 200L});

        // Act
        SopSankeyWholeResponse response = aiobSopStatisticService.sankeyWholeData(tenantId, request1);

        // Assert
        assertEquals(2, response.getSteps().size());
        assertEquals("意向分析", response.getSteps().get(1).getStepName());
        assertFalse(response.getEdges().isEmpty());
    }

    @Test
    void testSankeyWholeData_WithMultipleEdges_CalculatesPercentCorrectly() {
        // Arrange
        SopSankeyMetaResponse metaResponse = new SopSankeyMetaResponse();
        metaResponse.setManualChecked(true);

        SOPMetaInternal meta = new SOPMetaInternal();
        meta.setStepId("step1");
        meta.setStepName("Step 1");
        SOPMetaInternal.SopMetaNode node1 = new SOPMetaInternal.SopMetaNode();
        node1.setNodeId("node1");
        SOPMetaInternal.SopMetaNode node2 = new SOPMetaInternal.SopMetaNode();
        node2.setNodeId("node2");
        meta.setNodes(List.of(node1, node2));
        metaResponse.setSopMeta(List.of(meta));

        SopNode nodeMetric1 = new SopNode("node1", 100L, 200L, 0, 0, "");
        SopNode nodeMetric2 = new SopNode("node2", 50L, 100L, 0, 0, "");
        SopEdge edgeMetric1 = new SopEdge("node1", "node2", 50L, 25L);
        SopEdge edgeMetric2 = new SopEdge("node1", "node3", 50L, 25L);

        when(aiobSOPService.listSOPMeta(anyString(), anyString(), anyString())).thenReturn(metaResponse);
        when(aiobSopMetricService.wholeNodeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(List.of(nodeMetric1, nodeMetric2));
        when(aiobSopMetricService.wholeEdgeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(List.of(edgeMetric1, edgeMetric2));
        when(aiobSopMetricService.nodeMetricWithIds(anyString(), anyList(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(new Long[]{100L, 200L});

        // Act
        SopSankeyWholeResponse response = aiobSopStatisticService.sankeyWholeData(tenantId, request1);

        // Assert
        assertEquals(100, response.getSteps().get(0).getNodes().stream()
                .filter(n -> n.getNodeId().equals("node2"))
                .findFirst()
                .get()
                .getUvPercent());
    }

    @Test
    void testSankeyWholeData_WithEmptyMeta_ReturnsEmptyResponse() {
        // Arrange
        SopSankeyMetaResponse metaResponse = new SopSankeyMetaResponse();
        metaResponse.setManualChecked(false);

        when(aiobSOPService.listSOPMeta(anyString(), anyString(), anyString())).thenReturn(metaResponse);

        // Act
        SopSankeyWholeResponse response = aiobSopStatisticService.sankeyWholeData(tenantId, request1);

        // Assert
        assertTrue(response.getSteps().isEmpty());
        assertTrue(response.getEdges().isEmpty());
    }

    @Test
    void testSankeyWholeData_WithEmptyNodeMetrics_ReturnsDefaultResponse() {
        // Arrange
        SopSankeyMetaResponse metaResponse = new SopSankeyMetaResponse();
        metaResponse.setManualChecked(true);

        SOPMetaInternal meta = new SOPMetaInternal();
        meta.setStepId("step1");
        meta.setStepName("Step 1");
        SOPMetaInternal.SopMetaNode node = new SOPMetaInternal.SopMetaNode();
        node.setNodeId("node1");
        meta.setNodes(List.of(node));
        metaResponse.setSopMeta(List.of(meta));

        when(aiobSOPService.listSOPMeta(anyString(), anyString(), anyString())).thenReturn(metaResponse);
        when(aiobSopMetricService.wholeNodeMetric(anyString(), anyString(), anyString(), anyString(), anyString(), any(), any()))
                .thenReturn(Collections.emptyList());

        // Act
        SopSankeyWholeResponse response = aiobSopStatisticService.sankeyWholeData(tenantId, request1);

        // Assert
        assertEquals(1, response.getSteps().size());
        assertEquals("step1", response.getSteps().get(0).getStepId());
        assertTrue(response.getEdges().isEmpty());
    }

    @Test
    public void testSankeyWholeDataNoAllNodeIds() {
        tenantId = "testTenant";
        request = new SopFlexibleWholeRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");
        request.setTopicId("topic1");
        request.setStartTime(new Date());
        request.setEndTime(new Date());
        mockNodes = List.of(new SopNode("node1", 1L, 1L, 0, 0, ""),
                new SopNode("node2", 1L, 1L, 0, 0, ""));
        mockEdges = List.of(new SopEdge("node1", "node2", 1L));
        request1 = new SopSankeyWholeRequest();
        request1.setTaskId("task1");
        request1.setRobotId("robot1");
        request1.setRobotVer("1.0");
        request1.setTopicId("topic1");
        request1.setStartTime(new Date());
        request1.setEndTime(new Date());

        String tenantId = "tenant1";
        SopSankeyWholeRequest request = new SopSankeyWholeRequest();
        request.setTaskId("task1");
        request.setRobotVer("ver1");
        request.setRobotId("robot1");
        request.setTopicId("topic1");
        request.setStartTime(new Date());
        request.setEndTime(new Date());
        SopSankeyMetaResponse sopSankeyMetaResponse = new SopSankeyMetaResponse();
        sopSankeyMetaResponse.setManualChecked(true);
        sopSankeyMetaResponse.setSopMeta(Lists.newArrayList(
                new SOPMetaInternal("step1", "Step 1", Collections.emptyList())
        ));
        when(aiobSOPService.listSOPMeta(tenantId, request.getTaskId(), request.getRobotVer()))
                .thenReturn(sopSankeyMetaResponse);
        SopSankeyWholeResponse response = aiobSopStatisticService.sankeyWholeData(tenantId, request);
        assertEquals(1, response.getSteps().size());
        assertEquals(Collections.emptyList(), response.getEdges());
    }

    @Test
    public void testNodeDetailStatistics() {
        MockitoAnnotations.openMocks(this);

        // Arrange
        String tenantId = "tenant1";
        SopNodeDetailRequest request = new SopNodeDetailRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("ver1");
        request.setCurrNodeId("node1");
        request.setTopicId("topic1");

        AiobSopMetricService aiobSopMetricService = Mockito.mock(AiobSopMetricService.class);
        Integer robotScene = 5;
        when(aiobSopMetricService.checkTaskSceneType(tenantId, request.getTaskId())).thenReturn(robotScene);

        List<String> previewNodeIds = Lists.newArrayList("node2", "node3");
        when(aiobSopMetricService.getAllPreviewNodes(tenantId, request.getCurrNodeId(), request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime()))
                .thenReturn(previewNodeIds);

        List<SopNode> previewNodeMetrics = Lists.newArrayList();
        SopNode node1 = new SopNode();
        node1.setNodeId("node2");
        node1.setUv(100L);
        node1.setPv(50L);
        previewNodeMetrics.add(node1);

        SopNode node2 = new SopNode();
        node2.setNodeId("node3");
        node2.setUv(50L);
        node2.setPv(25L);
        previewNodeMetrics.add(node2);

        List<String> nodeIds = Lists.newArrayList("node1", "node2", "node3");
        when(aiobSopMetricService.wholeNodeMetricWithIds(tenantId, nodeIds, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime()))
                .thenReturn(previewNodeMetrics);

        List<SopNode> currNodeToForwardNodeMetrics = Lists.newArrayList();
        SopNode currNode = new SopNode();
        currNode.setNodeId("node1");
        currNode.setUv(200L);
        currNode.setPv(100L);
        currNodeToForwardNodeMetrics.add(currNode);

        when(aiobSopMetricService.currNodeToForwardNodeEdgeMetric(tenantId, request.getCurrNodeId(), request.getTaskId(),
                request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime()))
                .thenReturn(currNodeToForwardNodeMetrics);

        List<SopEdge> previewNodeToCurrNodeEdgeMetricList = Lists.newArrayList();
        SopEdge edge1 = new SopEdge();
        edge1.setFromNodeId("node2");
        edge1.setEndNodeId("node1");
        edge1.setWeight(50L);
        edge1.setWeightUv(25L);
        previewNodeToCurrNodeEdgeMetricList.add(edge1);

        when(aiobSopMetricService.previewNodeToCurrNodeEdgeMetric(tenantId, previewNodeIds, request.getCurrNodeId(), request.getTaskId(), request.getRobotId(),
                request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime()))
                .thenReturn(previewNodeToCurrNodeEdgeMetricList);

        List<SopNodeDailyDistribute> dailyCurrNodeToForwardMetrics = Lists.newArrayList();
        SopNodeDailyDistribute daily1 = new SopNodeDailyDistribute();
        daily1.setDate("2021-01-01");
        List<SopNode> nodes1 = Lists.newArrayList();
        SopNode node3 = new SopNode();
        node3.setNodeId("node3");
        node3.setUv(50L);
        node3.setPv(25L);
        nodes1.add(node3);
        daily1.setNodes(nodes1);
        dailyCurrNodeToForwardMetrics.add(daily1);

        when(aiobSopMetricService.dailyCurrNodeMetric(tenantId, request.getCurrNodeId(), request.getTaskId(),
                request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime()))
                .thenReturn(Collections.singletonMap("2021-01-01", currNode));

        when(aiobSopMetricService.dailyCurrNodeToForwardNodeEdgeMetric(tenantId, request.getCurrNodeId(), request.getTaskId(),
                request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime()))
                .thenReturn(dailyCurrNodeToForwardMetrics);

        Map<String, SopNode> nodeHangupMetricMap = new HashMap<>();
        SopNode hangupNode = new SopNode();
        hangupNode.setNodeId("node1");
        hangupNode.setUv(50L);
        hangupNode.setPv(25L);
        nodeHangupMetricMap.put("node1", hangupNode);

        when(aiobSopMetricService.wholeNodeHangupMetric(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(),
                request.getTopicId(), request.getStartTime(), request.getEndTime()))
                .thenReturn(nodeHangupMetricMap);

        // Act
        SopNodeDetailResponse response = aiobSopStatisticService.nodeDetailStatistics(tenantId, request);

        // Assert
        assertEquals(0, response.getNodes().size());
        assertEquals(0, response.getEdges().size());
        assertEquals(0, response.getDailyDistribute().size());
    }

    @Test
    void testCompleteHangup() {
        tenantId = "testTenant";
        SopNodeDetailRequest request = new SopNodeDetailRequest();
        request.setTaskId("task1");
        request.setRobotId("robot1");
        request.setRobotVer("1.0");
        request.setTopicId("topic1");
        request.setStartTime(new Date());
        request.setEndTime(new Date());

        mockNodes = List.of(
                new SopNode("node1", 1L, 1L, 0, 0, ""),
                new SopNode("node2", 1L, 1L, 0, 0, "")
        );

        // Mock the behavior of aiobSopMetricService
        Map<String, SopNode> nodeHangupMetricMap = Map.of(
                "node1", new SopNode("node1", 1L, 1L, 1, 1, ""),
                "node2", new SopNode("node2", 2L, 2L, 2, 2, "")
        );
        when(aiobSopMetricService.wholeNodeHangupMetric(
                tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime()))
                .thenReturn(nodeHangupMetricMap);

        // Call the method under test
        List<SopNodeWithHangup> result = aiobSopStatisticService.completeHangup(tenantId, request, mockNodes);

        // Verify the results
        assertEquals(2, result.size());
        assertEquals("node1", result.get(0).getNodeId());
        assertEquals(1L, result.get(0).getUv());
        assertEquals(1L, result.get(0).getPv());
        assertEquals(100, result.get(0).getHangup().getUvPercent());
        assertEquals(100, result.get(0).getHangup().getPvPercent());

        assertEquals("node2", result.get(1).getNodeId());
        assertEquals(1L, result.get(1).getUv());
        assertEquals(1L, result.get(1).getPv());
        assertEquals(200, result.get(1).getHangup().getUvPercent());
        assertEquals(200, result.get(1).getHangup().getPvPercent());

        // Verify that the wholeNodeHangupMetric method was called
        verify(aiobSopMetricService, times(1)).wholeNodeHangupMetric(
                tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());
    }
}