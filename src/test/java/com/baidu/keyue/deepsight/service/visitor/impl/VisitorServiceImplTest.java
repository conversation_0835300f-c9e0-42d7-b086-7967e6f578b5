package com.baidu.keyue.deepsight.service.visitor.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.visitor.request.*;
import com.baidu.keyue.deepsight.models.visitor.request.VisitorSaveRequest;
import com.baidu.keyue.deepsight.models.visitor.response.*;
import com.baidu.keyue.deepsight.models.visitor.response.VisitorSaveResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.utils.AESUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.*;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class VisitorServiceImplTest{


    private VisitorSaveRequest saveRequest;

    private Map<String, Object> testData;

    private DataTableInfo tableInfo;

    private List<Map<String, Object>> queryResults;

    private List<VisibleFieldResponse> visibleFields;

    private final String tenantId = "123456";

    private final String tableName = "mock_user_123456";

    private final String userId = "123";

    @Mock
    private DataTableManageService dataTableManageService;
    @Mock
    private DorisService dorisService;

    @InjectMocks
    private VisitorServiceImpl visitorService;

    @Mock
    private TableRecordCommonService tableRecordCommonService;

    @BeforeEach
    void setUp() {
        visitorService.init();
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(Long.parseLong(userId));
        userAuthInfo.setTenantId(Long.parseLong(tenantId));
        WebContextHolder.setDeepSightWebContext(new DeepSightWebContext(userAuthInfo));
    }

    @Test
    void testInsert() {
        try (MockedStatic<TenantUtils> tenantUtilsMocked = mockStatic(TenantUtils.class)) {
            tenantUtilsMocked.when(() -> TenantUtils.generateMockUserTableName(tenantId)).thenReturn(tableName);
            
            VisitorSaveRequest request = new VisitorSaveRequest();
            request.setTenantId(tenantId);
            request.setIsDorisData(false);
            
            Map<String, Object> data = new HashMap<>();
            data.put("customer_name", "test");
            data.put("source", "web");
            data.put("mobile", List.of(13800138000L));
            request.setData(data);
            
            DataTableInfo tableInfo = new DataTableInfo();
            tableInfo.setId(1L);
            when(tableRecordCommonService.getTableByTableName(tableName)).thenReturn(tableInfo);
            
            Map<String, String> encryptFields = new HashMap<>();
            encryptFields.put("mobile", "secret");
            when(tableRecordCommonService.getEncryptFields(1L)).thenReturn(encryptFields);
            
            VisitorSaveResponse response = visitorService.insert(request);
            assertNotNull(response);
            assertNotNull(response.getId());
        }
    }

    @Test
    void testCovertToUserData() {
        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setId(1L);
        when(tableRecordCommonService.getTableByTableName("mock_table")).thenReturn(tableInfo);
        
        Map<String, String> encryptFields = new HashMap<>();
        encryptFields.put("mobile", "secret");
        when(tableRecordCommonService.getEncryptFields(1L)).thenReturn(encryptFields);
        
        Map<String, Object> data = new HashMap<>();
        data.put("customer_name", "test");
        data.put("mobile", List.of(13800138000L));
        data.put("mail", List.of("<EMAIL>"));
        data.put("age", 30);
        
        Map<String, Object> result = visitorService.covertToUserData(data, tableInfo.getId(), false);
        data.put("age", null);
        Map<String, Object> resultFull = visitorService.covertToUserData(data, tableInfo.getId(), true);
        assertNotNull(result);
        assertEquals("test", result.get("user_name"));
        assertTrue(result.containsKey("mobile_list"));
        assertTrue(result.containsKey("email_list"));
        assertEquals(30, result.get("age"));
        assertTrue(resultFull.containsKey("age"));
    }

    @Test
    void testCheckVisitorDataForInsert() {
        Map<String, Object> validData = new HashMap<>();
        validData.put("customer_name", "test");
        validData.put("source", "web");
        validData.put("mobile", List.of(13800138000L));
        
        assertDoesNotThrow(() -> visitorService.checkVisitorData(validData, true));
        
        Map<String, Object> invalidData = new HashMap<>();
        assertThrows(DeepSightException.ParamsErrorException.class, 
            () -> visitorService.checkVisitorData(invalidData, true));
    }

    @Test
    void testCheckVisitorDataFail() {
        Map<String, Object> validData = new HashMap<>();
        validData.put("customer_name", "test");
        validData.put("source", "web");
        validData.put("age", "");
        validData.put("mobile", List.of(13800138000L));
        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> visitorService.checkVisitorData(validData, true));
    }

    @Test
    void testDeleteWithId() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtilsMocked = mockStatic(TenantUtils.class)) {
            
            mocked.when(WebContextHolder::getTenantId).thenReturn("tenant1");
            tenantUtilsMocked.when(() -> TenantUtils.generateMockUserTableName("tenant1")).thenReturn("mock_table");
            
            VisitorDeleteRequest request = new VisitorDeleteRequest();
            request.setId("123");
            
            assertDoesNotThrow(() -> visitorService.delete(request));
        }
    }

    @Test
    void testHandleSingle() {
        assertEquals("'test'", visitorService.handleSingle("test"));
        assertEquals("123", visitorService.handleSingle(123));
        assertEquals("['a','b']", visitorService.handleSingle(List.of("a", "b")));
        Date date = new Date(1744362895261L);
        LocalDateTime localDateTime = LocalDateTime.of(2025, 4, 11, 17, 14, 55);
        assertEquals("'2025-04-11 17:14:55'", visitorService.handleSingle(date));
        assertEquals("'2025-04-11 17:14:55'", visitorService.handleSingle(localDateTime));
    }

    @Test
    void testInit() {
        assertNotNull(visitorService);
    }

    @Test
    void testQueryListWithId() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtilsMocked = mockStatic(TenantUtils.class)) {
            
            mocked.when(WebContextHolder::getTenantId).thenReturn("tenant1");
            tenantUtilsMocked.when(() -> TenantUtils.generateMockUserTableName("tenant1")).thenReturn("mock_table");
            
            VisitorQueryRequest request = new VisitorQueryRequest();
            request.setId("123");
            
            DataTableInfo tableInfo = new DataTableInfo();
            tableInfo.setId(1L);
            when(tableRecordCommonService.getTableByTableName("mock_table")).thenReturn(tableInfo);
            
            Map<String, String> encryptFields = new HashMap<>();
            encryptFields.put("mobile", "secret");
            when(tableRecordCommonService.getEncryptFields(1L)).thenReturn(encryptFields);
            
            List<Map<String, Object>> mockResult = new ArrayList<>();
            Map<String, Object> data = new HashMap<>();
            data.put("user_id", "123");
            data.put("user_name", "test");
            mockResult.add(data);
            when(dorisService.selectList(anyString())).thenReturn(mockResult);
            
            VisitorQueryResponse response = visitorService.queryList(request);
            assertNotNull(response);
            assertEquals(1, response.getDataList().size());
        }
    }

    @Test
    void testCovertToVisitorData() {
        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setId(1L);
        when(tableRecordCommonService.getTableByTableName("mock_table")).thenReturn(tableInfo);
        
        Map<String, String> encryptFields = new HashMap<>();
        encryptFields.put("mobile", "secret");
        when(tableRecordCommonService.getEncryptFields(1L)).thenReturn(encryptFields);
        
        Map<String, Object> data = new HashMap<>();
        data.put("user_id", "123");
        data.put("user_name", "test");
        data.put("mobile_list", List.of(AESUtils.encrypt("13800138000", "secret")));
        data.put("email_list", List.of("<EMAIL>"));
        data.put("age", 30);
        
        Map<String, Object> result = visitorService.covertToVisitorData(data, "mock_table");
        assertNotNull(result);
        assertEquals("123", result.get("id"));
        assertEquals("test", result.get("customer_name"));
        assertTrue(result.containsKey("mobile"));
    }

    @Test
    void updateShouldSuccessWhenValidRequest() {
        testData = new HashMap<>();
        testData.put("id", "123");
        testData.put("customer_name", "test customer");
        testData.put("age", 30);
        testData.put("mobile", List.of(13800138000L));
        testData.put("mail", List.of("<EMAIL>"));
    
        saveRequest = new VisitorSaveRequest();
        saveRequest.setData(testData);
        saveRequest.setFullUpdate(false);
    
        tableInfo = new DataTableInfo();
        tableInfo.setId(1L);
    
        Map<String, Object> oldData = new HashMap<>();
        oldData.put("user_id", "123");
        oldData.put("name", "old name");
        oldData.put("age", 25);
        oldData.put("mobile_list", List.of("encrypted_mobile"));
        oldData.put("email_list", List.of("<EMAIL>"));
        queryResults = List.of(oldData);
    
        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("name");
        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("age");
        VisibleFieldResponse field3 = new VisibleFieldResponse();
        field3.setEnName("mobile_list");
        VisibleFieldResponse field4 = new VisibleFieldResponse();
        field4.setEnName("email_list");
        visibleFields = List.of(field1, field2, field3, field4);
    
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
            
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            tenantUtils.when(() -> TenantUtils.generateMockUserTableName("test_tenant")).thenReturn("mock_user_table");
    
            when(dorisService.selectList(anyString())).thenReturn(queryResults);
            when(tableRecordCommonService.getTableByTableName(anyString())).thenReturn(tableInfo);
            when(tableRecordCommonService.getEncryptFields(anyLong())).thenReturn(Map.of("mobile", "encrypt_key"));
            when(dataTableManageService.getVisibleFields(anyLong(), eq(false))).thenReturn(visibleFields);
            
            VisitorSaveResponse response = visitorService.update(saveRequest);
            
            assertEquals("123", response.getId());
            verify(dorisService).execSql(contains("UPDATE mock_user_table SET"));
        }
    }

    @Test
    void updateShouldThrowWhenTenantIdIsBlank() {
        testData = new HashMap<>();
        testData.put("id", "123");
        testData.put("customer_name", "test customer");
        testData.put("age", 30);
        testData.put("mobile", List.of(13800138000L));
        testData.put("mail", List.of("<EMAIL>"));
    
        saveRequest = new VisitorSaveRequest();
        saveRequest.setData(testData);
        saveRequest.setFullUpdate(false);
    
        tableInfo = new DataTableInfo();
        tableInfo.setId(1L);
    
        Map<String, Object> oldData = new HashMap<>();
        oldData.put("user_id", "123");
        oldData.put("name", "old name");
        oldData.put("age", 25);
        oldData.put("mobile_list", List.of("encrypted_mobile"));
        oldData.put("email_list", List.of("<EMAIL>"));
        queryResults = List.of(oldData);
    
        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("name");
        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("age");
        VisibleFieldResponse field3 = new VisibleFieldResponse();
        field3.setEnName("mobile_list");
        VisibleFieldResponse field4 = new VisibleFieldResponse();
        field4.setEnName("email_list");
        visibleFields = List.of(field1, field2, field3, field4);
    
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("");

            DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class, 
                () -> visitorService.update(saveRequest));
            assertEquals(ErrorCode.BAD_REQUEST, exception.getErrorCode());
            assertEquals("租户ID不能为空", exception.getMessage());
        }
    }

    @Test
    void updateShouldThrowWhenIdIsMissing() {
        testData = new HashMap<>();
        testData.put("id", "123");
        testData.put("customer_name", "test customer");
        testData.put("age", 30);
        testData.put("mobile", List.of(13800138000L));
        testData.put("mail", List.of("<EMAIL>"));
    
        saveRequest = new VisitorSaveRequest();
        saveRequest.setData(testData);
        saveRequest.setFullUpdate(false);
    
        tableInfo = new DataTableInfo();
        tableInfo.setId(1L);
    
        Map<String, Object> oldData = new HashMap<>();
        oldData.put("user_id", "123");
        oldData.put("name", "old name");
        oldData.put("age", 25);
        oldData.put("mobile_list", List.of("encrypted_mobile"));
        oldData.put("email_list", List.of("<EMAIL>"));
        queryResults = List.of(oldData);
    
        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("name");
        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("age");
        VisibleFieldResponse field3 = new VisibleFieldResponse();
        field3.setEnName("mobile_list");
        VisibleFieldResponse field4 = new VisibleFieldResponse();
        field4.setEnName("email_list");
        visibleFields = List.of(field1, field2, field3, field4);
    
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            testData.remove("id");

            DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class, 
                () -> visitorService.update(saveRequest));
            assertEquals(ErrorCode.BAD_REQUEST, exception.getErrorCode());
            assertEquals("id不能为空", exception.getMessage());
        }
    }

    @Test
    void updateShouldThrowWhenDataNotExist() {
        testData = new HashMap<>();
        testData.put("id", "123");
        testData.put("customer_name", "test customer");
        testData.put("age", 30);
        testData.put("mobile", List.of(13800138000L));
        testData.put("mail", List.of("<EMAIL>"));
    
        saveRequest = new VisitorSaveRequest();
        saveRequest.setData(testData);
        saveRequest.setFullUpdate(false);
    
        tableInfo = new DataTableInfo();
        tableInfo.setId(1L);
    
        Map<String, Object> oldData = new HashMap<>();
        oldData.put("user_id", "123");
        oldData.put("name", "old name");
        oldData.put("age", 25);
        oldData.put("mobile_list", List.of("encrypted_mobile"));
        oldData.put("email_list", List.of("<EMAIL>"));
        queryResults = List.of(oldData);
    
        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("name");
        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("age");
        VisibleFieldResponse field3 = new VisibleFieldResponse();
        field3.setEnName("mobile_list");
        VisibleFieldResponse field4 = new VisibleFieldResponse();
        field4.setEnName("email_list");
        visibleFields = List.of(field1, field2, field3, field4);
    
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
            
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            tenantUtils.when(() -> TenantUtils.generateMockUserTableName("test_tenant")).thenReturn("mock_user_table");
            when(dorisService.selectList(anyString())).thenReturn(Collections.emptyList());

            DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class, 
                () -> visitorService.update(saveRequest));
            assertEquals(ErrorCode.BAD_REQUEST, exception.getErrorCode());
            assertEquals("更新数据不存在", exception.getMessage());
        }
    }

    @Test
    void updateShouldHandleFullUpdateCorrectly() {
        testData = new HashMap<>();
        testData.put("id", "123");
        testData.put("customer_name", "test customer");
        testData.put("age", 30);
        testData.put("mobile", List.of(13800138000L));
        testData.put("mail", List.of("<EMAIL>"));
    
        saveRequest = new VisitorSaveRequest();
        saveRequest.setData(testData);
        saveRequest.setFullUpdate(false);
    
        tableInfo = new DataTableInfo();
        tableInfo.setId(1L);
    
        Map<String, Object> oldData = new HashMap<>();
        oldData.put("user_id", "123");
        oldData.put("name", "old name");
        oldData.put("age", 25);
        oldData.put("mobile_list", List.of("encrypted_mobile"));
        oldData.put("email_list", List.of("<EMAIL>"));
        queryResults = List.of(oldData);
    
        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("name");
        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("age");
        VisibleFieldResponse field3 = new VisibleFieldResponse();
        field3.setEnName("mobile_list");
        VisibleFieldResponse field4 = new VisibleFieldResponse();
        field4.setEnName("email_list");
        visibleFields = List.of(field1, field2, field3, field4);
    
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
            
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            tenantUtils.when(() -> TenantUtils.generateMockUserTableName("test_tenant")).thenReturn("mock_user_table");
            saveRequest.setFullUpdate(true);
            
            when(dorisService.selectList(anyString())).thenReturn(queryResults);
            when(tableRecordCommonService.getTableByTableName(anyString())).thenReturn(tableInfo);
            when(tableRecordCommonService.getEncryptFields(anyLong())).thenReturn(Map.of("mobile", "encrypt_key"));
            when(dataTableManageService.getVisibleFields(anyLong(), eq(false))).thenReturn(visibleFields);
            
            VisitorSaveResponse response = visitorService.update(saveRequest);
            
            assertEquals("123", response.getId());
            verify(dorisService).execSql(contains("email_list = ['<EMAIL>']"));
        }
    }

    @Test
    void updateShouldSkipNullValuesWhenNotFullUpdate() {
        testData = new HashMap<>();
        testData.put("id", "123");
        testData.put("customer_name", "test customer");
        testData.put("age", 30);
        testData.put("mobile", List.of(13800138000L));
        testData.put("mail", List.of("<EMAIL>"));
    
        saveRequest = new VisitorSaveRequest();
        saveRequest.setData(testData);
        saveRequest.setFullUpdate(false);
    
        tableInfo = new DataTableInfo();
        tableInfo.setId(1L);
    
        Map<String, Object> oldData = new HashMap<>();
        oldData.put("user_id", "123");
        oldData.put("name", "old name");
        oldData.put("age", 25);
        oldData.put("mobile_list", List.of("encrypted_mobile"));
        oldData.put("email_list", List.of("<EMAIL>"));
        queryResults = List.of(oldData);
    
        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("name");
        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("age");
        VisibleFieldResponse field3 = new VisibleFieldResponse();
        field3.setEnName("mobile_list");
        VisibleFieldResponse field4 = new VisibleFieldResponse();
        field4.setEnName("email_list");
        visibleFields = List.of(field1, field2, field3, field4);
    
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
            
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            tenantUtils.when(() -> TenantUtils.generateMockUserTableName("test_tenant")).thenReturn("mock_user_table");
            testData.put("age", null);
            
            when(dorisService.selectList(anyString())).thenReturn(queryResults);
            when(tableRecordCommonService.getTableByTableName(anyString())).thenReturn(tableInfo);
            when(tableRecordCommonService.getEncryptFields(anyLong())).thenReturn(Map.of("mobile", "encrypt_key"));
            when(dataTableManageService.getVisibleFields(anyLong(), eq(false))).thenReturn(visibleFields);
            
            VisitorSaveResponse response = visitorService.update(saveRequest);
            
            assertEquals("123", response.getId());
        }
    }

}