package com.baidu.keyue.deepsight.service.impl;

import com.baidu.keyue.deepsight.BaseTest;
import com.baidu.keyue.deepsight.enums.SwitchEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.predict.PredictConfig;
import com.baidu.keyue.deepsight.models.predict.PredictDataSource;
import com.baidu.keyue.deepsight.models.predict.PredictSwitchUpdateRequest;
import com.baidu.keyue.deepsight.service.dataprediction.impl.DataPredictionServiceImpl;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.kybase.commons.utils.JsonUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import java.io.Serializable;
import java.util.Base64;
import java.util.UUID;

public class DataPredictionServiceImplCustomTest extends BaseTest {
    @Autowired
    private DataPredictionServiceImpl dataPredictionService;

    @Ignore
    @Test
    public void getDateSourceDetail() {
        String tenantId = "test1";
        PredictDataSource dateSourceDetail = dataPredictionService.getDataSourceDetail(tenantId, "test1");
        System.out.println(JsonUtil.toJson(dateSourceDetail));
    }

    @Ignore
    @Test
    public void updateDataSource() {
        String s = "{\"dataSourceList\":[{\"datasetId\":1,\"datasetName\":\"test\",\"fieldId\":0,\"fieldName\":\"test\"}],\"promptType\":\"CUSTOM\",\"prompt\":\"test prompt\",\"predictionUpdateType\":\"REPLACE\",\"triggerFrequency\":\"REALTIME\",\"triggerFrequencyValue\":{\"hour\":0,\"dayOfMonth\":0,\"dayOfWeek\":0},\"description\":\"test desc\"}";
        PredictDataSource request = JsonUtil.fromJson(s, PredictDataSource.class);
        String userId = "test modify";
        String tenantId = "test2";
        dataPredictionService.updateDataSource(request, userId, tenantId);
    }

    @Ignore
    @Test
    public void initTenantDateSource() {
        String tenantId = UUID.randomUUID().toString();
        String userId = "test init";
        dataPredictionService.initTenantDateSourceAndConfig(tenantId, userId);
    }

    @Ignore
    @Test
    public void pageQueryPredictConfig() {
        BasePageResponse.Page<PredictConfig> pageData = dataPredictionService.pageQueryPredictConfig(1, 2,
                "bb9ddcd8-708a-42d2-911b-c5a1a6123d43", "test1");
        System.out.println(JsonUtil.toJson(pageData));
    }

    @Ignore
    @Test
    public void updatePredictSwitch() {
        PredictSwitchUpdateRequest request = new PredictSwitchUpdateRequest();
        request.setId(179L);
        request.setStatus(SwitchEnum.OFF);
        dataPredictionService.updatePredictSwitch(request, "test modify", "c505ad4b-7a0c-476f-851d-7f8e7847ae73");
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public class DorisConfig implements Serializable {
        private static final long serialVersionUID = 1L;

        private String feNodes;
        private String username;
        private String password;
        private String db;
        private String table;
        private String tablePrefix;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public class KafkaConfig implements Serializable {
        private static final long serialVersionUID = 1L;

        private String kafkaTopic;
        private String kafkaGroupId;
        private String kafkaCerBosUrl;
        private String kafkaBootstrapServers;
        private String kafkaStart;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public class DataPredictCalRequest implements Serializable {
        private static final long serialVersionUID = 1L;
        private KafkaConfig kafkaConfig;
        private DorisConfig dorisConfig;
    }
    private final Base64.Encoder encoder = Base64.getEncoder();
    @Test
    public void generateParams() {
        DataPredictCalRequest request = new DataPredictCalRequest();
        DorisConfig dorisConfig = new DorisConfig();
        dorisConfig.setFeNodes("989059752669614080-fe-service.palo.bj.baidubce.com:9030");
        dorisConfig.setUsername("admin");
        dorisConfig.setDb("deep_sight_dev");
        dorisConfig.setPassword("znkf_2024");

        KafkaConfig kafkaConfig = new KafkaConfig();
        kafkaConfig.setKafkaTopic("deep_sight_data_predict_dev");
        kafkaConfig.setKafkaGroupId("flink-data-predict");
        kafkaConfig.setKafkaCerBosUrl("https://customer-insight-dev.bj.bcebos.com/v1/kafka/kafka-key-dev.zip?authorization=bce-auth-v1%2FALTAKmniqTxNtiypuTZfCvKaxy%2F2025-02-19T02%3A17%3A00Z%2F-1%2Fhost%2F8037f8265133d654586ad58cfc4239ff72d2bdc24820e8d4b0562dae37dae521");
        kafkaConfig.setKafkaBootstrapServers("***********:9097,***********:9097,***********:9097");
        kafkaConfig.setKafkaStart("latest");
        request.setDorisConfig(dorisConfig);
        request.setKafkaConfig(kafkaConfig);
        String params = JsonUtils.toJsonWithOutException(request);
        String paramsBase64 = encoder.encodeToString(params.getBytes());
        System.out.println(paramsBase64);
    }

    @Test
    public void testSql() {
        String tables = "mock_user_test_162004539\n";
        for (String table : tables.split("\n")) {
            String sql = "ALTER TABLE xxxxx\n" +
                    "    ADD COLUMN merge_gender VARCHAR(8) NULL DEFAULT '' COMMENT '合并性别',\n" +
                    "    ADD COLUMN merge_age_group VARCHAR(32) NULL DEFAULT '' COMMENT '合并年龄',\n" +
                    "    ADD COLUMN merge_life_stage VARCHAR(32) NULL DEFAULT '' COMMENT '合并人生阶段',\n" +
                    "    ADD COLUMN merge_marriage_status VARCHAR(8) NULL DEFAULT '' COMMENT '合并婚姻状况',\n" +
                    "    ADD COLUMN merge_industry VARCHAR(32) NULL DEFAULT '' COMMENT  '合并所在行业',\n" +
                    "    ADD COLUMN merge_education_level VARCHAR(32) NULL DEFAULT '' COMMENT '合并教育水平',\n" +
                    "    ADD COLUMN merge_occupation VARCHAR(32) NULL DEFAULT '' COMMENT '合并职业类别',\n" +
                    "    ADD COLUMN merge_consume_level VARCHAR(8) NULL DEFAULT '' COMMENT '合并消费水平',\n" +
                    "    ADD COLUMN merge_consume_intent VARCHAR(8) NULL DEFAULT '' COMMENT '合并消费意愿',\n" +
                    "    ADD COLUMN merge_geographic_location VARCHAR(128) NULL DEFAULT '' COMMENT '合并地理位置',\n" +
                    "    ADD COLUMN merge_interests VARCHAR(256) NULL DEFAULT '' COMMENT '合并兴趣关注';\n";
            sql = sql.replace("xxxxx", table);
            System.out.println(sql);
        }
    }
}
