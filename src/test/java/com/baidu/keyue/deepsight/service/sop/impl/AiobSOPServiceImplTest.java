package com.baidu.keyue.deepsight.service.sop.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.SOPStatusEnum;
import com.baidu.keyue.deepsight.enums.SopUserConfigAssistMetricEnum;
import com.baidu.keyue.deepsight.enums.SopUserConfigMainMetricEnum;
import com.baidu.keyue.deepsight.enums.SopUserConfigWarningThresholdEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.models.sop.SOPNodePredictRequest;
import com.baidu.keyue.deepsight.models.sop.SOPNodePredictResponse;
import com.baidu.keyue.deepsight.models.sop.SOPQuickNodeListResponse;
import com.baidu.keyue.deepsight.models.sop.SopBaseRequest;
import com.baidu.keyue.deepsight.models.sop.SopSankeyMetaResponse;
import com.baidu.keyue.deepsight.models.sop.SopUserConfigUpdateRequest;
import com.baidu.keyue.deepsight.models.sop.SopUserDetailRequest;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionRequest;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionResponse;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordResp;
import com.baidu.keyue.deepsight.models.sop.nodepredict.NodeProcessSummaryResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMetaCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.AiobSopMetaMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.SopUserConfigMapper;
import com.baidu.keyue.deepsight.service.datamanage.AiobRobotVersionService;
import com.baidu.keyue.deepsight.service.datamanage.impl.DataTableManageServiceImpl;
import com.baidu.keyue.deepsight.service.datamanage.impl.DorisConfServiceImpl;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.commons.utils.HttpUtil;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.context.TestPropertySource;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@TestPropertySource(properties = {
        "node-predict.node-summary-url=http://test-url",
        "aiob.diagram-record-view-url=http://test-url"
})
@TestPropertySource(properties = {
        "node-predict.node-summary-url=http://test-url",
        "aiob.diagram-record-view-url=http://test-url"
})
@TestPropertySource(properties = {"node-predict.node-summary-url=http://test-url", "aiobDiagramRecordViewUrl = http://test-url"})
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class AiobSOPServiceImplTest {


    @Mock
    private AiobRobotVersionService aiobRobotVersionService;

    private SopWholeRobotVersionRequest request1;

    private List<SopWholeRobotVersionResponse> mockVersions;

    @Mock
    private RuleManagerService ruleManagerService;

    @Mock
    private DorisConfServiceImpl dorisConfService;

    private SopUserDetailRequest request;

    @Mock
    private DorisService dorisService;

    @Mock
    private DataTableManageServiceImpl tableManageService;

    private TableFieldMetaInfo primaryKeyField;

    private VisibleFieldResponse visibleField;

    private DqlParseResult dqlParseResult;

    private AiobSopMeta meta2;

    private AiobSopMeta meta1;

    @InjectMocks
    private AiobSOPServiceImpl aiobSOPServiceImpl;
    private AiobSopMeta aiobSopMeta;

    @Mock
    private AiobSopMetaMapper sopMetaMapper;

    @Mock
    private SopUserConfigMapper sopUserConfigMapper;

    @Mock
    private AiobSOPReCalServiceImpl aiobSOPReCalService;

    @InjectMocks
    private AiobSOPServiceImpl aiobSOPService;

    @BeforeEach
    void setUp() {
        aiobSopMeta = new AiobSopMeta();
        aiobSopMeta.setTaskId("task1");
        aiobSopMeta.setManualCheck(true);
        aiobSopMeta.setDel(false);
        aiobSopMeta.setTaskRule("rule1");
        aiobSopMeta.setStepName("step1");
        aiobSopMeta.setNodeName("node1");
    }

    // testListQuickSOPNodesNonEmptyList 用于测试 listQuickSOPNodes
    // generated by Comate
    @Test
    void testListQuickSOPNodesNonEmptyList() {
        List<AiobSopMeta> aiobSopMetaList = Collections.singletonList(aiobSopMeta);
        when(sopMetaMapper.selectByExampleWithBLOBs(any(AiobSopMetaCriteria.class))).thenReturn(aiobSopMetaList);

        SOPQuickNodeListResponse response = aiobSOPService.listQuickSOPNodes("tenant1", "task1", "");

        assertEquals(1, response.getList().size());
        assertEquals("rule1", response.getRule());
    }

    // testListQuickSOPNodesEmptyList 用于测试 listQuickSOPNodes
    // generated by Comate
    @Test
    public void testListQuickSOPNodesEmptyList() {
        MockitoAnnotations.openMocks(this);

        // Arrange
        String taskId = "testTaskId";
        String tenantId = "testTenantId";
        AiobSopMetaCriteria sopMetaCriteria = new AiobSopMetaCriteria();
        sopMetaCriteria.createCriteria().andTaskIdEqualTo(taskId)
                .andManualCheckEqualTo(Boolean.TRUE)
                .andDelEqualTo(Boolean.FALSE);

        // Act
        SOPQuickNodeListResponse response = aiobSOPServiceImpl.listQuickSOPNodes(tenantId, taskId, "");

        // Assert
        assertEquals(null, response.getRule());
        assertEquals(null, response.getList());
    }

    @Test
    void testGetUserConfigSuccess() {
        SopBaseRequest request = new SopBaseRequest();
        request.setTaskId("task1");

        SopUserConfig userConfig = new SopUserConfig();
        userConfig.setTaskId("task1");
        userConfig.setTenantId("tenant1");
        userConfig.setCoreMetric((byte) 1);
        userConfig.setAssistMetric((byte) 1);
        userConfig.setWarningThreshold(5);
        userConfig.setDel(DelEnum.NOT_DELETED.getBoolean());

        when(sopUserConfigMapper.selectByExample(any(SopUserConfigCriteria.class)))
                .thenReturn(Collections.singletonList(userConfig));
        try (MockedStatic<WebContextHolder> mockedWebContextHolder = mockStatic(WebContextHolder.class)) {
            mockedWebContextHolder.when(WebContextHolder::getUserId).thenReturn("user1");

            SopUserConfigUpdateRequest response = aiobSOPService.getUserConfig("tenant1", request);

            assertNotNull(response);
            assertEquals(SopUserConfigMainMetricEnum.ARRIVAL_NUM.getCode(), response.getMainMetric());
            assertEquals(SopUserConfigAssistMetricEnum.ARRIVAL_NUM_RATIO.getCode(), response.getAssistMetric());
            assertEquals(SopUserConfigWarningThresholdEnum.PERCENT_50.getCode(), response.getWarningThreshold());
        }

    }

    @Test
    void updateUserConfigShouldThrowExceptionWhenConfigNotFound() {
        try (MockedStatic<WebContextHolder> mockedWebContextHolder = mockStatic(WebContextHolder.class)) {
            SopUserConfig userConfig = new SopUserConfig();
            userConfig.setTaskId("task1");
            userConfig.setTenantId("tenant1");
            userConfig.setDel(DelEnum.NOT_DELETED.getBoolean());

            SopUserConfigUpdateRequest request = new SopUserConfigUpdateRequest();
            request.setTaskId("task1");

            mockedWebContextHolder.when(WebContextHolder::getUserId).thenReturn("user1");

            when(sopUserConfigMapper.selectByExample(any(SopUserConfigCriteria.class)))
                    .thenReturn(Collections.emptyList());

            DeepSightException.ParamsErrorException exception = assertThrows(
                    DeepSightException.ParamsErrorException.class,
                    () -> aiobSOPService.updateUserConfig("tenant1", request)
            );

            assertEquals(ErrorCode.NOT_FOUND, exception.getErrorCode());
        }
    }

    @Test
    void updateUserConfigShouldUpdateConfigWithArrivalUserNum() {
        try (MockedStatic<WebContextHolder> mockedWebContextHolder = mockStatic(WebContextHolder.class)) {
            SopUserConfig userConfig = new SopUserConfig();
            userConfig.setTaskId("task1");
            userConfig.setTenantId("tenant1");
            userConfig.setDel(DelEnum.NOT_DELETED.getBoolean());

            SopUserConfigUpdateRequest request = new SopUserConfigUpdateRequest();
            request.setTaskId("task1");

            mockedWebContextHolder.when(WebContextHolder::getUserId).thenReturn("user1");

            request.setMainMetric(SopUserConfigMainMetricEnum.ARRIVAL_USER_NUM.getCode());
            request.setAssistMetric(SopUserConfigAssistMetricEnum.ARRIVAL_NUM_RATIO.getCode());
            request.setWarningThreshold(SopUserConfigWarningThresholdEnum.PERCENT_50.getCode());

            when(sopUserConfigMapper.selectByExample(any(SopUserConfigCriteria.class)))
                    .thenReturn(Collections.singletonList(userConfig));

            aiobSOPService.updateUserConfig("tenant1", request);

            assertEquals(SopUserConfigMainMetricEnum.ARRIVAL_USER_NUM.getCode().byteValue(), userConfig.getCoreMetric());
            assertEquals(SopUserConfigAssistMetricEnum.ARRIVAL_NUM_RATIO.getCode().byteValue(), userConfig.getAssistMetric());
            assertEquals(SopUserConfigWarningThresholdEnum.PERCENT_50.getCode(), userConfig.getWarningThreshold());
            assertEquals("user1", userConfig.getModifier());
            assertNotNull(userConfig.getUpdateTime());

            verify(sopUserConfigMapper).updateByPrimaryKeySelective(userConfig);
        }
    }

    @Test
    void updateUserConfigShouldUpdateConfigWithArrivalNum() {
        try (MockedStatic<WebContextHolder> mockedWebContextHolder = mockStatic(WebContextHolder.class)) {
            SopUserConfig userConfig = new SopUserConfig();
            userConfig.setTaskId("task1");
            userConfig.setTenantId("tenant1");
            userConfig.setDel(DelEnum.NOT_DELETED.getBoolean());

            SopUserConfigUpdateRequest request = new SopUserConfigUpdateRequest();
            request.setTaskId("task1");

            mockedWebContextHolder.when(WebContextHolder::getUserId).thenReturn("user1");

            request.setMainMetric(SopUserConfigMainMetricEnum.ARRIVAL_NUM.getCode());
            request.setAssistMetric(SopUserConfigAssistMetricEnum.ARRIVAL_NUM_RATIO.getCode());
            request.setWarningThreshold(SopUserConfigWarningThresholdEnum.PERCENT_30.getCode());

            when(sopUserConfigMapper.selectByExample(any(SopUserConfigCriteria.class)))
                    .thenReturn(Collections.singletonList(userConfig));

            aiobSOPService.updateUserConfig("tenant1", request);

            assertEquals(SopUserConfigMainMetricEnum.ARRIVAL_NUM.getCode().byteValue(), userConfig.getCoreMetric());
            assertEquals(SopUserConfigAssistMetricEnum.ARRIVAL_NUM_RATIO.getCode().byteValue(), userConfig.getAssistMetric());
            assertEquals(SopUserConfigWarningThresholdEnum.PERCENT_30.getCode(), userConfig.getWarningThreshold());
            assertEquals("user1", userConfig.getModifier());
            assertNotNull(userConfig.getUpdateTime());

            verify(sopUserConfigMapper).updateByPrimaryKeySelective(userConfig);
        }
    }


    @Test
    void testGetUserDetailNoVisibleFields() {
        request = new SopUserDetailRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setDataTableId(1L);
        request.setCurrNodeId("node1");

        primaryKeyField = new TableFieldMetaInfo();
        primaryKeyField.setEnField("id");

        visibleField = new VisibleFieldResponse();
        visibleField.setEnName("mobile");
        visibleField.setCnName("手机号");

        dqlParseResult = new DqlParseResult();
        dqlParseResult.setFrom("test_table");

        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(dorisService.selectList(anyString())).thenReturn(Collections.singletonList(new HashMap<>()));
        when(tableManageService.getVisibleFields(eq(1L), eq(false)))
                .thenReturn(Collections.emptyList());

        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> aiobSOPService.getUserDetail("tenant1", request));
    }

    @Test
    void getDiagramRecords() {
        String mockResp = """
                {
                    "time": 1747639962043,
                    "data": {
                        "menu": {
                            "id": "6ecfe32e-3717-425b-a26e-2f20b4d4135e",
                            "version": 21,
                            "structure": {
                                "startTopic": "0ef92386-fd22-4719-ac76-b72d32466c39",
                                "topicIDs": [
                                    "0ef92386-fd22-4719-ac76-b72d32466c39",
                                    "HJTKht2Yn9zMcGmUjhnsamWvSvPENnYfKRYT",
                                    "5rKzGQ7ErF5HEoz7R2iKTyAHhtWQpCLa8cMS",
                                    "md-01H8lycXaYCCJOyaCRb6ggRUwwe8Vug7p",
                                    "lBaq3KXU-9jiNcEGoVsggDIMeEFePlX98Wiw"
                                ],
                                "diagrams": {
                                    "0ef92386-fd22-4719-ac76-b72d32466c39": {
                                        "sourceID": "0ef92386-fd22-4719-ac76-b72d32466c39",
                                        "name": "起始主题",
                                        "type": "TOPIC",
                                        "menuItems": [
                                            {
                                                "type": "NODE",
                                                "sourceID": "start00000000000000000000",
                                                "intentNameEn": null,
                                                "intentNameZh": null
                                            }
                                        ]
                                    },
                                    "HJTKht2Yn9zMcGmUjhnsamWvSvPENnYfKRYT": {
                                        "sourceID": "HJTKht2Yn9zMcGmUjhnsamWvSvPENnYfKRYT",
                                        "name": "主题 2",
                                        "type": "TOPIC",
                                        "menuItems": [
                                            {
                                                "type": "NODE",
                                                "sourceID": "GC9SxTmdn3Rl3GmO5598Hdo0bdzj6s2ou062",
                                                "intentNameEn": null,
                                                "intentNameZh": null
                                            }
                                        ]
                                    },
                                    "5rKzGQ7ErF5HEoz7R2iKTyAHhtWQpCLa8cMS": {
                                        "sourceID": "5rKzGQ7ErF5HEoz7R2iKTyAHhtWQpCLa8cMS",
                                        "name": "主题 3",
                                        "type": "TOPIC",
                                        "menuItems": [
                                            {
                                                "type": "NODE",
                                                "sourceID": "5unfXT5KMHEDxCRidjSYzXtFhgEhUTK4I7tw",
                                                "intentNameEn": null,
                                                "intentNameZh": null
                                            },
                                            {
                                                "type": "NODE",
                                                "sourceID": "3h8chIh-WISgke1Z3sYnpqFy10HDX5m4tCQJ",
                                                "intentNameEn": null,
                                                "intentNameZh": null
                                            },
                                            {
                                                "type": "NODE",
                                                "sourceID": "gjzJVI3KK-YDcPnXiesfeBpZ9JhOIvdpcJk9",
                                                "intentNameEn": null,
                                                "intentNameZh": null
                                            }
                                        ]
                                    },
                                    "md-01H8lycXaYCCJOyaCRb6ggRUwwe8Vug7p": {
                                        "sourceID": "md-01H8lycXaYCCJOyaCRb6ggRUwwe8Vug7p",
                                        "name": "主题 4",
                                        "type": "TOPIC",
                                        "menuItems": [
                                            {
                                                "type": "NODE",
                                                "sourceID": "nBtW9PExDyasjAFn_HgvTGK1jyKfS3y6ku63",
                                                "intentNameEn": null,
                                                "intentNameZh": null
                                            }
                                        ]
                                    },
                                    "lBaq3KXU-9jiNcEGoVsggDIMeEFePlX98Wiw": {
                                        "sourceID": "lBaq3KXU-9jiNcEGoVsggDIMeEFePlX98Wiw",
                                        "name": "主题 5",
                                        "type": "TOPIC",
                                        "menuItems": [
                                            {
                                                "type": "NODE",
                                                "sourceID": "reQkzwaIZ5TeNMCuW99QqbF0QjhInLorHM-C",
                                                "intentNameEn": null,
                                                "intentNameZh": null
                                            }
                                        ]
                                    }
                                }
                            }
                        },
                        "topicList": [
                            {
                                "id": "md-01H8lycXaYCCJOyaCRb6ggRUwwe8Vug7p",
                                "content": {
                                    "sourceID": "md-01H8lycXaYCCJOyaCRb6ggRUwwe8Vug7p",
                                    "edges": "{\\"edges\\":[{\\"id\\":\\"reactflow__edge-nBtW9PExDyasjAFn_HgvTGK1jyKfS3y6ku639DIzlA1uB1U5ZVrYag2ylUMhq-spke43HB9o-E5QQ4JTQAReytHaQuMTDHU86ncJjx9DrA2xVE5QQ4JTQAReytHaQuMTDHU86ncJjx9DrA2xV\\",\\"source\\":\\"nBtW9PExDyasjAFn_HgvTGK1jyKfS3y6ku63\\",\\"sourceHandle\\":\\"9DIzlA1uB1U5ZVrYag2ylUMhq-spke43HB9o\\",\\"target\\":\\"E5QQ4JTQAReytHaQuMTDHU86ncJjx9DrA2xV\\",\\"targetHandle\\":\\"E5QQ4JTQAReytHaQuMTDHU86ncJjx9DrA2xV\\",\\"type\\":\\"CustomEdge\\"},{\\"id\\":\\"reactflow__edge-E5QQ4JTQAReytHaQuMTDHU86ncJjx9DrA2xVtfgNa9lKUhfAPoARbILb_Dyn-IACxxf-S7pL-P5XCHq3y-8IFiOhXxwB9HBX9v13M600Uv7C7P5XCHq3y-8IFiOhXxwB9HBX9v13M600Uv7C7\\",\\"source\\":\\"E5QQ4JTQAReytHaQuMTDHU86ncJjx9DrA2xV\\",\\"sourceHandle\\":\\"tfgNa9lKUhfAPoARbILb_Dyn-IACxxf-S7pL\\",\\"target\\":\\"P5XCHq3y-8IFiOhXxwB9HBX9v13M600Uv7C7\\",\\"targetHandle\\":\\"P5XCHq3y-8IFiOhXxwB9HBX9v13M600Uv7C7\\",\\"type\\":\\"CustomEdge\\"},{\\"id\\":\\"reactflow__edge-P5XCHq3y-8IFiOhXxwB9HBX9v13M600Uv7C7JYiF1Vtw5MkY55Qt0qO_2kpkIimpObCiCIjy-2pKgx-rPO8ahMq9zQoj7Bw8W9L__F_KAUeTdQfLg08-3HkwDFw4_X9wxYhy80Tdya3Tp9MPW\\",\\"source\\":\\"P5XCHq3y-8IFiOhXxwB9HBX9v13M600Uv7C7\\",\\"sourceHandle\\":\\"JYiF1Vtw5MkY55Qt0qO_2kpkIimpObCiCIjy\\",\\"target\\":\\"2pKgx-rPO8ahMq9zQoj7Bw8W9L__F_KAUeTd\\",\\"targetHandle\\":\\"QfLg08-3HkwDFw4_X9wxYhy80Tdya3Tp9MPW\\",\\"type\\":\\"CustomStraightEdge\\"}]}",
                                    "nodes": {
                                        "nBtW9PExDyasjAFn_HgvTGK1jyKfS3y6ku63": {
                                            "type": "block",
                                            "nodeID": "nBtW9PExDyasjAFn_HgvTGK1jyKfS3y6ku63",
                                            "blockId": null,
                                            "name": "选择意图",
                                            "coords": [
                                                200.0,
                                                200.0
                                            ],
                                            "data": {
                                                "label": "选择意图",
                                                "steps": [
                                                    "JWCDva2j7gyVDpaIh2qzjMNLUkutKwTNSpYZ"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "JWCDva2j7gyVDpaIh2qzjMNLUkutKwTNSpYZ": {
                                            "type": "intent",
                                            "nodeID": "JWCDva2j7gyVDpaIh2qzjMNLUkutKwTNSpYZ",
                                            "blockId": "nBtW9PExDyasjAFn_HgvTGK1jyKfS3y6ku63",
                                            "name": "选择意图",
                                            "coords": null,
                                            "data": {
                                                "intent": "sys_sensitive",
                                                "intentNameZh": "敏感词意图",
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": "E5QQ4JTQAReytHaQuMTDHU86ncJjx9DrA2xV",
                                                            "targetTopicId": "md-01H8lycXaYCCJOyaCRb6ggRUwwe8Vug7p",
                                                            "id": "9DIzlA1uB1U5ZVrYag2ylUMhq-spke43HB9o",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                },
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "E5QQ4JTQAReytHaQuMTDHU86ncJjx9DrA2xV": {
                                            "type": "block",
                                            "nodeID": "E5QQ4JTQAReytHaQuMTDHU86ncJjx9DrA2xV",
                                            "blockId": null,
                                            "name": "",
                                            "coords": [
                                                398.0,
                                                178.0
                                            ],
                                            "data": {
                                                "label": "任务 1",
                                                "steps": [
                                                    "_ZR5vQyMMxHHaa7Bo8N7UNJI1xWntUTdDNTP"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "P5XCHq3y-8IFiOhXxwB9HBX9v13M600Uv7C7": {
                                            "type": "block",
                                            "nodeID": "P5XCHq3y-8IFiOhXxwB9HBX9v13M600Uv7C7",
                                            "blockId": null,
                                            "name": "",
                                            "coords": [
                                                712.5,
                                                178.0
                                            ],
                                            "data": {
                                                "label": "任务 2",
                                                "steps": [
                                                    "l9FGF0pe1DbL5QmCJVdASazKF1FObYStxoWS"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "_ZR5vQyMMxHHaa7Bo8N7UNJI1xWntUTdDNTP": {
                                            "type": "text",
                                            "nodeID": "_ZR5vQyMMxHHaa7Bo8N7UNJI1xWntUTdDNTP",
                                            "blockId": "E5QQ4JTQAReytHaQuMTDHU86ncJjx9DrA2xV",
                                            "name": "任务 1, 步骤1",
                                            "coords": null,
                                            "data": {
                                                "texts": [
                                                    {
                                                        "id": "aEbEId1nGawuCJEbjjmG1LCRbD0RmcL0kc79",
                                                        "type": 1,
                                                        "richText": "",
                                                        "text": [
                                                            {
                                                                "text": "这里是敏感词",
                                                                "id": null,
                                                                "name": null,
                                                                "nameZh": null,
                                                                "type": null
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "id": "_iCfINzmykuIada_3CdxBpn7URbJk-viXQLf",
                                                        "type": 3,
                                                        "richText": "",
                                                        "text": []
                                                    }
                                                ],
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": "P5XCHq3y-8IFiOhXxwB9HBX9v13M600Uv7C7",
                                                            "targetTopicId": "md-01H8lycXaYCCJOyaCRb6ggRUwwe8Vug7p",
                                                            "id": "xHQOoeC2Am4NGqO44S3bzY8pnRZV3YHaP1Tp",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                },
                                                "hubSlot": false,
                                                "sourceHandle": "tfgNa9lKUhfAPoARbILb_Dyn-IACxxf-S7pL"
                                            }
                                        },
                                        "l9FGF0pe1DbL5QmCJVdASazKF1FObYStxoWS": {
                                            "type": "condition",
                                            "nodeID": "l9FGF0pe1DbL5QmCJVdASazKF1FObYStxoWS",
                                            "blockId": "P5XCHq3y-8IFiOhXxwB9HBX9v13M600Uv7C7",
                                            "name": "任务 2, 步骤1",
                                            "coords": null,
                                            "data": {
                                                "expressions": [
                                                    {
                                                        "id": "-PJHrwNDlPZ5kZgVVv3RRvm7GBMKmaunHSKk",
                                                        "type": "and",
                                                        "name": "",
                                                        "value": [],
                                                        "action": []
                                                    },
                                                    {
                                                        "id": "JYiF1Vtw5MkY55Qt0qO_2kpkIimpObCiCIjy",
                                                        "type": "other",
                                                        "name": "其他条件",
                                                        "value": [],
                                                        "action": [
                                                            {
                                                                "mode": "nlu",
                                                                "topic": null,
                                                                "nodeId": null,
                                                                "intent": null,
                                                                "intentNameZh": null,
                                                                "id": "QfLg08-3HkwDFw4_X9wxYhy80Tdya3Tp9MPW",
                                                                "parentId": null,
                                                                "setting": {
                                                                    "id": "f1FTqFrdFxullwRjuADRIpXTpKYvwWUpget5",
                                                                    "mode": "nlu",
                                                                    "nodeId": "",
                                                                    "intent": "",
                                                                    "intentNameZh": "",
                                                                    "topic": "",
                                                                    "parentId": ""
                                                                }
                                                            }
                                                        ]
                                                    }
                                                ],
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": null,
                                                            "targetTopicId": "",
                                                            "id": "WS8n8G_UFdE99wfttf_3XFJeUhwUZon6B2p3",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": [
                                                        {
                                                            "type": "",
                                                            "target": null,
                                                            "targetTopicId": "",
                                                            "id": "-PJHrwNDlPZ5kZgVVv3RRvm7GBMKmaunHSKk",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        {
                                                            "type": "",
                                                            "target": "2pKgx-rPO8ahMq9zQoj7Bw8W9L__F_KAUeTd",
                                                            "targetTopicId": "md-01H8lycXaYCCJOyaCRb6ggRUwwe8Vug7p",
                                                            "id": "JYiF1Vtw5MkY55Qt0qO_2kpkIimpObCiCIjy",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        }
                                                    ]
                                                },
                                                "hubSlot": true,
                                                "sourceHandle": "yfiDyrDfcw3hNbXdK5caKB_Nc6nACm15L3j_"
                                            }
                                        },
                                        "2pKgx-rPO8ahMq9zQoj7Bw8W9L__F_KAUeTd": {
                                            "type": "actions",
                                            "nodeID": "2pKgx-rPO8ahMq9zQoj7Bw8W9L__F_KAUeTd",
                                            "blockId": "P5XCHq3y-8IFiOhXxwB9HBX9v13M600Uv7C7",
                                            "name": null,
                                            "coords": [
                                                292.0,
                                                100.0
                                            ],
                                            "data": {
                                                "label": "",
                                                "steps": [
                                                    "QfLg08-3HkwDFw4_X9wxYhy80Tdya3Tp9MPW"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "QfLg08-3HkwDFw4_X9wxYhy80Tdya3Tp9MPW": {
                                            "type": "goTo",
                                            "nodeID": "QfLg08-3HkwDFw4_X9wxYhy80Tdya3Tp9MPW",
                                            "blockId": "2pKgx-rPO8ahMq9zQoj7Bw8W9L__F_KAUeTd",
                                            "name": null,
                                            "coords": null,
                                            "data": {
                                                "mode": "nlu",
                                                "topic": "",
                                                "nodeId": "",
                                                "parentId": "l9FGF0pe1DbL5QmCJVdASazKF1FObYStxoWS",
                                                "intent": "",
                                                "intentNameZh": "",
                                                "hubSlot": false,
                                                "sourceHandle": "7NZueeZoIYWd8ocJUBfIUUGp7eEgqdh1Bv7c",
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": null,
                                                            "targetTopicId": "",
                                                            "id": "cV8DNINYH_3cPq4klJ-eKiVNlIR-tqnWaxeS",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                }
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "id": "0ef92386-fd22-4719-ac76-b72d32466c39",
                                "content": {
                                    "sourceID": "0ef92386-fd22-4719-ac76-b72d32466c39",
                                    "edges": "{\\"edges\\":[]}",
                                    "nodes": {
                                        "start00000000000000000000": {
                                            "type": "start",
                                            "nodeID": "start00000000000000000000",
                                            "blockId": null,
                                            "name": null,
                                            "coords": [
                                                125.0,
                                                -45.376594358188754
                                            ],
                                            "data": {
                                                "label": "开始",
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": null,
                                                            "targetTopicId": "0ef92386-fd22-4719-ac76-b72d32466c39",
                                                            "id": "63dab0568e48f700079d1e51",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                },
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "vkDpB2FN1t0UKu0idcCIn9slMNJDW4i9Y5kJ": {
                                            "type": "block",
                                            "nodeID": "vkDpB2FN1t0UKu0idcCIn9slMNJDW4i9Y5kJ",
                                            "blockId": null,
                                            "name": "",
                                            "coords": [
                                                -67.0703125,
                                                -274.90393810818875
                                            ],
                                            "data": {
                                                "label": "任务 1",
                                                "steps": [
                                                    "xHVJlWBOPuz_RZafYZJSJl4MneLxka6sO1xs"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "xHVJlWBOPuz_RZafYZJSJl4MneLxka6sO1xs": {
                                            "type": "text",
                                            "nodeID": "xHVJlWBOPuz_RZafYZJSJl4MneLxka6sO1xs",
                                            "blockId": "vkDpB2FN1t0UKu0idcCIn9slMNJDW4i9Y5kJ",
                                            "name": "任务 1, 步骤1",
                                            "coords": null,
                                            "data": {
                                                "texts": [
                                                    {
                                                        "id": "ZRElKySuZ21Z8Ox1-vhwc2xGoDgfhf_XC6TT",
                                                        "type": 1,
                                                        "richText": "",
                                                        "text": []
                                                    },
                                                    {
                                                        "id": "DGlizwkjm9c-zYxLazjcGXLqe0vOQV-DQP5e",
                                                        "type": 3,
                                                        "richText": "<p><span class=\\"aipd-tp-variable\\" data-type=\\"variable\\" data-id=\\"75ab3386-c373-4389-bbfe-cd69538e367c\\" data-label=\\"test\\">test</span>。&nbsp;&nbsp;&nbsp;&nbsp;111</p>",
                                                        "text": []
                                                    }
                                                ],
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": null,
                                                            "targetTopicId": "",
                                                            "id": "ywO6E1gyvBjjqFasluSyM9bSm3341uEABnTE",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                },
                                                "hubSlot": false,
                                                "sourceHandle": "k3gZwgkqYU23RXDrZfI4ZPhgKJ9mrbUPYnH2"
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "id": "5rKzGQ7ErF5HEoz7R2iKTyAHhtWQpCLa8cMS",
                                "content": {
                                    "sourceID": "5rKzGQ7ErF5HEoz7R2iKTyAHhtWQpCLa8cMS",
                                    "edges": "{\\"edges\\":[{\\"id\\":\\"reactflow__edge-gjzJVI3KK-YDcPnXiesfeBpZ9JhOIvdpcJk9WKxf7Sovo2wDwET3mKnf9fU32McH21bMDsxM-e_M7NjDxNj3isiiH70M9bitvL5loJQWD_9ite_M7NjDxNj3isiiH70M9bitvL5loJQWD_9it\\",\\"source\\":\\"gjzJVI3KK-YDcPnXiesfeBpZ9JhOIvdpcJk9\\",\\"sourceHandle\\":\\"WKxf7Sovo2wDwET3mKnf9fU32McH21bMDsxM\\",\\"target\\":\\"e_M7NjDxNj3isiiH70M9bitvL5loJQWD_9it\\",\\"targetHandle\\":\\"e_M7NjDxNj3isiiH70M9bitvL5loJQWD_9it\\",\\"type\\":\\"CustomEdge\\"}]}",
                                    "nodes": {
                                        "gjzJVI3KK-YDcPnXiesfeBpZ9JhOIvdpcJk9": {
                                            "type": "block",
                                            "nodeID": "gjzJVI3KK-YDcPnXiesfeBpZ9JhOIvdpcJk9",
                                            "blockId": null,
                                            "name": "",
                                            "coords": [
                                                -399.0,
                                                -146.0
                                            ],
                                            "data": {
                                                "label": "",
                                                "steps": [
                                                    "IRmWXvtnV8RFZ3efdrxnHdq9mXIH441gNqVs"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "IRmWXvtnV8RFZ3efdrxnHdq9mXIH441gNqVs": {
                                            "type": "intent",
                                            "nodeID": "IRmWXvtnV8RFZ3efdrxnHdq9mXIH441gNqVs",
                                            "blockId": "gjzJVI3KK-YDcPnXiesfeBpZ9JhOIvdpcJk9",
                                            "name": "",
                                            "coords": null,
                                            "data": {
                                                "intent": "E9bb287b",
                                                "intentNameZh": "查天气",
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": "e_M7NjDxNj3isiiH70M9bitvL5loJQWD_9it",
                                                            "targetTopicId": "5rKzGQ7ErF5HEoz7R2iKTyAHhtWQpCLa8cMS",
                                                            "id": "WKxf7Sovo2wDwET3mKnf9fU32McH21bMDsxM",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                },
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "e_M7NjDxNj3isiiH70M9bitvL5loJQWD_9it": {
                                            "type": "block",
                                            "nodeID": "e_M7NjDxNj3isiiH70M9bitvL5loJQWD_9it",
                                            "blockId": null,
                                            "name": "",
                                            "coords": [
                                                -205.5,
                                                -164.0
                                            ],
                                            "data": {
                                                "label": "任务 1",
                                                "steps": [
                                                    "ms5fYmNZ0yenpi18gnUOEEVL9zIUYWDP2xs5"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "ms5fYmNZ0yenpi18gnUOEEVL9zIUYWDP2xs5": {
                                            "type": "generative",
                                            "nodeID": "ms5fYmNZ0yenpi18gnUOEEVL9zIUYWDP2xs5",
                                            "blockId": "e_M7NjDxNj3isiiH70M9bitvL5loJQWD_9it",
                                            "name": "任务 1, 步骤1",
                                            "coords": null,
                                            "data": {
                                                "source": "aiModel",
                                                "aiModel": {
                                                    "mode": "prompt",
                                                    "prompt": [
                                                        {
                                                            "text": "查今天天气",
                                                            "id": null,
                                                            "name": null,
                                                            "nameZh": null,
                                                            "type": null
                                                        }
                                                    ],
                                                    "system": [
                                                        {
                                                            "text": "你是⼀个耐⼼的客服⼈员",
                                                            "id": null,
                                                            "name": null,
                                                            "nameZh": null,
                                                            "type": null
                                                        }
                                                    ],
                                                    "temperature": 0.1
                                                },
                                                "knowledgeBase": {
                                                    "prompt": [],
                                                    "labelSettings": {
                                                        "generateMode": 1,
                                                        "labelIds": [],
                                                        "labelValueIds": [],
                                                        "dirIds": [],
                                                        "labelMaps": null
                                                    }
                                                },
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": null,
                                                            "targetTopicId": "",
                                                            "id": "8wfuhxXKFXV58T3JvKl3fEz6ulxMd8RqvzkD",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                },
                                                "hubSlot": false,
                                                "sourceHandle": "o6j3r3ibGqTNyMbth4MddsR8UJLRpQ6eqdWl",
                                                "labelSettings": null
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "id": "lBaq3KXU-9jiNcEGoVsggDIMeEFePlX98Wiw",
                                "content": {
                                    "sourceID": "lBaq3KXU-9jiNcEGoVsggDIMeEFePlX98Wiw",
                                    "edges": "{\\"edges\\":[{\\"id\\":\\"reactflow__edge-OfMn-mZ9SnxawiQJpjCI3fwACaYpRlyzDWzEkKSglFQZ8Zc0lSMECMEOoOxB87dvrtACN29P-2Tq4SVJ2o4rCCGUf9RYQm6udSuwVAaGZlfwOkZC6fRCaazoSOM7OU0IJjh-sB4S1AciJuac-\\",\\"source\\":\\"OfMn-mZ9SnxawiQJpjCI3fwACaYpRlyzDWzE\\",\\"sourceHandle\\":\\"kKSglFQZ8Zc0lSMECMEOoOxB87dvrtACN29P\\",\\"target\\":\\"2Tq4SVJ2o4rCCGUf9RYQm6udSuwVAaGZlfwO\\",\\"targetHandle\\":\\"kZC6fRCaazoSOM7OU0IJjh-sB4S1AciJuac-\\",\\"type\\":\\"CustomEdge\\"},{\\"id\\":\\"reactflow__edge-reQkzwaIZ5TeNMCuW99QqbF0QjhInLorHM-C5mJQnkvyrvIJi5lnHHJrjCDU1juE962KHgHO-OfMn-mZ9SnxawiQJpjCI3fwACaYpRlyzDWzEOfMn-mZ9SnxawiQJpjCI3fwACaYpRlyzDWzE\\",\\"source\\":\\"reQkzwaIZ5TeNMCuW99QqbF0QjhInLorHM-C\\",\\"sourceHandle\\":\\"5mJQnkvyrvIJi5lnHHJrjCDU1juE962KHgHO\\",\\"target\\":\\"OfMn-mZ9SnxawiQJpjCI3fwACaYpRlyzDWzE\\",\\"targetHandle\\":\\"OfMn-mZ9SnxawiQJpjCI3fwACaYpRlyzDWzE\\",\\"type\\":\\"CustomEdge\\"}]}",
                                    "nodes": {
                                        "reQkzwaIZ5TeNMCuW99QqbF0QjhInLorHM-C": {
                                            "type": "block",
                                            "nodeID": "reQkzwaIZ5TeNMCuW99QqbF0QjhInLorHM-C",
                                            "blockId": null,
                                            "name": "",
                                            "coords": [
                                                51.0,
                                                164.0
                                            ],
                                            "data": {
                                                "label": "",
                                                "steps": [
                                                    "TbRnYZYVyXZh48KDYxZ_AMu_PSbvKtwiuJAN"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "TbRnYZYVyXZh48KDYxZ_AMu_PSbvKtwiuJAN": {
                                            "type": "intent",
                                            "nodeID": "TbRnYZYVyXZh48KDYxZ_AMu_PSbvKtwiuJAN",
                                            "blockId": "reQkzwaIZ5TeNMCuW99QqbF0QjhInLorHM-C",
                                            "name": "",
                                            "coords": null,
                                            "data": {
                                                "intent": "sys_manual_service",
                                                "intentNameZh": "转人工意图",
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": "OfMn-mZ9SnxawiQJpjCI3fwACaYpRlyzDWzE",
                                                            "targetTopicId": "lBaq3KXU-9jiNcEGoVsggDIMeEFePlX98Wiw",
                                                            "id": "5mJQnkvyrvIJi5lnHHJrjCDU1juE962KHgHO",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                },
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "2Tq4SVJ2o4rCCGUf9RYQm6udSuwVAaGZlfwO": {
                                            "type": "block",
                                            "nodeID": "2Tq4SVJ2o4rCCGUf9RYQm6udSuwVAaGZlfwO",
                                            "blockId": null,
                                            "name": "",
                                            "coords": [
                                                601.0,
                                                159.0
                                            ],
                                            "data": {
                                                "label": "任务 3",
                                                "steps": [
                                                    "kZC6fRCaazoSOM7OU0IJjh-sB4S1AciJuac-"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "OfMn-mZ9SnxawiQJpjCI3fwACaYpRlyzDWzE": {
                                            "type": "block",
                                            "nodeID": "OfMn-mZ9SnxawiQJpjCI3fwACaYpRlyzDWzE",
                                            "blockId": null,
                                            "name": "",
                                            "coords": [
                                                271.5,
                                                152.0
                                            ],
                                            "data": {
                                                "label": "任务 3副本",
                                                "steps": [
                                                    "KyD2HY_GztetIv6eRpE9RerCFgNZXDDNCKdO"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "kZC6fRCaazoSOM7OU0IJjh-sB4S1AciJuac-": {
                                            "type": "text",
                                            "nodeID": "kZC6fRCaazoSOM7OU0IJjh-sB4S1AciJuac-",
                                            "blockId": "2Tq4SVJ2o4rCCGUf9RYQm6udSuwVAaGZlfwO",
                                            "name": "任务 3, 步骤1",
                                            "coords": null,
                                            "data": {
                                                "texts": [
                                                    {
                                                        "id": "T94htDyo0iXtx4OHP5D5op7MbGZ-zZhFHOB5",
                                                        "type": 1,
                                                        "richText": "",
                                                        "text": [
                                                            {
                                                                "text": "转人工",
                                                                "id": null,
                                                                "name": null,
                                                                "nameZh": null,
                                                                "type": null
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "id": "8K4yjjXre0C28plO7aE0ezRkWALuuIy3x5dD",
                                                        "type": 3,
                                                        "richText": "",
                                                        "text": []
                                                    }
                                                ],
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": null,
                                                            "targetTopicId": "",
                                                            "id": "LNwXxPyNNkxlIlHxPJ1xdblyPUoSDLPVexiF",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                },
                                                "hubSlot": false,
                                                "sourceHandle": "0XYD_CwGpIZ_eJ1-yj8z4eh87NuY0OmytlzB"
                                            }
                                        },
                                        "KyD2HY_GztetIv6eRpE9RerCFgNZXDDNCKdO": {
                                            "type": "collect",
                                            "nodeID": "KyD2HY_GztetIv6eRpE9RerCFgNZXDDNCKdO",
                                            "blockId": "OfMn-mZ9SnxawiQJpjCI3fwACaYpRlyzDWzE",
                                            "name": "任务 3副本, 步骤1",
                                            "coords": null,
                                            "data": {
                                                "collect": [
                                                    {
                                                        "name": "x65a8bdf",
                                                        "nameZh": "规则项目号",
                                                        "needEntityCheck": true,
                                                        "fixedGuideReply": false,
                                                        "guide": [
                                                            "请问规则项目号是多少？"
                                                        ],
                                                        "fixedFailedReply": false,
                                                        "failedGuide": "规则项目号信息不完整，请您重新提供一下规则项目号。",
                                                        "entityLimit": "",
                                                        "required": true,
                                                        "checkTimes": 1
                                                    }
                                                ],
                                                "example": null,
                                                "referGuide": null,
                                                "needCheck": false,
                                                "collectMemory": true,
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": "kZC6fRCaazoSOM7OU0IJjh-sB4S1AciJuac-",
                                                            "targetTopicId": "lBaq3KXU-9jiNcEGoVsggDIMeEFePlX98Wiw",
                                                            "id": "1PAQ6nuUWnp-B9dkQN1lnz3Jxt5NGX3X80Pm",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                },
                                                "hubSlot": false,
                                                "sourceHandle": "kKSglFQZ8Zc0lSMECMEOoOxB87dvrtACN29P",
                                                "ignoreHistoryDialog": false,
                                                "skipCollectIfUnchecked": false
                                            }
                                        }
                                    }
                                }
                            },
                            {
                                "id": "HJTKht2Yn9zMcGmUjhnsamWvSvPENnYfKRYT",
                                "content": {
                                    "sourceID": "HJTKht2Yn9zMcGmUjhnsamWvSvPENnYfKRYT",
                                    "edges": "{\\"edges\\":[{\\"id\\":\\"reactflow__edge-GC9SxTmdn3Rl3GmO5598Hdo0bdzj6s2ou0626kp60xNIjW4u6WcnUOaKTxoRMdfu9dyUw7X0-rC2hFA3-WRkSORMLISW9r2r72uqfpqG7_rYZrC2hFA3-WRkSORMLISW9r2r72uqfpqG7_rYZ\\",\\"source\\":\\"GC9SxTmdn3Rl3GmO5598Hdo0bdzj6s2ou062\\",\\"sourceHandle\\":\\"6kp60xNIjW4u6WcnUOaKTxoRMdfu9dyUw7X0\\",\\"target\\":\\"rC2hFA3-WRkSORMLISW9r2r72uqfpqG7_rYZ\\",\\"targetHandle\\":\\"rC2hFA3-WRkSORMLISW9r2r72uqfpqG7_rYZ\\",\\"type\\":\\"CustomEdge\\"},{\\"id\\":\\"reactflow__edge-rC2hFA3-WRkSORMLISW9r2r72uqfpqG7_rYZnmcLkw-l_EaufIsn9tGjZcQoHFJCMHXY2hKy-FqmIgVpm4WG6PpwsWAxhcvS9rAoJ0CuRcIPsFqmIgVpm4WG6PpwsWAxhcvS9rAoJ0CuRcIPs\\",\\"source\\":\\"rC2hFA3-WRkSORMLISW9r2r72uqfpqG7_rYZ\\",\\"sourceHandle\\":\\"nmcLkw-l_EaufIsn9tGjZcQoHFJCMHXY2hKy\\",\\"target\\":\\"FqmIgVpm4WG6PpwsWAxhcvS9rAoJ0CuRcIPs\\",\\"targetHandle\\":\\"FqmIgVpm4WG6PpwsWAxhcvS9rAoJ0CuRcIPs\\",\\"type\\":\\"CustomEdge\\"}]}",
                                    "nodes": {
                                        "GC9SxTmdn3Rl3GmO5598Hdo0bdzj6s2ou062": {
                                            "type": "block",
                                            "nodeID": "GC9SxTmdn3Rl3GmO5598Hdo0bdzj6s2ou062",
                                            "blockId": null,
                                            "name": "",
                                            "coords": [
                                                198.0,
                                                199.0
                                            ],
                                            "data": {
                                                "label": "",
                                                "steps": [
                                                    "zy3Pho2JES-7ks9ekPwLcfNa66I5L5nmZw5Y"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "zy3Pho2JES-7ks9ekPwLcfNa66I5L5nmZw5Y": {
                                            "type": "intent",
                                            "nodeID": "zy3Pho2JES-7ks9ekPwLcfNa66I5L5nmZw5Y",
                                            "blockId": "GC9SxTmdn3Rl3GmO5598Hdo0bdzj6s2ou062",
                                            "name": "",
                                            "coords": null,
                                            "data": {
                                                "intent": "e811295b",
                                                "intentNameZh": "租车",
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": "rC2hFA3-WRkSORMLISW9r2r72uqfpqG7_rYZ",
                                                            "targetTopicId": "HJTKht2Yn9zMcGmUjhnsamWvSvPENnYfKRYT",
                                                            "id": "6kp60xNIjW4u6WcnUOaKTxoRMdfu9dyUw7X0",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                },
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "rC2hFA3-WRkSORMLISW9r2r72uqfpqG7_rYZ": {
                                            "type": "block",
                                            "nodeID": "rC2hFA3-WRkSORMLISW9r2r72uqfpqG7_rYZ",
                                            "blockId": null,
                                            "name": "",
                                            "coords": [
                                                384.0,
                                                192.0
                                            ],
                                            "data": {
                                                "label": "任务 1",
                                                "steps": [
                                                    "WqQ4hnbtpeITUAAXv3IZkgEJDuRr_oLaJKwt"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "FqmIgVpm4WG6PpwsWAxhcvS9rAoJ0CuRcIPs": {
                                            "type": "block",
                                            "nodeID": "FqmIgVpm4WG6PpwsWAxhcvS9rAoJ0CuRcIPs",
                                            "blockId": null,
                                            "name": "",
                                            "coords": [
                                                715.5,
                                                231.0
                                            ],
                                            "data": {
                                                "label": "任务 2",
                                                "steps": [
                                                    "kShE0leneklb-XroKO6KgMrEbYcCuMYP3BYW"
                                                ],
                                                "hubSlot": false,
                                                "sourceHandle": null
                                            }
                                        },
                                        "WqQ4hnbtpeITUAAXv3IZkgEJDuRr_oLaJKwt": {
                                            "type": "collect",
                                            "nodeID": "WqQ4hnbtpeITUAAXv3IZkgEJDuRr_oLaJKwt",
                                            "blockId": "rC2hFA3-WRkSORMLISW9r2r72uqfpqG7_rYZ",
                                            "name": "任务 1, 步骤1",
                                            "coords": null,
                                            "data": {
                                                "collect": [
                                                    {
                                                        "name": "u9f48ce3",
                                                        "nameZh": "详细地址",
                                                        "needEntityCheck": true,
                                                        "fixedGuideReply": false,
                                                        "guide": [
                                                            "请问您的详细地址是什么？"
                                                        ],
                                                        "fixedFailedReply": false,
                                                        "failedGuide": "",
                                                        "entityLimit": "",
                                                        "required": true,
                                                        "checkTimes": 1
                                                    }
                                                ],
                                                "example": null,
                                                "referGuide": null,
                                                "needCheck": false,
                                                "collectMemory": true,
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": "FqmIgVpm4WG6PpwsWAxhcvS9rAoJ0CuRcIPs",
                                                            "targetTopicId": "HJTKht2Yn9zMcGmUjhnsamWvSvPENnYfKRYT",
                                                            "id": "5idmkLCxwc_QkwgFgfR-bojOsAJoQqw6KqcP",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                },
                                                "hubSlot": false,
                                                "sourceHandle": "nmcLkw-l_EaufIsn9tGjZcQoHFJCMHXY2hKy",
                                                "ignoreHistoryDialog": false,
                                                "skipCollectIfUnchecked": false
                                            }
                                        },
                                        "kShE0leneklb-XroKO6KgMrEbYcCuMYP3BYW": {
                                            "type": "text",
                                            "nodeID": "kShE0leneklb-XroKO6KgMrEbYcCuMYP3BYW",
                                            "blockId": "FqmIgVpm4WG6PpwsWAxhcvS9rAoJ0CuRcIPs",
                                            "name": "任务 2, 步骤1",
                                            "coords": null,
                                            "data": {
                                                "texts": [
                                                    {
                                                        "id": "uJ7O9xuiobryOrs0nvvPJFmve5X7DLH6dQW_",
                                                        "type": 1,
                                                        "richText": "",
                                                        "text": [
                                                            {
                                                                "text": "222",
                                                                "id": null,
                                                                "name": null,
                                                                "nameZh": null,
                                                                "type": null
                                                            }
                                                        ]
                                                    },
                                                    {
                                                        "id": "hG8vvoKzPoEiTX5o_LnL6NiMBeoRl6OWg0gL",
                                                        "type": 3,
                                                        "richText": "",
                                                        "text": []
                                                    }
                                                ],
                                                "portsV2": {
                                                    "builtIn": {
                                                        "next": {
                                                            "type": "next",
                                                            "target": null,
                                                            "targetTopicId": "",
                                                            "id": "YFK440K0Xzv294LCvuCwLnDTRjVAyxC-oYDO",
                                                            "data": {
                                                                "points": null
                                                            }
                                                        },
                                                        "fail": null
                                                    },
                                                    "dynamic": []
                                                },
                                                "hubSlot": false,
                                                "sourceHandle": "HVIXMVXfXmHusgyM29-_9zCFIM3VIdRZBU_h"
                                            }
                                        }
                                    }
                                }
                            }
                        ],
                        "diagramEditStatus": null,
                        "updateReason": null,
                        "id": null,
                        "agentId": null,
                        "tenantId": null,
                        "createdUserId": null,
                        "editUserId": null,
                        "created": null,
                        "updated": null,
                        "diagram": null
                    },
                    "code": 200,
                    "msg": "OK"
                }
                """;
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            mockedHttpUtil.when(() -> HttpUtil.getWithTry(anyString())).thenReturn(mockResp);
            assertDoesNotThrow(() -> aiobSOPServiceImpl.getDiagramRecords("agentId", "versionId"));
        }
    }

    @Test
    void testGetSopConfigByTenantId() {
        // Prepare test data
        String tenantId = "testTenant";
        SopUserConfig config = new SopUserConfig();
        config.setTenantId(tenantId);
        List<SopUserConfig> expectedConfigs = Collections.singletonList(config);

        // Mock the behavior
        when(sopUserConfigMapper.selectByExample(any(SopUserConfigCriteria.class)))
                .thenReturn(expectedConfigs);

        // Call the method
        List<SopUserConfig> actualConfigs = aiobSOPService.getSopConfigByTenantId(tenantId);

        // Verify the results
        assertEquals(expectedConfigs.size(), actualConfigs.size());
        assertEquals(tenantId, actualConfigs.get(0).getTenantId());

        // Verify the interaction
        verify(sopUserConfigMapper).selectByExample(any(SopUserConfigCriteria.class));
    }

    @Test
    void testGetSopConfigByTenantId_EmptyResult() {
        // Prepare test data
        String tenantId = "emptyTenant";

        // Mock the behavior
        when(sopUserConfigMapper.selectByExample(any(SopUserConfigCriteria.class)))
                .thenReturn(Collections.emptyList());

        // Call the method
        List<SopUserConfig> actualConfigs = aiobSOPService.getSopConfigByTenantId(tenantId);

        // Verify the results
        assertTrue(actualConfigs.isEmpty());

        // Verify the interaction
        verify(sopUserConfigMapper).selectByExample(any(SopUserConfigCriteria.class));
    }

    @Test
    void getDiagramRecordsFailed() {
        String mockResp = """
                {
                    "code": 500,
                    "msg": "failed"
                }
                """;
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            mockedHttpUtil.when(() -> HttpUtil.getWithTry(anyString())).thenReturn(mockResp);
            assertThrows(RuntimeException.class, () -> aiobSOPServiceImpl.getDiagramRecords("agentId", "versionId"));
        }
    }


    @Test
    void testGetOrCreateSopUserConfig_WhenConfigExists() {
        // Given
        String tenantId = "tenant1";
        String taskId = "task1";
        String userId = "user1";

        SopUserConfig existingConfig = new SopUserConfig();
        existingConfig.setTenantId(tenantId);
        existingConfig.setTaskId(taskId);

        when(sopUserConfigMapper.selectByExample(any(SopUserConfigCriteria.class)))
                .thenReturn(Collections.singletonList(existingConfig));

        // When
        SopUserConfig result = aiobSOPService.getOrCreateSopUserConfig(tenantId, taskId, userId);

        // Then
        assertNotNull(result);
        assertEquals(tenantId, result.getTenantId());
        assertEquals(taskId, result.getTaskId());
        verify(sopUserConfigMapper, never()).insert(any());
        verify(sopUserConfigMapper, never()).updateByPrimaryKey(any());
    }

    @Test
    void testGetOrCreateSopUserConfig_WhenConfigNotExists() {
        // Given
        String tenantId = "tenant1";
        String taskId = "task1";
        String userId = "user1";

        when(sopUserConfigMapper.selectByExample(any(SopUserConfigCriteria.class)))
                .thenReturn(Collections.emptyList());

        // When
        SopUserConfig result = aiobSOPService.getOrCreateSopUserConfig(tenantId, taskId, userId);

        // Then
        assertNotNull(result);
        assertEquals(tenantId, result.getTenantId());
        assertEquals(taskId, result.getTaskId());
        assertEquals(Constants.SYSTEM_DEFAULT_USER_ID, result.getCreator());
        assertEquals(SopUserConfigMainMetricEnum.ARRIVAL_USER_NUM.getCode().byteValue(), result.getCoreMetric());
        assertEquals(SopUserConfigAssistMetricEnum.ARRIVAL_USER_NUM_RATIO.getCode().byteValue(), result.getAssistMetric());
        assertEquals(SopUserConfigWarningThresholdEnum.PERCENT_20.getCode(), result.getWarningThreshold());
        assertFalse(result.getDel());

        verify(sopUserConfigMapper, times(1)).insert(any(SopUserConfig.class));
        verify(sopUserConfigMapper, times(1)).updateByPrimaryKey(any(SopUserConfig.class));
    }

    @Test
    void testListRobotVersionSuccess() {
        SopWholeRobotVersionRequest request = new SopWholeRobotVersionRequest();
        request.setRobotId("robot1");
        mockVersions = Collections.singletonList(new SopWholeRobotVersionResponse());

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(123L);

        try (MockedStatic<WebContextHolder> mocked = Mockito.mockStatic(WebContextHolder.class)) {
            mocked.when(() -> WebContextHolder.getUserAuthInfo().getTenantId()).thenReturn(userAuthInfo);

            when(aiobRobotVersionService.getAiobRobotVersionList("123", "robot1"))
                    .thenReturn(mockVersions);

            List<SopWholeRobotVersionResponse> result = aiobSOPService.listRobotVersion(request);

            assertEquals(1, result.size());
            verify(aiobRobotVersionService).getAiobRobotVersionList("123", "robot1");
        }
    }

    @Test
    void testListRobotVersionThrowsException() {
        SopWholeRobotVersionRequest request = new SopWholeRobotVersionRequest();
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(123L);
        request.setRobotId("robot1");
        mockVersions = Collections.singletonList(new SopWholeRobotVersionResponse());

        try (MockedStatic<WebContextHolder> mocked = Mockito.mockStatic(WebContextHolder.class)) {
            mocked.when(() -> WebContextHolder.getUserAuthInfo().getTenantId()).thenReturn(userAuthInfo);

            when(aiobRobotVersionService.getAiobRobotVersionList("123", "robot1"))
                    .thenThrow(new RuntimeException("Test exception"));

            DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class,
                    () -> aiobSOPService.listRobotVersion(request));

            assertEquals("获取机器人版本列表失败", exception.getMessage());
            verify(aiobRobotVersionService).getAiobRobotVersionList("123", "robot1");
        }
    }

    @Test
    void testListRobotVersionSuccess1() {
        SopWholeRobotVersionRequest request = new SopWholeRobotVersionRequest();
        request.setRobotId("robot1");
        List<SopWholeRobotVersionResponse> mockVersions = Collections.singletonList(new SopWholeRobotVersionResponse());

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(123L);

        try (MockedStatic<WebContextHolder> mocked = Mockito.mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getUserAuthInfo).thenReturn(userAuthInfo);

            when(aiobRobotVersionService.getAiobRobotVersionList("123", "robot1"))
                    .thenReturn(mockVersions);

            List<SopWholeRobotVersionResponse> result = aiobSOPService.listRobotVersion(request);

            assertEquals(1, result.size());
            verify(aiobRobotVersionService).getAiobRobotVersionList("123", "robot1");
        }
    }

    @Test
    void testListRobotVersionFailure() {
        SopWholeRobotVersionRequest request = new SopWholeRobotVersionRequest();
        request.setRobotId("robot1");

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(123L);

        try (MockedStatic<WebContextHolder> mocked = Mockito.mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getUserAuthInfo).thenReturn(userAuthInfo);

            when(aiobRobotVersionService.getAiobRobotVersionList("123", "robot1"))
                    .thenThrow(new RuntimeException("Test Exception"));

            assertThrows(DeepSightException.ParamsErrorException.class, () -> {
                aiobSOPService.listRobotVersion(request);
            });

            verify(aiobRobotVersionService).getAiobRobotVersionList("123", "robot1");
        }
    }

    @Test
    void testListRobotVersionEmptyResponse() {
        SopWholeRobotVersionRequest request = new SopWholeRobotVersionRequest();
        request.setRobotId("robot1");

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(123L);

        try (MockedStatic<WebContextHolder> mocked = Mockito.mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getUserAuthInfo).thenReturn(userAuthInfo);

            when(aiobRobotVersionService.getAiobRobotVersionList("123", "robot1"))
                    .thenReturn(Collections.emptyList());

            List<SopWholeRobotVersionResponse> result = aiobSOPService.listRobotVersion(request);

            assertTrue(result.isEmpty());
            verify(aiobRobotVersionService).getAiobRobotVersionList("123", "robot1");
        }
    }

    @Test
    void confirmNode() {
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(123L);

        try (MockedStatic<WebContextHolder> mocked = Mockito.mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getUserAuthInfo).thenReturn(userAuthInfo);
            String rule = "- 第一步\\\n  - 目标：开场白，介绍自己的身份，明确来电原因。\\\n  - 要求：\\\n    1. 打招呼，告知对方你的身份、此次回访的目的是调查满意度。\\\n- 第二步\\\n  - 目标：判断对方是否愿意接受这次满意度调查。\\\n  - 要求：\\\n    1. 如果用户表示愿意接受回访，回复：可以、有时间、好的等同意话术，则进入第三步。\\\n    2. 如果用户明确表示不愿意接受回访，回复：不参加、在忙、不是本人、没时间、等拒绝话术，则进入第五步。\\\n- 第三步\\\n  - 目标：询问满意度得分。\\\n  - 要求：\\\n    1. 先表示感谢，然后参考话术“请问您对客悦智能客服的满意度打几分呢，1分表示非常不满意，5分表示非常满意”，礼貌咨询用户满意度情况。\\\n    2. 如果用户回复的满意度是5分或者表示很满意，则表示感谢，然后进入第五步。\\\n    3. 如果用户回复的满意度是其他分数或者没有表示很满意，则进入第四步。\\\n- 第四步\\\n  - 目标：咨询不满意的地方或需要改进的地方\\\n  - 要求：\\\n    1. 简要咨询不满意的地方或需要改进的地方，参考话术“请问是哪些地方让您不满意呢”、“请问您觉得有哪些地方需要改进呢”。\\\n    2. 如果用户比较配合，礼貌引导用户多表达。\\\n    3. 如果用户没有表达完，可以重复第四步。\\\n    4. 如果用户表达结束，则表示感谢，然后进入第五步。\\\n    5. 如果用户有不耐烦的表现，立即进入第五步。\\\n- 第五步\\\n  - 目标：结束对话流程\\\n  - 要求：\\\n    1. 话术参考“那这边就先不打扰了，祝您生活愉快，再见”，整个流程结";
            String md = "# 咨询改进点\n## 咨询不满原因\n## 引导用户多说\n## 用户说完感谢\n## 用户不耐烦结束\n## 咨询不满原因\n## 引导用户多说\n## 用户说完感谢\n## 用户不耐烦结束\n# 结束对话\n## 礼貌结束对话\n# 判断意愿\n## 用户同意回访\n## 用户拒绝回访\n## 用户同意回访\n## 用户拒绝回访\n# 满意度询问\n## 询问满意度得分\n# 不满意咨询\n## 咨询不满意点\n# 对话结束\n## 结束对话流程\n# 询问满意度\n## 感谢并询问分数\n## 用户满意结束\n## 用户不满转下一步\n## 感谢并询问分数\n## 用户满意结束\n## 用户不满转下一步\n# 开场介绍\n## 自我介绍及目的\n# 开场白\n## 介绍身份目的\n## 介绍身份目的\n# 意愿判断\n## 判断是否接受回访\n";
            when(sopMetaMapper.deleteByExample(any())).thenReturn(1);
            when(sopMetaMapper.insert(any())).thenReturn(1);
            assertThrows(DeepSightException.MarkDownSameH2Exception.class, () -> aiobSOPServiceImpl.confirmNode("tenantId", "taskId", rule, md, "0.0.1"));

            String md1 = "## 咨询不满原因\n## 引导用户多说\n## 用户说完感谢\n## 用户不耐烦结束\n## 咨询不满原因\n## 引导用户多说\n## 用户说完感谢\n## 用户不耐烦结束\n# 结束对话\n## 礼貌结束对话\n## 礼貌结束对话\n# 判断意愿\n## 用户同意回访\n## 用户拒绝回访\n## 用户同意回访\n## 用户拒绝回访\n# 满意度询问\n## 询问满意度得分\n# 不满意咨询\n## 咨询不满意点\n# 对话结束\n## 结束对话流程\n# 询问满意度\n## 感谢并询问分数\n## 用户满意结束\n## 用户不满转下一步\n## 感谢并询问分数\n## 用户满意结束\n## 用户不满转下一步\n# 开场介绍\n## 自我介绍及目的\n# 开场白\n## 介绍身份目的\n## 介绍身份目的\n# 意愿判断\n## 判断是否接受回访\n";
            assertThrows(DeepSightException.MarkDownH1EmptyException.class, () -> aiobSOPServiceImpl.confirmNode("tenantId", "taskId", rule, md1, "0.0.1"));
        }
    }


    @Test
    void testGetUserDetailSuccess1() {
        request = new SopUserDetailRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setDataTableId(1L);
        request.setCurrNodeId("node1");

        primaryKeyField = new TableFieldMetaInfo();
        primaryKeyField.setEnField("id");

        visibleField = new VisibleFieldResponse();
        visibleField.setEnName("mobile");
        visibleField.setCnName("手机号");

        dqlParseResult = new DqlParseResult();
        dqlParseResult.setFrom("test_table");

        // Mock memberId query
        Map<String, Object> memberIdMap = new HashMap<>();
        memberIdMap.put("oneId", "oneId1");
        memberIdMap.put("memberId", "member1");
        when(dorisService.selectList(anyString())).thenReturn(Collections.singletonList(memberIdMap));

        // Mock count query
        when(dorisService.getCount(anyString())).thenReturn(1L);

        // Mock visible fields
        when(tableManageService.getVisibleFields(eq(1L), eq(false)))
                .thenReturn(Collections.singletonList(visibleField));

        // Mock primary key field
        when(tableManageService.queryTableFieldByTag(eq(1L), eq(TableFieldTagEnum.PRIMARY)))
                .thenReturn(primaryKeyField);

        // Mock mobile field
        TableFieldMetaInfo mobileField = new TableFieldMetaInfo();
        mobileField.setId(1L);
        when(tableManageService.getTableFieldByEnName(eq(1L), eq(Constants.TABLE_MOBILE_FIELD)))
                .thenReturn(mobileField);

        // Mock rule parsing
        when(ruleManagerService.parseRuleNode(any(RuleNode.class)))
                .thenReturn(dqlParseResult);

        // Mock data query
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("oneId", "oneId1");
        dataMap.put("mobile", "13800138000");
        when(dorisService.selectList(anyString()))
                .thenReturn(Collections.singletonList(dataMap));

        // Mock data conversion
        Map<String, String> convertedData = new HashMap<>();
        convertedData.put("mobile", "138****8000");
        when(dorisConfService.dorisDataConvertToShowData(eq(1L), anyMap(), anyList()))
                .thenReturn(convertedData);

        BasePageResponse.Page<HashMap<String, Object>> result = aiobSOPService.getUserDetail("tenant1", request);

        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getResults().size());
        assertEquals("138****8000", result.getResults().get(0).get("mobile"));
    }


    @Test
    void testListSOPMetaWithMultipleStepsAndNodes() {

        AiobSopMeta meta1 = new AiobSopMeta();
        meta1.setTenantId("tenant1");
        meta1.setTaskId("task1");
        meta1.setVersion("version1");
        meta1.setNodeId("node1");
        meta1.setStepId("step1");
        meta1.setStepName("step1");
        AiobSopMeta meta2 = new AiobSopMeta();
        meta2.setTenantId("tenant1");
        meta2.setTaskId("task1");
        meta2.setVersion("version1");
        meta2.setNodeId("node2");
        meta2.setStepId("step2");
        meta2.setStepName("step2");
        List<AiobSopMeta> metas = List.of(
                meta1, meta2
        );

        when(sopMetaMapper.selectByExampleWithBLOBs(any(AiobSopMetaCriteria.class)))
                .thenReturn(metas);

        SopSankeyMetaResponse response = aiobSOPService.listSOPMeta("tenant1", "task1", "version1");

        assertEquals(2, response.getSopMeta().size());

        // Verify step1
        assertEquals("step1", response.getSopMeta().get(0).getStepId());
        assertEquals(1, response.getSopMeta().get(0).getNodes().size());

        // Verify step2
        assertEquals("step2", response.getSopMeta().get(1).getStepId());
        assertEquals(1, response.getSopMeta().get(1).getNodes().size());
    }

    @Test
    void testListSOPMetaWithEmptyList() {
        when(sopMetaMapper.selectByExampleWithBLOBs(any(AiobSopMetaCriteria.class))).thenReturn(Collections.emptyList());

        SopSankeyMetaResponse response = aiobSOPService.listSOPMeta("tenant1", "task1", "version1");

        assertTrue(response.getSopMeta().isEmpty());
        assertFalse(response.getManualChecked());
    }

    @Test
    void testListSOPMetaWithSingleStepSingleNode() {
        AiobSopMeta meta = new AiobSopMeta();
        meta.setTenantId("tenant1");
        meta.setTaskId("task1");
        meta.setVersion("version1");
        meta.setStepId("step1");
        meta.setStepName("Step 1");
        meta.setNodeId("node1");
        meta.setNodeName("Node 1");
        meta.setManualCheck(false);
        meta.setDel(false);

        when(sopMetaMapper.selectByExampleWithBLOBs(any(AiobSopMetaCriteria.class)))
                .thenReturn(Collections.singletonList(meta));

        SopSankeyMetaResponse response = aiobSOPService.listSOPMeta("tenant1", "task1", "version1");

        assertEquals(1, response.getSopMeta().size());
        assertEquals("step1", response.getSopMeta().get(0).getStepId());
        assertEquals("Step 1", response.getSopMeta().get(0).getStepName());
        assertEquals(1, response.getSopMeta().get(0).getNodes().size());
        assertEquals("node1", response.getSopMeta().get(0).getNodes().get(0).getNodeId());
        assertEquals("Node 1", response.getSopMeta().get(0).getNodes().get(0).getNodeName());
        assertFalse(response.getManualChecked());
    }

    @Test
    void testGetNodeInfoWithNodeId_WhenNodeExists_ShouldReturnNode() {
        // Arrange
        String nodeId = "node1";
        String tenantId = "tenant1";
        AiobSopMeta expectedNode = new AiobSopMeta();
        expectedNode.setNodeId(nodeId);
        expectedNode.setTenantId(tenantId);
        expectedNode.setDel(DelEnum.NOT_DELETED.getBoolean());

        when(sopMetaMapper.selectByExample(any(AiobSopMetaCriteria.class)))
                .thenReturn(Collections.singletonList(expectedNode));

        // Act
        AiobSopMeta actualNode = aiobSOPService.getNodeInfoWithNodeId(nodeId, tenantId);

        // Assert
        assertNotNull(actualNode);
        assertEquals(nodeId, actualNode.getNodeId());
        assertEquals(tenantId, actualNode.getTenantId());
        verify(sopMetaMapper).selectByExample(any(AiobSopMetaCriteria.class));
    }

    @Test
    void testGetNodeInfoWithNodeId_WhenNodeNotExists_ShouldReturnNull() {
        // Arrange
        String nodeId = "nonexistent";
        String tenantId = "tenant1";

        when(sopMetaMapper.selectByExample(any(AiobSopMetaCriteria.class)))
                .thenReturn(Collections.emptyList());

        // Act
        AiobSopMeta actualNode = aiobSOPService.getNodeInfoWithNodeId(nodeId, tenantId);

        // Assert
        assertNull(actualNode);
        verify(sopMetaMapper).selectByExample(any(AiobSopMetaCriteria.class));
    }

    @Test
    void testGetNodeInfoWithNodeId_WhenNodeDeleted_ShouldReturnNull() {
        // Arrange
        String nodeId = "deletedNode";
        String tenantId = "tenant1";
        AiobSopMeta deletedNode = new AiobSopMeta();
        deletedNode.setNodeId(nodeId);
        deletedNode.setTenantId(tenantId);
        deletedNode.setDel(DelEnum.DELETED.getBoolean());

        // Even though we create a deleted node, the criteria should filter it out
        when(sopMetaMapper.selectByExample(any(AiobSopMetaCriteria.class)))
                .thenReturn(Collections.emptyList());

        // Act
        AiobSopMeta actualNode = aiobSOPService.getNodeInfoWithNodeId(nodeId, tenantId);

        // Assert
        assertNull(actualNode);
        verify(sopMetaMapper).selectByExample(any(AiobSopMetaCriteria.class));
    }

    void testPredictNodeWithSavedNodes() {
        // Arrange
        String tenantId = "tenant1";
        SOPNodePredictRequest request = new SOPNodePredictRequest();
        request.setTaskId("task1");
        request.setRobotVer("robot1");
        request.setRule("rule1");

        AiobSopMeta meta = new AiobSopMeta();
        meta.setTaskId("task1");
        meta.setVersion("robot1");
        meta.setDel(false);
        List<AiobSopMeta> metaList = Collections.singletonList(meta);

        when(sopMetaMapper.selectByExampleWithBLOBs(any(AiobSopMetaCriteria.class)))
                .thenReturn(metaList);

        // Act
        SOPNodePredictResponse response = aiobSOPService.predictNode(tenantId, request);

        // Assert
        assertTrue(response.getIsSaved());
        assertNotNull(response.getMarkdown());
        verify(sopMetaMapper).selectByExampleWithBLOBs(any(AiobSopMetaCriteria.class));
    }

    @Test
    void testPredictNodeWithNoSavedNodes() throws Exception {
        // Arrange
        String tenantId = "tenant1";
        SOPNodePredictRequest request = new SOPNodePredictRequest();
        request.setTaskId("task1");
        request.setRobotVer("robot1");
        request.setRule("rule1");

        when(sopMetaMapper.selectByExampleWithBLOBs(any(AiobSopMetaCriteria.class)))
                .thenReturn(Collections.emptyList());

        NodeProcessSummaryResponse mockResponse = new NodeProcessSummaryResponse();
        mockResponse.setStatus("success");
        mockResponse.setResults(Collections.emptyList());
        String respString = JsonUtils.toJson(mockResponse);

        aiobSOPService = Mockito.spy(aiobSOPService);
        doReturn(mockResponse).when(aiobSOPService).aiNodePredict(any());
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            mockedHttpUtil.when(() -> HttpUtil.postJsonWithTry(anyString(), anyString())).thenReturn(respString);
            // Act
            SOPNodePredictResponse response = aiobSOPService.predictNode(tenantId, request);

            // Assert
            assertFalse(response.getIsSaved());
            assertNotNull(response.getMarkdown());
            verify(sopMetaMapper).selectByExampleWithBLOBs(any(AiobSopMetaCriteria.class));
        }
    }

    @Test
    void testPredictNodeWhenHttpRequestFails() {
        // Arrange
        String tenantId = "tenant1";
        SOPNodePredictRequest request = new SOPNodePredictRequest();
        request.setTaskId("task1");
        request.setRobotVer("robot1");
        request.setRule("rule1");

        when(sopMetaMapper.selectByExampleWithBLOBs(any(AiobSopMetaCriteria.class)))
                .thenReturn(Collections.emptyList());

        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            mockedHttpUtil.when(() -> HttpUtil.postJsonWithTry(anyString(), anyString()))
                    .thenThrow(new RuntimeException("Network error"));

            // Act & Assert
            assertThrows(DeepSightException.NodePredictFailedException.class,
                    () -> aiobSOPService.predictNode(tenantId, request));

            verify(sopMetaMapper).selectByExampleWithBLOBs(any(AiobSopMetaCriteria.class));
        }
    }

    @Test
    void testPredictNodeWhenResponseNotSuccess() throws Exception {
        // Arrange
        String tenantId = "tenant1";
        SOPNodePredictRequest request = new SOPNodePredictRequest();
        request.setTaskId("task1");
        request.setRobotVer("robot1");
        request.setRule("rule1");

        when(sopMetaMapper.selectByExampleWithBLOBs(any(AiobSopMetaCriteria.class)))
                .thenReturn(Collections.emptyList());

        NodeProcessSummaryResponse mockResponse = new NodeProcessSummaryResponse();
        mockResponse.setStatus("failed");

        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class);
             MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            mockedHttpUtil.when(() -> HttpUtil.postJsonWithTry(anyString(), anyString()))
                    .thenReturn("mock-response");
            mockedJsonUtils.when(() -> JsonUtils.toObject(anyString(), eq(NodeProcessSummaryResponse.class)))
                    .thenReturn(mockResponse);

            // Act & Assert
            assertThrows(DeepSightException.NodePredictFailedException.class,
                    () -> aiobSOPService.predictNode(tenantId, request));

            verify(sopMetaMapper).selectByExampleWithBLOBs(any(AiobSopMetaCriteria.class));
        }
    }

    @Test
    void testGetDiagramVersions_Failure() {
        // Arrange
        String agentId = "agent1";
        String versionId = "version1";
        String expectedUrl = "http://test-url?id=version1&agentId=agent1&pageNo=0&pageSize=1";
        String mockResponse = "{\"data\":[],\"time\":123456789,\"msg\":\"failed\",\"code\":500}";

        AiobDiagramVersionRecordResp errorResp = new AiobDiagramVersionRecordResp();
        errorResp.setCode(500);
        errorResp.setMsg("failed");

        try (MockedStatic<HttpUtil> httpUtilMock = mockStatic(HttpUtil.class);
             MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            httpUtilMock.when(() -> HttpUtil.getWithTry(any()))
                    .thenReturn(mockResponse);
            jsonUtilsMock.when(() -> JsonUtils.toObject(mockResponse, AiobDiagramVersionRecordResp.class))
                    .thenReturn(errorResp);

            // Act & Assert
            assertThrows(RuntimeException.class, () -> {
                aiobSOPService.getDiagramVersions(agentId, versionId);
            });

            httpUtilMock.verify(() -> HttpUtil.getWithTry(any()));
            jsonUtilsMock.verify(() -> JsonUtils.toObject(mockResponse, AiobDiagramVersionRecordResp.class));
        }
    }

    @Test
    void testGetDiagramVersions_Exception() {
        // Arrange
        String agentId = "agent1";
        String versionId = "version1";
        String expectedUrl = "http://test-url?id=version1&agentId=agent1&pageNo=0&pageSize=1";

        try (MockedStatic<HttpUtil> httpUtilMock = mockStatic(HttpUtil.class)) {
            httpUtilMock.when(() -> HttpUtil.getWithTry(any()))
                    .thenThrow(new RuntimeException("Network error"));

            // Act & Assert
            assertThrows(RuntimeException.class, () -> {
                aiobSOPService.getDiagramVersions(agentId, versionId);
            });

            httpUtilMock.verify(() -> HttpUtil.getWithTry(any()));
        }
    }

    @Test
    void testGetDiagramVersions_Success() throws Exception {
        // Arrange
        String agentId = "agent1";
        String versionId = "version1";
        String expectedUrl = "http://test-url?id=version1&agentId=agent1&pageNo=0&pageSize=1";
        String mockResponse = "{\"data\":[],\"time\":123456789,\"msg\":\"success\",\"code\":200}";

        AiobDiagramVersionRecordResp expectedResp = new AiobDiagramVersionRecordResp();
        expectedResp.setCode(200);
        expectedResp.setMsg("success");

        try (MockedStatic<HttpUtil> httpUtilMock = mockStatic(HttpUtil.class);
             MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            httpUtilMock.when(() -> HttpUtil.getWithTry(any()))
                    .thenReturn(mockResponse);
            jsonUtilsMock.when(() -> JsonUtils.toObject(mockResponse, AiobDiagramVersionRecordResp.class))
                    .thenReturn(expectedResp);

            // Act
            AiobDiagramVersionRecordResp actualResp = aiobSOPService.getDiagramVersions(agentId, versionId);

            // Assert
            assertEquals(expectedResp, actualResp);
            httpUtilMock.verify(() -> HttpUtil.getWithTry(any()));
            jsonUtilsMock.verify(() -> JsonUtils.toObject(mockResponse, AiobDiagramVersionRecordResp.class));
        }
    }

    @Test
    void testGetSopConfigByStatus() {
        // Prepare test data
        String tenantId = "testTenant";
        Integer status = 1;
        SopUserConfig config1 = new SopUserConfig();
        config1.setTenantId(tenantId);
        config1.setStatus((byte) 2);
        SopUserConfig config2 = new SopUserConfig();
        config2.setTenantId(tenantId);
        config2.setStatus((byte) 3);
        List<SopUserConfig> expectedConfigs = Arrays.asList(config1, config2);

        // Mock behavior
        when(sopUserConfigMapper.selectByExample(any(SopUserConfigCriteria.class)))
                .thenReturn(expectedConfigs);

        // Execute method
        List<SopUserConfig> result = aiobSOPService.getSopConfigByStatus(tenantId, status);

        // Verify results
        assertEquals(2, result.size());
        assertEquals(expectedConfigs, result);
        verify(sopUserConfigMapper, times(1)).selectByExample(any(SopUserConfigCriteria.class));
    }

    @Test
    void testGetSopConfigByStatusWithEmptyResult() {
        // Prepare test data
        String tenantId = "testTenant";
        Integer status = 1;

        // Mock behavior
        when(sopUserConfigMapper.selectByExample(any(SopUserConfigCriteria.class)))
                .thenReturn(Collections.emptyList());

        // Execute method
        List<SopUserConfig> result = aiobSOPService.getSopConfigByStatus(tenantId, status);

        // Verify results
        assertTrue(result.isEmpty());
        verify(sopUserConfigMapper, times(1)).selectByExample(any(SopUserConfigCriteria.class));
    }


    @Test
    void testGetSopConfigStatus_EmptyResponse() throws Exception {
        try (var mockedStatic = mockStatic(HttpUtil.class)) {
            mockedStatic.when(() -> HttpUtil.get(anyString(), anyMap()))
                    .thenReturn("");

            Integer result = aiobSOPService.getSopConfigStatus("tenant1", "task1");

            assertNull(result);
        }
    }

    @Test
    void testGetSopConfigStatus_InvalidJson() throws Exception {
        try (var mockedStatic = mockStatic(HttpUtil.class)) {
            mockedStatic.when(() -> HttpUtil.get(anyString(), anyMap()))
                    .thenReturn("invalid json");

            Integer result = aiobSOPService.getSopConfigStatus("tenant1", "task1");

            assertNull(result);
        }
    }

    @Test
    void testGetSopConfigStatus_Exception() throws Exception {
        try (var mockedStatic = mockStatic(HttpUtil.class)) {
            mockedStatic.when(() -> HttpUtil.get(anyString(), anyMap()))
                    .thenThrow(new RuntimeException("test exception"));

            Integer result = aiobSOPService.getSopConfigStatus("tenant1", "task1");

            assertNull(result);
        }
    }

    @Test
    void testGetSopConfigStatus_NoDataField() throws Exception {
        String mockResponse = "{\"error\":\"not found\"}";

        try (var mockedStatic = mockStatic(HttpUtil.class)) {
            mockedStatic.when(() -> HttpUtil.get(anyString(), anyMap()))
                    .thenReturn(mockResponse);

            Integer result = aiobSOPService.getSopConfigStatus("tenant1", "task1");

            assertNull(result);
        }
    }

    @Test
    void testUpdateStatus() {
        // Given
        String tenantId = "tenant1";
        String taskId = "task1";
        SOPStatusEnum status = SOPStatusEnum.COMPLETED;

        SopUserConfig config = new SopUserConfig();
        config.setStatus(status.getCode().byteValue());

        // When
        aiobSOPService.updateStatus(tenantId, status, taskId);

        // Then
        ArgumentCaptor<SopUserConfig> configCaptor = ArgumentCaptor.forClass(SopUserConfig.class);
        ArgumentCaptor<SopUserConfigCriteria> criteriaCaptor = ArgumentCaptor.forClass(SopUserConfigCriteria.class);

        verify(sopUserConfigMapper).updateByExampleSelective(configCaptor.capture(), criteriaCaptor.capture());

        assertEquals(status.getCode().byteValue(), configCaptor.getValue().getStatus());
        assertEquals(tenantId, criteriaCaptor.getValue().getOredCriteria().get(0).getAllCriteria().get(0).getValue());
        assertEquals(taskId, criteriaCaptor.getValue().getOredCriteria().get(0).getAllCriteria().get(1).getValue());
    }

    @Test
    void testUpdateStatusWithDifferentStatus() {
        // Given
        String tenantId = "tenant2";
        String taskId = "task2";
        SOPStatusEnum status = SOPStatusEnum.PAUSED;

        SopUserConfig config = new SopUserConfig();
        config.setStatus(status.getCode().byteValue());

        // When
        aiobSOPService.updateStatus(tenantId, status, taskId);

        // Then
        ArgumentCaptor<SopUserConfig> configCaptor = ArgumentCaptor.forClass(SopUserConfig.class);
        ArgumentCaptor<SopUserConfigCriteria> criteriaCaptor = ArgumentCaptor.forClass(SopUserConfigCriteria.class);

    }


    @Test
    void testGetFrontNodeInfoWithNodeId_CurrentNodeNotFound() {
        // Setup
        String nodeId = "node1";
        String tenantId = "tenant1";

        // Mock
        when(sopMetaMapper.selectByExample(any(AiobSopMetaCriteria.class)))
                .thenReturn(Collections.emptyList());

        // Execute and Verify
        assertThrows(NullPointerException.class, () -> {
            aiobSOPService.getFrontNodeInfoWithNodeId(nodeId, tenantId);
        });
        verify(sopMetaMapper, times(1)).selectByExample(any(AiobSopMetaCriteria.class));
    }

    @Test
    void testGetFrontNodeInfoWithNodeId_StepIdZero() {
        // Setup
        String nodeId = "node1";
        String tenantId = "tenant1";

        AiobSopMeta currentMeta = new AiobSopMeta();
        currentMeta.setStepId("0");
        currentMeta.setNodeId(nodeId);
        currentMeta.setTenantId(tenantId);

        // Mock
        when(sopMetaMapper.selectByExample(any(AiobSopMetaCriteria.class)))
                .thenReturn(Collections.singletonList(currentMeta));

        // Execute
        AiobSopMeta result = aiobSOPService.getFrontNodeInfoWithNodeId(nodeId, tenantId);

        // Verify
        assertEquals(currentMeta, result);
        verify(sopMetaMapper, times(1)).selectByExample(any(AiobSopMetaCriteria.class));
    }

    @Test
    void testGetUserDetailWithIntentTrue() {
        request = new SopUserDetailRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setDataTableId(1L);
        request.setCurrNodeId("node1");
        request.setIntent(true);

        Map<String, Object> sessionMap = new HashMap<>();
        sessionMap.put("sessionId", "session1");
        sessionMap.put("startTime", "2023-01-01 10:00:00");
        sessionMap.put("mobile", "13800138000");
        List<Map<String, Object>> mobileMap = Collections.singletonList(sessionMap);

        VisibleFieldResponse field = new VisibleFieldResponse();
        field.setEnName("mobile");
        field.setCnName("手机号");
        List<VisibleFieldResponse> visibleFields = Collections.singletonList(field);

        TableFieldMetaInfo primaryKeyField = new TableFieldMetaInfo();
        primaryKeyField.setEnField("id");

        dqlParseResult = new DqlParseResult();
        dqlParseResult.setFrom("test_table");

        when(dorisService.selectList(anyString())).thenReturn(mobileMap);
        when(tableManageService.getVisibleFields(eq(1L), eq(false))).thenReturn(visibleFields);
        when(tableManageService.getTableFieldByEnName(eq(1L), eq("mobile"))).thenReturn(primaryKeyField);
        when(tableManageService.queryTableFieldByTag(eq(1L), eq(TableFieldTagEnum.PRIMARY))).thenReturn(primaryKeyField);
        when(ruleManagerService.parseRuleNode(any(RuleNode.class))).thenReturn(dqlParseResult);
        when(dorisService.getCount(anyString())).thenReturn(1L);
//        when(dorisService.selectList(anyString())).thenReturn(Collections.singletonList(new HashMap<>()));
        when(dorisConfService.dorisDataConvertToShowData(eq(1L), anyMap(), eq(visibleFields))).thenReturn(new HashMap<>());

        BasePageResponse.Page<HashMap<String, Object>> result = aiobSOPService.getUserDetail("tenant1", request);

        assertNotNull(result);
        assertEquals(1, result.getTotal());
    }

    @Test
    void testGetUserDetailSuccess() {
        // Prepare request
        request = new SopUserDetailRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setDataTableId(1L);
        request.setCurrNodeId("node1");
        request.setIntent(false);

        // Prepare mock data
        Map<String, Object> sessionMap = new HashMap<>();
        sessionMap.put("sessionId", "session1");
        sessionMap.put("startTime", "2023-01-01 10:00:00");
        sessionMap.put("mobile", "13800138000");
        List<Map<String, Object>> mobileMap = Collections.singletonList(sessionMap);

        // Prepare visible fields
        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("mobile");
        field1.setCnName("手机号");
        field1.setIsShowValue(true);

        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("name");
        field2.setCnName("姓名");
        field2.setIsShowValue(true);
        List<VisibleFieldResponse> visibleFields = Arrays.asList(field1, field2);

        // Prepare primary key field
        TableFieldMetaInfo primaryKeyField = new TableFieldMetaInfo();
        primaryKeyField.setEnField("id");
        primaryKeyField.setIsShowValue(true);

        // Prepare dql parse result
        dqlParseResult = new DqlParseResult();
        dqlParseResult.setFrom("test_table");
        dqlParseResult.setSelect("`mobile`, `name`");
        dqlParseResult.setOrderBy("`id`");

        // Prepare doris data
        Map<String, Object> dorisData = new HashMap<>();
        dorisData.put("mobile", "13800138000");
        dorisData.put("name", "张三");
        dorisData.put("id", 123);
        List<Map<String, Object>> dorisResult = Collections.singletonList(dorisData);

        // Prepare converted data
        Map<String, String> convertedData = new HashMap<>();
        convertedData.put("mobile", "138****8000");
        convertedData.put("name", "张三");

        // Mock dependencies
        when(dorisService.selectList(anyString())).thenReturn(mobileMap);
        when(tableManageService.getVisibleFields(eq(1L), eq(false))).thenReturn(visibleFields);
        when(tableManageService.getTableFieldByEnName(eq(1L), eq("mobile"))).thenReturn(primaryKeyField);
        when(tableManageService.queryTableFieldByTag(eq(1L), eq(TableFieldTagEnum.PRIMARY))).thenReturn(primaryKeyField);
        when(ruleManagerService.parseRuleNode(any(RuleNode.class))).thenReturn(dqlParseResult);
        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(dorisService.selectList(anyString())).thenReturn(dorisResult);
        when(dorisConfService.dorisDataConvertToShowData(eq(1L), anyMap(), eq(visibleFields))).thenReturn(convertedData);

        // Execute and verify
        BasePageResponse.Page<HashMap<String, Object>> result = aiobSOPService.getUserDetail("tenant1", request);

        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getPageNo());
        assertEquals(10, result.getPageSize());
        assertEquals(1, result.getResults().size());
        assertEquals("138****8000", result.getResults().get(0).get("mobile"));
        assertEquals("张三", result.getResults().get(0).get("name"));
        assertNotNull(result.getResults().get(0).get("sessionIdList"));
    }

    @Test
    void testGetUserDetailWithEmptyMobileMap() {
        request = new SopUserDetailRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setDataTableId(1L);
        request.setCurrNodeId("node1");

        when(dorisService.selectList(anyString())).thenReturn(Collections.emptyList());

        BasePageResponse.Page<HashMap<String, Object>> result = aiobSOPService.getUserDetail("tenant1", request);

        assertNotNull(result);
        assertEquals(0, result.getTotal());
        assertEquals(1, result.getPageNo());
        assertEquals(10, result.getPageSize());
        assertTrue(result.getResults().isEmpty());
    }

    @Test
    void testQuerySessionStartTime_EmptySessionIds() {
        Set<String> emptySessionIds = Collections.emptySet();
        Map<String, Map<String, String>> result = aiobSOPService.querySessionStartTime("tenant1", emptySessionIds);

        assertTrue(result.isEmpty());
    }

    @Test
    void testQuerySessionStartTime_DorisServiceThrowsException() {
        Set<String> sessionIds = Set.of("session1", "session2");
        String expectedTableName = "aiob_session_tenant1";

        when(dorisService.selectList(anyString())).thenThrow(new RuntimeException("Doris error"));

        Map<String, Map<String, String>> result = aiobSOPService.querySessionStartTime("tenant1", sessionIds);

        assertTrue(result.isEmpty());
        verify(dorisService).selectList(anyString());
    }

    @Test
    void testQuerySessionStartTime_Success() {
        Set<String> sessionIds = Set.of("session1", "session2");
        List<Map<String, Object>> mockRecords = List.of(
                Map.of(
                        "sessionId", "session1",
                        "startTime", "2023-01-01 10:00:00",
                        "mobile", "13800138000",
                        "oneId", "oneId1"
                ),
                Map.of(
                        "sessionId", "session2",
                        "startTime", "2023-01-02 11:00:00",
                        "mobile", "13800138001",
                        "oneId", "oneId2"
                )
        );

        when(dorisService.selectList(anyString())).thenReturn(mockRecords);

        Map<String, Map<String, String>> result = aiobSOPService.querySessionStartTime("tenant1", sessionIds);

        assertEquals(2, result.size());
        assertEquals("session1", result.get("session1").get("sessionId"));
        assertEquals("2023-01-01 10:00:00", result.get("session1").get("startTime"));
        assertEquals("13800138000", result.get("session1").get("mobile"));
        assertEquals("oneId1", result.get("session1").get("oneId"));

        assertEquals("session2", result.get("session2").get("sessionId"));
        assertEquals("2023-01-02 11:00:00", result.get("session2").get("startTime"));
        assertEquals("13800138001", result.get("session2").get("mobile"));
        assertEquals("oneId2", result.get("session2").get("oneId"));

        verify(dorisService).selectList(anyString());
    }

    @Test
    void testGetUserDetailV2WhenNoVisibleFields() {
        String tenantId = "tenant1";
        SopUserDetailRequest request = new SopUserDetailRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setTaskId("task1");
        request.setDataTableId(1L);
        request.setIntent(false);

        String nodeTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        String countSql = ORMUtils.generateCountSopUserDetailSQL(nodeTableName, request.getTaskId(),
                null, null, null, null, null);
        String querySql = ORMUtils.generateQuerySopUserDetailSQL(nodeTableName, request.getTaskId(),
                null, null, null, null, null, request.getPageNo(), request.getPageSize());

        when(dorisService.getCount(eq(countSql))).thenReturn(10L);

        List<Map<String, Object>> mockRecordList = new ArrayList<>();
        Map<String, Object> record = new HashMap<>();
        record.put("oneId", "oneId1");
        record.put("sessionIds", "session1,session2");
        mockRecordList.add(record);
        when(dorisService.selectList(querySql)).thenReturn(mockRecordList);

        Map<String, Map<String, String>> mockSessionTimeMap = new HashMap<>();
        Map<String, String> sessionData = new HashMap<>();
        sessionData.put("oneId", "oneId1");
        mockSessionTimeMap.put("session1", sessionData);
        mockSessionTimeMap.put("session2", sessionData);

        when(tableManageService.getVisibleFields(request.getDataTableId(), false)).thenReturn(Collections.emptyList());
    }

    @Test
    void testQueryUserContent_Success() {
        // Prepare test data
        SopUserDetailRequest request = new SopUserDetailRequest();
        request.setDataTableId(1L);

        Set<String> mobileSet = Set.of("13800138000", "13800138001");
        VisibleFieldResponse field1 = VisibleFieldResponse.builder()
                .enName("field1")
                .cnName("字段1")
                .build();
        VisibleFieldResponse field2 = VisibleFieldResponse.builder()
                .enName("field2")
                .cnName("字段2")
                .build();
        List<VisibleFieldResponse> visibleFields = List.of(field1, field2);

        TableFieldMetaInfo userOneIdField = new TableFieldMetaInfo();
        userOneIdField.setId(100L);

        TableFieldMetaInfo primaryKeyField = new TableFieldMetaInfo();
        primaryKeyField.setEnField("primary_key");

        DqlParseResult dqlParseResult = new DqlParseResult();
        dqlParseResult.setSelect("*");
        dqlParseResult.setOrderBy("`primary_key`");

        List<Map<String, Object>> expectedResult = List.of(
                Map.of("field1", "value1", "field2", "value2"),
                Map.of("field1", "value3", "field2", "value4")
        );

        // Mock dependencies
        when(tableManageService.getTableFieldByEnName(eq(1L), eq(Constants.TABLE_USER_ONE_ID)))
                .thenReturn(userOneIdField);
        when(tableManageService.queryTableFieldByTag(eq(1L), eq(TableFieldTagEnum.PRIMARY)))
                .thenReturn(primaryKeyField);
        when(ruleManagerService.parseRuleNode(any(RuleNode.class)))
                .thenReturn(dqlParseResult);
        when(dorisService.selectList(anyString()))
                .thenReturn(expectedResult);

        // Execute method
        List<Map<String, Object>> result = aiobSOPService.queryUserContent(request, mobileSet, visibleFields, Constants.TABLE_USER_ONE_ID);

        // Verify results
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(expectedResult, result);

        // Verify interactions
        verify(tableManageService).getTableFieldByEnName(1L, Constants.TABLE_USER_ONE_ID);
        verify(tableManageService).queryTableFieldByTag(1L, TableFieldTagEnum.PRIMARY);
        verify(ruleManagerService).parseRuleNode(any(RuleNode.class));
        verify(dorisService).selectList(anyString());
    }

    @Test
    void testQueryUserContent_ExceptionCase() {
        // Prepare test data
        SopUserDetailRequest request = new SopUserDetailRequest();
        request.setDataTableId(1L);

        Set<String> mobileSet = Set.of("13800138000");
        List<VisibleFieldResponse> visibleFields = List.of(
                VisibleFieldResponse.builder().enName("field1").build()
        );

        TableFieldMetaInfo userOneIdField = new TableFieldMetaInfo();
        userOneIdField.setId(100L);

        TableFieldMetaInfo primaryKeyField = new TableFieldMetaInfo();
        primaryKeyField.setEnField("primary_key");

        DqlParseResult dqlParseResult = new DqlParseResult();
        dqlParseResult.setSelect("*");
        dqlParseResult.setOrderBy("`primary_key`");

        // Mock dependencies
        when(tableManageService.getTableFieldByEnName(eq(1L), eq(Constants.TABLE_USER_ONE_ID)))
                .thenReturn(userOneIdField);
        when(tableManageService.queryTableFieldByTag(eq(1L), eq(TableFieldTagEnum.PRIMARY)))
                .thenReturn(primaryKeyField);
        when(ruleManagerService.parseRuleNode(any(RuleNode.class)))
                .thenReturn(dqlParseResult);
        when(dorisService.selectList(anyString()))
                .thenThrow(new RuntimeException("Database error"));

        // Execute method
        List<Map<String, Object>> result = aiobSOPService.queryUserContent(request, mobileSet, visibleFields, Constants.TABLE_USER_ONE_ID);

        // Verify results
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // Verify interactions
        verify(dorisService).selectList(anyString());
    }

    @Test
    void testQueryUserContent_EmptyMobileSet() {
        // Prepare test data
        SopUserDetailRequest request = new SopUserDetailRequest();
        request.setDataTableId(1L);

        Set<String> mobileSet = Set.of();
        List<VisibleFieldResponse> visibleFields = List.of(
                VisibleFieldResponse.builder().enName("field1").build()
        );

        TableFieldMetaInfo userOneIdField = new TableFieldMetaInfo();
        userOneIdField.setId(100L);

        TableFieldMetaInfo primaryKeyField = new TableFieldMetaInfo();
        primaryKeyField.setEnField("primary_key");

        DqlParseResult dqlParseResult = new DqlParseResult();
        dqlParseResult.setSelect("*");
        dqlParseResult.setOrderBy("`primary_key`");

        // Mock dependencies
        when(tableManageService.getTableFieldByEnName(eq(1L), eq(Constants.TABLE_USER_ONE_ID)))
                .thenReturn(userOneIdField);
        when(tableManageService.queryTableFieldByTag(eq(1L), eq(TableFieldTagEnum.PRIMARY)))
                .thenReturn(primaryKeyField);
        when(ruleManagerService.parseRuleNode(any(RuleNode.class)))
                .thenReturn(dqlParseResult);
        when(dorisService.selectList(anyString()))
                .thenReturn(List.of());

        // Execute method
        List<Map<String, Object>> result = aiobSOPService.queryUserContent(request, mobileSet, visibleFields, Constants.TABLE_USER_ONE_ID);

        // Verify results
        assertNotNull(result);
        assertTrue(result.isEmpty());

        // Verify interactions
        verify(dorisService).selectList(anyString());
    }

    @Test
    void testGetUserDetailCount_Exception() {
        String testSql = "INVALID SQL";
        long expectedCount = 0L;

        when(dorisService.getCount(testSql)).thenThrow(new RuntimeException("SQL Error"));

        Long actualCount = aiobSOPService.getUserDetailCount(testSql);

        assertEquals(expectedCount, actualCount);
        verify(dorisService, times(1)).getCount(testSql);
    }

    @Test
    void testGetUserDetailCount_Success() {
        String testSql = "SELECT COUNT(*) FROM test_table";
        long expectedCount = 100L;

        when(dorisService.getCount(testSql)).thenReturn(expectedCount);

        Long actualCount = aiobSOPService.getUserDetailCount(testSql);

        assertEquals(expectedCount, actualCount);
        verify(dorisService, times(1)).getCount(testSql);
    }

    @Test
    void testGetUserNodeSessionIds_ExceptionHandling() {
        // Arrange
        String querySql = "SELECT * FROM invalid_table";
        when(dorisService.selectList(querySql)).thenThrow(new RuntimeException("Database error"));

        // Act
        List<Map<String, Object>> result = aiobSOPService.getUserNodeSessionIds(querySql);

        // Assert
        assertTrue(result.isEmpty());
        verify(dorisService, times(1)).selectList(querySql);
    }

    @Test
    void testGetUserNodeSessionIds_Success() {
        // Arrange
        String querySql = "SELECT * FROM test_table";
        List<Map<String, Object>> expectedList = List.of(Map.of("sessionId", "123", "nodeId", "456"));
        when(dorisService.selectList(querySql)).thenReturn(expectedList);

        // Act
        List<Map<String, Object>> result = aiobSOPService.getUserNodeSessionIds(querySql);

        // Assert
        assertEquals(expectedList, result);
        verify(dorisService, times(1)).selectList(querySql);
    }

    @Test
    void testGetUserNodeSessionIds_EmptyResult() {
        // Arrange
        String querySql = "SELECT * FROM empty_table";
        when(dorisService.selectList(querySql)).thenReturn(List.of());

        // Act
        List<Map<String, Object>> result = aiobSOPService.getUserNodeSessionIds(querySql);

        // Assert
        assertTrue(result.isEmpty());
        verify(dorisService, times(1)).selectList(querySql);
    }

    @Test
    void testConstructUserDetail() {
        // Prepare test data
        List<Map<String, Object>> tableContentList = new ArrayList<>();
        Map<String, Object> record1 = new HashMap<>();
        record1.put(Constants.TABLE_USER_USER_ID, "user1");
        record1.put(Constants.TABLE_USER_ONE_ID, "oneId1");
        tableContentList.add(record1);

        List<VisibleFieldResponse> visibleFieldResponse = new ArrayList<>();
        Long dataTableId = 1L;

        Map<String, List<String>> oneIdToSessionIds = new HashMap<>();
        oneIdToSessionIds.put("oneId1", Arrays.asList("session1", "session2"));

        Map<String, Map<String, String>> sessionToStartTimeMap = new HashMap<>();
        sessionToStartTimeMap.put("session1", Map.of("startTime", "2023-01-01"));
        sessionToStartTimeMap.put("session2", Map.of("startTime", "2023-01-02"));

        // Mock dorisConfService behavior
        Map<String, String> convertedData = Map.of("userId", "user1", "oneId", "oneId1");
        when(dorisConfService.dorisDataConvertToShowData(dataTableId, record1, visibleFieldResponse))
                .thenReturn(convertedData);

        // Execute method
        List<HashMap<String, Object>> result = aiobSOPService.constructUserDetail(
                tableContentList, visibleFieldResponse, dataTableId, oneIdToSessionIds, sessionToStartTimeMap);

        // Verify results
        assertEquals(1, result.size());
        HashMap<String, Object> userDetail = result.get(0);
        assertEquals("user1", userDetail.get("userId"));

        @SuppressWarnings("unchecked")
        List<Map<String, String>> sessionIdList = (List<Map<String, String>>) userDetail.get("sessionIdList");
        assertEquals(2, sessionIdList.size());
        assertEquals("session1", sessionIdList.get(0).get("sessionId"));
        assertEquals("2023-01-01", sessionIdList.get(0).get("startTime"));
        assertEquals("session2", sessionIdList.get(1).get("sessionId"));
        assertEquals("2023-01-02", sessionIdList.get(1).get("startTime"));

        // Verify mock interactions
        verify(dorisConfService).dorisDataConvertToShowData(dataTableId, record1, visibleFieldResponse);
    }

    @Test
    void testConstructUserDetailWithEmptySessionIds() {
        // Prepare test data
        List<Map<String, Object>> tableContentList = new ArrayList<>();
        Map<String, Object> record1 = new HashMap<>();
        record1.put(Constants.TABLE_USER_USER_ID, "user1");
        record1.put(Constants.TABLE_USER_ONE_ID, "oneId1");
        tableContentList.add(record1);

        List<VisibleFieldResponse> visibleFieldResponse = new ArrayList<>();
        Long dataTableId = 1L;

        Map<String, List<String>> oneIdToSessionIds = new HashMap<>();
        oneIdToSessionIds.put("oneId1", Collections.emptyList());

        Map<String, Map<String, String>> sessionToStartTimeMap = new HashMap<>();

        // Mock dorisConfService behavior
        Map<String, String> convertedData = Map.of("userId", "user1", "oneId", "oneId1");
        when(dorisConfService.dorisDataConvertToShowData(dataTableId, record1, visibleFieldResponse))
                .thenReturn(convertedData);

        // Execute method
        List<HashMap<String, Object>> result = aiobSOPService.constructUserDetail(
                tableContentList, visibleFieldResponse, dataTableId, oneIdToSessionIds, sessionToStartTimeMap);

        // Verify results
        assertEquals(1, result.size());
        HashMap<String, Object> userDetail = result.get(0);
        assertEquals("user1", userDetail.get("userId"));

        @SuppressWarnings("unchecked")
        List<Map<String, String>> sessionIdList = (List<Map<String, String>>) userDetail.get("sessionIdList");
        assertTrue(sessionIdList.isEmpty());
    }

    @Test
    void testConstructUserDetailWithMultipleUsers() {
        // Prepare test data
        List<Map<String, Object>> tableContentList = new ArrayList<>();

        Map<String, Object> record1 = new HashMap<>();
        record1.put(Constants.TABLE_USER_USER_ID, "user1");
        record1.put(Constants.TABLE_USER_ONE_ID, "oneId1");
        tableContentList.add(record1);

        Map<String, Object> record2 = new HashMap<>();
        record2.put(Constants.TABLE_USER_USER_ID, "user2");
        record2.put(Constants.TABLE_USER_ONE_ID, "oneId2");
        tableContentList.add(record2);

        List<VisibleFieldResponse> visibleFieldResponse = new ArrayList<>();
        Long dataTableId = 1L;

        Map<String, List<String>> oneIdToSessionIds = new HashMap<>();
        oneIdToSessionIds.put("oneId1", Arrays.asList("session1"));
        oneIdToSessionIds.put("oneId2", Arrays.asList("session2"));

        Map<String, Map<String, String>> sessionToStartTimeMap = new HashMap<>();
        sessionToStartTimeMap.put("session1", Map.of("startTime", "2023-01-01"));
        sessionToStartTimeMap.put("session2", Map.of("startTime", "2023-01-02"));

        // Mock dorisConfService behavior
        when(dorisConfService.dorisDataConvertToShowData(dataTableId, record1, visibleFieldResponse))
                .thenReturn(Map.of("userId", "user1", "oneId", "oneId1"));
        when(dorisConfService.dorisDataConvertToShowData(dataTableId, record2, visibleFieldResponse))
                .thenReturn(Map.of("userId", "user2", "oneId", "oneId2"));

        // Execute method
        List<HashMap<String, Object>> result = aiobSOPService.constructUserDetail(
                tableContentList, visibleFieldResponse, dataTableId, oneIdToSessionIds, sessionToStartTimeMap);

        // Verify results
        assertEquals(2, result.size());
        assertEquals("user1", result.get(0).get("userId"));
        assertEquals("user2", result.get(1).get("userId"));

        // Verify sorting
        assertTrue(((String) result.get(0).get("userId")).compareTo((String) result.get(1).get("userId")) < 0);
    }

    @Test
    void testGetUserDetailV2WithIntent() {
        // Prepare test data
        String tenantId = "tenant1";
        SopUserDetailRequest request = new SopUserDetailRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setTaskId("task1");
        request.setDataTableId(1L);
        request.setIntent(true);
        request.setRobotId("robot1");
        request.setRobotVer("ver1");
        request.setStartTime(new Date());
        request.setEndTime(new Date());
        request.setCurrNodeId("node1");

        String nodeTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        String countSql = ORMUtils.generateCountSopUserDetailWithIntentSQL(nodeTableName, request.getTaskId(),
                request.getRobotId(), request.getRobotVer(), request.getStartTime(), request.getEndTime(), request.getCurrNodeId());
        String querySql = ORMUtils.generateQuerySopUserDetailWithIntentSQL(nodeTableName, request.getTaskId(),
                request.getRobotId(), request.getRobotVer(), request.getStartTime(), request.getEndTime(),
                request.getCurrNodeId(), request.getPageNo(), request.getPageSize());

        // Mock count query
        when(dorisService.getCount(eq(countSql))).thenReturn(100L);

        // Mock record list query
        List<Map<String, Object>> mockRecordList = new ArrayList<>();
        Map<String, Object> record1 = new HashMap<>();
        record1.put("oneId", "oneId1");
        record1.put("sessionIds", "session1,session2");
        mockRecordList.add(record1);
        when(dorisService.selectList(eq(querySql))).thenReturn(mockRecordList);

        // Mock session time query
        Map<String, Map<String, String>> mockSessionTimeMap = new HashMap<>();
        Map<String, String> session1Data = new HashMap<>();
        session1Data.put("sessionId", "session1");
        session1Data.put("startTime", "2023-01-01");
        session1Data.put("mobile", "13800000000");
        session1Data.put("oneId", "oneId1");
        mockSessionTimeMap.put("session1", session1Data);

        Map<String, String> session2Data = new HashMap<>();
        session2Data.put("sessionId", "session2");
        session2Data.put("startTime", "2023-01-02");
        session2Data.put("mobile", "13800000000");
        session2Data.put("oneId", "oneId1");
        mockSessionTimeMap.put("session2", session2Data);

        when(dorisService.selectList(anyString())).thenReturn(new ArrayList<>());

        // Mock visible fields
        List<VisibleFieldResponse> visibleFields = new ArrayList<>();
        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("field1");
        visibleFields.add(field1);
        when(tableManageService.getVisibleFields(eq(request.getDataTableId()), eq(false))).thenReturn(visibleFields);

        // Mock user content query
        List<Map<String, Object>> mockUserContent = new ArrayList<>();
        Map<String, Object> user1 = new HashMap<>();
        user1.put("oneId", "oneId1");
        user1.put("userId", "user1");
        user1.put("mobile", "13800000000");
        mockUserContent.add(user1);
        when(dorisService.selectList(anyString())).thenReturn(mockUserContent);

        // Execute test
        BasePageResponse.Page<HashMap<String, Object>> result = aiobSOPService.getUserDetailV2(tenantId, request);

        // Verify results
        assertEquals(100L, result.getTotal());
        assertEquals(0, result.getResults().size());
    }

    @Test
    void testGetUserDetailV2WithEmptySessionIds() {
        String tenantId = "tenant1";
        SopUserDetailRequest request = new SopUserDetailRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setTaskId("task1");
        request.setDataTableId(1L);
        request.setIntent(false);

        String nodeTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        String countSql = ORMUtils.generateCountSopUserDetailSQL(nodeTableName, request.getTaskId(),
                null, null, null, null, null);
        String querySql = ORMUtils.generateQuerySopUserDetailSQL(nodeTableName, request.getTaskId(),
                null, null, null, null, null, request.getPageNo(), request.getPageSize());

        // Mock count query
        when(dorisService.getCount(eq(countSql))).thenReturn(5L);

        // Mock empty session IDs
        List<Map<String, Object>> mockRecordList = new ArrayList<>();
        Map<String, Object> record = new HashMap<>();
        record.put("oneId", "oneId1");
        record.put("sessionIds", ""); // empty session IDs
        mockRecordList.add(record);
        when(dorisService.selectList(eq(querySql))).thenReturn(mockRecordList);

        // Execute test
        BasePageResponse.Page<HashMap<String, Object>> result = aiobSOPService.getUserDetailV2(tenantId, request);

        // Verify results
        assertEquals(5L, result.getTotal());
        assertTrue(result.getResults().isEmpty());
    }

    @Test
    void testGetUserDetailV2WithNoVisibleFields() {
        String tenantId = "tenant1";
        SopUserDetailRequest request = new SopUserDetailRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setTaskId("task1");
        request.setDataTableId(1L);
        request.setIntent(false);

        String nodeTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        String countSql = ORMUtils.generateCountSopUserDetailSQL(nodeTableName, request.getTaskId(),
                null, null, null, null, null);
        String querySql = ORMUtils.generateQuerySopUserDetailSQL(nodeTableName, request.getTaskId(),
                null, null, null, null, null, request.getPageNo(), request.getPageSize());

        // Mock count query
        when(dorisService.getCount(eq(countSql))).thenReturn(10L);

        // Mock record list
        List<Map<String, Object>> mockRecordList = new ArrayList<>();
        Map<String, Object> record = new HashMap<>();
        record.put("oneId", "oneId1");
        record.put("sessionIds", "session1,session2");
        mockRecordList.add(record);
        when(dorisService.selectList(eq(querySql))).thenReturn(mockRecordList);

        // Mock session time query
        Map<String, Map<String, String>> mockSessionTimeMap = new HashMap<>();
        Map<String, String> sessionData = new HashMap<>();
        sessionData.put("oneId", "oneId1");
        mockSessionTimeMap.put("session1", sessionData);
        mockSessionTimeMap.put("session2", sessionData);
//        when(aiobSOPService.querySessionStartTime(anyString(), anySet())).thenReturn(mockSessionTimeMap);

        // Mock empty visible fields
        when(tableManageService.getVisibleFields(request.getDataTableId(), false)).thenReturn(Collections.emptyList());

        assertDoesNotThrow(() -> {
            aiobSOPService.getUserDetailV2(tenantId, request);
        });
    }

    @Test
    void testMergeDeduplication_MobileListEmpty() {
    Map<String, Object> oneIdData1 = Map.of(Constants.TABLE_USER_USER_ID, "user1", "name", "Alice");
    Map<String, Object> oneIdData2 = Map.of(Constants.TABLE_USER_USER_ID, "user2", "name", "Bob");
    List<Map<String, Object>> oneIdList = List.of(oneIdData1, oneIdData2);
    
    List<Map<String, Object>> result = aiobSOPService.mergeDeduplication(oneIdList, Collections.emptyList());
    assertEquals(2, result.size());
    assertTrue(result.containsAll(oneIdList));
    }

    @Test
    void testMergeDeduplication_DuplicateMobileNumbers() {
    Map<String, Object> mobileData1 = Map.of(Constants.TABLE_MOBILE_FIELD, "13800000001", Constants.TABLE_USER_USER_ID, "user1");
    Map<String, Object> mobileData2 = Map.of(Constants.TABLE_MOBILE_FIELD, "13800000001", Constants.TABLE_USER_USER_ID, "user2");
    Map<String, Object> mobileData3 = Map.of(Constants.TABLE_MOBILE_FIELD, "13800000002", Constants.TABLE_USER_USER_ID, "user3");
    List<Map<String, Object>> mobileList = List.of(mobileData1, mobileData2, mobileData3);
    
    List<Map<String, Object>> result = aiobSOPService.mergeDeduplication(Collections.emptyList(), mobileList);
    assertEquals(2, result.size());
    assertTrue(result.stream().anyMatch(m -> "user1".equals(m.get(Constants.TABLE_USER_USER_ID))));
    assertTrue(result.stream().anyMatch(m -> "user3".equals(m.get(Constants.TABLE_USER_USER_ID))));
    }

    @Test
    void testMergeDeduplication_MergeAndDeduplicate() {
    Map<String, Object> oneIdData1 = Map.of(Constants.TABLE_USER_USER_ID, "user1", "name", "Alice");
    Map<String, Object> oneIdData2 = Map.of(Constants.TABLE_USER_USER_ID, "user2", "name", "Bob");
    List<Map<String, Object>> oneIdList = List.of(oneIdData1, oneIdData2);
    
    Map<String, Object> mobileData1 = Map.of(Constants.TABLE_MOBILE_FIELD, "13800000001", Constants.TABLE_USER_USER_ID, "user1", "phone", "13800000001");
    Map<String, Object> mobileData2 = Map.of(Constants.TABLE_MOBILE_FIELD, "13800000002", Constants.TABLE_USER_USER_ID, "user3", "phone", "13800000002");
    List<Map<String, Object>> mobileList = List.of(mobileData1, mobileData2);
    
    List<Map<String, Object>> result = aiobSOPService.mergeDeduplication(oneIdList, mobileList);
    assertEquals(3, result.size());
    
    Map<String, Object> mergedUser1 = result.stream()
            .filter(m -> "user1".equals(m.get(Constants.TABLE_USER_USER_ID)))
            .findFirst()
            .orElseThrow();
    assertEquals(null, mergedUser1.get("name"));
    assertEquals("13800000001", mergedUser1.get("phone"));
    }

    @Test
    void testMergeDeduplication_BothListsEmpty() {
    List<Map<String, Object>> result = aiobSOPService.mergeDeduplication(Collections.emptyList(), Collections.emptyList());
    assertTrue(result.isEmpty());
    }

    @Test
    void testMergeDeduplication_OneIdListEmpty() {
    Map<String, Object> mobileData1 = Map.of(Constants.TABLE_MOBILE_FIELD, "13800000001", Constants.TABLE_USER_USER_ID, "user1");
    Map<String, Object> mobileData2 = Map.of(Constants.TABLE_MOBILE_FIELD, "13800000002", Constants.TABLE_USER_USER_ID, "user2");
    List<Map<String, Object>> mobileList = List.of(mobileData1, mobileData2);
    
    List<Map<String, Object>> result = aiobSOPService.mergeDeduplication(Collections.emptyList(), mobileList);
    assertEquals(2, result.size());
    assertTrue(result.containsAll(mobileList));
    }
}