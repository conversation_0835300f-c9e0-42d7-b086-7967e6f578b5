package com.baidu.keyue.deepsight.service.datamanage.impl;

import org.elasticsearch.client.core.CountResponse;
import org.junit.jupiter.api.Assertions;
import static org.mockito.ArgumentMatchers.any;

import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableContentListRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.rules.response.FilterEnumInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.service.rules.impl.RuleParseServiceImpl;

import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.TestPropertySource;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {"spring.data.elasticsearch.prefix=test_"})
public class EsConfServiceImplTest {


    private List<VisibleFieldResponse> visibleFields;

    @Mock
    private DataTableManageServiceImpl tableManageService;

    @Mock
    private TableRecordCommonService commonService;

    private GetTableContentListRequest request;

    @Mock
    private RuleParseServiceImpl ruleParseService;

    private DataTableInfo dataTableInfo;

    private Map<Long, TableFieldMetaInfo> fieldInfoMap;
    private BoolQueryBuilder boolQueryBuilder;

    private Pageable pageable;

    private String indexName;

    private SearchResponse searchResponse;

    private SearchHits searchHits;

    private SearchHit searchHit;

    @Mock
    private RestHighLevelClient client;

    @InjectMocks
    private EsConfServiceImpl esConfService;

    @BeforeEach
    void setUp() {
        boolQueryBuilder = new BoolQueryBuilder();
        pageable = PageRequest.of(1, 10);
        indexName = "test_index";
    }

    @Test
    void searchListShouldReturnEmptyListWhenResponseIsNull() throws IOException {
        when(client.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(null);

        List<Map<String, Object>> result = esConfService.searchList(boolQueryBuilder, pageable, indexName);

        Assertions.assertTrue(result.isEmpty());
        verify(client).search(any(SearchRequest.class), any(RequestOptions.class));
    }

    @Test
    void searchListShouldReturnDataListWhenResponseHasHits() throws IOException {
        // Setup mock objects
        searchResponse = mock(SearchResponse.class);
        searchHits = mock(SearchHits.class);
        searchHit = mock(SearchHit.class);

        Map<String, Object> sourceMap = new HashMap<>();
        sourceMap.put("field1", "value1");
        sourceMap.put("field2", "value2");

        when(client.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(searchResponse);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.iterator()).thenReturn(List.of(searchHit).iterator());
        when(searchHit.getSourceAsMap()).thenReturn(sourceMap);
        when(searchHit.getId()).thenReturn("123");

        // Execute
        List<Map<String, Object>> result = esConfService.searchList(boolQueryBuilder, pageable, indexName);

        // Verify
        Assertions.assertEquals(1, result.size());
        Map<String, Object> resultMap = result.get(0);
        Assertions.assertEquals("value1", resultMap.get("field1"));
        Assertions.assertEquals("value2", resultMap.get("field2"));
        Assertions.assertEquals("123", resultMap.get("_id"));

        verify(client).search(any(SearchRequest.class), any(RequestOptions.class));
    }

    @Test
    void searchListShouldHandleIOException() throws IOException {
        when(client.search(any(SearchRequest.class), any(RequestOptions.class))).thenThrow(new IOException("Connection failed"));

        Assertions.assertThrows(IOException.class, () -> {
            esConfService.searchList(boolQueryBuilder, pageable, indexName);
        });

        verify(client).search(any(SearchRequest.class), any(RequestOptions.class));
    }

    @Test
    void searchListShouldApplyIndexPrefix() throws IOException {
        when(client.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(null);

        esConfService.searchList(boolQueryBuilder, pageable, indexName);

        verify(client).search(any(SearchRequest.class), any(RequestOptions.class));
    }

    @Test
    void searchListShouldSetCorrectPagination() throws IOException {
        when(client.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(null);

        esConfService.searchList(boolQueryBuilder, pageable, indexName);

        verify(client).search(any(SearchRequest.class), any(RequestOptions.class));
    }

    @Test
    void searchListShouldSetTimeout() throws IOException {
        when(client.search(any(SearchRequest.class), any(RequestOptions.class))).thenReturn(null);

        esConfService.searchList(boolQueryBuilder, pageable, indexName);

        verify(client).search(any(SearchRequest.class),
                any(RequestOptions.class));
    }

    @Test
    void searchShouldReturnPageWithResults() throws IOException {
        request = new GetTableContentListRequest();
        request.setDataTableId(1L);
        request.setPageNo(1);
        request.setPageSize(10);
        request.setFilters(new ArrayList<>());

        dataTableInfo = new DataTableInfo();
        dataTableInfo.setTableName("test_table");

        visibleFields = new ArrayList<>();
        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("field1");
        field1.setTableFieldTag(TableFieldTagEnum.SEARCH);
        field1.setIsShowValue(true);
        visibleFields.add(field1);

        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("field2");
        field2.setTableFieldTag(TableFieldTagEnum.SENSITIVE);
        visibleFields.add(field2);

        VisibleFieldResponse enumField = new VisibleFieldResponse();
        enumField.setEnName("enumField");
        enumField.setTableFieldTag(TableFieldTagEnum.SEARCH);
        enumField.setIsShowValue(true);
        enumField.setEnums(List.of(new FilterEnumInfo("key1", "value1", "desc")));
        visibleFields.add(enumField);

        fieldInfoMap = new HashMap<>();
        TableFieldMetaInfo metaInfo = new TableFieldMetaInfo();
        metaInfo.setEnField("field1");
        fieldInfoMap.put(1L, metaInfo);

        when(tableManageService.getVisibleFields(anyLong(), anyBoolean())).thenReturn(visibleFields);
        when(ruleParseService.getFiledMetaInfoMap(anyList())).thenReturn(fieldInfoMap);
        when(commonService.getDataTableDetail(anyLong())).thenReturn(dataTableInfo);
        when(tableManageService.getTableEncryFields(anyLong())).thenReturn(new ArrayList<>());

        // Mock count response
        CountResponse countResponse = mock(CountResponse.class);
        when(countResponse.getCount()).thenReturn(2L);
        when(client.count(any(), any())).thenReturn(countResponse);

        // Mock search response
        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHits searchHits = mock(SearchHits.class);
        SearchHit searchHit = mock(SearchHit.class);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.iterator()).thenReturn(List.of(searchHit).iterator());
        when(searchHit.getId()).thenReturn("id");
        when(searchHit.getSourceAsMap()).thenReturn(Map.of(
                "field1", "value1",
                "field2", "value2",
                "enumField", "key1",
                "dateField", LocalDateTime.now()
        ));
        when(searchHit.getId()).thenReturn("1");
        when(client.search(any(), any())).thenReturn(searchResponse);

        BasePageResponse.Page<Map<String, String>> result = esConfService.search(request, true);

        Assertions.assertEquals(2, result.getTotal());
    }

    @Test
    void searchShouldHandleSensitiveAndEncryptedFields() throws IOException {
        request = new GetTableContentListRequest();
        request.setDataTableId(1L);
        request.setPageNo(1);
        request.setPageSize(10);
        request.setFilters(new ArrayList<>());

        dataTableInfo = new DataTableInfo();
        dataTableInfo.setTableName("test_table");

        visibleFields = new ArrayList<>();
        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("field1");
        field1.setTableFieldTag(TableFieldTagEnum.SEARCH);
        field1.setIsShowValue(true);
        visibleFields.add(field1);

        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("field2");
        field2.setTableFieldTag(TableFieldTagEnum.SENSITIVE);
        visibleFields.add(field2);

        VisibleFieldResponse enumField = new VisibleFieldResponse();
        enumField.setEnName("enumField");
        enumField.setTableFieldTag(TableFieldTagEnum.SEARCH);
        enumField.setIsShowValue(true);
        enumField.setEnums(List.of(new FilterEnumInfo("key1", "value1", "desc")));
        visibleFields.add(enumField);

        fieldInfoMap = new HashMap<>();
        TableFieldMetaInfo metaInfo = new TableFieldMetaInfo();
        metaInfo.setEnField("field1");
        fieldInfoMap.put(1L, metaInfo);

        when(tableManageService.getVisibleFields(anyLong(), anyBoolean())).thenReturn(visibleFields);
        when(ruleParseService.getFiledMetaInfoMap(anyList())).thenReturn(fieldInfoMap);
        when(commonService.getDataTableDetail(anyLong())).thenReturn(dataTableInfo);
        when(tableManageService.getTableEncryFields(anyLong())).thenReturn(List.of("field1"));

        // Mock count response
        CountResponse countResponse = mock(CountResponse.class);
        when(countResponse.getCount()).thenReturn(1L);
        when(client.count(any(), any())).thenReturn(countResponse);

        // Mock search response
        SearchResponse searchResponse = mock(SearchResponse.class);
        SearchHits searchHits = mock(SearchHits.class);
        SearchHit searchHit = mock(SearchHit.class);
        when(searchResponse.getHits()).thenReturn(searchHits);
        when(searchHits.iterator()).thenReturn(List.of(searchHit).iterator());
        when(searchHit.getId()).thenReturn("id");
        when(searchHit.getSourceAsMap()).thenReturn(Map.of(
                "field1", "value1",
                "field2", "value2"
        ));
        when(searchHit.getId()).thenReturn("1");
        when(client.search(any(), any())).thenReturn(searchResponse);

        BasePageResponse.Page<Map<String, String>> result = esConfService.search(request, true);

        Assertions.assertEquals(1, result.getTotal());
    }

    @Test
    void searchShouldHandleIOException() throws IOException {
        request = new GetTableContentListRequest();
        request.setDataTableId(1L);
        request.setPageNo(1);
        request.setPageSize(10);
        request.setFilters(new ArrayList<>());

        dataTableInfo = new DataTableInfo();
        dataTableInfo.setTableName("test_table");

        visibleFields = new ArrayList<>();
        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("field1");
        field1.setTableFieldTag(TableFieldTagEnum.SEARCH);
        field1.setIsShowValue(true);
        visibleFields.add(field1);

        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("field2");
        field2.setTableFieldTag(TableFieldTagEnum.SENSITIVE);
        visibleFields.add(field2);

        VisibleFieldResponse enumField = new VisibleFieldResponse();
        enumField.setEnName("enumField");
        enumField.setTableFieldTag(TableFieldTagEnum.SEARCH);
        enumField.setIsShowValue(true);
        enumField.setEnums(List.of(new FilterEnumInfo("key1", "value1", "desc")));
        visibleFields.add(enumField);

        fieldInfoMap = new HashMap<>();
        TableFieldMetaInfo metaInfo = new TableFieldMetaInfo();
        metaInfo.setEnField("field1");
        fieldInfoMap.put(1L, metaInfo);

        when(tableManageService.getVisibleFields(anyLong(), anyBoolean())).thenReturn(visibleFields);
        when(ruleParseService.getFiledMetaInfoMap(anyList())).thenReturn(fieldInfoMap);
        when(commonService.getDataTableDetail(anyLong())).thenReturn(dataTableInfo);
        when(tableManageService.getTableEncryFields(anyLong())).thenReturn(new ArrayList<>());
        CountResponse countResponse = mock(CountResponse.class);
        when(countResponse.getCount()).thenReturn(0L);
        when(client.count(any(), any())).thenReturn(countResponse);

        BasePageResponse.Page<Map<String, String>> result = esConfService.search(request, true);

        Assertions.assertEquals(0, result.getTotal());
        Assertions.assertTrue(result.getResults().isEmpty());
    }

    @Test
    void searchShouldReturnEmptyPageWhenNoResultsFound() throws IOException {
        request = new GetTableContentListRequest();
        request.setDataTableId(1L);
        request.setPageNo(1);
        request.setPageSize(10);
        request.setFilters(new ArrayList<>());

        dataTableInfo = new DataTableInfo();
        dataTableInfo.setTableName("test_table");

        visibleFields = new ArrayList<>();
        VisibleFieldResponse field1 = new VisibleFieldResponse();
        field1.setEnName("field1");
        field1.setTableFieldTag(TableFieldTagEnum.SEARCH);
        field1.setIsShowValue(true);
        visibleFields.add(field1);

        VisibleFieldResponse field2 = new VisibleFieldResponse();
        field2.setEnName("field2");
        field2.setTableFieldTag(TableFieldTagEnum.SENSITIVE);
        visibleFields.add(field2);

        VisibleFieldResponse enumField = new VisibleFieldResponse();
        enumField.setEnName("enumField");
        enumField.setTableFieldTag(TableFieldTagEnum.SEARCH);
        enumField.setIsShowValue(true);
        enumField.setEnums(List.of(new FilterEnumInfo("key1", "value1", "desc")));
        visibleFields.add(enumField);

        fieldInfoMap = new HashMap<>();
        TableFieldMetaInfo metaInfo = new TableFieldMetaInfo();
        metaInfo.setEnField("field1");
        fieldInfoMap.put(1L, metaInfo);

        when(tableManageService.getVisibleFields(anyLong(), anyBoolean())).thenReturn(visibleFields);
        when(ruleParseService.getFiledMetaInfoMap(anyList())).thenReturn(fieldInfoMap);
        when(commonService.getDataTableDetail(anyLong())).thenReturn(dataTableInfo);
        CountResponse countResponse = mock(CountResponse.class);
        when(countResponse.getCount()).thenReturn(0L);
        when(client.count(any(), any())).thenReturn(countResponse);
        when(tableManageService.getTableEncryFields(anyLong())).thenReturn(new ArrayList<>());
        SearchHit searchHit = mock(SearchHit.class);
        when(searchHit.getId()).thenReturn("id");

        BasePageResponse.Page<Map<String, String>> result = esConfService.search(request, true);

        Assertions.assertEquals(0, result.getTotal());
        Assertions.assertTrue(result.getResults().isEmpty());
    }

}