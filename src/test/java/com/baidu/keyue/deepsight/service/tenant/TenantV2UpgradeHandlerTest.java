package com.baidu.keyue.deepsight.service.tenant;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@ExtendWith(MockitoExtension.class)
public class TenantV2UpgradeHandlerTest {


    @Mock
    private DorisService dorisService;

    @Mock
    private TransactionTemplate transactionTemplate;

    @InjectMocks
    private TenantV2UpgradeHandler tenantV2UpgradeHandler;

    @Mock
    private TenantInfoMapper tenantInfoMapper;

    @Mock
    private TransactionStatus transactionStatus;
    @Mock
    private TenantInfoService tenantInfoService;

    @Captor
    private ArgumentCaptor<TransactionCallback<Object>> transactionCallbackCaptor;

    private TenantDTO tenantDTO;
    private TenantInfo tenantInfo;

    @BeforeEach
    public void setUp() {
        tenantDTO = new TenantDTO();
        tenantInfo = new TenantInfo();
        tenantInfo.setId(1L);
        tenantDTO.setTenantInfo(tenantInfo);
        tenantDTO.setTenantId("1");

        // Set the version field if needed, for example, to 3
        ReflectionTestUtils.setField(tenantV2UpgradeHandler, "version", 3);
        ReflectionTestUtils.setField(tenantV2UpgradeHandler, "tenantInfoService", tenantInfoService);
    }

//    @Test
//    void testNeedUpdateTenantWhenTenantVersionIsLower() {
//        TenantInfo tenant = new TenantInfo();
//        tenant.setVersion(2);
//        assertTrue(tenantV2UpgradeHandler.needUpdateTenant(tenant));
//    }
//
//    @Test
//    void testNeedUpdateTenantWhenTenantVersionIsEqualOrHigher() {
//        TenantInfo tenant = new TenantInfo();
//        tenant.setVersion(3);
//        assertFalse(tenantV2UpgradeHandler.needUpdateTenant(tenant));
//    }

    @Test
    void testExecuteUpgrade() {
        TenantDTO tenant = new TenantDTO();
        tenant.setTenantId("1");
        ReflectionTestUtils.setField(tenantV2UpgradeHandler, "transactionTemplate", transactionTemplate);
        tenantV2UpgradeHandler.executeUpgrade(tenant);

        verify(transactionTemplate, times(1)).execute(any());
    }

    @Test
    void testInit() {
        // Since init() is a void method with no parameters, we just verify it can be called
        assertDoesNotThrow(() -> tenantV2UpgradeHandler.init());
    }


    @Test
    public void testExecuteUpgrade_Success() {
        ReflectionTestUtils.setField(tenantV2UpgradeHandler, "transactionTemplate", transactionTemplate);
        ReflectionTestUtils.setField(tenantV2UpgradeHandler, "tenantInfoMapper", tenantInfoMapper);
        doAnswer(invocation -> {
            TransactionCallback<Object> callback = invocation.getArgument(0);
            callback.doInTransaction(transactionStatus);
            return null;
        }).when(transactionTemplate).execute(transactionCallbackCaptor.capture());
        tenantV2UpgradeHandler.executeUpgrade(tenantDTO);
        TransactionCallback<Object> capturedCallback = transactionCallbackCaptor.getValue();
        assertNotNull(capturedCallback);
        // Verify that the callback doesn't throw any exceptions
        assertDoesNotThrow(() -> capturedCallback.doInTransaction(transactionStatus));
        // Verify that the tenantInfoMapper was called with the correct tenantInfo
        verify(tenantInfoMapper, times(2)).updateByPrimaryKeySelective(any(TenantInfo.class));
    }
}