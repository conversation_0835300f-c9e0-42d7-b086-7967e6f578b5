package com.baidu.keyue.deepsight.service.memory;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.MemoryTypeEnum;
import com.baidu.keyue.deepsight.models.memory.DeleteUserMemoryDataRequest;
import com.baidu.keyue.deepsight.models.memory.UserMemoryDetailRequest;
import com.baidu.keyue.deepsight.models.memory.UserMemoryDetailResponse;
import com.baidu.keyue.deepsight.service.user.UserService;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class MemoryServiceTest {

    @Mock
    private UserService userService;

    @Mock
    private DorisService dorisService;

    @InjectMocks
    private MemoryService memoryService;


    private UserMemoryDetailRequest request;

    private static final String TENANT_ID = "test-tenant";

    private static final String ONE_ID = "one123";

    private static final String QUERY_TEXT = "test query";

    private static final String EXTRACT_DATE = "2023-01-01T12:00:00";

    private static final String DEEPSIGHT_DATETIME = "2023-01-01T12:00:00";

    private static final String USER_ID = "user123";

    private static final String MEMORY_CONTENT = "test memory content";

    @BeforeEach
    public void setUp() {
        request = new UserMemoryDetailRequest();
    }

    @Test
    void deleteMemoryResultShouldDoNothingWhenUserIdAndOneIdAreBlank() {
        DeleteUserMemoryDataRequest request = new DeleteUserMemoryDataRequest();
        request.setUserId("");
        request.setOneId("");

        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
            memoryService.deleteMemoryResult(request);
        }

        verifyNoInteractions(userService, dorisService);
    }

    @Test
    void deleteMemoryResultShouldGetOneIdFromUserServiceWhenOneIdIsBlank() {
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            String tenantId = "testTenant";
            String userId = "testUserId";
            String oneId = "testOneId";
            String tableName = "testTable";
            String expectedSql = "DELETE FROM testTable WHERE one_id = 'testOneId'";

            webContextHolder.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            tenantUtils.when(() -> TenantUtils.generateMemoryExtractTableName(tenantId)).thenReturn(tableName);
            when(userService.getUserOneIdByUserId(tenantId, userId)).thenReturn(oneId);
            try (MockedStatic<ORMUtils> ormUtils = mockStatic(ORMUtils.class)) {
                ormUtils.when(() -> ORMUtils.clearMemoryExtractResult(tableName, oneId)).thenReturn(expectedSql);

                DeleteUserMemoryDataRequest request = new DeleteUserMemoryDataRequest();
                request.setUserId(userId);
                request.setOneId("");

                memoryService.deleteMemoryResult(request);

                verify(userService).getUserOneIdByUserId(tenantId, userId);
                verify(dorisService).execSql(expectedSql);
            }
        }
    }

    @Test
    public void testQueryUserMemoryDetailMultipleRecordsSameContent() throws Exception {
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
    
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            tenantUtils.when(() -> TenantUtils.generateMemoryExtractTableName(TENANT_ID))
                    .thenReturn("memory_extract_table");
    
            request.setOneId(ONE_ID);
    
            Map<String, Object> record1 = new HashMap<>();
            record1.put("id", "1");
            record1.put("user_id", USER_ID);
            record1.put("extract_date", EXTRACT_DATE);
            record1.put("memory_content", MEMORY_CONTENT);
            record1.put("memory_type", "event");
            record1.put("deepsight_datetime", DEEPSIGHT_DATETIME);
    
            Map<String, Object> record2 = new HashMap<>();
            record2.put("id", "2");
            record2.put("user_id", USER_ID);
            record2.put("extract_date", EXTRACT_DATE);
            record2.put("memory_content", MEMORY_CONTENT);
            record2.put("memory_type", "event");
            record2.put("deepsight_datetime", DEEPSIGHT_DATETIME);
    
            when(dorisService.selectList(anyString())).thenReturn(Arrays.asList(record1, record2));
    
            UserMemoryDetailResponse response = memoryService.queryUserMemoryDetail(request);
    
            assertNotNull(response);
            assertEquals(1, response.getMemoryList().size());
            assertEquals(1, response.getMemoryList().get(0).getMemoryList().size());
        }
    }

    @Test
    public void testQueryUserMemoryDetailMultipleRecordsSameContent2() throws Exception {
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            webContextHolder.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            tenantUtils.when(() -> TenantUtils.generateMemoryExtractTableName(TENANT_ID))
                    .thenReturn("memory_extract_table");

            request.setOneId(ONE_ID);

            Map<String, Object> record1 = new HashMap<>();
            record1.put("id", "1");
            record1.put("user_id", USER_ID);
            record1.put("extract_date", EXTRACT_DATE);
            record1.put("memory_content", MEMORY_CONTENT);
            record1.put("memory_type", "event");
            record1.put("deepsight_datetime", DEEPSIGHT_DATETIME);

            Map<String, Object> record2 = new HashMap<>();
            record2.put("id", "2");
            record2.put("user_id", USER_ID);
            record2.put("extract_date", "EXTRACT_DATE");
            record2.put("memory_content", MEMORY_CONTENT);
            record2.put("memory_type", "event");
            record2.put("deepsight_datetime", DEEPSIGHT_DATETIME);

            when(dorisService.selectList(anyString())).thenReturn(Arrays.asList(record1, record2));

            UserMemoryDetailResponse response = memoryService.queryUserMemoryDetail(request);

            assertNotNull(response);
            assertEquals(1, response.getMemoryList().size());
            assertEquals(1, response.getMemoryList().get(0).getMemoryList().size());
        }
    }

    @Test
    public void testQueryUserMemoryDetailWithOneId() throws Exception {
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
    
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            tenantUtils.when(() -> TenantUtils.generateMemoryExtractTableName(TENANT_ID))
                    .thenReturn("memory_extract_table");
    
            request.setOneId(ONE_ID);
            request.setQueryText(QUERY_TEXT);
            request.setType(MemoryTypeEnum.EVENT);
    
            Map<String, Object> record = new HashMap<>();
            record.put("id", "1");
            record.put("user_id", USER_ID);
            record.put("extract_date", EXTRACT_DATE);
            record.put("memory_content", MEMORY_CONTENT);
            record.put("memory_type", "event");
            record.put("deepsight_datetime", DEEPSIGHT_DATETIME);
    
            when(dorisService.selectList(anyString())).thenReturn(Collections.singletonList(record));
    
            UserMemoryDetailResponse response = memoryService.queryUserMemoryDetail(request);
    
            assertNotNull(response);
            assertTrue(CollectionUtils.isNotEmpty(response.getMemoryList()));
            assertEquals(1, response.getMemoryList().size());
            assertEquals(MEMORY_CONTENT, response.getMemoryList().get(0).getMemoryList().get(0).getContent());
        }
    }

    @Test
    void deleteMemoryResultShouldUseProvidedOneIdWhenOneIdIsNotBlank() {
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            String tenantId = "testTenant";
            String oneId = "testOneId";
            String tableName = "testTable";
            String expectedSql = "DELETE FROM testTable WHERE one_id = 'testOneId'";

            webContextHolder.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            tenantUtils.when(() -> TenantUtils.generateMemoryExtractTableName(tenantId)).thenReturn(tableName);
            try (MockedStatic<ORMUtils> ormUtils = mockStatic(ORMUtils.class)) {
                ormUtils.when(() -> ORMUtils.clearMemoryExtractResult(tableName, oneId)).thenReturn(expectedSql);

                DeleteUserMemoryDataRequest request = new DeleteUserMemoryDataRequest();
                request.setOneId(oneId);

                memoryService.deleteMemoryResult(request);

                verifyNoInteractions(userService);
                verify(dorisService).execSql(expectedSql);
            }
        }
    }

    public void testQueryUserMemoryDetailWithUserId() throws Exception {
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
    
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            tenantUtils.when(() -> TenantUtils.generateMemoryExtractTableName(TENANT_ID))
                    .thenReturn("memory_extract_table");
    
            request.setUserId(USER_ID);
            when(userService.getUserOneIdByUserId(TENANT_ID, USER_ID)).thenReturn(ONE_ID);
    
            Map<String, Object> record = new HashMap<>();
            record.put("id", "1");
            record.put("user_id", USER_ID);
            record.put("extract_date", EXTRACT_DATE);
            record.put("memory_content", MEMORY_CONTENT);
            record.put("memory_type", "event");
            record.put("deepsight_datetime", DEEPSIGHT_DATETIME);
    
            when(dorisService.selectList(anyString())).thenReturn(Collections.singletonList(record));
    
            UserMemoryDetailResponse response = memoryService.queryUserMemoryDetail(request);
    
            assertNotNull(response);
            assertTrue(CollectionUtils.isNotEmpty(response.getMemoryList()));
            verify(userService).getUserOneIdByUserId(TENANT_ID, USER_ID);
        }
    }

    @Test
    void deleteMemoryResultShouldLogWarningWhenUserOneIdNotFound() {
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            String tenantId = "testTenant";
            String userId = "testUserId";

            webContextHolder.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            when(userService.getUserOneIdByUserId(tenantId, userId)).thenReturn(null);

            DeleteUserMemoryDataRequest request = new DeleteUserMemoryDataRequest();
            request.setUserId(userId);
            request.setOneId("");

            memoryService.deleteMemoryResult(request);

            verify(userService).getUserOneIdByUserId(tenantId, userId);
            verifyNoInteractions(dorisService);
        }
    }

    public void testQueryUserMemoryDetailEmptyResult() throws Exception {
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
    
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            tenantUtils.when(() -> TenantUtils.generateMemoryExtractTableName(TENANT_ID))
                    .thenReturn("memory_extract_table");
    
            request.setOneId(ONE_ID);
            when(dorisService.selectList(anyString())).thenReturn(Collections.emptyList());
    
            UserMemoryDetailResponse response = memoryService.queryUserMemoryDetail(request);
    
            assertNotNull(response);
            assertTrue(CollectionUtils.isEmpty(response.getMemoryList()));
        }
    }

    @Test
    void deleteMemoryResultShouldHandleExceptionGracefully() {
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {

            String tenantId = "testTenant";
            String oneId = "testOneId";
            String tableName = "testTable";
            String expectedSql = "DELETE FROM testTable WHERE one_id = 'testOneId'";

            webContextHolder.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            tenantUtils.when(() -> TenantUtils.generateMemoryExtractTableName(tenantId)).thenReturn(tableName);
            try (MockedStatic<ORMUtils> ormUtils = mockStatic(ORMUtils.class)) {
                ormUtils.when(() -> ORMUtils.clearMemoryExtractResult(tableName, oneId)).thenReturn(expectedSql);
                doThrow(new RuntimeException("DB error")).when(dorisService).execSql(expectedSql);

                DeleteUserMemoryDataRequest request = new DeleteUserMemoryDataRequest();
                request.setOneId(oneId);

                memoryService.deleteMemoryResult(request);

                verify(dorisService).execSql(expectedSql);
            }
        }
    }

    @Test
    public void testQueryUserMemoryDetailExceptionHandling() throws Exception {
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
    
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            tenantUtils.when(() -> TenantUtils.generateMemoryExtractTableName(TENANT_ID))
                    .thenReturn("memory_extract_table");
    
            request.setOneId(ONE_ID);
            when(dorisService.selectList(anyString())).thenThrow(new RuntimeException("Test exception"));
    
            UserMemoryDetailResponse response = memoryService.queryUserMemoryDetail(request);
    
            assertNotNull(response);
            assertTrue(CollectionUtils.isEmpty(response.getMemoryList()));
        }
    }

}