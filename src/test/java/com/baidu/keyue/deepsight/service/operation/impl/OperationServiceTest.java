package com.baidu.keyue.deepsight.service.operation.impl;

import com.baidu.kybase.sdk.beans.admin.vo.TenantAppPackageInfo;
import com.baidu.kybase.sdk.beans.admin.vo.TenantAppPackageInfoReq;
import com.baidu.kybase.sdk.beans.admin.vo.TenantAppPackageInfoResp;
import com.baidu.kybase.sdk.user.service.OpAdminApi;
import com.google.common.collect.Lists;
import static org.junit.jupiter.api.Assertions.*;
import org.mockito.InjectMocks;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.enums.OperationModeEnum;
import com.baidu.keyue.deepsight.models.operation.response.OperationModeResponse;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OperationServiceTest {

    @Mock
    private OpAdminApi opAdminApi;

    @InjectMocks
    private OperationServiceImpl operationService;

    @Test
    void detectTenantOperationModeShouldReturnBaiduOpWhenTypeIs2() throws Exception {
        // Arrange
        String tenantId = "123";
        OperationModeResponse response = new OperationModeResponse();
        response.setType(2);

        TenantAppPackageInfo packageInfo = new TenantAppPackageInfo();
        packageInfo.setAppId(7);
        packageInfo.setAppName("");
        packageInfo.setPackageType(2);
        TenantAppPackageInfoResp tenantAppPackageInfoResp = new TenantAppPackageInfoResp();
        tenantAppPackageInfoResp.setApps(Lists.newArrayList(packageInfo));
        when(opAdminApi.getAdminTenantAppPakcageInfo(any(TenantAppPackageInfoReq.class))).thenReturn(tenantAppPackageInfoResp);

        // Act
        OperationModeEnum result = operationService.detectTenantOperationMode(tenantId);

        // Assert
        assertEquals(OperationModeEnum.OPERATION_BY_BAIDU_OP, result);
    }

    @Test
    void detectTenantOperationModeShouldReturnSelfOpWhenTypeIsNot2() throws Exception {
        // Arrange
        String tenantId = "123";
        OperationModeResponse response = new OperationModeResponse();
        response.setType(1);

        TenantAppPackageInfo packageInfo = new TenantAppPackageInfo();
        packageInfo.setAppId(7);
        packageInfo.setAppName("");
        packageInfo.setPackageType(1);
        TenantAppPackageInfoResp tenantAppPackageInfoResp = new TenantAppPackageInfoResp();
        tenantAppPackageInfoResp.setApps(Lists.newArrayList(packageInfo));
        when(opAdminApi.getAdminTenantAppPakcageInfo(any(TenantAppPackageInfoReq.class))).thenReturn(tenantAppPackageInfoResp);

        // Act
        OperationModeEnum result = operationService.detectTenantOperationMode(tenantId);

        // Assert
        assertEquals(OperationModeEnum.OPERATION_BY_SELF, result);
    }

    @Test
    void detectTenantOperationModeShouldReturnSelfOpWhenTypeIsNull() throws Exception {
        // Arrange
        String tenantId = "123";
        OperationModeResponse response = new OperationModeResponse();
        response.setType(null);

        TenantAppPackageInfo packageInfo = new TenantAppPackageInfo();
        packageInfo.setAppId(7);
        packageInfo.setAppName("");
        packageInfo.setPackageType(null);
        TenantAppPackageInfoResp tenantAppPackageInfoResp = new TenantAppPackageInfoResp();
        tenantAppPackageInfoResp.setApps(Lists.newArrayList(packageInfo));
        when(opAdminApi.getAdminTenantAppPakcageInfo(any(TenantAppPackageInfoReq.class))).thenReturn(tenantAppPackageInfoResp);

        // Act
        OperationModeEnum result = operationService.detectTenantOperationMode(tenantId);

        // Assert
        assertEquals(OperationModeEnum.OPERATION_BY_SELF, result);
    }

}