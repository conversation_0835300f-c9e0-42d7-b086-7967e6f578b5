package com.baidu.keyue.deepsight.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baidu.keyue.deepsight.BaseTest;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.service.tenant.TenantV1BetaUpgradeHandler;
import com.baidu.keyue.deepsight.service.tenant.impl.TenantInfoServiceImpl;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Random;

@Slf4j
public class TenantInfoServiceCustomerTest extends BaseTest {
    @Autowired
    private TenantInfoServiceImpl tenantInfoService;
    
    @Resource
    private TenantV1BetaUpgradeHandler v1BetaUpgradeHandler;


    @Test
    public void initTableAndField() {
        int num = Math.abs(new Random().nextInt());
        v1BetaUpgradeHandler.initTableAndField("test_" + num, "type");
    }

    @Test
    public void mockPaloTableContent() {
        String tenantId = "test_" + Math.abs(new Random().nextInt());
        log.info("===============tenantId:{}", tenantId);
        v1BetaUpgradeHandler.mockPaloTableContent(tenantId);
    }


    @Test
    public void initTenantTest() {
        String tenantId = "test_" + Math.abs(new Random().nextInt());
        log.info("===============tenantId:{}", tenantId);
        v1BetaUpgradeHandler.mockPaloTableContent("3808251032371200");
    }
    
    @Test
    public void testInitOrUpgrade(){
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(IdUtil.getSnowflakeNextId());
        DeepSightWebContext deepSightWebContext = new DeepSightWebContext(userAuthInfo);
        deepSightWebContext.setRequestId(IdUtil.fastSimpleUUID());
        WebContextHolder.setDeepSightWebContext(deepSightWebContext);
//        TenantDTO tenantDTO = new TenantDTO();
//        tenantDTO.setType(Constants.TENANT_LOGIN_TYPE);
//        tenantDTO.setTenantId("38802906427544576");
        
////        MDC.put(Constants.REQUEST_USER_FIELD,userAuthInfo.getUserId().toString());
//        tenantDTO.setAuthInfo(userAuthInfo);
//        TenantInfo tenantInfo = tenantInfoService.queryTenantInfo("38802906427544576");
//        tenantDTO.setTenantInfo(tenantInfo);
//        tenantDTO.setType(tenantInfo.getTenantSource());
//        userAuthInfo.setUserId(Long.parseLong(tenantInfo.getUserId()));
//        MDC.put(Constants.REQUEST_USER_FIELD,userAuthInfo.getUserId().toString());

        TenantDTO tenantDTO = new TenantDTO();
        tenantDTO.setTenantId("38802906427544576");
        tenantDTO.setAuthInfo(userAuthInfo);
        tenantDTO.setType(Constants.TENANT_DEFAULT_TYPE);
        tenantDTO.setTenantInfo(null);
        tenantInfoService.initOrUpgradeTenant(tenantDTO);
        log.info("初始化/升级完毕");


        
    }

}
