package com.baidu.keyue.deepsight.service.datamanage.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

import com.baidu.keyue.deepsight.service.datamanage.DbConfService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Map;

public class DbConfServiceFactoryTest{

    private DbConfService mockDbConfService;

    private DbConfServiceFactory dbConfServiceFactory;

    private Map<String, DbConfService> mockDbConfServiceMap;

    @BeforeEach
    void setUp() {
        mockDbConfServiceMap = Mockito.mock(Map.class);
        mockDbConfService = Mockito.mock(DbConfService.class);
        dbConfServiceFactory = new DbConfServiceFactory(mockDbConfServiceMap);
    }

    @Test
    void constructorShouldInitializeServiceMap() {
        assertNotNull(dbConfServiceFactory);
    }

    @Test
    void getDbServiceShouldReturnServiceWhenTypeExists() {
        String testType = "testType";
        when(mockDbConfServiceMap.get(testType)).thenReturn(mockDbConfService);
    
        DbConfService result = dbConfServiceFactory.getDbService(testType);
    
        assertNotNull(result);
        assertEquals(mockDbConfService, result);
    }

    @Test
    void getDbServiceShouldReturnNullWhenTypeDoesNotExist() {
        String nonExistentType = "nonExistentType";
        when(mockDbConfServiceMap.get(nonExistentType)).thenReturn(null);
    
        DbConfService result = dbConfServiceFactory.getDbService(nonExistentType);
    
        assertNull(result);
    }

    @Test
    void getDbServiceShouldHandleNullInput() {
        DbConfService result = dbConfServiceFactory.getDbService(null);
        assertNull(result);
    }

}