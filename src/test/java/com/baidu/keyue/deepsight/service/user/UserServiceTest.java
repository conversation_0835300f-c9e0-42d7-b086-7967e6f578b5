package com.baidu.keyue.deepsight.service.user;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class UserServiceTest{

    @Mock
    private DorisService dorisService;

    @InjectMocks
    private UserService userService;

    @Test
    void queryUserByOneIdShouldReturnEmptyMapWhenNoRecordsFound() {
        String id = "testId";
        String tenantId = "testTenant";
        String mockTableName = "mock_table";
        String expectedSql = String.format("SELECT * FROM %s WHERE %s = '%s' limit 1",
                mockTableName, Constants.TABLE_USER_ONE_ID, id);
    
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(mockTableName);
    
            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());
    
            Map<String, Object> result = userService.queryUserByOneId(id, tenantId);
    
            assertTrue(result.isEmpty());
            verify(dorisService).selectList(expectedSql);
        }
    }

    @Test
    void queryUserByOneIdShouldReturnFirstRecordWhenRecordsFound() {
        String id = "testId";
        String tenantId = "testTenant";
        String mockTableName = "mock_table";
        String expectedSql = String.format("SELECT * FROM %s WHERE %s = '%s' limit 1",
                mockTableName, Constants.TABLE_USER_ONE_ID, id);
        Map<String, Object> mockRecord = new HashMap<>();
        mockRecord.put("key", "value");
        List<Map<String, Object>> mockRecords = List.of(mockRecord);
    
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(mockTableName);
    
            when(dorisService.selectList(expectedSql)).thenReturn(mockRecords);
    
            Map<String, Object> result = userService.queryUserByOneId(id, tenantId);
    
            assertEquals(mockRecord, result);
            verify(dorisService).selectList(expectedSql);
        }
    }

    @Test
    void queryUserByOneIdShouldHandleSqlInjectionSafely() {
        String id = "test' OR '1'='1";
        String tenantId = "testTenant";
        String mockTableName = "mock_table";
        String escapedId = "test'' OR ''1''=''1";
        String expectedSql = String.format("SELECT * FROM %s WHERE %s = '%s' limit 1",
                mockTableName, Constants.TABLE_USER_ONE_ID, escapedId);
    
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(mockTableName);
    
            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());
    
            Map<String, Object> result = userService.queryUserByOneId(id, tenantId);
    
            assertTrue(result.isEmpty());
            verify(dorisService).selectList(expectedSql);
        }
    }

    @Test
    void queryUserByOneIdShouldReturnEmptyMapWhenExceptionOccurs() {
        String id = "testId";
        String tenantId = "testTenant";
        String mockTableName = "mock_table";
        String expectedSql = String.format("SELECT * FROM %s WHERE %s = '%s' limit 1",
                mockTableName, Constants.TABLE_USER_ONE_ID, id);
    
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(mockTableName);
    
            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("DB error"));
    
            Map<String, Object> result = userService.queryUserByOneId(id, tenantId);
    
            assertTrue(result.isEmpty());
            verify(dorisService).selectList(expectedSql);
        }
    }

    @Test
    void queryUserByIdShouldReturnEmptyMapWhenNoRecordsFound() {
        String id = "testId";
        String tenantId = "testTenant";
        String mockTableName = "mock_table";
        String expectedSql = String.format("SELECT * FROM %s WHERE user_id = '%s' limit 1", mockTableName, id);
    
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(mockTableName);
    
            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());
    
            Map<String, Object> result = userService.queryUserById(id, tenantId);
    
            assertTrue(result.isEmpty());
            verify(dorisService).selectList(expectedSql);
        }
    }

    @Test
    void queryUserByIdShouldReturnUserDataWhenRecordExists() {
        String id = "testId";
        String tenantId = "testTenant";
        String mockTableName = "mock_table";
        String expectedSql = String.format("SELECT * FROM %s WHERE user_id = '%s' limit 1", mockTableName, id);
        Map<String, Object> mockUser = new HashMap<>();
        mockUser.put("user_id", id);
        mockUser.put("name", "Test User");
    
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(mockTableName);
    
            when(dorisService.selectList(expectedSql)).thenReturn(List.of(mockUser));
    
            Map<String, Object> result = userService.queryUserById(id, tenantId);
    
            assertFalse(result.isEmpty());
            assertEquals(mockUser, result);
            verify(dorisService).selectList(expectedSql);
        }
    }

    @Test
    void queryUserByIdShouldHandleExceptionGracefully() {
        String id = "testId";
        String tenantId = "testTenant";
        String mockTableName = "mock_table";
        String expectedSql = String.format("SELECT * FROM %s WHERE user_id = '%s' limit 1", mockTableName, id);
    
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(mockTableName);
    
            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("DB error"));
    
            Map<String, Object> result = userService.queryUserById(id, tenantId);
    
            assertTrue(result.isEmpty());
            verify(dorisService).selectList(expectedSql);
        }
    }

}