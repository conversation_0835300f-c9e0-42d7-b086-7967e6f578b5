package com.baidu.keyue.deepsight.service.datamanage.impl;

import com.baidu.keyue.deepsight.utils.AESUtils;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.mysqldb.entity.AccessToken;
import com.baidu.keyue.deepsight.mysqldb.entity.AccessTokenCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.AccessTokenMapper;

import jakarta.servlet.http.HttpServletRequest;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.RedissonBucket;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.test.context.TestPropertySource;

import java.time.Duration;
import java.util.Collections;

@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {
        "access.token.max=1000",
        "access.token.cache.expire.seconds=2592000"
})
public class AccessTokenServiceTest {

    @InjectMocks
    private AccessTokenService accessTokenService;

    @Mock
    private AccessTokenMapper tokenMapper;

    @Mock
    private TableRecordCommonService commonService;

    @Mock
    private RedissonClient redisson;

    @Test
    void getTableTokenShouldReturnToken() {
        // Arrange
        String tableName = "test_table";
        Long tenantId = 1L;
        AccessToken token = new AccessToken();
        token.setAccessKey("ak");
        token.setSecretKey("sk");
        token.setTableName(tableName);
        token.setTenantId(tenantId);

        RedissonBucket bucket = mock(RedissonBucket.class);
        RList list = mock(RList.class);

        when(tokenMapper.selectByExample(any(AccessTokenCriteria.class)))
                .thenReturn(Collections.singletonList(token));
        when(redisson.getBucket(anyString())).thenReturn(bucket);
        when(redisson.getList(anyString())).thenReturn(list);

        try (MockedStatic<AESUtils> mockedAes = mockStatic(AESUtils.class)) {
            mockedAes.when(() -> AESUtils.encrypt(anyString(), anyString()))
                    .thenReturn("encrypted");

            // Act
            String result = accessTokenService.getTableToken(tableName, tenantId);

            // Assert
            assertNotNull(result);
            assertTrue(result.startsWith(AccessTokenService.TOKEN_VERSION_V1));
        }
    }

    @Test
    void fetchAccessBeanFromCacheShouldReturnFromCache() {
        // Arrange
        String ak = "test_ak";
        String sk = "test_sk";
        AccessToken cachedToken = new AccessToken();

        RedissonBucket bucket = mock(RedissonBucket.class);
        when(redisson.getBucket(anyString())).thenReturn(bucket);
        when(bucket.get()).thenReturn(cachedToken);

        // Act
        AccessToken result = accessTokenService.fetchAccessBeanFromCache(ak, sk);

        // Assert
        assertEquals(cachedToken, result);
        verify(tokenMapper, never()).selectByExample(any());
    }

    @Test
    void fetchAccessBeanFromCacheShouldReturnFromDBWhenCacheMiss() {
        // Arrange
        String ak = "test_ak";
        String sk = "test_sk";
        AccessToken dbToken = new AccessToken();
        dbToken.setAccessKey(ak);
        dbToken.setSecretKey(sk);

        RedissonBucket bucket = mock(RedissonBucket.class);

        when(redisson.getBucket(anyString())).thenReturn(bucket);
        when(bucket.get()).thenReturn(null);
        when(tokenMapper.selectByExample(any(AccessTokenCriteria.class)))
                .thenReturn(Collections.singletonList(dbToken));

        // Act
        AccessToken result = accessTokenService.fetchAccessBeanFromCache(ak, sk);

        // Assert
        assertEquals(dbToken, result);
        verify(bucket).set(eq(dbToken), eq(Duration.ofSeconds(5)));
    }

    @Test
    void validateAuthorizationShouldReturnTrueWhenValidToken() {
        // Arrange
        String accessToken = "valid_token";
        AccessToken token = new AccessToken();
        token.setTableName("test_table");
        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setId(1L);
        tableInfo.setDbType("mysql");

        RedissonBucket bucket = mock(RedissonBucket.class);
        HttpServletRequest request = mock(HttpServletRequest.class);

        when(redisson.getBucket(anyString())).thenReturn(bucket);
        when(bucket.get()).thenReturn(token);
        when(commonService.getTableByTableName(anyString())).thenReturn(tableInfo);

        // Act
        boolean result = accessTokenService.validateAuthorization(request, accessToken);

        // Assert
        assertTrue(result);
        verify(request).setAttribute("dataTableId", 1L);
        verify(request).setAttribute("dbType", "mysql");
    }

    @Test
    void validateAuthorizationShouldReturnFalseWhenInvalidToken() {
        // Arrange
        String accessToken = "invalid_token";
        HttpServletRequest request = mock(HttpServletRequest.class);

        RedissonBucket bucket = mock(RedissonBucket.class);
        when(redisson.getBucket(anyString())).thenReturn(bucket);
        when(bucket.get()).thenReturn(null);

        // Act
        boolean result = accessTokenService.validateAuthorization(request, accessToken);

        // Assert
        assertFalse(result);
        verify(commonService, never()).getTableByTableName(anyString());
    }

    @Test
    void createTableTokenShouldInsertNewToken() {
        // Arrange
        String tableName = "test_table";
        Long tenantId = 1L;

        // Act
        accessTokenService.createTableToken(tableName, tenantId);

        // Assert
        verify(tokenMapper).insert(any(AccessToken.class));
    }

}