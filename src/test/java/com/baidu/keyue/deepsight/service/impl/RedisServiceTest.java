package com.baidu.keyue.deepsight.service.impl;

import com.baidu.keyue.deepsight.BaseTest;
import com.baidu.keyue.deepsight.service.tool.RedisService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.TimeUnit;

public class RedisServiceTest extends BaseTest {
    @Autowired
    private RedisService redisService;

    @Test
    public void test() {
        // 存储键值对，不设置过期时间（永久有效）
        redisService.set("permanentKey", "permanentValue");

        // 存储键值对，设置过期时间为 10 秒
        redisService.set("temporaryKey", "temporaryValue", 10, TimeUnit.SECONDS);

        // 获取永久键的值
        String permanentValue = redisService.get("permanentKey");
        System.out.println("Value for key 'permanentKey': " + permanentValue);

        // 获取临时键的值
        String temporaryValue = redisService.get("temporaryKey");
        System.out.println("Value for key 'temporaryKey': " + temporaryValue);

        // 等待 11 秒，让临时键过期
        try {
            Thread.sleep(11000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        // 再次获取临时键的值
        String expiredValue = redisService.get("temporaryKey");
        System.out.println("Value for key 'temporaryKey' after expiration: " + expiredValue);
    }
}
