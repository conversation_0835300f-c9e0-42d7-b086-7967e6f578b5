package com.baidu.keyue.deepsight.service.tenant;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTable;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelation;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendIdMappingRuleMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingDataTableMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRelationMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRuleMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.idmapping.impl.IdMappingManagerServiceImpl;
import com.baidu.keyue.deepsight.service.idmapping.impl.IdMappingRelServiceImpl;
import com.baidu.keyue.deepsight.service.idmapping.impl.IdMappingRuleServiceImpl;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.doCallRealMethod;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class TenantV4UpgradeHandlerTest{


    @Mock
    private IdMappingManagerServiceImpl idMappingManagerService;

    @Mock
    private TenantInfoService tenantInfoService;

    @Mock
    private TenantInfoMapper tenantInfoMapper;

    @Mock
    private TransactionTemplate transactionTemplate;
    @Mock
    private DorisService dorisService;

    @Mock
    private IdMappingDataTableMapper idMappingDataTableMapper;

    @Mock
    private IdMappingRelationMapper idMappingRelMapper;
    @Mock
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Mock
    private IdMappingRuleMapper idMappingRuleMapper;

    @Mock
    private ExtendIdMappingRuleMapper extendIdMappingRuleMapper;

    @Mock
    private IdMappingRelServiceImpl idMappingRelService;

    @Mock
    private IdMappingRuleServiceImpl idMappingRuleService;

    @Mock
    private DataTableManageService dataTableManageService;

    @InjectMocks
    private TenantV4UpgradeHandler tenantV4UpgradeHandler;

    private TenantInfo tenantInfo;
    private TenantDTO tenantDTO;

    @BeforeEach
    void setUp() {
        tenantInfo = new TenantInfo();
        tenantInfo.setId(1L);
        tenantInfo.setVersion(3);

        tenantDTO = new TenantDTO();
        tenantDTO.setTenantId("testTenant");
        tenantDTO.setTenantInfo(tenantInfo);
        tenantDTO.setIsInit(false);

        ReflectionTestUtils.setField(idMappingRelService, "tableFieldMetaInfoMapper", tableFieldMetaInfoMapper);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "dorisService", dorisService);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "idMappingRuleService", idMappingRuleService);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "extendIdMappingRuleMapper", extendIdMappingRuleMapper);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "idMappingDataTableMapper", idMappingDataTableMapper);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "dataTableManageService", dataTableManageService);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "idMappingRelMapper", idMappingRelMapper);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "idMappingRelService", idMappingRelService);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "idMappingRuleMapper", idMappingRuleMapper);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "transactionTemplate", transactionTemplate);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "sqlList",
                List.of("ALTER TABLE `aiob_conversation_session_service_^&` ADD COLUMN `customTagList` ARRAY<VARCHAR(128)> NULL DEFAULT '[]' COMMENT '意向';"));
    }

    @Test
    void testAutoAddIdMappingDataTableExistingTable() {
        IdMappingRelation relation = new IdMappingRelation();
        relation.setTenantId("testTenant");
        relation.setDataTableId(1L);
        relation.setUpdateTime(new Date());
        String userId = "testUser";
        IdMappingDataTable existingTable = new IdMappingDataTable();
    
        when(idMappingDataTableMapper.selectByExample(any())).thenReturn(List.of(existingTable));
    
        tenantV4UpgradeHandler.autoAddIdMappingDataTable(relation, userId);
    
        verify(idMappingDataTableMapper).updateByPrimaryKeySelective(any(IdMappingDataTable.class));
    }

    @Test
    void testInit() {
        assertDoesNotThrow(() -> tenantV4UpgradeHandler.init());
    }

    @Test
    void testConstructor() {
        TenantV4UpgradeHandler handler = new TenantV4UpgradeHandler(dorisService);
        assertNotNull(handler);
    }

    @Test
    void testAddIdRelationNewRelation() {
        String userId = "testUser";
        String tenantId = "testTenant";
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        TableFieldMetaInfo metaInfo = new TableFieldMetaInfo();
        metaInfo.setEnField("taskId");
        metaInfo.setCnField("任务ID");
        when(dataTableManageService.validDataTableByTableName(tableName, tenantId)).thenReturn(dataTableInfo);
        when(idMappingRelMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(List.of(metaInfo));
        doCallRealMethod().when(idMappingRelService).assembleIdMappingRel(anyLong(), anyList(), any());
        tenantV4UpgradeHandler.addIdRelation(userId, tenantId);
    
        verify(idMappingRelMapper).insertSelective(any(IdMappingRelation.class));
    }
    @Test
    void testAddIdRelationNewRelation2() {
        String userId = "testUser";
        String tenantId = "testTenant";
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        TableFieldMetaInfo metaInfo = new TableFieldMetaInfo();
        metaInfo.setEnField("taskId");
        metaInfo.setCnField("任务ID");
        List<IdMappingRule> idMappingRules = new ArrayList<>();
        IdMappingRule rule = new IdMappingRule();
        rule.setPriority(1);
        rule.setEnField("field1");
        idMappingRules.add(rule);
        IdMappingRule rule2 = new IdMappingRule();
        rule2.setPriority(10);
        rule2.setEnField("mobile");
        idMappingRules.add(rule2);
        when(dataTableManageService.validDataTableByTableName(tableName, tenantId)).thenReturn(dataTableInfo);
        when(idMappingRelMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(List.of(metaInfo));
        doCallRealMethod().when(idMappingRelService).assembleIdMappingRel(anyLong(), anyList(), any());
        
        when(idMappingRuleService.getExistedFields(tenantId)).thenReturn(idMappingRules);
        tenantV4UpgradeHandler.addIdRelation(userId, tenantId);

        verify(idMappingRelMapper).insertSelective(any(IdMappingRelation.class));
    }

    @Test
    void testAddIdRelationNewRelation3() {
        String userId = "testUser";
        String tenantId = "testTenant";
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        TableFieldMetaInfo metaInfo = new TableFieldMetaInfo();
        metaInfo.setEnField("taskId");
        metaInfo.setCnField("任务ID");
        List<IdMappingRule> idMappingRules = new ArrayList<>();
        IdMappingRule rule = new IdMappingRule();
        rule.setPriority(2);
        rule.setEnField("field1");
        idMappingRules.add(rule);
        IdMappingRule rule2 = new IdMappingRule();
        rule2.setPriority(10);
        rule2.setEnField("mobile");
        idMappingRules.add(rule2);
        when(dataTableManageService.validDataTableByTableName(tableName, tenantId)).thenReturn(dataTableInfo);
        when(idMappingRelMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(List.of(metaInfo));
        doCallRealMethod().when(idMappingRelService).assembleIdMappingRel(anyLong(), anyList(), any());

        when(idMappingRuleService.getExistedFields(tenantId)).thenReturn(idMappingRules);
        tenantV4UpgradeHandler.addIdRelation(userId, tenantId);

        verify(idMappingRelMapper).insertSelective(any(IdMappingRelation.class));
    }

    @Test
    void testAddIdRelationNewRelation4() {
        String userId = "testUser";
        String tenantId = "testTenant";
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        TableFieldMetaInfo metaInfo = new TableFieldMetaInfo();
        metaInfo.setEnField("taskId");
        metaInfo.setCnField("任务ID");
        List<IdMappingRule> idMappingRules = new ArrayList<>();
        IdMappingRule rule = new IdMappingRule();
        rule.setPriority(2);
        rule.setEnField("field1");
        idMappingRules.add(rule);
        IdMappingRule rule2 = new IdMappingRule();
        rule2.setPriority(1);
        rule2.setEnField("mobile");
        idMappingRules.add(rule2);
        when(dataTableManageService.validDataTableByTableName(tableName, tenantId)).thenReturn(dataTableInfo);
        when(idMappingRelMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(List.of(metaInfo));
        doCallRealMethod().when(idMappingRelService).assembleIdMappingRel(anyLong(), anyList(), any());

        when(idMappingRuleService.getExistedFields(tenantId)).thenReturn(idMappingRules);
        tenantV4UpgradeHandler.addIdRelation(userId, tenantId);

        verify(idMappingRelMapper).insertSelective(any(IdMappingRelation.class));
    }

    @Test
    void testAddIdRelationExistingRelation() {
        String userId = "testUser";
        String tenantId = "testTenant";
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        IdMappingRelation existingRelation = new IdMappingRelation();
        existingRelation.setDataTableId(1L);
        existingRelation.setTenantId(tenantId);
        existingRelation.setId(1L);
        existingRelation.setEnFields("[\"taskId\"]");
        existingRelation.setCnFields("[\"任务ID\"]");
    
        when(dataTableManageService.validDataTableByTableName(tableName, tenantId)).thenReturn(dataTableInfo);
        when(idMappingRelMapper.selectByExample(any())).thenReturn(List.of(existingRelation));
        doNothing().when(idMappingRelService).assembleIdMappingRel(anyLong(), anyList(), any());
    
        tenantV4UpgradeHandler.addIdRelation(userId, tenantId);
    
        verify(idMappingRelMapper).updateByPrimaryKeySelective(any(IdMappingRelation.class));
    }

    @Test
    void testAutoAddIdMappingRuleNoNewRules() {
        IdMappingRelation relation = new IdMappingRelation();
        relation.setDataTableId(1L);
        relation.setEnFields("[\"taskId\"]");
        relation.setCnFields("[\"任务ID\"]");
        String tenantId = "testTenant";
        IdMappingRule existingRule = new IdMappingRule();
        existingRule.setEnField("taskId");
    
        when(idMappingRuleService.getExistedFields(tenantId)).thenReturn(List.of(existingRule));
    
        tenantV4UpgradeHandler.autoAddIdMappingRule(relation, tenantId);
    
        verify(extendIdMappingRuleMapper, never()).batchInsert(anyList());
    }

    @Test
    void testAutoAddIdMappingRuleWithNewRules() {
        IdMappingRelation relation = new IdMappingRelation();
        relation.setDataTableId(1L);
        relation.setEnFields("[\"taskId\"]");
        relation.setCnFields("[\"任务ID\"]");
        String tenantId = "testTenant";
        IdMappingRule existingRule = new IdMappingRule();
        existingRule.setEnField("existingField");
    
        when(idMappingRuleService.getExistedFields(tenantId)).thenReturn(List.of(existingRule));
        when(idMappingRuleService.getDefaultDesc(anyList(), anyLong())).thenReturn(Map.of());
    
        tenantV4UpgradeHandler.autoAddIdMappingRule(relation, tenantId);
    
        verify(extendIdMappingRuleMapper).batchInsert(anyList());
    }

    @Test
    void testAutoAddIdMappingDataTableNewTable() {
        IdMappingRelation relation = new IdMappingRelation();
        relation.setTenantId("testTenant");
        relation.setDataTableId(1L);
        relation.setUpdateTime(new Date());
        String userId = "testUser";
    
        when(idMappingDataTableMapper.selectByExample(any())).thenReturn(Collections.emptyList());
    
        tenantV4UpgradeHandler.autoAddIdMappingDataTable(relation, userId);
    
        verify(idMappingDataTableMapper).insert(any(IdMappingDataTable.class));
    }

    @Test
    void testAutoAddIdMappingRuleWithNewRules1() {
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "dorisService", dorisService);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "idMappingRuleMapper", idMappingRuleMapper);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "idMappingDataTableMapper", idMappingDataTableMapper);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "extendIdMappingRuleMapper", extendIdMappingRuleMapper);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "idMappingRelService", idMappingRelService);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "idMappingRelMapper", idMappingRelMapper);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "idMappingRuleService", idMappingRuleService);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "dataTableManageService", dataTableManageService);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "idMappingManagerService", idMappingManagerService);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "tenantInfoMapper", tenantInfoMapper);
        ReflectionTestUtils.setField(tenantV4UpgradeHandler, "transactionTemplate", transactionTemplate);
    
        IdMappingRelation relation = new IdMappingRelation();
        relation.setTenantId("testTenant");
        relation.setDataTableId(1L);
        relation.setUpdateTime(new Date());
        relation.setEnFields("[\"taskId\"]");
        relation.setCnFields("[\"taskId\"]");
    
        IdMappingRule existingRule = new IdMappingRule();
        existingRule.setEnField("existingField");
        when(idMappingRuleService.getExistedFields("testTenant")).thenReturn(List.of(existingRule));
        when(idMappingRuleService.getDefaultDesc(anyList(), anyLong())).thenReturn(Collections.emptyMap());
    
        tenantV4UpgradeHandler.autoAddIdMappingRule(relation, "testTenant");
    
        verify(extendIdMappingRuleMapper).batchInsert(anyList());
    }

    @Test
    void testAutoAddIdMappingRuleWithNoNewRules() {
        IdMappingRelation relation = new IdMappingRelation();
        relation.setTenantId("testTenant");
        relation.setDataTableId(1L);
        relation.setUpdateTime(new Date());
        relation.setEnFields("[\"taskId\"]");
        relation.setCnFields("[\"taskId\"]");
    
        IdMappingRule existingRule = new IdMappingRule();
        existingRule.setEnField("existingField");
        when(idMappingRuleService.getExistedFields("testTenant")).thenReturn(List.of(existingRule));
    
        tenantV4UpgradeHandler.autoAddIdMappingRule(relation, "testTenant");
    }

    @Test
    void testAutoAddIdMappingDataTableNewTable1() {
        IdMappingRelation relation = new IdMappingRelation();
        relation.setTenantId("testTenant");
        relation.setDataTableId(1L);
        relation.setUpdateTime(new Date());
        String userId = "testUser";
        when(idMappingDataTableMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        tenantV4UpgradeHandler.autoAddIdMappingDataTable(relation, userId);
        verify(idMappingDataTableMapper).insert(any(IdMappingDataTable.class));
    }
    

    @Test
    void testAddIdRelationWhenNoExistingRelation() {
        String userId = "testUser";
        String tenantId = "testTenant";
        String tableName = "aiob_conversation_session_service_testTenant";
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        IdMappingRule existingRule = new IdMappingRule();
        existingRule.setEnField("existingField");
        existingRule.setCnField("existingField");
        IdMappingRelation relation = new IdMappingRelation();
        relation.setTenantId("testTenant");
        relation.setDataTableId(1L);
        relation.setUpdateTime(new Date());
        relation.setEnFields("[\"taskId\"]");
        relation.setCnFields("[\"taskId\"]");
        when(dataTableManageService.validDataTableByTableName(tableName, tenantId)).thenReturn(dataTableInfo);
        when(idMappingRelMapper.selectByExample(any())).thenReturn(List.of(relation));
        when(idMappingRuleService.getExistedFields(tenantId)).thenReturn(List.of(existingRule));
    
        tenantV4UpgradeHandler.addIdRelation(userId, tenantId);
    
        verify(dataTableManageService).validDataTableByTableName(tableName, tenantId);
    }

    @Test
    void testAddIdRelationWhenExistingRelation() {
        String userId = "testUser";
        String tenantId = "testTenant";
        String tableName = "aiob_conversation_session_service_testTenant";
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        IdMappingRelation existingRelation = new IdMappingRelation();
        existingRelation.setId(1L);
        existingRelation.setDataTableId(1L);
        existingRelation.setTenantId(tenantId);
        existingRelation.setId(1L);
        existingRelation.setEnFields("[\"taskId\"]");
        existingRelation.setCnFields("[\"任务ID\"]");
    
        when(dataTableManageService.validDataTableByTableName(tableName, tenantId)).thenReturn(dataTableInfo);
        when(idMappingRelMapper.selectByExample(any())).thenReturn(List.of(existingRelation));
        when(idMappingRuleService.getExistedFields(tenantId)).thenReturn(new ArrayList<>());
    
        tenantV4UpgradeHandler.addIdRelation(userId, tenantId);
    
        verify(dataTableManageService).validDataTableByTableName(tableName, tenantId);
        verify(idMappingRelMapper).updateByPrimaryKeySelective(any(IdMappingRelation.class));
    }

    @Test
    void testAutoAddIdMappingRule() {
        IdMappingRelation relation = new IdMappingRelation();
        relation.setTenantId("testTenant");
        relation.setDataTableId(1L);
        relation.setUpdateTime(new Date());
        relation.setEnFields("[\"taskId\"]");
        relation.setCnFields("[\"taskId\"]");
        relation.setTenantId("testTenant");
        relation.setDataTableId(1L);
        doThrow(new RuntimeException()).when(idMappingManagerService).setSwitchStatus(true, tenantDTO.getTenantId());
        tenantV4UpgradeHandler.executeUpgrade(tenantDTO);
    }
    @Test
    void testAutoAddIdMappingRule2() {
        IdMappingRelation relation = new IdMappingRelation();
        relation.setTenantId("testTenant");
        relation.setDataTableId(1L);
        relation.setUpdateTime(new Date());
        relation.setEnFields("[\"taskId\"]");
        relation.setCnFields("[\"taskId\"]");
        relation.setTenantId("testTenant");
        relation.setDataTableId(1L);
        tenantV4UpgradeHandler.executeUpgrade(tenantDTO);
    }
}