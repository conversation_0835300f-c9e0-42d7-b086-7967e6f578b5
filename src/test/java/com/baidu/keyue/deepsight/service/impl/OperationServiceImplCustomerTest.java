package com.baidu.keyue.deepsight.service.impl;

import com.baidu.keyue.deepsight.BaseTest;
import com.baidu.keyue.deepsight.models.operation.response.OperationModeResponse;
import com.baidu.keyue.deepsight.service.operation.impl.OperationServiceImpl;
import com.baidu.kybase.commons.utils.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class OperationServiceImplCustomerTest extends BaseTest {
    @Autowired
    private OperationServiceImpl operationService;

    @Test
    public void getTenantOperationMode() {
        OperationModeResponse tenantOperationMode = operationService.getTenantOperationMode("1");
        System.out.println(JsonUtil.toJson(tenantOperationMode));
    }
}
