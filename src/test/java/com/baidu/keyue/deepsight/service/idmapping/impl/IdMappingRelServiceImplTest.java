package com.baidu.keyue.deepsight.service.idmapping.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.models.idmapping.response.relation.IdMappingDatasetFieldListResponse;
import com.baidu.keyue.deepsight.models.idmapping.response.relation.IdMappingDatasetListResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelation;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRelationMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class IdMappingRelServiceImplTest{


    @Mock
    private IdMappingRelationMapper idMappingRelMapper;

    @Mock
    private DataTableInfoMapper dataTableInfoMapper;

    private static final String TEST_TENANT_ID = "test_tenant";
    private static final Long TEST_DATA_TABLE_ID = 123L;

    @Mock
    private DataTableManageService dataTableManageService;

    @Mock
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @InjectMocks
    private IdMappingRelServiceImpl idMappingRelService;

    @Test
    void listIdMappingDatasetFieldShouldReturnFieldsResponseWhenFieldsFound() {
        // Arrange
        TableFieldMetaInfo field = new TableFieldMetaInfo();
        field.setEnField("test_field");
        field.setCnField("测试字段");
        List<TableFieldMetaInfo> mockFields = Collections.singletonList(field);
    
        when(dataTableManageService.validDataTableByTenantId(TEST_DATA_TABLE_ID)).thenReturn(new DataTableInfo());
        when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(mockFields);
    
        // Act
        IdMappingDatasetFieldListResponse response = idMappingRelService.listIdMappingDatasetField(TEST_DATA_TABLE_ID);
    
        // Assert
        assertNotNull(response);
        assertEquals(1, response.getList().size());
        assertEquals("test_field", response.getList().get(0).getTableEnField());
        assertEquals("测试字段", response.getList().get(0).getTableCnField());
        verify(dataTableManageService).validDataTableByTenantId(TEST_DATA_TABLE_ID);
        verify(tableFieldMetaInfoMapper).selectByExample(any(TableFieldMetaInfoCriteria.class));
    }

    @Test
    void listIdMappingDatasetFieldShouldFilterOutUserOneIdField() {
        // Arrange
        TableFieldMetaInfo field1 = new TableFieldMetaInfo();
        field1.setEnField("normal_field");
        field1.setCnField("普通字段");
        
        TableFieldMetaInfo field2 = new TableFieldMetaInfo();
        field2.setEnField(Constants.TABLE_USER_ONE_ID);
        field2.setCnField("用户ID");
        
        List<TableFieldMetaInfo> mockFields = List.of(field1, field2);
    
        when(dataTableManageService.validDataTableByTenantId(TEST_DATA_TABLE_ID)).thenReturn(new DataTableInfo());
        when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(Collections.singletonList(field1));
    
        // Act
        IdMappingDatasetFieldListResponse response = idMappingRelService.listIdMappingDatasetField(TEST_DATA_TABLE_ID);
    
        // Assert
        assertNotNull(response);
        assertEquals(1, response.getList().size());
        assertEquals("normal_field", response.getList().get(0).getTableEnField());
        verify(dataTableManageService).validDataTableByTenantId(TEST_DATA_TABLE_ID);
        verify(tableFieldMetaInfoMapper).selectByExample(any(TableFieldMetaInfoCriteria.class));
    }

    @Test
    void listIdMappingDatasetFieldShouldReturnEmptyResponseWhenNoFieldsFound() {
        // Arrange
        when(dataTableManageService.validDataTableByTenantId(TEST_DATA_TABLE_ID)).thenReturn(new DataTableInfo());
        when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class)))
                .thenReturn(Collections.emptyList());
    
        // Act
        IdMappingDatasetFieldListResponse response = idMappingRelService.listIdMappingDatasetField(TEST_DATA_TABLE_ID);
    
        // Assert
        assertNotNull(response);
        assertTrue(response.getList().isEmpty());
        verify(dataTableManageService).validDataTableByTenantId(TEST_DATA_TABLE_ID);
        verify(tableFieldMetaInfoMapper).selectByExample(any(TableFieldMetaInfoCriteria.class));
    }

    @Test
    void listIdMappingDatasetShouldReturnResponseWithUsedAndAvailableDatasets() {
        try (MockedStatic<WebContextHolder> mockedWebContext = mockStatic(WebContextHolder.class)) {
            // Arrange
            mockedWebContext.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            
            IdMappingRelation relation = new IdMappingRelation();
            relation.setDataTableId(1L);
            List<IdMappingRelation> mockRelations = Collections.singletonList(relation);
            
            DataTableInfo dataTableInfo = new DataTableInfo();
            dataTableInfo.setId(1L);
            dataTableInfo.setTableName("test_table");
            List<DataTableInfo> mockDataTableInfos = Collections.singletonList(dataTableInfo);
            
            when(idMappingRelMapper.selectByExample(any())).thenReturn(mockRelations);
            when(dataTableInfoMapper.selectByExample(any())).thenReturn(mockDataTableInfos);
            
            // Act
            IdMappingDatasetListResponse response = idMappingRelService.listIdMappingDataset();
            
            // Assert
            assertNotNull(response);
            assertEquals(1, response.getList().size());
            verify(idMappingRelMapper).selectByExample(any());
            verify(dataTableInfoMapper).selectByExample(any());
        }
    }

    @Test
    void listIdMappingDatasetShouldReturnEmptyResponseWhenNoDataFound() {
        try (MockedStatic<WebContextHolder> mockedWebContext = mockStatic(WebContextHolder.class)) {
            // Arrange
            mockedWebContext.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            
            when(idMappingRelMapper.selectByExample(any())).thenReturn(Collections.emptyList());
            when(dataTableInfoMapper.selectByExample(any())).thenReturn(Collections.emptyList());
            
            // Act
            IdMappingDatasetListResponse response = idMappingRelService.listIdMappingDataset();
            
            // Assert
            assertNotNull(response);
            assertEquals(0, response.getList().size());
            verify(idMappingRelMapper).selectByExample(any());
            verify(dataTableInfoMapper).selectByExample(any());
        }
    }

}