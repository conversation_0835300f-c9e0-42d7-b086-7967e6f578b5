package com.baidu.keyue.deepsight.service.impl;

import com.baidu.keyue.deepsight.BaseTest;
import com.baidu.keyue.deepsight.database.service.impl.UserPropertiesServiceImpl;
import com.baidu.keyue.deepsight.models.rules.response.UserPropertiesResult;
import com.baidu.kybase.commons.utils.JsonUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class UserPropertiesServiceImplCustomerTest extends BaseTest {
    @Autowired
    private UserPropertiesServiceImpl userPropertiesService;

    @Test
    public void test() {
        List<UserPropertiesResult> includeBaidu = userPropertiesService.getUserProperties("test_122865401", true);
        System.out.println(JsonUtil.toJson(includeBaidu));
        List<UserPropertiesResult> excludeBaidu = userPropertiesService.getUserProperties("test_122865401", false);
        System.out.println(JsonUtil.toJson(excludeBaidu));
    }
}
