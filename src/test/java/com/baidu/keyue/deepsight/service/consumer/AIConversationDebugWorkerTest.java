package com.baidu.keyue.deepsight.service.consumer;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableDataCurlRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.TableSyncDetailResponse;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordResp;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordResp.AiobDiagramRecordData;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.service.datamanage.AiobRobotVersionService;
import com.baidu.keyue.deepsight.service.datamanage.TableContentService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.keyue.deepsight.service.tenant.impl.TenantInfoServiceImpl;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.kybase.commons.utils.HttpUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
@TestPropertySource(properties = {
        "aiob.debugPath=testPath",
        "aiob.url=http://127.0.0.1:8080",
        "kafka.topics.dataSync=testTopic"
})
public class AIConversationDebugWorkerTest {


    @Mock
    private TableRecordCommonService commonService;

    @Mock
    private AiobRobotVersionService aiobRobotVersionService;

    @Mock
    private AiobSOPService sopService;
    @Mock
    private TenantInfoServiceImpl tenantInfoService;

    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;

    @InjectMocks
    private AIConversationDebugWorker worker;

    @Mock
    private TableContentService contentService;
    private Map<String, String> headers = new HashMap<>();
    
    private final String agentId = "1";
    private final Long tenantId = 123L;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(worker, "aiobUrl", "http://127.0.0.1:8080");
        ReflectionTestUtils.setField(worker, "debugPath", "/mock/path");
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "debugPathRetry", 3);
        ReflectionTestUtils.setField(worker, "topic", "testTopic");
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "sopService", sopService);
        ReflectionTestUtils.setField(worker, "aiobRobotVersionService", aiobRobotVersionService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);
         
        headers.put("Content-Type", "application/json");
        headers.put("agent-id", String.valueOf(tenantId));
        headers.put(Constants.ACCESS_TOKEN, Constants.ACCESS_TOKEN_VALUE);
    }

    @Test
    void testConstructor() {
        assertNotNull(worker);
    }

    @Test
    void testProcessDataTenantIdIsNUll() throws Exception {
        String testData = "\"{\\\"uid\\\":null,\\\"userName\\\":null,\\\"queryText\\\":\\\"\\\",\\\"queryTime\\\":\\\"2025-06-04 17:55:47\\\",\\\"agentName\\\":\\\"灵活画布-吴山虎测试勿删_aiob_4289257709502464\\\",\\\"startId\\\":null,\\\"online\\\":true,\\\"endTime\\\":\\\"2025-06-04 17:55:48\\\",\\\"sessionId\\\":\\\"3808251032371200_11231e0a3fb045e991a6bd67be8c1a60\\\",\\\"topicId\\\":null,\\\"variables\\\":null,\\\"variablesJson\\\":\\\"{\\\\\\\"sys_date\\\\\\\":\\\\\\\"2025-06-04\\\\\\\",\\\\\\\"date\\\\\\\":\\\\\\\"2025-06-04\\\\\\\",\\\\\\\"last_intent\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"week\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"sys_phonenum\\\\\\\":\\\\\\\"13002077690\\\\\\\",\\\\\\\"sys_weekday\\\\\\\":\\\\\\\"星期三\\\\\\\",\\\\\\\"LLM_rewrite_query\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"last_task_prompt\\\\\\\":\\\\\\\"你好\\\\\\\",\\\\\\\"sys_time\\\\\\\":\\\\\\\"17:55:37\\\\\\\",\\\\\\\"time\\\\\\\":\\\\\\\"17:55:47\\\\\\\"}\\\",\\\"agentId\\\":\\\"bbf4f3c4-5959-454b-b191-e0cef1fb0523\\\",\\\"queryId\\\":\\\"ee28eac3-1d67-44cf-911c-469cd86edf61\\\",\\\"answer\\\":\\\"[{\\\\\\\"chunkId\\\\\\\":0,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":null,\\\\\\\"nodeId\\\\\\\":\\\\\\\"start00000000000000000000\\\\\\\",\\\\\\\"name\\\\\\\":null,\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":null,\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":\\\\\\\"start\\\\\\\",\\\\\\\"debugReplyTypeV2\\\\\\\":\\\\\\\"start\\\\\\\",\\\\\\\"debugNodeName\\\\\\\":null,\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":3,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":null,\\\\\\\"debugInputVariable\\\\\\\":{},\\\\\\\"debugOutputVariable\\\\\\\":{},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null},{\\\\\\\"chunkId\\\\\\\":1,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":null,\\\\\\\"nodeId\\\\\\\":\\\\\\\"0egyuQpgoQcDDGVJrOjQeyImAash4kpRjGVG\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 1\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":null,\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":null,\\\\\\\"debugReplyTypeV2\\\\\\\":null,\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 1\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":1,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":null,\\\\\\\"debugInputVariable\\\\\\\":{},\\\\\\\"debugOutputVariable\\\\\\\":{},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null},{\\\\\\\"chunkId\\\\\\\":2,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":\\\\\\\"0egyuQpgoQcDDGVJrOjQeyImAash4kpRjGVG\\\\\\\",\\\\\\\"nodeId\\\\\\\":\\\\\\\"4FDe2D7stOGnUMf35rCRIFbOczTFJSH3fcNI\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 1, 步骤1\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":{\\\\\\\"type\\\\\\\":1,\\\\\\\"audioData\\\\\\\":null,\\\\\\\"text\\\\\\\":\\\\\\\"你好\\\\\\\",\\\\\\\"textWithFullUrl\\\\\\\":\\\\\\\"你好\\\\\\\",\\\\\\\"collectImage\\\\\\\":null,\\\\\\\"textList\\\\\\\":null,\\\\\\\"clarifyGuide\\\\\\\":null,\\\\\\\"replySource\\\\\\\":\\\\\\\"TEXT_REPLY\\\\\\\",\\\\\\\"progressDesc\\\\\\\":null,\\\\\\\"engineType\\\\\\\":null,\\\\\\\"showDocumentSource\\\\\\\":null,\\\\\\\"showDialogAnalysis\\\\\\\":false,\\\\\\\"documents\\\\\\\":null,\\\\\\\"tableQAInfo\\\\\\\":null,\\\\\\\"docDebug\\\\\\\":null,\\\\\\\"smTableqaDebug\\\\\\\":null,\\\\\\\"faqSearch\\\\\\\":null,\\\\\\\"slotCollectInfo\\\\\\\":null,\\\\\\\"plugin\\\\\\\":null,\\\\\\\"url\\\\\\\":null,\\\\\\\"buttons\\\\\\\":null,\\\\\\\"intentName\\\\\\\":null,\\\\\\\"displayAIGenerated\\\\\\\":false,\\\\\\\"sensitiveWord\\\\\\\":null,\\\\\\\"reasoningResult\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"showThinkingProcess\\\\\\\":0,\\\\\\\"thinkingProcessExpand\\\\\\\":0,\\\\\\\"searchInfoList\\\\\\\":[],\\\\\\\"showWebSearchResult\\\\\\\":0,\\\\\\\"webSearchStatus\\\\\\\":0,\\\\\\\"webSearchResultCount\\\\\\\":0,\\\\\\\"knowledgeSourceType\\\\\\\":0,\\\\\\\"knowledgeI18nType\\\\\\\":0,\\\\\\\"empty\\\\\\\":false,\\\\\\\"isRepeatable\\\\\\\":false},\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":\\\\\\\"text\\\\\\\",\\\\\\\"debugReplyTypeV2\\\\\\\":\\\\\\\"text\\\\\\\",\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 1, 步骤1\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":9,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"text\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"你好\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":[{\\\\\\\"text\\\\\\\":\\\\\\\"你好\\\\\\\"}],\\\\\\\"debugInputVariable\\\\\\\":{\\\\\\\"date\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"last_intent\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"week\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"time\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"debugOutputVariable\\\\\\\":{\\\\\\\"date\\\\\\\":\\\\\\\"2025-06-04\\\\\\\",\\\\\\\"last_intent\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"week\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"time\\\\\\\":\\\\\\\"17:55:47\\\\\\\"},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":{},\\\\\\\"debugReferencedVariable\\\\\\\":{}},{\\\\\\\"chunkId\\\\\\\":3,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":null,\\\\\\\"nodeId\\\\\\\":\\\\\\\"ciyEjmek7WazuC3HidXrSczGabVeDD98PFnv\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 2\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":null,\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":null,\\\\\\\"debugReplyTypeV2\\\\\\\":null,\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 2\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":1,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":null,\\\\\\\"debugInputVariable\\\\\\\":{},\\\\\\\"debugOutputVariable\\\\\\\":{},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null},{\\\\\\\"chunkId\\\\\\\":4,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":\\\\\\\"ciyEjmek7WazuC3HidXrSczGabVeDD98PFnv\\\\\\\",\\\\\\\"nodeId\\\\\\\":\\\\\\\"qLO4KF8AtBoBxeKHQvmG72ncKlDtTiIaOMex\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 2, 步骤1\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":{\\\\\\\"type\\\\\\\":1,\\\\\\\"audioData\\\\\\\":null,\\\\\\\"text\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"textWithFullUrl\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"collectImage\\\\\\\":null,\\\\\\\"textList\\\\\\\":null,\\\\\\\"clarifyGuide\\\\\\\":null,\\\\\\\"replySource\\\\\\\":\\\\\\\"CAPTURE\\\\\\\",\\\\\\\"progressDesc\\\\\\\":null,\\\\\\\"engineType\\\\\\\":null,\\\\\\\"showDocumentSource\\\\\\\":null,\\\\\\\"showDialogAnalysis\\\\\\\":false,\\\\\\\"documents\\\\\\\":null,\\\\\\\"tableQAInfo\\\\\\\":null,\\\\\\\"docDebug\\\\\\\":null,\\\\\\\"smTableqaDebug\\\\\\\":null,\\\\\\\"faqSearch\\\\\\\":null,\\\\\\\"slotCollectInfo\\\\\\\":null,\\\\\\\"plugin\\\\\\\":null,\\\\\\\"url\\\\\\\":null,\\\\\\\"buttons\\\\\\\":null,\\\\\\\"intentName\\\\\\\":null,\\\\\\\"displayAIGenerated\\\\\\\":false,\\\\\\\"sensitiveWord\\\\\\\":null,\\\\\\\"reasoningResult\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"showThinkingProcess\\\\\\\":0,\\\\\\\"thinkingProcessExpand\\\\\\\":0,\\\\\\\"searchInfoList\\\\\\\":[],\\\\\\\"showWebSearchResult\\\\\\\":0,\\\\\\\"webSearchStatus\\\\\\\":0,\\\\\\\"webSearchResultCount\\\\\\\":0,\\\\\\\"knowledgeSourceType\\\\\\\":0,\\\\\\\"knowledgeI18nType\\\\\\\":0,\\\\\\\"empty\\\\\\\":true,\\\\\\\"isRepeatable\\\\\\\":false},\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":\\\\\\\"caption\\\\\\\",\\\\\\\"debugReplyTypeV2\\\\\\\":\\\\\\\"caption\\\\\\\",\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 2, 步骤1\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":2,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"image\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"text\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":[{\\\\\\\"image\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"text\\\\\\\":\\\\\\\"\\\\\\\"}],\\\\\\\"debugInputVariable\\\\\\\":{\\\\\\\"last_user_response\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"debugOutputVariable\\\\\\\":{\\\\\\\"last_user_response\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null}]\\\",\\\"recommendList\\\":\\\"[]\\\",\\\"tableQaInfo\\\":null,\\\"intent\\\":\\\"\\\",\\\"agentType\\\":null,\\\"replySource\\\":null,\\\"effectiveQa\\\":false,\\\"channel\\\":null,\\\"tenantChannel\\\":null,\\\"querySource\\\":null,\\\"querySourceInfo\\\":null,\\\"multimodal\\\":\\\"\\\",\\\"qps\\\":1,\\\"solved\\\":false,\\\"toManual\\\":false,\\\"totalTokens\\\":0,\\\"knowledgeTypeSet\\\":null,\\\"knowledgeInfo\\\":null,\\\"intentInfo\\\":null,\\\"rewriteQuery\\\":null,\\\"synonymRewriteQuery\\\":\\\"\\\",\\\"generationNodeList\\\":null,\\\"collectNode\\\":null,\\\"conditionNodeList\\\":null,\\\"instructionNode\\\":null,\\\"unMatchNode\\\":null,\\\"errorNode\\\":null,\\\"intentNode\\\":null,\\\"overMaxStepNode\\\":null,\\\"sensitiveIntentNode\\\":null,\\\"firstChunkTime\\\":0.453,\\\"instruction\\\":[],\\\"interventionRuleStatistics\\\":{\\\"unMatch\\\":0,\\\"entityCheckFail\\\":0,\\\"sensitive\\\":0,\\\"manual\\\":0},\\\"userInfoPair\\\":\\\"\\\",\\\"redBookAnswer\\\":null,\\\"queryCard\\\":null,\\\"hint\\\":null,\\\"hintType\\\":null,\\\"redBookKosId\\\":null,\\\"dialogCount\\\":0,\\\"versionId\\\":\\\"8b61c419-687f-4339-beb5-fa4a9ed22dcb\\\"}\"";
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);

        TableSyncDetailResponse mockResponse = new TableSyncDetailResponse();
        mockResponse.setStatus(200);
        when(contentService.getSyncInfo(any(GetTableDataCurlRequest.class))).thenReturn(mockResponse);

        TenantInfo mockTenant = new TenantInfo();
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(mockTenant);
        String mockResp = "{\n" +
                "  \"requestId\" : \"f3a121d6256649f3a8b78d47c8114917\",\n" +
                "  \"time\" : 1749203221576,\n" +
                "  \"code\" : 200,\n" +
                "  \"msg\" : \"OK\",\n" +
                "  \"data\" : {\n" +
                "    \"robotId\" : \"f359ef92-af50-4e53-868d-fd26334b13a1\",\n" +
                "    \"botVersionId\" : 4687053755351040,\n" +
                "    \"botVersionName\" : \"灵活画布-吴山虎测试勿删20250605172450\",\n" +
                "    \"agentId\" : \"bbf4f3c4-5959-454b-b191-e0cef1fb0523\"\n" +
                "  }\n" +
                "}";
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            mockedHttpUtil.when(() -> HttpUtil.get(anyString(), any(Map.class))).thenReturn(mockResp);
            worker.processData(testData);
        }

    }

    @Test
    void testProcessData() throws Exception {
        String testData = "\"{\\\"uid\\\":null,\\\"userName\\\":null,\\\"queryText\\\":\\\"\\\",\\\"queryTime\\\":\\\"2025-06-04 17:55:47\\\",\\\"agentName\\\":\\\"灵活画布-吴山虎测试勿删_aiob_4289257709502464\\\",\\\"startId\\\":null,\\\"online\\\":true,\\\"endTime\\\":\\\"2025-06-04 17:55:48\\\",\\\"sessionId\\\":\\\"3808251032371200_11231e0a3fb045e991a6bd67be8c1a60\\\",\\\"topicId\\\":null,\\\"variables\\\":null,\\\"variablesJson\\\":\\\"{\\\\\\\"sys_date\\\\\\\":\\\\\\\"2025-06-04\\\\\\\",\\\\\\\"date\\\\\\\":\\\\\\\"2025-06-04\\\\\\\",\\\\\\\"last_intent\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"week\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"sys_phonenum\\\\\\\":\\\\\\\"13002077690\\\\\\\",\\\\\\\"sys_weekday\\\\\\\":\\\\\\\"星期三\\\\\\\",\\\\\\\"LLM_rewrite_query\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"last_task_prompt\\\\\\\":\\\\\\\"你好\\\\\\\",\\\\\\\"sys_time\\\\\\\":\\\\\\\"17:55:37\\\\\\\",\\\\\\\"time\\\\\\\":\\\\\\\"17:55:47\\\\\\\"}\\\",\\\"agentId\\\":\\\"bbf4f3c4-5959-454b-b191-e0cef1fb0523\\\",\\\"queryId\\\":\\\"ee28eac3-1d67-44cf-911c-469cd86edf61\\\",\\\"answer\\\":\\\"[{\\\\\\\"chunkId\\\\\\\":0,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"blockId\\\\\\\":null,\\\\\\\"nodeId\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"name\\\\\\\":null,\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":null,\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":\\\\\\\"start\\\\\\\",\\\\\\\"debugReplyTypeV2\\\\\\\":\\\\\\\"start\\\\\\\",\\\\\\\"debugNodeName\\\\\\\":null,\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":3,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":null,\\\\\\\"debugInputVariable\\\\\\\":{},\\\\\\\"debugOutputVariable\\\\\\\":{},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null},{\\\\\\\"chunkId\\\\\\\":1,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":null,\\\\\\\"nodeId\\\\\\\":\\\\\\\"0egyuQpgoQcDDGVJrOjQeyImAash4kpRjGVG\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 1\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":null,\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":null,\\\\\\\"debugReplyTypeV2\\\\\\\":null,\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 1\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":1,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":null,\\\\\\\"debugInputVariable\\\\\\\":{},\\\\\\\"debugOutputVariable\\\\\\\":{},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null},{\\\\\\\"chunkId\\\\\\\":2,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":\\\\\\\"0egyuQpgoQcDDGVJrOjQeyImAash4kpRjGVG\\\\\\\",\\\\\\\"nodeId\\\\\\\":\\\\\\\"4FDe2D7stOGnUMf35rCRIFbOczTFJSH3fcNI\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 1, 步骤1\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":{\\\\\\\"type\\\\\\\":1,\\\\\\\"audioData\\\\\\\":null,\\\\\\\"text\\\\\\\":\\\\\\\"你好\\\\\\\",\\\\\\\"textWithFullUrl\\\\\\\":\\\\\\\"你好\\\\\\\",\\\\\\\"collectImage\\\\\\\":null,\\\\\\\"textList\\\\\\\":null,\\\\\\\"clarifyGuide\\\\\\\":null,\\\\\\\"replySource\\\\\\\":\\\\\\\"TEXT_REPLY\\\\\\\",\\\\\\\"progressDesc\\\\\\\":null,\\\\\\\"engineType\\\\\\\":null,\\\\\\\"showDocumentSource\\\\\\\":null,\\\\\\\"showDialogAnalysis\\\\\\\":false,\\\\\\\"documents\\\\\\\":null,\\\\\\\"tableQAInfo\\\\\\\":null,\\\\\\\"docDebug\\\\\\\":null,\\\\\\\"smTableqaDebug\\\\\\\":null,\\\\\\\"faqSearch\\\\\\\":null,\\\\\\\"slotCollectInfo\\\\\\\":null,\\\\\\\"plugin\\\\\\\":null,\\\\\\\"url\\\\\\\":null,\\\\\\\"buttons\\\\\\\":null,\\\\\\\"intentName\\\\\\\":null,\\\\\\\"displayAIGenerated\\\\\\\":false,\\\\\\\"sensitiveWord\\\\\\\":null,\\\\\\\"reasoningResult\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"showThinkingProcess\\\\\\\":0,\\\\\\\"thinkingProcessExpand\\\\\\\":0,\\\\\\\"searchInfoList\\\\\\\":[],\\\\\\\"showWebSearchResult\\\\\\\":0,\\\\\\\"webSearchStatus\\\\\\\":0,\\\\\\\"webSearchResultCount\\\\\\\":0,\\\\\\\"knowledgeSourceType\\\\\\\":0,\\\\\\\"knowledgeI18nType\\\\\\\":0,\\\\\\\"empty\\\\\\\":false,\\\\\\\"isRepeatable\\\\\\\":false},\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":\\\\\\\"text\\\\\\\",\\\\\\\"debugReplyTypeV2\\\\\\\":\\\\\\\"text\\\\\\\",\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 1, 步骤1\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":9,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"text\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"你好\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":[{\\\\\\\"text\\\\\\\":\\\\\\\"你好\\\\\\\"}],\\\\\\\"debugInputVariable\\\\\\\":{\\\\\\\"date\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"last_intent\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"week\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"time\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"debugOutputVariable\\\\\\\":{\\\\\\\"date\\\\\\\":\\\\\\\"2025-06-04\\\\\\\",\\\\\\\"last_intent\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"week\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"time\\\\\\\":\\\\\\\"17:55:47\\\\\\\"},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":{},\\\\\\\"debugReferencedVariable\\\\\\\":{}},{\\\\\\\"chunkId\\\\\\\":3,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":null,\\\\\\\"nodeId\\\\\\\":\\\\\\\"ciyEjmek7WazuC3HidXrSczGabVeDD98PFnv\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 2\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":null,\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":null,\\\\\\\"debugReplyTypeV2\\\\\\\":null,\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 2\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":1,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":null,\\\\\\\"debugInputVariable\\\\\\\":{},\\\\\\\"debugOutputVariable\\\\\\\":{},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null},{\\\\\\\"chunkId\\\\\\\":4,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":\\\\\\\"ciyEjmek7WazuC3HidXrSczGabVeDD98PFnv\\\\\\\",\\\\\\\"nodeId\\\\\\\":\\\\\\\"qLO4KF8AtBoBxeKHQvmG72ncKlDtTiIaOMex\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 2, 步骤1\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":{\\\\\\\"type\\\\\\\":1,\\\\\\\"audioData\\\\\\\":null,\\\\\\\"text\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"textWithFullUrl\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"collectImage\\\\\\\":null,\\\\\\\"textList\\\\\\\":null,\\\\\\\"clarifyGuide\\\\\\\":null,\\\\\\\"replySource\\\\\\\":\\\\\\\"CAPTURE\\\\\\\",\\\\\\\"progressDesc\\\\\\\":null,\\\\\\\"engineType\\\\\\\":null,\\\\\\\"showDocumentSource\\\\\\\":null,\\\\\\\"showDialogAnalysis\\\\\\\":false,\\\\\\\"documents\\\\\\\":null,\\\\\\\"tableQAInfo\\\\\\\":null,\\\\\\\"docDebug\\\\\\\":null,\\\\\\\"smTableqaDebug\\\\\\\":null,\\\\\\\"faqSearch\\\\\\\":null,\\\\\\\"slotCollectInfo\\\\\\\":null,\\\\\\\"plugin\\\\\\\":null,\\\\\\\"url\\\\\\\":null,\\\\\\\"buttons\\\\\\\":null,\\\\\\\"intentName\\\\\\\":null,\\\\\\\"displayAIGenerated\\\\\\\":false,\\\\\\\"sensitiveWord\\\\\\\":null,\\\\\\\"reasoningResult\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"showThinkingProcess\\\\\\\":0,\\\\\\\"thinkingProcessExpand\\\\\\\":0,\\\\\\\"searchInfoList\\\\\\\":[],\\\\\\\"showWebSearchResult\\\\\\\":0,\\\\\\\"webSearchStatus\\\\\\\":0,\\\\\\\"webSearchResultCount\\\\\\\":0,\\\\\\\"knowledgeSourceType\\\\\\\":0,\\\\\\\"knowledgeI18nType\\\\\\\":0,\\\\\\\"empty\\\\\\\":true,\\\\\\\"isRepeatable\\\\\\\":false},\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":\\\\\\\"caption\\\\\\\",\\\\\\\"debugReplyTypeV2\\\\\\\":\\\\\\\"caption\\\\\\\",\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 2, 步骤1\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":2,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"image\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"text\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":[{\\\\\\\"image\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"text\\\\\\\":\\\\\\\"\\\\\\\"}],\\\\\\\"debugInputVariable\\\\\\\":{\\\\\\\"last_user_response\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"debugOutputVariable\\\\\\\":{\\\\\\\"last_user_response\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null}]\\\",\\\"recommendList\\\":\\\"[]\\\",\\\"tableQaInfo\\\":null,\\\"intent\\\":\\\"\\\",\\\"agentType\\\":null,\\\"replySource\\\":null,\\\"effectiveQa\\\":false,\\\"channel\\\":null,\\\"tenantChannel\\\":null,\\\"querySource\\\":null,\\\"querySourceInfo\\\":null,\\\"multimodal\\\":\\\"\\\",\\\"qps\\\":1,\\\"solved\\\":false,\\\"toManual\\\":false,\\\"totalTokens\\\":0,\\\"knowledgeTypeSet\\\":null,\\\"knowledgeInfo\\\":null,\\\"intentInfo\\\":null,\\\"tenantId\\\":\\\"34498286238000000\\\",\\\"rewriteQuery\\\":null,\\\"synonymRewriteQuery\\\":\\\"\\\",\\\"generationNodeList\\\":null,\\\"collectNode\\\":null,\\\"conditionNodeList\\\":null,\\\"instructionNode\\\":null,\\\"unMatchNode\\\":null,\\\"errorNode\\\":null,\\\"intentNode\\\":null,\\\"overMaxStepNode\\\":null,\\\"sensitiveIntentNode\\\":null,\\\"firstChunkTime\\\":0.453,\\\"instruction\\\":[],\\\"interventionRuleStatistics\\\":{\\\"unMatch\\\":0,\\\"entityCheckFail\\\":0,\\\"sensitive\\\":0,\\\"manual\\\":0},\\\"userInfoPair\\\":\\\"\\\",\\\"redBookAnswer\\\":null,\\\"queryCard\\\":null,\\\"hint\\\":null,\\\"hintType\\\":null,\\\"redBookKosId\\\":null,\\\"dialogCount\\\":0,\\\"versionId\\\":\\\"8b61c419-687f-4339-beb5-fa4a9ed22dcb\\\"}\"";
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);

        TableSyncDetailResponse mockResponse = new TableSyncDetailResponse();
        mockResponse.setStatus(200);
        when(contentService.getSyncInfo(any(GetTableDataCurlRequest.class))).thenReturn(mockResponse);

        TenantInfo mockTenant = new TenantInfo();
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(mockTenant);
        String mockResp = "{\n" +
                "  \"requestId\" : \"f3a121d6256649f3a8b78d47c8114917\",\n" +
                "  \"time\" : 1749203221576,\n" +
                "  \"code\" : 200,\n" +
                "  \"msg\" : \"OK\",\n" +
                "  \"data\" : {\n" +
                "    \"robotId\" : \"f359ef92-af50-4e53-868d-fd26334b13a1\",\n" +
                "    \"botVersionId\" : 4687053755351040,\n" +
                "    \"botVersionName\" : \"灵活画布-吴山虎测试勿删20250605172450\",\n" +
                "    \"agentId\" : \"bbf4f3c4-5959-454b-b191-e0cef1fb0523\"\n" +
                "  }\n" +
                "}";
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            mockedHttpUtil.when(() -> HttpUtil.get(anyString(), any(Map.class))).thenReturn(mockResp);
            worker.processData(testData);
        }

    }

    @Test
    void testProcessDataAIobISNull() throws Exception {
        String testData = "\"{\\\"uid\\\":null,\\\"userName\\\":null,\\\"queryText\\\":\\\"\\\",\\\"queryTime\\\":\\\"2025-06-04 17:55:47\\\",\\\"agentName\\\":\\\"灵活画布-吴山虎测试勿删_aiob_4289257709502464\\\",\\\"startId\\\":null,\\\"online\\\":true,\\\"endTime\\\":\\\"2025-06-04 17:55:48\\\",\\\"sessionId\\\":\\\"3808251032371200_11231e0a3fb045e991a6bd67be8c1a60\\\",\\\"topicId\\\":null,\\\"variables\\\":null,\\\"variablesJson\\\":\\\"{\\\\\\\"sys_date\\\\\\\":\\\\\\\"2025-06-04\\\\\\\",\\\\\\\"date\\\\\\\":\\\\\\\"2025-06-04\\\\\\\",\\\\\\\"last_intent\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"week\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"sys_phonenum\\\\\\\":\\\\\\\"13002077690\\\\\\\",\\\\\\\"sys_weekday\\\\\\\":\\\\\\\"星期三\\\\\\\",\\\\\\\"LLM_rewrite_query\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"last_task_prompt\\\\\\\":\\\\\\\"你好\\\\\\\",\\\\\\\"sys_time\\\\\\\":\\\\\\\"17:55:37\\\\\\\",\\\\\\\"time\\\\\\\":\\\\\\\"17:55:47\\\\\\\"}\\\",\\\"agentId\\\":\\\"bbf4f3c4-5959-454b-b191-e0cef1fb0523\\\",\\\"queryId\\\":\\\"ee28eac3-1d67-44cf-911c-469cd86edf61\\\",\\\"answer\\\":\\\"[{\\\\\\\"chunkId\\\\\\\":0,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":null,\\\\\\\"nodeId\\\\\\\":\\\\\\\"start00000000000000000000\\\\\\\",\\\\\\\"name\\\\\\\":null,\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":null,\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":\\\\\\\"start\\\\\\\",\\\\\\\"debugReplyTypeV2\\\\\\\":\\\\\\\"start\\\\\\\",\\\\\\\"debugNodeName\\\\\\\":null,\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":3,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":null,\\\\\\\"debugInputVariable\\\\\\\":{},\\\\\\\"debugOutputVariable\\\\\\\":{},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null},{\\\\\\\"chunkId\\\\\\\":1,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":null,\\\\\\\"nodeId\\\\\\\":\\\\\\\"0egyuQpgoQcDDGVJrOjQeyImAash4kpRjGVG\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 1\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":null,\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":null,\\\\\\\"debugReplyTypeV2\\\\\\\":null,\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 1\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":1,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":null,\\\\\\\"debugInputVariable\\\\\\\":{},\\\\\\\"debugOutputVariable\\\\\\\":{},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null},{\\\\\\\"chunkId\\\\\\\":2,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":\\\\\\\"0egyuQpgoQcDDGVJrOjQeyImAash4kpRjGVG\\\\\\\",\\\\\\\"nodeId\\\\\\\":\\\\\\\"4FDe2D7stOGnUMf35rCRIFbOczTFJSH3fcNI\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 1, 步骤1\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":{\\\\\\\"type\\\\\\\":1,\\\\\\\"audioData\\\\\\\":null,\\\\\\\"text\\\\\\\":\\\\\\\"你好\\\\\\\",\\\\\\\"textWithFullUrl\\\\\\\":\\\\\\\"你好\\\\\\\",\\\\\\\"collectImage\\\\\\\":null,\\\\\\\"textList\\\\\\\":null,\\\\\\\"clarifyGuide\\\\\\\":null,\\\\\\\"replySource\\\\\\\":\\\\\\\"TEXT_REPLY\\\\\\\",\\\\\\\"progressDesc\\\\\\\":null,\\\\\\\"engineType\\\\\\\":null,\\\\\\\"showDocumentSource\\\\\\\":null,\\\\\\\"showDialogAnalysis\\\\\\\":false,\\\\\\\"documents\\\\\\\":null,\\\\\\\"tableQAInfo\\\\\\\":null,\\\\\\\"docDebug\\\\\\\":null,\\\\\\\"smTableqaDebug\\\\\\\":null,\\\\\\\"faqSearch\\\\\\\":null,\\\\\\\"slotCollectInfo\\\\\\\":null,\\\\\\\"plugin\\\\\\\":null,\\\\\\\"url\\\\\\\":null,\\\\\\\"buttons\\\\\\\":null,\\\\\\\"intentName\\\\\\\":null,\\\\\\\"displayAIGenerated\\\\\\\":false,\\\\\\\"sensitiveWord\\\\\\\":null,\\\\\\\"reasoningResult\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"showThinkingProcess\\\\\\\":0,\\\\\\\"thinkingProcessExpand\\\\\\\":0,\\\\\\\"searchInfoList\\\\\\\":[],\\\\\\\"showWebSearchResult\\\\\\\":0,\\\\\\\"webSearchStatus\\\\\\\":0,\\\\\\\"webSearchResultCount\\\\\\\":0,\\\\\\\"knowledgeSourceType\\\\\\\":0,\\\\\\\"knowledgeI18nType\\\\\\\":0,\\\\\\\"empty\\\\\\\":false,\\\\\\\"isRepeatable\\\\\\\":false},\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":\\\\\\\"text\\\\\\\",\\\\\\\"debugReplyTypeV2\\\\\\\":\\\\\\\"text\\\\\\\",\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 1, 步骤1\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":9,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"text\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"你好\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":[{\\\\\\\"text\\\\\\\":\\\\\\\"你好\\\\\\\"}],\\\\\\\"debugInputVariable\\\\\\\":{\\\\\\\"date\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"last_intent\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"week\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"time\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"debugOutputVariable\\\\\\\":{\\\\\\\"date\\\\\\\":\\\\\\\"2025-06-04\\\\\\\",\\\\\\\"last_intent\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"week\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"time\\\\\\\":\\\\\\\"17:55:47\\\\\\\"},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":{},\\\\\\\"debugReferencedVariable\\\\\\\":{}},{\\\\\\\"chunkId\\\\\\\":3,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":null,\\\\\\\"nodeId\\\\\\\":\\\\\\\"ciyEjmek7WazuC3HidXrSczGabVeDD98PFnv\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 2\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":null,\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":null,\\\\\\\"debugReplyTypeV2\\\\\\\":null,\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 2\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":1,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":null,\\\\\\\"debugInputVariable\\\\\\\":{},\\\\\\\"debugOutputVariable\\\\\\\":{},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null},{\\\\\\\"chunkId\\\\\\\":4,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":\\\\\\\"ciyEjmek7WazuC3HidXrSczGabVeDD98PFnv\\\\\\\",\\\\\\\"nodeId\\\\\\\":\\\\\\\"qLO4KF8AtBoBxeKHQvmG72ncKlDtTiIaOMex\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 2, 步骤1\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":{\\\\\\\"type\\\\\\\":1,\\\\\\\"audioData\\\\\\\":null,\\\\\\\"text\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"textWithFullUrl\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"collectImage\\\\\\\":null,\\\\\\\"textList\\\\\\\":null,\\\\\\\"clarifyGuide\\\\\\\":null,\\\\\\\"replySource\\\\\\\":\\\\\\\"CAPTURE\\\\\\\",\\\\\\\"progressDesc\\\\\\\":null,\\\\\\\"engineType\\\\\\\":null,\\\\\\\"showDocumentSource\\\\\\\":null,\\\\\\\"showDialogAnalysis\\\\\\\":false,\\\\\\\"documents\\\\\\\":null,\\\\\\\"tableQAInfo\\\\\\\":null,\\\\\\\"docDebug\\\\\\\":null,\\\\\\\"smTableqaDebug\\\\\\\":null,\\\\\\\"faqSearch\\\\\\\":null,\\\\\\\"slotCollectInfo\\\\\\\":null,\\\\\\\"plugin\\\\\\\":null,\\\\\\\"url\\\\\\\":null,\\\\\\\"buttons\\\\\\\":null,\\\\\\\"intentName\\\\\\\":null,\\\\\\\"displayAIGenerated\\\\\\\":false,\\\\\\\"sensitiveWord\\\\\\\":null,\\\\\\\"reasoningResult\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"showThinkingProcess\\\\\\\":0,\\\\\\\"thinkingProcessExpand\\\\\\\":0,\\\\\\\"searchInfoList\\\\\\\":[],\\\\\\\"showWebSearchResult\\\\\\\":0,\\\\\\\"webSearchStatus\\\\\\\":0,\\\\\\\"webSearchResultCount\\\\\\\":0,\\\\\\\"knowledgeSourceType\\\\\\\":0,\\\\\\\"knowledgeI18nType\\\\\\\":0,\\\\\\\"empty\\\\\\\":true,\\\\\\\"isRepeatable\\\\\\\":false},\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":\\\\\\\"caption\\\\\\\",\\\\\\\"debugReplyTypeV2\\\\\\\":\\\\\\\"caption\\\\\\\",\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 2, 步骤1\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":2,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"image\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"text\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":[{\\\\\\\"image\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"text\\\\\\\":\\\\\\\"\\\\\\\"}],\\\\\\\"debugInputVariable\\\\\\\":{\\\\\\\"last_user_response\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"debugOutputVariable\\\\\\\":{\\\\\\\"last_user_response\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null}]\\\",\\\"recommendList\\\":\\\"[]\\\",\\\"tableQaInfo\\\":null,\\\"intent\\\":\\\"\\\",\\\"agentType\\\":null,\\\"replySource\\\":null,\\\"effectiveQa\\\":false,\\\"channel\\\":null,\\\"tenantChannel\\\":null,\\\"querySource\\\":null,\\\"querySourceInfo\\\":null,\\\"multimodal\\\":\\\"\\\",\\\"qps\\\":1,\\\"solved\\\":false,\\\"toManual\\\":false,\\\"totalTokens\\\":0,\\\"knowledgeTypeSet\\\":null,\\\"knowledgeInfo\\\":null,\\\"intentInfo\\\":null,\\\"tenantId\\\":\\\"34498286238000000\\\",\\\"rewriteQuery\\\":null,\\\"synonymRewriteQuery\\\":\\\"\\\",\\\"generationNodeList\\\":null,\\\"collectNode\\\":null,\\\"conditionNodeList\\\":null,\\\"instructionNode\\\":null,\\\"unMatchNode\\\":null,\\\"errorNode\\\":null,\\\"intentNode\\\":null,\\\"overMaxStepNode\\\":null,\\\"sensitiveIntentNode\\\":null,\\\"firstChunkTime\\\":0.453,\\\"instruction\\\":[],\\\"interventionRuleStatistics\\\":{\\\"unMatch\\\":0,\\\"entityCheckFail\\\":0,\\\"sensitive\\\":0,\\\"manual\\\":0},\\\"userInfoPair\\\":\\\"\\\",\\\"redBookAnswer\\\":null,\\\"queryCard\\\":null,\\\"hint\\\":null,\\\"hintType\\\":null,\\\"redBookKosId\\\":null,\\\"dialogCount\\\":0,\\\"versionId\\\":\\\"8b61c419-687f-4339-beb5-fa4a9ed22dcb\\\"}\"";
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);

        TableSyncDetailResponse mockResponse = new TableSyncDetailResponse();
        mockResponse.setStatus(200);
        when(contentService.getSyncInfo(any(GetTableDataCurlRequest.class))).thenReturn(mockResponse);

        TenantInfo mockTenant = new TenantInfo();
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(mockTenant);
        String mockResp = "";
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            mockedHttpUtil.when(() -> HttpUtil.get(anyString(), any(Map.class))).thenReturn(mockResp);
            worker.processData(testData);
        }

    }

    @Test
    void testProcessDataTenantISNull() throws Exception {
        String testData = "\"{\\\"uid\\\":null,\\\"userName\\\":null,\\\"queryText\\\":\\\"\\\",\\\"queryTime\\\":\\\"2025-06-04 17:55:47\\\",\\\"agentName\\\":\\\"灵活画布-吴山虎测试勿删_aiob_4289257709502464\\\",\\\"startId\\\":null,\\\"online\\\":true,\\\"endTime\\\":\\\"2025-06-04 17:55:48\\\",\\\"sessionId\\\":\\\"3808251032371200_11231e0a3fb045e991a6bd67be8c1a60\\\",\\\"topicId\\\":null,\\\"variables\\\":null,\\\"variablesJson\\\":\\\"{\\\\\\\"sys_date\\\\\\\":\\\\\\\"2025-06-04\\\\\\\",\\\\\\\"date\\\\\\\":\\\\\\\"2025-06-04\\\\\\\",\\\\\\\"last_intent\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"week\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"sys_phonenum\\\\\\\":\\\\\\\"13002077690\\\\\\\",\\\\\\\"sys_weekday\\\\\\\":\\\\\\\"星期三\\\\\\\",\\\\\\\"LLM_rewrite_query\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"last_task_prompt\\\\\\\":\\\\\\\"你好\\\\\\\",\\\\\\\"sys_time\\\\\\\":\\\\\\\"17:55:37\\\\\\\",\\\\\\\"time\\\\\\\":\\\\\\\"17:55:47\\\\\\\"}\\\",\\\"agentId\\\":\\\"bbf4f3c4-5959-454b-b191-e0cef1fb0523\\\",\\\"queryId\\\":\\\"ee28eac3-1d67-44cf-911c-469cd86edf61\\\",\\\"answer\\\":\\\"[{\\\\\\\"chunkId\\\\\\\":0,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":null,\\\\\\\"nodeId\\\\\\\":\\\\\\\"start00000000000000000000\\\\\\\",\\\\\\\"name\\\\\\\":null,\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":null,\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":\\\\\\\"start\\\\\\\",\\\\\\\"debugReplyTypeV2\\\\\\\":\\\\\\\"start\\\\\\\",\\\\\\\"debugNodeName\\\\\\\":null,\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":3,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":null,\\\\\\\"debugInputVariable\\\\\\\":{},\\\\\\\"debugOutputVariable\\\\\\\":{},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null},{\\\\\\\"chunkId\\\\\\\":1,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":null,\\\\\\\"nodeId\\\\\\\":\\\\\\\"0egyuQpgoQcDDGVJrOjQeyImAash4kpRjGVG\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 1\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":null,\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":null,\\\\\\\"debugReplyTypeV2\\\\\\\":null,\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 1\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":1,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":null,\\\\\\\"debugInputVariable\\\\\\\":{},\\\\\\\"debugOutputVariable\\\\\\\":{},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null},{\\\\\\\"chunkId\\\\\\\":2,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":\\\\\\\"0egyuQpgoQcDDGVJrOjQeyImAash4kpRjGVG\\\\\\\",\\\\\\\"nodeId\\\\\\\":\\\\\\\"4FDe2D7stOGnUMf35rCRIFbOczTFJSH3fcNI\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 1, 步骤1\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":{\\\\\\\"type\\\\\\\":1,\\\\\\\"audioData\\\\\\\":null,\\\\\\\"text\\\\\\\":\\\\\\\"你好\\\\\\\",\\\\\\\"textWithFullUrl\\\\\\\":\\\\\\\"你好\\\\\\\",\\\\\\\"collectImage\\\\\\\":null,\\\\\\\"textList\\\\\\\":null,\\\\\\\"clarifyGuide\\\\\\\":null,\\\\\\\"replySource\\\\\\\":\\\\\\\"TEXT_REPLY\\\\\\\",\\\\\\\"progressDesc\\\\\\\":null,\\\\\\\"engineType\\\\\\\":null,\\\\\\\"showDocumentSource\\\\\\\":null,\\\\\\\"showDialogAnalysis\\\\\\\":false,\\\\\\\"documents\\\\\\\":null,\\\\\\\"tableQAInfo\\\\\\\":null,\\\\\\\"docDebug\\\\\\\":null,\\\\\\\"smTableqaDebug\\\\\\\":null,\\\\\\\"faqSearch\\\\\\\":null,\\\\\\\"slotCollectInfo\\\\\\\":null,\\\\\\\"plugin\\\\\\\":null,\\\\\\\"url\\\\\\\":null,\\\\\\\"buttons\\\\\\\":null,\\\\\\\"intentName\\\\\\\":null,\\\\\\\"displayAIGenerated\\\\\\\":false,\\\\\\\"sensitiveWord\\\\\\\":null,\\\\\\\"reasoningResult\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"showThinkingProcess\\\\\\\":0,\\\\\\\"thinkingProcessExpand\\\\\\\":0,\\\\\\\"searchInfoList\\\\\\\":[],\\\\\\\"showWebSearchResult\\\\\\\":0,\\\\\\\"webSearchStatus\\\\\\\":0,\\\\\\\"webSearchResultCount\\\\\\\":0,\\\\\\\"knowledgeSourceType\\\\\\\":0,\\\\\\\"knowledgeI18nType\\\\\\\":0,\\\\\\\"empty\\\\\\\":false,\\\\\\\"isRepeatable\\\\\\\":false},\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":\\\\\\\"text\\\\\\\",\\\\\\\"debugReplyTypeV2\\\\\\\":\\\\\\\"text\\\\\\\",\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 1, 步骤1\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":9,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"text\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"你好\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":[{\\\\\\\"text\\\\\\\":\\\\\\\"你好\\\\\\\"}],\\\\\\\"debugInputVariable\\\\\\\":{\\\\\\\"date\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"last_intent\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"week\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"time\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"debugOutputVariable\\\\\\\":{\\\\\\\"date\\\\\\\":\\\\\\\"2025-06-04\\\\\\\",\\\\\\\"last_intent\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"week\\\\\\\":\\\\\\\"3\\\\\\\",\\\\\\\"time\\\\\\\":\\\\\\\"17:55:47\\\\\\\"},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":{},\\\\\\\"debugReferencedVariable\\\\\\\":{}},{\\\\\\\"chunkId\\\\\\\":3,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":null,\\\\\\\"nodeId\\\\\\\":\\\\\\\"ciyEjmek7WazuC3HidXrSczGabVeDD98PFnv\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 2\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":null,\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":null,\\\\\\\"debugReplyTypeV2\\\\\\\":null,\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 2\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":1,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":null,\\\\\\\"debugInputVariable\\\\\\\":{},\\\\\\\"debugOutputVariable\\\\\\\":{},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null},{\\\\\\\"chunkId\\\\\\\":4,\\\\\\\"status\\\\\\\":\\\\\\\"done\\\\\\\",\\\\\\\"topicId\\\\\\\":\\\\\\\"c17b2f93-ad9c-4a34-810a-76dd6d5afa99\\\\\\\",\\\\\\\"blockId\\\\\\\":\\\\\\\"ciyEjmek7WazuC3HidXrSczGabVeDD98PFnv\\\\\\\",\\\\\\\"nodeId\\\\\\\":\\\\\\\"qLO4KF8AtBoBxeKHQvmG72ncKlDtTiIaOMex\\\\\\\",\\\\\\\"name\\\\\\\":\\\\\\\"任务 2, 步骤1\\\\\\\",\\\\\\\"taskFlowSolved\\\\\\\":false,\\\\\\\"reply\\\\\\\":{\\\\\\\"type\\\\\\\":1,\\\\\\\"audioData\\\\\\\":null,\\\\\\\"text\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"textWithFullUrl\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"collectImage\\\\\\\":null,\\\\\\\"textList\\\\\\\":null,\\\\\\\"clarifyGuide\\\\\\\":null,\\\\\\\"replySource\\\\\\\":\\\\\\\"CAPTURE\\\\\\\",\\\\\\\"progressDesc\\\\\\\":null,\\\\\\\"engineType\\\\\\\":null,\\\\\\\"showDocumentSource\\\\\\\":null,\\\\\\\"showDialogAnalysis\\\\\\\":false,\\\\\\\"documents\\\\\\\":null,\\\\\\\"tableQAInfo\\\\\\\":null,\\\\\\\"docDebug\\\\\\\":null,\\\\\\\"smTableqaDebug\\\\\\\":null,\\\\\\\"faqSearch\\\\\\\":null,\\\\\\\"slotCollectInfo\\\\\\\":null,\\\\\\\"plugin\\\\\\\":null,\\\\\\\"url\\\\\\\":null,\\\\\\\"buttons\\\\\\\":null,\\\\\\\"intentName\\\\\\\":null,\\\\\\\"displayAIGenerated\\\\\\\":false,\\\\\\\"sensitiveWord\\\\\\\":null,\\\\\\\"reasoningResult\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"showThinkingProcess\\\\\\\":0,\\\\\\\"thinkingProcessExpand\\\\\\\":0,\\\\\\\"searchInfoList\\\\\\\":[],\\\\\\\"showWebSearchResult\\\\\\\":0,\\\\\\\"webSearchStatus\\\\\\\":0,\\\\\\\"webSearchResultCount\\\\\\\":0,\\\\\\\"knowledgeSourceType\\\\\\\":0,\\\\\\\"knowledgeI18nType\\\\\\\":0,\\\\\\\"empty\\\\\\\":true,\\\\\\\"isRepeatable\\\\\\\":false},\\\\\\\"instruction\\\\\\\":null,\\\\\\\"statisticsInfo\\\\\\\":null,\\\\\\\"debugReplyType\\\\\\\":\\\\\\\"caption\\\\\\\",\\\\\\\"debugReplyTypeV2\\\\\\\":\\\\\\\"caption\\\\\\\",\\\\\\\"debugNodeName\\\\\\\":\\\\\\\"任务 2, 步骤1\\\\\\\",\\\\\\\"debugInputParam\\\\\\\":null,\\\\\\\"debugCost\\\\\\\":2,\\\\\\\"debugResult\\\\\\\":[{\\\\\\\"value\\\\\\\":\\\\\\\"[{\\\\\\\\\\\\\\\"image\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"text\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\"}]\\\\\\\",\\\\\\\"key\\\\\\\":\\\\\\\"result\\\\\\\"}],\\\\\\\"debugResultV2\\\\\\\":[{\\\\\\\"image\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"text\\\\\\\":\\\\\\\"\\\\\\\"}],\\\\\\\"debugInputVariable\\\\\\\":{\\\\\\\"last_user_response\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"debugOutputVariable\\\\\\\":{\\\\\\\"last_user_response\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"debugInputEntity\\\\\\\":{},\\\\\\\"debugOutputEntity\\\\\\\":{},\\\\\\\"debugReferencedEntity\\\\\\\":null,\\\\\\\"debugReferencedVariable\\\\\\\":null}]\\\",\\\"recommendList\\\":\\\"[]\\\",\\\"tableQaInfo\\\":null,\\\"intent\\\":\\\"\\\",\\\"agentType\\\":null,\\\"replySource\\\":null,\\\"effectiveQa\\\":false,\\\"channel\\\":null,\\\"tenantChannel\\\":null,\\\"querySource\\\":null,\\\"querySourceInfo\\\":null,\\\"multimodal\\\":\\\"\\\",\\\"qps\\\":1,\\\"solved\\\":false,\\\"toManual\\\":false,\\\"totalTokens\\\":0,\\\"knowledgeTypeSet\\\":null,\\\"knowledgeInfo\\\":null,\\\"intentInfo\\\":null,\\\"tenantId\\\":\\\"34498286238000000\\\",\\\"rewriteQuery\\\":null,\\\"synonymRewriteQuery\\\":\\\"\\\",\\\"generationNodeList\\\":null,\\\"collectNode\\\":null,\\\"conditionNodeList\\\":null,\\\"instructionNode\\\":null,\\\"unMatchNode\\\":null,\\\"errorNode\\\":null,\\\"intentNode\\\":null,\\\"overMaxStepNode\\\":null,\\\"sensitiveIntentNode\\\":null,\\\"firstChunkTime\\\":0.453,\\\"instruction\\\":[],\\\"interventionRuleStatistics\\\":{\\\"unMatch\\\":0,\\\"entityCheckFail\\\":0,\\\"sensitive\\\":0,\\\"manual\\\":0},\\\"userInfoPair\\\":\\\"\\\",\\\"redBookAnswer\\\":null,\\\"queryCard\\\":null,\\\"hint\\\":null,\\\"hintType\\\":null,\\\"redBookKosId\\\":null,\\\"dialogCount\\\":0,\\\"versionId\\\":\\\"8b61c419-687f-4339-beb5-fa4a9ed22dcb\\\"}\"";
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        TableSyncDetailResponse mockResponse = new TableSyncDetailResponse();
        mockResponse.setStatus(200);
        when(contentService.getSyncInfo(any(GetTableDataCurlRequest.class))).thenReturn(mockResponse);
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(null);
        worker.processData(testData);
    }

    @Test
    void testAddRobotVersionWhenRequiredFieldsMissingShouldDoNothing() {
        Map<String, String> map1 = new HashMap<>();
        worker.addRobotVersion(map1);
        verifyNoInteractions(aiobRobotVersionService, sopService);

        Map<String, String> map2 = new HashMap<>();
        map2.put("versionId", "v1");
        worker.addRobotVersion(map2);
        verifyNoInteractions(aiobRobotVersionService, sopService);

        Map<String, String> map3 = new HashMap<>();
        map3.put("versionId", "v1");
        map3.put("agentId", "a1");
        worker.addRobotVersion(map3);
        verifyNoInteractions(aiobRobotVersionService, sopService);
    }

    @Test
    void testAddRobotVersionWhenVersionInfoNotCompleteShouldDoNothing() {
        Map<String, String> map = new HashMap<>();
        map.put("versionId", "v1");
        map.put("agentId", "a1");
        map.put("tenantId", "t1");

        AiobDiagramVersionRecordResp resp = new AiobDiagramVersionRecordResp();
        AiobDiagramRecordData data = new AiobDiagramRecordData();
        data.setId("v1");
        resp.setData(List.of(data));

        when(aiobRobotVersionService.getAiobRobotVersion("t1", "a1", "v1")).thenReturn(false);
        when(sopService.getDiagramVersions("a1", "v1")).thenReturn(resp);

        worker.addRobotVersion(map);

        verify(aiobRobotVersionService, never()).saveDiagramRobotVersion(any(), any(), any(), any(), any());
    }

    @Test
    void testAddRobotVersionWhenVersionExistsShouldDoNothing() {
        Map<String, String> map = new HashMap<>();
        map.put("versionId", "v1");
        map.put("agentId", "a1");
        map.put("tenantId", "t1");

        when(aiobRobotVersionService.getAiobRobotVersion("t1", "a1", "v1")).thenReturn(true);

        worker.addRobotVersion(map);

        verify(aiobRobotVersionService, never()).saveDiagramRobotVersion(any(), any(), any(), any(), any());
    }

    @Test
    void testAddRobotVersionWhenVersionNotExistsShouldSaveVersion() {
        Map<String, String> map = new HashMap<>();
        map.put("versionId", "v1");
        map.put("agentId", "a1");
        map.put("tenantId", "t1");

        AiobDiagramVersionRecordResp resp = new AiobDiagramVersionRecordResp();
        AiobDiagramRecordData data = new AiobDiagramRecordData();
        data.setId("v1");
        data.setVersionName("version1");
        data.setCreateTime("2023-01-01");
        resp.setData(List.of(data));

        when(aiobRobotVersionService.getAiobRobotVersion("t1", "a1", "v1")).thenReturn(false);
        when(sopService.getDiagramVersions("a1", "v1")).thenReturn(resp);

        worker.addRobotVersion(map);

        verify(aiobRobotVersionService).saveDiagramRobotVersion("t1", "a1", "v1", "version1", "2023-01-01");
    }

    @Test
    void testProcessDataWithEmptyTenantId() throws Exception {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(worker, "aiobUrl", "http://127.0.0.1:8080");
        ReflectionTestUtils.setField(worker, "debugPath", "testPath");
        ReflectionTestUtils.setField(worker, "topic", "testTopic");
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "sopService", sopService);
        ReflectionTestUtils.setField(worker, "aiobRobotVersionService", aiobRobotVersionService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);

        String testData = "{\"tenantId\":\"\",\"queryId\":\"q1\"}";

        worker.processData(testData);

        verify(tenantInfoService, never()).queryTenantInfo(any());
    }

    @Test
    void testProcessDataWithNullTenantInfo() throws Exception {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(worker, "aiobUrl", "http://127.0.0.1:8080");
        ReflectionTestUtils.setField(worker, "debugPath", "testPath");
        ReflectionTestUtils.setField(worker, "topic", "testTopic");
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "sopService", sopService);
        ReflectionTestUtils.setField(worker, "aiobRobotVersionService", aiobRobotVersionService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);

        String testData = "{\"tenantId\":\"123\",\"queryId\":\"q1\",\"sessionId\":\"s1\"}";

        when(tenantInfoService.queryTenantInfo("123")).thenReturn(null);

        worker.processData(testData);

        verify(kafkaTemplate, never()).send(any(), any());
    }

    @Test
    void testProcessDataWithHttpException() throws Exception {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(worker, "aiobUrl", "http://127.0.0.1:8080");
        ReflectionTestUtils.setField(worker, "debugPath", "testPath");
        ReflectionTestUtils.setField(worker, "topic", "testTopic");
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "sopService", sopService);
        ReflectionTestUtils.setField(worker, "aiobRobotVersionService", aiobRobotVersionService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);

        String testData = "{\"tenantId\":\"123\",\"queryId\":\"q1\",\"sessionId\":\"s1\",\"agentId\":\"a1\",\"versionId\":\"v1\",\"answer\":[{\"topicId\":\"t1\",\"nodeId\":\"n1\",\"chunkId\":1}]}";

        TenantInfo mockTenant = new TenantInfo();
        when(tenantInfoService.queryTenantInfo("123")).thenReturn(mockTenant);

        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            mockedHttpUtil.when(() -> HttpUtil.get(anyString(), any(Map.class))).thenThrow(new RuntimeException("test error"));

            worker.processData(testData);

            verify(kafkaTemplate, never()).send(any(), any());
        }
    }

    @Test
    void testAddRobotVersionWhenVersionNotExists() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(worker, "aiobUrl", "http://127.0.0.1:8080");
        ReflectionTestUtils.setField(worker, "debugPath", "testPath");
        ReflectionTestUtils.setField(worker, "topic", "testTopic");
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "sopService", sopService);
        ReflectionTestUtils.setField(worker, "aiobRobotVersionService", aiobRobotVersionService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);

        Map<String, String> map = new HashMap<>();
        map.put("versionId", "testVersion");
        map.put("agentId", "testAgent");
        map.put("tenantId", "testTenant");

        AiobDiagramVersionRecordResp mockResp = new AiobDiagramVersionRecordResp();
        AiobDiagramVersionRecordResp.AiobDiagramRecordData data = new AiobDiagramVersionRecordResp.AiobDiagramRecordData();
        data.setId("testVersion");
        data.setVersionName("testName");
        data.setCreateTime("2023-01-01");
        mockResp.setData(List.of(data));

        when(aiobRobotVersionService.getAiobRobotVersion("testTenant", "testAgent", "testVersion")).thenReturn(false);
        when(sopService.getDiagramVersions("testAgent", "testVersion")).thenReturn(mockResp);

        worker.addRobotVersion(map);

        verify(aiobRobotVersionService).saveDiagramRobotVersion("testTenant", "testAgent", "testVersion", "testName", "2023-01-01");
    }

    @Test
    void testAddRobotVersionWhenEmptyVersionInfo() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(worker, "aiobUrl", "http://127.0.0.1:8080");
        ReflectionTestUtils.setField(worker, "debugPath", "testPath");
        ReflectionTestUtils.setField(worker, "topic", "testTopic");
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "sopService", sopService);
        ReflectionTestUtils.setField(worker, "aiobRobotVersionService", aiobRobotVersionService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);

        Map<String, String> map = new HashMap<>();
        map.put("versionId", "testVersion");
        map.put("agentId", "testAgent");
        map.put("tenantId", "testTenant");

        AiobDiagramVersionRecordResp mockResp = new AiobDiagramVersionRecordResp();
        mockResp.setData(new ArrayList<>());

        when(aiobRobotVersionService.getAiobRobotVersion("testTenant", "testAgent", "testVersion")).thenReturn(false);
        when(sopService.getDiagramVersions("testAgent", "testVersion")).thenReturn(mockResp);

        worker.addRobotVersion(map);

        verify(aiobRobotVersionService, never()).saveDiagramRobotVersion(any(), any(), any(), any(), any());
    }

    @Test
    void testAddRobotVersionWhenMissingRequiredFields() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(worker, "aiobUrl", "http://127.0.0.1:8080");
        ReflectionTestUtils.setField(worker, "debugPath", "testPath");
        ReflectionTestUtils.setField(worker, "topic", "testTopic");
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "sopService", sopService);
        ReflectionTestUtils.setField(worker, "aiobRobotVersionService", aiobRobotVersionService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);

        Map<String, String> map = new HashMap<>();
        map.put("versionId", "testVersion");

        worker.addRobotVersion(map);

        verifyNoInteractions(aiobRobotVersionService, sopService);
    }

    @Test
    void testProcessDataWithValidTenantId() throws Exception {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(worker, "aiobUrl", "http://127.0.0.1:8080");
        ReflectionTestUtils.setField(worker, "debugPath", "testPath");
        ReflectionTestUtils.setField(worker, "topic", "testTopic");
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "sopService", sopService);
        ReflectionTestUtils.setField(worker, "aiobRobotVersionService", aiobRobotVersionService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);

        String testData = "{\"tenantId\":\"123\",\"queryId\":\"q1\",\"sessionId\":\"s1\",\"agentId\":\"a1\",\"versionId\":\"v1\",\"answer\":[{\"topicId\":\"t1\",\"nodeId\":\"n1\",\"chunkId\":1}],\"intent\":[{\"nameZh\":\"intent1\"}]}";

        TenantInfo mockTenant = new TenantInfo();
        when(tenantInfoService.queryTenantInfo("123")).thenReturn(mockTenant);

        String mockResp = "{\"data\":{\"robotId\":\"r1\",\"botVersionId\":1}}";
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class);
             MockedStatic<TenantUtils> mockedTenantUtils = mockStatic(TenantUtils.class)) {
            mockedHttpUtil.when(() -> HttpUtil.get(anyString(), any(Map.class))).thenReturn(mockResp);
            mockedTenantUtils.when(() -> TenantUtils.generateAiobDebugTableName(anyString())).thenReturn("table1");

            worker.processData(testData);
        }
    }

    @Test
    void testAddRobotVersionWhenVersionExists() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(worker, "aiobUrl", "http://127.0.0.1:8080");
        ReflectionTestUtils.setField(worker, "debugPath", "testPath");
        ReflectionTestUtils.setField(worker, "topic", "testTopic");
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "sopService", sopService);
        ReflectionTestUtils.setField(worker, "aiobRobotVersionService", aiobRobotVersionService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);

        Map<String, String> map = new HashMap<>();
        map.put("versionId", "testVersion");
        map.put("agentId", "testAgent");
        map.put("tenantId", "testTenant");

        when(aiobRobotVersionService.getAiobRobotVersion("testTenant", "testAgent", "testVersion")).thenReturn(true);

        worker.addRobotVersion(map);

        verify(aiobRobotVersionService, times(1)).getAiobRobotVersion("testTenant", "testAgent", "testVersion");
        verifyNoMoreInteractions(sopService, aiobRobotVersionService);
    }

    @Test
    void conversationDebugLogConsumerShouldSkipWhenTenantIdEmpty() {
        // Arrange
        String msgWithEmptyTenant = "{\"queryId\":\"123\",\"tenantId\":\"\",\"sessionId\":\"789\"}";

        // Act
        worker.conversationDebugLogConsumer(msgWithEmptyTenant);

        // Assert
        verify(tenantInfoService, never()).queryTenantInfo(anyString());
    }

    @Test
    void conversationDebugLogConsumerShouldSkipWhenTenantNotExist() {
        // Arrange
        String msg = "{\"queryId\":\"123\",\"tenantId\":\"456\",\"sessionId\":\"789\"}";
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(null);

        // Act
        worker.conversationDebugLogConsumer(msg);

        // Assert
        verify(tenantInfoService).queryTenantInfo("456");
        verify(kafkaTemplate, never()).send(anyString(), anyString());
    }

    @Test
    void conversationDebugLogConsumerShouldHandleQuotedMessage() {
        // Arrange
        String quotedMsg = "\"{\\\"queryId\\\":\\\"123\\\",\\\"tenantId\\\":\\\"456\\\",\\\"sessionId\\\":\\\"789\\\"}\"";
        TenantInfo tenantInfo = new TenantInfo();
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(tenantInfo);

        // Act
        worker.conversationDebugLogConsumer(quotedMsg);

        // Assert
        verify(tenantInfoService).queryTenantInfo("456");
    }

    @Test
    void conversationDebugLogConsumerShouldHandleHttpRequestFailure() {
        // Arrange
        String validMsg = "{\"queryId\":\"123\",\"tenantId\":\"456\"}";
        TenantInfo tenantInfo = new TenantInfo();
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(tenantInfo);
        doThrow(new RuntimeException("HTTP Error")).when(kafkaTemplate).send(anyString(), anyString());

        // Act
        worker.conversationDebugLogConsumer(validMsg);

        // Assert
        verify(tenantInfoService).queryTenantInfo("456");
    }

    @Test
    void conversationDebugLogConsumerShouldProcessValidMessage() throws Exception {
        // Arrange
        String validMsg = "{\"queryId\":\"123\",\"tenantId\":\"456\"}";
        TenantInfo tenantInfo = new TenantInfo();
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(tenantInfo);

        // Act
        worker.conversationDebugLogConsumer(validMsg);

        // Assert
        verify(tenantInfoService).queryTenantInfo("456");
    }

    @Test
    void testGetRobotInfoSuccessOnFirstAttempt() throws Exception {
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            // Setup
            String expectedUrl = "http://127.0.0.1:8080//mock/path" + "?agentId=" + agentId;
            String mockResponse = "{\"data\":{\"robotName\":\"TestBot\"}}";
            
            mockedHttpUtil.when(() -> HttpUtil.get(expectedUrl, headers))
                .thenReturn(mockResponse);
    
            // Execute
            Map<String, Object> result = worker.getRobotInfo(tenantId, agentId, "session123");
    
            // Verify
            assertNotNull(result);
            assertEquals("TestBot", result.get("robotName"));
        }
    }

    @Test
    void testGetRobotInfoRetryAfterFailure() throws Exception {
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            // Setup
            String expectedUrl = "http://127.0.0.1:8080//mock/path" + "?agentId=" + agentId;
            String mockResponse = "{\"data\":{\"robotName\":\"TestBot\"}}";
            
            mockedHttpUtil.when(() -> HttpUtil.get(expectedUrl, headers))
                .thenThrow(new RuntimeException("First attempt failed"))
                .thenThrow(new RuntimeException("Second attempt failed"))
                .thenReturn(mockResponse);
    
            // Execute
            Map<String, Object> result = worker.getRobotInfo(tenantId, agentId, "session123");
    
            // Verify
            assertNotNull(result);
            assertEquals("TestBot", result.get("robotName"));

        }
    }

    @Test
    void testGetRobotInfoAllAttemptsFail() throws Exception {
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            // Setup
            String expectedUrl = "http://127.0.0.1:8080//mock/path" + "?agentId=" + agentId;
            mockedHttpUtil.when(() -> HttpUtil.get(expectedUrl, headers))
                .thenThrow(new RuntimeException("Network error"));
    
            // Execute
            Map<String, Object> result = worker.getRobotInfo(tenantId, agentId, "session123");
    
            // Verify
            assertNotNull(result);
            assertTrue(result.isEmpty());
            mockedHttpUtil.verify(() -> HttpUtil.get(expectedUrl, headers), times(3));

        }
    }

    @Test
    void testGetRobotInfoInvalidJsonResponse() throws Exception {
        try (MockedStatic<HttpUtil> mockedHttpUtil = mockStatic(HttpUtil.class)) {
            // Setup
            String expectedUrl = "http://127.0.0.1:8080//mock/path" + "?agentId=" + agentId;
            String invalidJson = "invalid json";
            
            mockedHttpUtil.when(() -> HttpUtil.get(expectedUrl, headers))
                .thenReturn(invalidJson);
           
            // Execute
            Map<String, Object> result = worker.getRobotInfo(tenantId, agentId, "session123");
    
            // Verify
            assertNotNull(result);
            assertTrue(result.isEmpty());
            mockedHttpUtil.verify(() -> HttpUtil.get(expectedUrl, headers), times(3));
        }
    }


}