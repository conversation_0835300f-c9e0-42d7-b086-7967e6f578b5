package com.baidu.keyue.deepsight.service.label.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.mysqldb.entity.LabelCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelMapper;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
public class LabelPropertiesServiceImplTest{

    @Mock
    private ExtendLabelMapper labelMapper;

    @InjectMocks
    private LabelPropertiesServiceImpl labelPropertiesService;

    @Test
    void successCatalogLabelCompleteShouldReturnNullWhenInputIsEmpty() {
        List<Long> catalogIds = Collections.emptyList();
        List<LabelWithBLOBs> result = labelPropertiesService.successCatalogLabelComplete(catalogIds);
        assertNull(result);
        verifyNoInteractions(labelMapper);
    }

    @Test
    void successCatalogLabelCompleteShouldReturnLabelsWhenInputIsValid() {
        List<Long> catalogIds = List.of(1L, 2L, 3L);
        List<LabelWithBLOBs> expectedLabels = List.of(new LabelWithBLOBs(), new LabelWithBLOBs());
        
        when(labelMapper.selectByExampleWithBLOBs(any(LabelCriteria.class))).thenReturn(expectedLabels);
        
        List<LabelWithBLOBs> result = labelPropertiesService.successCatalogLabelComplete(catalogIds);
        
        assertNotNull(result);
        assertEquals(expectedLabels, result);
        
        verify(labelMapper, times(1)).selectByExampleWithBLOBs(any(LabelCriteria.class));
    }

    @Test
    void successCatalogLabelCompleteShouldSetCorrectCriteriaWhenQueryingLabels() {
        List<Long> catalogIds = List.of(1L, 2L);
        LabelWithBLOBs label = new LabelWithBLOBs();
        
        when(labelMapper.selectByExampleWithBLOBs(any(LabelCriteria.class))).thenReturn(List.of(label));
        
        List<LabelWithBLOBs> result = labelPropertiesService.successCatalogLabelComplete(catalogIds);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(labelMapper, times(1)).selectByExampleWithBLOBs(any(LabelCriteria.class));
    }

}