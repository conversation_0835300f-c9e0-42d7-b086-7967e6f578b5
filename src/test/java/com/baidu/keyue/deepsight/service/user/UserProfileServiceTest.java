package com.baidu.keyue.deepsight.service.user;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.models.profile.BasicProfile;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRuleMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.label.LabelService;
import com.baidu.keyue.deepsight.web.WebContextHolder;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class UserProfileServiceTest{

    @InjectMocks
    private UserProfileService userProfileService;

    private static final String TEST_USER_ID = "testUserId";

    private static final String TEST_TENANT_ID = "testTenantId";

    private static final String TEST_ONE_ID = "testOneId";

    @Mock
    private UserService userService;

    @Mock
    private TableFieldMetaInfoMapper tableFieldMetaMapper;

    @Mock
    private CustomerGroupService customerGroupService;

    @Mock
    private LabelService labelService;

    @Mock
    private DataTableManageService dataTableManageService;

    @Mock
    private IdMappingRuleMapper idMappingRuleMapper;

    @BeforeEach
    void setUp() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(TEST_USER_ID);
        }
    }

    @Test
    void queryUserBasicProfileShouldReturnBasicProfileWithGroupsAndLabels() {
        Map<String, Object> mockUserData = new HashMap<>();
        mockUserData.put(Constants.DORIS_CUSTOMER_GROUP_FIELD_PREFIX + "1", "1");
        mockUserData.put(Constants.DORIS_LABEL_PROCESS_FIELD_NAME_PREFIX + "1", "1");

        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getUserId).thenReturn(TEST_USER_ID);
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);

            when(userService.queryUserById(TEST_USER_ID, TEST_TENANT_ID)).thenReturn(mockUserData);
            when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TEST_TENANT_ID)))
                    .thenReturn(Collections.emptyList());
            when(labelService.retrieveLabelWithFieldIds(anyList(), eq(TEST_TENANT_ID)))
                    .thenReturn(Collections.emptyList());
            BasicProfile result = userProfileService.queryUserBasicProfile(TEST_USER_ID, StringUtils.EMPTY);

            assertNotNull(result);
            assertNotNull(result.getGroups());
            assertNotNull(result.getLabels());
            assertTrue(result.getGroups().isEmpty());
            assertTrue(result.getLabels().isEmpty());
        }
    }

    @Test
    void queryUserBasicProfileShouldReturnBasicProfileWithAllFields() {
        Map<String, Object> mockUserData = new HashMap<>();
        mockUserData.put("field1", "value1");
        mockUserData.put(Constants.TABLE_USER_ONE_ID, TEST_ONE_ID);
        mockUserData.put(Constants.DORIS_CUSTOMER_GROUP_FIELD_PREFIX + "1", "1");
        mockUserData.put(Constants.DORIS_LABEL_PROCESS_FIELD_NAME_PREFIX + "1", "1");

        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TEST_TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(TEST_USER_ID);
            when(userService.queryUserById(TEST_USER_ID, TEST_TENANT_ID)).thenReturn(mockUserData);
            when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TEST_TENANT_ID)))
                    .thenReturn(Collections.emptyList());
            when(labelService.retrieveLabelWithFieldIds(anyList(), eq(TEST_TENANT_ID)))
                    .thenReturn(Collections.emptyList());
            when(dataTableManageService.getTableDetailWithTableName(anyString())).thenReturn(null);
            when(idMappingRuleMapper.selectByExample(any())).thenReturn(Collections.emptyList());
            when(tableFieldMetaMapper.selectByExample(any())).thenReturn(Collections.emptyList());

            BasicProfile result = userProfileService.queryUserBasicProfile(TEST_USER_ID, StringUtils.EMPTY);

            assertNotNull(result);
            assertNotNull(result.getOneIdGraph());
            assertNotNull(result.getGroups());
            assertNotNull(result.getLabels());
            assertNotNull(result.getUserInfo());
        }
    }

}