package com.baidu.keyue.deepsight.service.datamanage.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.MysqlToJavaTypeMapping;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableFieldInfoDTO;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class DorisConfServiceImplTest{

    @Mock
    private DataTableManageServiceImpl tableManageService;

    @InjectMocks
    private DorisConfServiceImpl dorisConfService;

    @Mock
    private DorisService dorisService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }
    @Test
    void testDeleteTable() {
        String tableName = "test_table";
    
        dorisConfService.deleteTable(tableName);
    
        verify(dorisService, times(1)).execSql(anyString());
    }

    @Test
    void testGenCreateTableSql() {
        List<TableFieldInfoDTO> fieldInfos = List.of(
                createFieldInfo("id", "VARCHAR", 1, true, "Primary key"),
                createFieldInfo("name", "VARCHAR", 0, false, "User name")
        );
        String tableName = "test_table";
    
        String sql = dorisConfService.genCreateTableSql(tableName, fieldInfos);
        assertNotNull(sql);
        assertTrue(sql.contains("CREATE TABLE"));
    }

    @Test
    void testGenAddTableFieldSql() {
        String tableName = "test_table";
        String field = "new_field";
        String dataType = "INT";
    
        String sql = dorisConfService.genAddTableFieldSql(tableName, field, dataType);
    
        assertNotNull(sql);
        assertTrue(sql.contains("ALTER TABLE"));
        assertTrue(sql.contains("ADD COLUMN"));
        assertTrue(sql.contains("new_field INT"));
    }

    @Test
    void testConvertEnum() {
        Map<String, Map<String, String>> enumFields = new HashMap<>();
        enumFields.put("status", Map.of("1", "Active", "0", "Inactive"));
    
        Map<String, VisibleFieldResponse> fieldMap = new HashMap<>();
        VisibleFieldResponse fieldResponse = new VisibleFieldResponse();
        fieldResponse.setEnName("status");
        fieldResponse.setIsShowValue(true);
        fieldMap.put("status", fieldResponse);
    
        String result = dorisConfService.convertEnum(enumFields, fieldMap, "status", "1");
        assertEquals("Active", result);
    
        result = dorisConfService.convertEnum(enumFields, fieldMap, "status", "0");
        assertEquals("Inactive", result);
    
        result = dorisConfService.convertEnum(enumFields, fieldMap, "status", "2");
    }

    @Test
    void testDbFieldUpdate() {
        TableFieldInfoDTO fieldInfo = createFieldInfo("new_field", "INT", 0, false, "New field");
        String tableName = "test_table";
    
        dorisConfService.dbFieldUpdate(fieldInfo, tableName);
    
        verify(dorisService, times(1)).execSql(anyString());
    }



    @Test
    void testDelete() {
        String tableName = "test_table";
        String field = "id";
        List<String> idList = List.of("1", "2", "3");
    
        dorisConfService.delete(tableName, field, idList);
    
        verify(dorisService, times(1)).execSql(anyString());
    }

    @Test
    void testGetFieldShowValue() {
        assertEquals("", dorisConfService.getFieldShowValue(null));
        assertEquals("123", dorisConfService.getFieldShowValue(123));
        assertEquals("test", dorisConfService.getFieldShowValue("test"));
        assertNotNull(dorisConfService.getFieldShowValue(LocalDateTime.now()));
    }

    private TableFieldInfoDTO createFieldInfo(String enName, String dataType, int fieldTag, boolean isRequired, String description) {
        TableFieldInfoDTO fieldInfo = new TableFieldInfoDTO();
        fieldInfo.setEnName(enName);
        fieldInfo.setDataType(dataType);
        fieldInfo.setFieldTag(fieldTag);
        fieldInfo.setIsRequired(isRequired);
        fieldInfo.setDescription(description);
        return fieldInfo;
    }

    @Test
    void testDbCollInit() {
        List<TableFieldInfoDTO> fieldInfos = List.of(
                createFieldInfo("id", "VARCHAR", 1, true, "Primary key"),
                createFieldInfo("name", "VARCHAR", 0, false, "User name")
        );
        String tableName = "test_table";
    
        dorisConfService.dbCollInit(fieldInfos, tableName);
    
        verify(dorisService, times(1)).execSql(anyString());
        verify(dorisService, times(1)).operationSchema(anyString());
    }

    @Test
    void testDorisDataConvertToShowData() {
        Long dataTableId = 1L;
        Map<String, Object> dorisData = new HashMap<>();
        dorisData.put("id", "123");
        dorisData.put("name", "test");
        dorisData.put("date", LocalDateTime.now());
    
        VisibleFieldResponse fieldResponse = new VisibleFieldResponse();
        fieldResponse.setEnName("id");
        fieldResponse.setTableFieldTag(TableFieldTagEnum.getByCode(1));
        List<VisibleFieldResponse> visibleFields = List.of(fieldResponse);
    
        when(tableManageService.getTableEncryFields(anyLong())).thenReturn(Collections.emptyList());
    
        Map<String, String> result = dorisConfService.dorisDataConvertToShowData(dataTableId, dorisData, visibleFields);
    
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    void testContainsIgnoreCase() {
        List<String> keywords = List.of("SELECT", "FROM", "WHERE");
        assertTrue(DorisConfServiceImpl.containsIgnoreCase(keywords, "select"));
        assertTrue(DorisConfServiceImpl.containsIgnoreCase(keywords, "FROM"));
        assertFalse(DorisConfServiceImpl.containsIgnoreCase(keywords, "JOIN"));
    }

    // testGenCreateTableSqlReservedKeyword 用于测试 genCreateTableSql
    // generated by Comate
    @Test
    public void testGenCreateTableSqlReservedKeyword() {
        // 测试字段名包含保留关键字的情况
        String tableName = "test_table";
        TableFieldInfoDTO field1 = new TableFieldInfoDTO();
        field1.setEnName("ADD"); // 保留关键字
        field1.setDataType(MysqlToJavaTypeMapping.VARCHAR.getDataType());
        field1.setIsRequired(true);
        field1.setDescription("Test Field");
        field1.setFieldTag(TableFieldTagEnum.PRIMARY.getCode());
    
        List<TableFieldInfoDTO> fieldInfos = Arrays.asList(field1);
    
        // 预期抛出异常
        DeepSightException.ParamsErrorException exception = assertThrows(DeepSightException.ParamsErrorException.class, () -> {
            dorisConfService.genCreateTableSql(tableName, fieldInfos);
        });
    
        // 验证异常信息
        assertEquals(ErrorCode.INTERNAL_ERROR, exception.getErrorCode());
        assertEquals("字段名不能包含保留关键字：ADD", exception.getMessage());
    }

    @Test
    void testDbCollInitWithReservedKeywordShouldThrowException() {
        List<TableFieldInfoDTO> fieldInfos = List.of(
                createFieldInfo("select", "VARCHAR", 1, true, "Primary key"),
                createFieldInfo("name", "VARCHAR", 0, false, "User name")
        );
        String tableName = "test_table";
        
        assertThrows(DeepSightException.ParamsErrorException.class, 
            () -> dorisConfService.dbCollInit(fieldInfos, tableName));
    }

    @Test
    void testDbCollInitWithoutPrimaryKeyShouldThrowException() {
        List<TableFieldInfoDTO> fieldInfos = List.of(
                createFieldInfo("id", "VARCHAR", 0, true, "Not primary"),
                createFieldInfo("name", "VARCHAR", 0, false, "User name")
        );
        String tableName = "test_table";
        
        assertThrows(DeepSightException.ParamsErrorException.class, 
            () -> dorisConfService.dbCollInit(fieldInfos, tableName));
    }

    @Test
    void testDbCollInitWithDifferentDataTypes() {
        List<TableFieldInfoDTO> fieldInfos = List.of(
                createFieldInfo("id", "VARCHAR", 1, true, "Primary key"),
                createFieldInfo("age", "INT", 0, false, "User age"),
                createFieldInfo("scores", "ARRAY", 0, false, "Test scores")
        );
        String tableName = "test_table";
    
        dorisConfService.dbCollInit(fieldInfos, tableName);
    
        verify(dorisService, times(1)).execSql(anyString());
        verify(dorisService, times(1)).operationSchema(anyString());
    }

}