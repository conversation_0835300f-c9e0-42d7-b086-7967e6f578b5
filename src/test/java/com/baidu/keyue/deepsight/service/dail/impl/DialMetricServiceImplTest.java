package com.baidu.keyue.deepsight.service.dail.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.AiobFailTypeEnum;
import com.baidu.keyue.deepsight.enums.AlertConfigTypeEnum;
import com.baidu.keyue.deepsight.enums.AlertTimeTypeEnum;
import com.baidu.keyue.deepsight.enums.DateLineTypeEnum;
import com.baidu.keyue.deepsight.enums.DialMetricQueryTypeEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.dial.AlertSettingQueryRequest;
import com.baidu.keyue.deepsight.models.dial.AlertSettingRequest;
import com.baidu.keyue.deepsight.models.dial.CallCoreMetricsRequest;
import com.baidu.keyue.deepsight.models.dial.CallCoreMetricsResponse;
import com.baidu.keyue.deepsight.models.dial.CallResultCompositionResponse;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendDetailRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendDetailResponse;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendResponse;
import com.baidu.keyue.deepsight.models.dial.CoreMetricsResponse;
import com.baidu.keyue.deepsight.models.dial.LineInfoResponse;
import com.baidu.keyue.deepsight.models.dial.LineRejectTrendResponse;
import com.baidu.keyue.deepsight.models.dial.RejectReasonResponse;
import com.baidu.keyue.deepsight.models.dial.RobotInfoResponse;
import com.baidu.keyue.deepsight.models.dial.TaskInfoResponse;
import com.baidu.keyue.deepsight.models.dial.ThirtyDayRankingRequest;
import com.baidu.keyue.deepsight.models.dial.ThirtyDayRankingResponse;
import com.baidu.keyue.deepsight.models.dial.UnconnectedHeatmapResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.AlertConfigMapper;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@TestPropertySource(properties = {"dial-online-time.online-time=2023-01-01 00:00:00"})
@ExtendWith(MockitoExtension.class)
public class DialMetricServiceImplTest {


    @Mock
    private AlertConfigMapper alertConfigMapper;

    @Mock
    private DorisService dorisService;

    @Mock
    private TenantUtils tenantUtils;

    @Mock
    private ORMUtils ormUtils;

    @InjectMocks
    private DialMetricServiceImpl dialMetricService;
    
    private final DateTime onlineTime = DateUtil.parse("2025-07-31 23:59:59");
    
    @BeforeEach
    public void setUp() {
        ReflectionTestUtils.setField(dialMetricService, "onlineTimeStr", "2025-07-31 23:59:59");
        ReflectionTestUtils.setField(dialMetricService, "onlineTime", DateUtil.parse("2025-07-31 23:59:59"));
    }

    @Test
    void testGetCoreMetricsWithoutData() {
        // Mock data
        String tenantId = "testTenant";
        String tableName = "testTable";
        Date now = new Date();
        DateTime endTime = DateUtil.beginOfDay(now);
        DateTime alertStart = DateUtil.offsetDay(endTime, -30);
        List<Map<String, Integer>> alertList = List.of(new HashMap<>(), new HashMap<>(), new HashMap<>());
        // Mock behavior
        when(dorisService.selectList(anyString())).thenReturn(List.of());

        // Test
        CoreMetricsResponse response = dialMetricService.getCoreMetrics(tenantId);

        // Verify
        assertEquals(0L, response.getYesterdayCallCount());
        assertEquals(0L, response.getYesterdayConnectedCount());
        assertEquals(0, response.getYesterdayConnectionRate());
        assertEquals(0, response.getLast30DaysAlertDays());
    }

    @Test
    void testGetConnectionRateTrendWithData() {
        // Mock data
        String tenantId = "testTenant";
        String tableName = "testTable";
        ConnectionRateTrendRequest request = new ConnectionRateTrendRequest();
        request.setType(DateLineTypeEnum.DAY);
        String day1 = DateUtil.offsetDay(DateUtil.beginOfDay(new DateTime()), -30).toDateStr();
        String day2 = DateUtil.offsetDay(DateUtil.beginOfDay(new DateTime()), -29).toDateStr();
        Map<String, Object> mockData1 = Map.of("dataTime", day1, "connect_rate", 0.9d);
        Map<String, Object> mockData2 = Map.of("dataTime", day2, "connect_rate", 0.8d);

        // Mock behavior
        when(dorisService.selectList(anyString())).thenReturn(List.of(mockData1, mockData2));

        // Test
        List<ConnectionRateTrendResponse> response = dialMetricService.getConnectionRateTrend(request, tenantId);

        // Verify
        assertEquals(30, response.size());
        assertEquals(90, response.get(0).getConnectedCallsRate());
    }

    @Test
    void testGetConnectionRateTrendWithoutData() {
        // Mock data
        String tenantId = "testTenant";
        String tableName = "testTable";
        ConnectionRateTrendRequest request = new ConnectionRateTrendRequest();
        request.setType(DateLineTypeEnum.HOUR);

        // Mock behavior
        when(dorisService.selectList(anyString())).thenReturn(List.of());

        // Test
        List<ConnectionRateTrendResponse> response = dialMetricService.getConnectionRateTrend(request, tenantId);

        // Verify
        assertTrue(!response.isEmpty());
    }

    @Test
    void testConfigAlertSettingNewConfig() {
        // Prepare test data
        AlertSettingRequest request = new AlertSettingRequest();
        request.setAlertFrequency(1);
        request.setCallCountThreshold(100);
        request.setConfigId("testConfig");
        request.setConfigType(AlertConfigTypeEnum.LINE);
        request.setAlertTimeType(AlertTimeTypeEnum.DAY_7);
        request.setConnectedRateThreshold(10);

        String tenantId = "testTenant";

        // Mock behavior
        when(alertConfigMapper.selectByExample(any(AlertConfigCriteria.class))).thenReturn(List.of());
        when(alertConfigMapper.insertSelective(any(AlertConfig.class))).thenReturn(1);

        // Test
        Integer result = dialMetricService.configAlertSetting(request, tenantId);

        // Verify
        verify(alertConfigMapper).insertSelective(any(AlertConfig.class));
    }

    @Test
    void testConfigAlertSettingUpdateConfig() {
        // Prepare test data
        AlertSettingRequest request = new AlertSettingRequest();
        request.setAlertFrequency(12);
        request.setCallCountThreshold(1000);
        request.setConfigId("testConfig");
        request.setConfigType(AlertConfigTypeEnum.LINE);
        request.setAlertTimeType(AlertTimeTypeEnum.DAY_7);
        request.setConnectedRateThreshold(10);
        String tenantId = "testTenant";

        AlertConfig existingConfig = new AlertConfig();
        existingConfig.setId(123);
        existingConfig.setIsActive(true);
        existingConfig.setConfigType("LINE");
        existingConfig.setAlertTime("24H");
        existingConfig.setThresholdRate(new BigDecimal(10));

        // Mock behavior
        when(alertConfigMapper.selectByExample(any(AlertConfigCriteria.class)))
                .thenReturn(List.of(existingConfig));
        when(alertConfigMapper.updateByPrimaryKeySelective(any(AlertConfig.class))).thenReturn(1);

        // Test
        Integer result = dialMetricService.configAlertSetting(request, tenantId);

        // Verify
        assertEquals(123, result);
        verify(alertConfigMapper).updateByPrimaryKeySelective(any(AlertConfig.class));
    }

    @Test
    void testConfigAlertSettingInvalidCallCountThreshold() {
        // Prepare test data
        AlertSettingRequest request = new AlertSettingRequest();
        request.setAlertFrequency(1);
        request.setCallCountThreshold(999); // Invalid value
        request.setConfigId("testConfig");
        request.setConfigType(AlertConfigTypeEnum.LINE);
        String tenantId = "testTenant";

        // Test & Verify
        DeepSightException.ParamsErrorException exception = assertThrows(
                DeepSightException.ParamsErrorException.class,
                () -> dialMetricService.configAlertSetting(request, tenantId)
        );
        assertEquals(ErrorCode.BAD_REQUEST, exception.getErrorCode());
        assertEquals("拨打次数阈值错误", exception.getMessage());
    }

    @Test
    void testConfigAlertSettingInvalidAlertFrequency() {
        // Prepare test data
        AlertSettingRequest request = new AlertSettingRequest();
        request.setAlertFrequency(2); // Invalid value
        request.setCallCountThreshold(100);
        request.setConfigId("testConfig");
        request.setConfigType(AlertConfigTypeEnum.LINE);
        String tenantId = "testTenant";

        // Test & Verify
        DeepSightException.ParamsErrorException exception = assertThrows(
                DeepSightException.ParamsErrorException.class,
                () -> dialMetricService.configAlertSetting(request, tenantId)
        );
        assertEquals(ErrorCode.BAD_REQUEST, exception.getErrorCode());
        assertEquals("告警频率值错误", exception.getMessage());
    }

    @Test
    void testQueryAlertSettingWithConfigFound() {
        // Prepare test data
        String tenantId = "testTenant";
        AlertSettingQueryRequest request = new AlertSettingQueryRequest();
        request.setConfigId("testConfig");
        request.setConfigType(AlertConfigTypeEnum.LINE);

        AlertConfig alertConfig = new AlertConfig();
        alertConfig.setId(1);
        alertConfig.setConfigType("LINE");
        alertConfig.setConfigTarget("testConfig");
        alertConfig.setThresholdRate(BigDecimal.valueOf(50.00));
        alertConfig.setDialCount(100);
        alertConfig.setAlertFreq(24);
        alertConfig.setIsActive(true);
        alertConfig.setAlertTime("24H");
        alertConfig.setTenantId(tenantId);

        // Mock behavior
        when(alertConfigMapper.selectByExample(any(AlertConfigCriteria.class)))
                .thenReturn(List.of(alertConfig));

        // Execute test
        AlertSettingRequest result = dialMetricService.queryAlertSetting(request, tenantId);

        // Verify results
        assertNotNull(result);
        assertEquals(1, result.getId());
        assertEquals("testConfig", result.getConfigId());
        assertEquals(AlertConfigTypeEnum.LINE, result.getConfigType());
        assertEquals(24, result.getAlertFrequency());
        assertEquals(100, result.getCallCountThreshold());
        assertEquals(50, result.getConnectedRateThreshold());
    }

    @Test
    void testQueryAlertSettingWithNoConfigFound() {
        // Prepare test data
        String tenantId = "testTenant";
        AlertSettingQueryRequest request = new AlertSettingQueryRequest();
        request.setConfigId("nonExistConfig");
        request.setConfigType(AlertConfigTypeEnum.LINE);

        // Mock behavior
        when(alertConfigMapper.selectByExample(any(AlertConfigCriteria.class)))
                .thenReturn(Collections.emptyList());

        // Execute test
        AlertSettingRequest result = dialMetricService.queryAlertSetting(request, tenantId);

        // Verify results
        assertNull(result);
    }

    @Test
    void testGetThirtyDayRankingEmptyResult() {
        // Prepare test data
        String tenantId = "testTenant";
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setType(AlertConfigTypeEnum.ROBOT);

        // Mock behavior
        try (MockedStatic<TenantUtils> mockedTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {
            mockedTenantUtils.when(() -> TenantUtils.generateAiobSessionTableName(tenantId))
                    .thenReturn("test_table");
            mockedORMUtils.when(() -> ORMUtils.generateAiobThirtyDayRankingCountSql(request, "test_table", tenantId))
                    .thenReturn("count_sql");
            when(dorisService.getCount("count_sql")).thenReturn(0L);

            // Execute test
            BasePageResponse.Page<ThirtyDayRankingResponse> result = dialMetricService.getThirtyDayRanking(request, tenantId);

            // Verify
            assertEquals(1, result.getPageNo());
            assertEquals(10, result.getPageSize());
            assertEquals(0L, result.getTotal());
            assertTrue(result.getResults().isEmpty());
        }
    }

    @Test
    void testGetThirtyDayRankingWithData() {
        // Prepare test data
        String tenantId = "testTenant";
        ThirtyDayRankingRequest request = new ThirtyDayRankingRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setType(AlertConfigTypeEnum.ROBOT);

        Map<String, Object> mockData = new HashMap<>();
        mockData.put("id", "robot1");
        mockData.put("callCount", 100);
        mockData.put("connectedCount", 80);
        mockData.put("connectedRate", 80);
        
        Map<String, Object> mockDetail = new HashMap<>();
        mockData.put("id", "robot1");
        mockData.put("callCount", 100);
        mockData.put("connectedCount", 80);
        mockData.put("connectedRate", 80);

        Map<String, Object> rateTrendData = new HashMap<>();
        rateTrendData.put("targetId", "robot1");
        rateTrendData.put("dataTime", "2025-06-08 10:00:00");
        rateTrendData.put("connected_rate", 80);

        AlertConfig alertConfig = new AlertConfig();
        alertConfig.setConfigTarget("robot1");
        alertConfig.setThresholdRate(new BigDecimal(12));

        // Mock behavior
        try (MockedStatic<TenantUtils> mockedTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {
            mockedTenantUtils.when(() -> TenantUtils.generateAiobSessionTableName(tenantId))
                    .thenReturn("test_table");
            mockedORMUtils.when(() -> ORMUtils.generateAiobThirtyDayRankingCountSql(request, "test_table", tenantId))
                    .thenReturn("count_sql");
            mockedORMUtils.when(() -> ORMUtils.generateAiobThirtyDayRankingPageSql(request, "test_table", tenantId))
                    .thenReturn("page_sql");
            mockedORMUtils.when(() -> ORMUtils.generateConnectionRateTrend(any(ConnectionRateTrendRequest.class), eq(tenantId)))
                    .thenReturn("rate_trend_sql");
            mockedORMUtils.when(() -> ORMUtils.generateSessionSimpleDetailSql(any(List.class), eq(tenantId), any(ThirtyDayRankingRequest.class)))
                    .thenReturn("detail_sql");

            when(dorisService.getCount("count_sql")).thenReturn(1L);
            when(dorisService.selectList("page_sql")).thenReturn(List.of(mockData));
            when(dorisService.selectList("rate_trend_sql")).thenReturn(List.of(rateTrendData));
            when(dorisService.selectList("detail_sql")).thenReturn(List.of(mockDetail));
            when(alertConfigMapper.selectByExample(any())).thenReturn(List.of(alertConfig));

            // Execute test
            BasePageResponse.Page<ThirtyDayRankingResponse> result = dialMetricService.getThirtyDayRanking(request, tenantId);

            // Verify
            assertEquals(1, result.getPageNo());
            assertEquals(10, result.getPageSize());
            assertEquals(1L, result.getTotal());
            assertEquals(1, result.getResults().size());

            ThirtyDayRankingResponse response = result.getResults().get(0);
            assertEquals("robot1", response.getId());
            assertEquals(0, response.getCallCount());
            assertEquals(0, response.getConnectedCount());
            assertEquals(0, response.getConnectedRate());
            assertEquals(12, response.getConnectedRateAlarmThreshold());
            assertEquals(12, response.getConnectedRateTrend().size());
        }
    }

    @Test
    void testQueryAlertSettingListEmptyConfigTargets() {
        List<AlertConfig> result = dialMetricService.queryAlertSettingList(
                "tenant1", AlertConfigTypeEnum.ROBOT, Collections.emptyList());

        assertTrue(result.isEmpty());
        verifyNoInteractions(alertConfigMapper);
    }

    @Test
    void testQueryAlertSettingListWithConfigTargets() {
        AlertConfig config = new AlertConfig();
        List<AlertConfig> expected = List.of(config);
        List<String> configTargets = List.of("target1", "target2");

        when(alertConfigMapper.selectByExample(any(AlertConfigCriteria.class)))
                .thenReturn(expected);

        List<AlertConfig> result = dialMetricService.queryAlertSettingList(
                "tenant1", AlertConfigTypeEnum.TASK, configTargets);

        assertEquals(expected, result);
        verify(alertConfigMapper).selectByExample(any(AlertConfigCriteria.class));
    }

    @Test
    void testQueryAlertSettingListNullConfigTargets() {
        List<AlertConfig> result = dialMetricService.queryAlertSettingList(
                "tenant1", AlertConfigTypeEnum.LINE, null);

        assertTrue(result.isEmpty());
        verifyNoInteractions(alertConfigMapper);
    }

    @Test
    void testGetRobotList() {
        try (MockedStatic<WebContextHolder> webContextHolderMock = mockStatic(WebContextHolder.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {
            // Mock data
            String tenantId = "testTenant";
            String sql = "testSql";
            CallCoreMetricsRequest request = new CallCoreMetricsRequest();
            request.setId("testId");
            request.setType("testType");
            Map<String, Object> mockRecord = Map.of("key1", "value1", "key2", 2);

            // Mock behavior
            webContextHolderMock.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            ormUtilsMock.when(() -> ORMUtils.generateQueryAiobRobotOrTaskOrLineListSql(any(), anyString(), any(DialMetricQueryTypeEnum.class), any(DateTime.class)))
                    .thenReturn(sql);
            when(dorisService.selectList(anyString())).thenReturn(List.of(mockRecord));

            // Test
            List<RobotInfoResponse> result = dialMetricService.getRobotList(request);

            // Verify
            assertNotNull(result);
            assertEquals(1, result.size());
        }
    }

    @Test
    void testGetTaskList() {
        try (MockedStatic<WebContextHolder> webContextHolderMock = mockStatic(WebContextHolder.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {
            // Prepare test data
            CallCoreMetricsRequest request = new CallCoreMetricsRequest();
            request.setId("testId");
            request.setType("testType");
            String tenantId = "testTenant";
            String expectedSql = "generated_sql";
            Map<String, Object> mockRecord = Map.of("taskId", "task1", "taskName", "任务1");
            List<Map<String, Object>> mockRecords = List.of(mockRecord);

            // Mock behavior
            webContextHolderMock.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            ormUtilsMock.when(() -> ORMUtils.generateQueryAiobRobotOrTaskOrLineListSql(any(), anyString(), any(DialMetricQueryTypeEnum.class), any(DateTime.class)))
                    .thenReturn(expectedSql);
            when(dorisService.selectList(expectedSql)).thenReturn(mockRecords);

            // Execute test
            List<TaskInfoResponse> result = dialMetricService.getTaskList(request);

            // Verify results
            assertEquals(1, result.size());
            TaskInfoResponse response = result.get(0);
            assertEquals("task1", response.getTaskId());
            assertEquals("任务1", response.getTaskName());
        }
    }

    @Test
    void testGetLineList() {
        try (MockedStatic<WebContextHolder> webContextHolderMock = mockStatic(WebContextHolder.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {
            // Mock data
            String tenantId = "testTenant";
            String sql = "testSql";
            CallCoreMetricsRequest request = new CallCoreMetricsRequest();
            request.setId("testId");
            request.setType("testType");
            Map<String, Object> record1 = Map.of("key1", "value1");
            Map<String, Object> record2 = Map.of("key2", "value2");
            List<Map<String, Object>> recordList = List.of(record1, record2);

            // Mock behavior
            webContextHolderMock.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            ormUtilsMock.when(() -> ORMUtils.generateQueryAiobRobotOrTaskOrLineListSql(any(), anyString(), any(DialMetricQueryTypeEnum.class), any(DateTime.class)))
                    .thenReturn(sql);
            when(dorisService.selectList(sql)).thenReturn(recordList);
            webContextHolderMock.when(WebContextHolder::getTenantId).thenReturn(tenantId);

            // Test
            List<LineInfoResponse> result = dialMetricService.getLineList(request);

            // Verify
            assertNotNull(result);
            assertEquals(2, result.size());
        }
    }

    @Test
    void testGetConnectionRateTrendDetail() {
        try (MockedStatic<WebContextHolder> webContextHolderMock = mockStatic(WebContextHolder.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {
            // Prepare test data
            ConnectionRateTrendDetailRequest request = new ConnectionRateTrendDetailRequest();
            request.setTimeType("24H");
            request.setRobotId("robot1");
            request.setTaskId("task1");
            request.setLineNum("line1");

            String tenantId = "testTenant";
            String mockSql = "mock sql";

            List<Map<String, Object>> mockRecords = List.of(Map.of(
                    "connected_calls_rate", 0.85,
                    "total_calls", 100,
                    "time", "2022-12-31 15:00:00"
            ));
            List<ConnectionRateTrendDetailResponse> expectedResponses = List.of(
                    new ConnectionRateTrendDetailResponse(Map.of(
                            "connected_calls_rate", 0.85,
                            "total_calls", 100,
                            "time", "2022-12-31 15:00:00"
                    ))
            );

            // Mock behavior
            webContextHolderMock.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            ormUtilsMock.when(() -> ORMUtils.generateQueryGetAiobConnectionRateTrendSql(any(), anyString(), any(), any(DateTime.class)))
                    .thenReturn(mockSql);
            when(dorisService.selectList(mockSql)).thenReturn(mockRecords);

            // Execute test
            List<ConnectionRateTrendDetailResponse> responses = dialMetricService.getConnectionRateTrendDetail(request);

            // Verify
            assertNotNull(responses);
            assertEquals(24, responses.size());
        }
    }

    @Test
    void testGetRejectReasons() {
        try (MockedStatic<WebContextHolder> mockedWebContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<ORMUtils> mockedORMUtils = mockStatic(ORMUtils.class)) {

            // Prepare test data
            CallCoreMetricsRequest request = new CallCoreMetricsRequest();
            request.setId("testId");
            request.setType("testType");
            AiobFailTypeEnum failType = AiobFailTypeEnum.CALLED_UP;
            String tenantId = "testTenant";
            String expectedSql = "expected sql";
            Map<String, Object> mockRecord = Map.of("reason", "test reason",
                    "count", 10L, "ratio", BigDecimal.valueOf(0.7));

            // Mock behavior
            mockedWebContextHolder.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            mockedORMUtils.when(() -> ORMUtils.generateQueryGetRejectRatioReasonsSql(any(), anyString(), any(), any(), any(DateTime.class)))
                    .thenReturn(expectedSql);
            when(dorisService.selectList(expectedSql)).thenReturn(List.of(mockRecord));

            // Execute test
            List<RejectReasonResponse> result = dialMetricService.getRejectReasons(request, failType);

            // Verify results
            assertEquals(1, result.size());
            assertEquals("test reason", result.get(0).getReason());
            assertEquals(10, result.get(0).getCount());
            assertEquals(0.7, result.get(0).getPercentage());
        }
    }

    @Test
    void testGetCallResultComposition() {
        // Prepare test data
        CallCoreMetricsRequest request = new CallCoreMetricsRequest();
        request.setId("testId");
        request.setType("testType");

        String tenantId = "testTenant";
        String mockSql = "mockSql";
        Map<String, Object> mockRecord = Map.of("percentage", BigDecimal.valueOf(50));

        // Mock static method calls
        try (MockedStatic<WebContextHolder> webContextHolderMock = mockStatic(WebContextHolder.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            webContextHolderMock.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            ormUtilsMock.when(() -> ORMUtils.generateQueryGetCallResultCompositionSql(any(), any(), any(), any(DateTime.class)))
                    .thenReturn(mockSql);

            // Mock service calls
            when(dorisService.selectList(mockSql)).thenReturn(List.of(mockRecord));

            // Execute test
            List<CallResultCompositionResponse> result = dialMetricService.getCallResultComposition(request);

            // Verify results
            assertEquals(1, result.size());
            assertEquals(50.0, result.get(0).getPercentage());}
    }

    @Test
    void testGetUnconnectedHeatmap() {
        // Mock data
        String tenantId = "testTenant";
        String sql = "testSql";
        CallCoreMetricsRequest request = new CallCoreMetricsRequest();
        List<Map<String, Object>> mockData = List.of(Map.of("weekday", 0, "hour", 1, "count", 10L));

        // Mock static method calls
        try (MockedStatic<WebContextHolder> webContextHolderMock = mockStatic(WebContextHolder.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            webContextHolderMock.when(WebContextHolder::getTenantId).thenReturn(tenantId);
            ormUtilsMock.when(() -> ORMUtils.generateQueryGetUnconnectedHeatmapSql(any(), any(), any(), any(DateTime.class)))
                    .thenReturn(sql);
            when(dorisService.selectList(anyString())).thenReturn(mockData);

            // Test
            UnconnectedHeatmapResponse response = dialMetricService.getUnconnectedHeatmap(request);

            // Verify
            assertNotNull(response);
            assertEquals(10, response.getHeatmapData().get(0).get(1));
        }
    }

    @Test
    void testGetLineRejectTrend() {
        try (MockedStatic<WebContextHolder> webContextHolderMock = mockStatic(WebContextHolder.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)){

            // Setup
            String tenantId = "testTenant";
            webContextHolderMock.when(WebContextHolder::getTenantId).thenReturn(tenantId);

            CallCoreMetricsRequest request = new CallCoreMetricsRequest();
            request.setId("testId");
            request.setType("testType");

            String expectedSql1 = "sql1";
            String expectedSql2 = "sql2";

            ormUtilsMock.when(() -> ORMUtils.generateQueryGetLineRejectTrendSql(any(), any(), any(), any(DateTime.class)))
                    .thenReturn(expectedSql1);
            ormUtilsMock.when(() -> ORMUtils.generateQueryGetRejectRatioReasonsByDaySql(any(), any(), any(), any(), any(DateTime.class)))
                    .thenReturn(expectedSql2);

            List<Map<String, Object>> mockRejectCountList = List.of(
                    Map.of("count", 10L, "time", "2023-01-01"));
            List<Map<String, Object>> mockRejectReasonsList = List.of(Map.of(
                    "reason", "busy", "count", 5L,
                    "time", "2023-01-01", "ratio", BigDecimal.valueOf(0.5)));

            when(dorisService.selectList(expectedSql1)).thenReturn(mockRejectCountList);
            when(dorisService.selectList(expectedSql2)).thenReturn(mockRejectReasonsList);

            // Execute
            List<LineRejectTrendResponse> result = dialMetricService.getLineRejectTrend(request);

            // Verify
            assertNotNull(result);
            assertEquals(30, result.size());
        }
    }

    @Test
    void testGetCallDetailCoreMetricsWithTypeAndAlertSetting() {
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class);
             MockedStatic<ORMUtils> ormUtils = mockStatic(ORMUtils.class)) {
            // Setup
            String tenantId = "testTenant";
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn(tenantId);

            CallCoreMetricsRequest request = new CallCoreMetricsRequest();
            request.setId("taskId");
            request.setType("TASK");

            String expectedSql = "test_sql";
            ormUtils.when(() -> ORMUtils.generateQueryGetCallDetailCoreMetricsSql(any(), any(), any(), any()))
                    .thenReturn(expectedSql);

            Map<String, Object> record = Map.of(
                    "callCount", 100L,
                    "connectedCount", 60L,
                    "unconnectedCount",40L,
                    "lineReasonUnconnectedRate", BigDecimal.valueOf(30)
            );
            when(dorisService.selectList(expectedSql)).thenReturn(List.of(record));


            AlertSettingRequest alertSetting = new AlertSettingRequest();
            alertSetting.setConnectedRateThreshold(50);

            // Execute
            CallCoreMetricsResponse response = dialMetricService.getCallDetailCoreMetrics(request);

            // Verify
            assertEquals(100, response.getCallCount());
            assertEquals(60, response.getConnectedCount());
            assertEquals(40, response.getUnconnectedCount());
            assertEquals(30, response.getLineReasonUnconnectedRate());

            request.setId("");
            request.setType("");
            response = dialMetricService.getCallDetailCoreMetrics(request);

            // Verify
            assertEquals(100, response.getCallCount());
            assertEquals(60, response.getConnectedCount());
            assertEquals(40, response.getUnconnectedCount());
            assertEquals(30, response.getLineReasonUnconnectedRate());
        }
    }

    @Test
    void testGenerateDateLineForDayType() {
        // Prepare test data
        Date startDate = DateUtil.parse("2023-01-01");
        int offsetDays = 3;
        
        // Execute method
        List<String> result = dialMetricService.generateDateLine(DateLineTypeEnum.DAY, offsetDays, startDate);
        
        // Verify results
        assertEquals(3, result.size());
        assertEquals("2023-01-01", result.get(0));
        assertEquals("2023-01-02", result.get(1));
        assertEquals("2023-01-03", result.get(2));
    }

    @Test
    void testGenerateDateLineForHourType() {
        // Prepare test data
        Date startDate = DateUtil.parse("2023-01-01 10:00:00");
        int offsetHours = 2;
        
        // Execute method
        List<String> result = dialMetricService.generateDateLine(DateLineTypeEnum.HOUR, offsetHours, startDate);
        
        // Verify results
        assertEquals(2, result.size());
        assertEquals("2023-01-01 10:00:00", result.get(0));
        assertEquals("2023-01-01 11:00:00", result.get(1));
    }

    @Test
    void testGenerateDateLineWithZeroOffset() {
        // Prepare test data
        Date startDate = DateUtil.parse("2023-01-01");
        
        // Execute method
        List<String> result = dialMetricService.generateDateLine(DateLineTypeEnum.DAY, 0, startDate);
        
        // Verify results
        assertEquals(0, result.size());
    }

    @Test
    void testGenerateDateLineWithNegativeOffset() {
        // Prepare test data
        Date startDate = DateUtil.parse("2023-01-01");
        
        // Execute method
        List<String> result = dialMetricService.generateDateLine(DateLineTypeEnum.DAY, -1, startDate);
        
        // Verify results
        assertEquals(0, result.size());
    }

    @Test
    void testInitWithCustomOnlineTime() {
        // Given - custom online time is set via @TestPropertySource
        
        // When
        dialMetricService.init();
        
        // Then
        DateTime expected = DateUtil.parse("2024-12-31 23:59:59");
    }

    @Test
    void testInitWithDefaultOnlineTime() {
        // Given - default online time is set via @TestPropertySource
        
        // When
        dialMetricService.init();
        
        // Then
        DateTime expected = DateUtil.parse("2023-01-01 00:00:00");
    }
}