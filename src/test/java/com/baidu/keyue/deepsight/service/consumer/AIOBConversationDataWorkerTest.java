package com.baidu.keyue.deepsight.service.consumer;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.SopUserConfigMapper;
import com.baidu.keyue.deepsight.service.datamanage.AiobRobotVersionService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.utils.AESUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.IOException;
import java.util.Base64;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;

@TestPropertySource(properties = {"aiob.default-task-ids=1,2,3"})
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {"kafka.topics.dataSync=test_topic"})
public class AIOBConversationDataWorkerTest{


    @Mock
    private DorisService dorisService;

    @Mock
    private AiobRobotVersionService aiobRobotVersionService;

    @Mock
    private RestHighLevelClient client;

    @Mock
    private ObjectMapper objectMapper;
    @InjectMocks
    private AIOBConversationDataWorker worker;

    private Map<String, Object> testMap;

    @Mock
    private TenantInfoService tenantInfoService;

    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;

    @Mock
    private TableRecordCommonService commonService;

    @Mock
    private AiobSOPService aiobSOPService;

    @Mock
    private SopUserConfigMapper sopUserConfigMapper;

    @Mock
    private RedissonClient redisson;

    @BeforeEach
    void setUp() {
        testMap = new HashMap<>();
        ReflectionTestUtils.setField(worker, "commonService", commonService);
        ReflectionTestUtils.setField(worker, "defaultTaskIdStr", "1,2");
        Set<String> ids = new HashSet<>();
        ids.add("1");
        ids.add("2");
        ReflectionTestUtils.setField(worker, "defaultTaskIds", ids);
    }

    @Test
    void checkAiobRecordShouldReturnTrueWhenEvtSequenceNumberExists() {
        testMap.put("EvtSequenceNumber", "123");
        assertTrue(worker.checkAiobRecord(testMap));
    }

    @Test
    void checkAiobRecordShouldReturnFalseWhenEvtSequenceNumberBlank() {
        testMap.put("EvtSequenceNumber", "");
        assertFalse(worker.checkAiobRecord(testMap));
    }

    @Test
    void checkAiobSessionShouldReturnFalseWhenSessionIdBlank() {
        testMap.put("sessionId", "");
        assertFalse(worker.checkAiobSession(testMap));
    }

    @Test
    void processDataShouldProcessRecordWhenValid() throws Exception {
        String tenantId = "1";
        String data = "{\"code\":\"record\",\"data\":{\"tenantId\":\"" + tenantId + "\",\"EvtSequenceNumber\":\"123\"}}";
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        when(tenantInfoService.queryTenantInfo(tenantId)).thenReturn(new TenantInfo());
        when(commonService.getDataTableDetail(anyString(), anyString())).thenReturn(new DataTableInfo());
        when(kafkaTemplate.send(anyString(), anyString())).thenThrow(new RuntimeException());
        try (MockedStatic<TenantUtils> utils = mockStatic(TenantUtils.class)) {
            utils.when(() -> TenantUtils.generateAiobRecordTableName(tenantId)).thenReturn("testTable");
            worker.processData(data);
        }
    }

    @Test
    void processDataShouldProcessSessionWhenValid() throws Exception {
        String tenantId = "1";
        String data = "{\"code\":\"session\",\"data\":{\"tenantId\":\"" + tenantId + "\",\"EvtSequenceNumber\":\"123\"}}";
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        when(tenantInfoService.queryTenantInfo(tenantId)).thenReturn(new TenantInfo());
        when(commonService.getDataTableDetail(anyString(), anyString())).thenReturn(new DataTableInfo());
        when(kafkaTemplate.send(anyString(), anyString())).thenThrow(new RuntimeException());
        try (MockedStatic<TenantUtils> utils = mockStatic(TenantUtils.class)) {
            utils.when(() -> TenantUtils.generateAiobRecordTableName(tenantId)).thenReturn("testTable");
            worker.processData(data);
        }
    }

    @Test
    void processDataShouldProcessUserWhenValid() throws Exception {
        String tenantId = "1";
        String data = "{\"code\":\"user\",\"data\":{\"tenantId\":\"" + tenantId + "\",\"EvtSequenceNumber\":\"123\"}}";
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);
        ReflectionTestUtils.setField(worker, "dorisService", dorisService);
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "commonService", commonService);
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        when(tenantInfoService.queryTenantInfo(tenantId)).thenReturn(new TenantInfo());
        when(commonService.getDataTableDetail(anyString(), anyString())).thenReturn(new DataTableInfo());
        when(kafkaTemplate.send(anyString(), anyString())).thenThrow(new RuntimeException());
        when(dorisService.getCount(anyString())).thenReturn(0L);
        when(commonService.getDataTableDetail(anyString())).thenReturn(dataTableInfo);
        try (MockedStatic<TenantUtils> utils = mockStatic(TenantUtils.class)) {
            utils.when(() -> TenantUtils.generateAiobRecordTableName(tenantId)).thenReturn("testTable");
            worker.processData(data);
        }
    }
    @Test
    void processDataShouldProcessUser() throws Exception {
        String tenantId = "1";
        String data = "{\"code\":\"user\",\"data\":{\"tenantId\":\"" + tenantId + "\",\"EvtSequenceNumber\":\"123\"}}";
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "commonService", commonService);
        ReflectionTestUtils.setField(worker, "dorisService", dorisService);
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "commonService", commonService);
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        when(tenantInfoService.queryTenantInfo(tenantId)).thenReturn(new TenantInfo());
        when(commonService.getDataTableDetail(anyString(), anyString())).thenReturn(new DataTableInfo());
        when(kafkaTemplate.send(anyString(), anyString())).thenThrow(new RuntimeException());
        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(commonService.getDataTableDetail(anyString())).thenReturn(dataTableInfo);
        try (MockedStatic<TenantUtils> utils = mockStatic(TenantUtils.class)) {
            utils.when(() -> TenantUtils.generateAiobRecordTableName(tenantId)).thenReturn("testTable");
            worker.processData(data);
        }
    }
    

    @Test
    void convertAiobRecordShouldAddRoleTypeWhenRoleExists() {
        testMap.put("role", "testRole");
        worker.convertAiobRecord(testMap);
        assertEquals("testRole", testMap.get("roleType"));
    }

    @Test
    void checkAiobSessionShouldReturnFalseWhenSecretTypeIs3() {
        testMap.put("sessionId", "123");
        testMap.put("secretType", 3);
        assertFalse(worker.checkAiobSession(testMap));
    }

    @Test
    void checkAiobSessionShouldReturnTrueWhenValid() {
        testMap.put("sessionId", "123");
        assertTrue(worker.checkAiobSession(testMap));
    }

    @Test
    void processDataShouldSkipWhenCodeNotInSet() throws Exception {
        String invalidData = "{\"code\":\"invalid\",\"data\":{}}";
        worker.processData(invalidData);
        verifyNoInteractions(kafkaTemplate);
    }

    @Test
    void processDataShouldSkipWhenTenantIdBlank() throws Exception {
        String data = "{\"code\":\"record\",\"data\":{\"tenantId\":\"11\",\"EvtSequenceNumber\":\"record\"}}";
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "dorisService", dorisService);
        when(dorisService.getCount(anyString())).thenReturn(0L);
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(new TenantInfo());
        worker.processData(data);
    }

    @Test
    void processDataShouldSkipWhenTenantIdBlank11() throws Exception {
        String data = "{\"code\":\"record\",\"data\":{\"tenantId\":\"11\",\"EvtSequenceNumber\":\"\"}}";
        ReflectionTestUtils.setField(worker, "kafkaTemplate", kafkaTemplate);
        ReflectionTestUtils.setField(worker, "tenantInfoService", tenantInfoService);
        ReflectionTestUtils.setField(worker, "dorisService", dorisService);
        when(dorisService.getCount(anyString())).thenReturn(0L);
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(new TenantInfo());
        worker.processData(data);
    }

    @Test
    void convertAiobRecordShouldProcessMapCorrectly() {
        testMap.put("recordId", "123");
        worker.convertAiobRecord(testMap);
    }


    @Test
    void getRecordCntBySessionIdShouldReturnCount() {
        when(commonService.getTableByTableName(anyString())).thenReturn(null);
        assertEquals(0L, worker.getRecordCntBySessionId("123"));
    }

    @Test
    void conversationHistoryLogConsumerShouldProcessMessage() {
        String testMsg = "test message";
        worker.conversationHistoryLogConsumer(testMsg);
    }

    @Test
    void checkAiobSessionShouldReturnFalseWhenTenantIdNotExists() {
        testMap.put("sessionId", "123");
    }

    @Test
    void checkAiobSessionShouldReturnTrueWhenRequiredFieldsExist() {
        testMap.put("sessionId", "123");
        testMap.put("tenantId", "456");
        assertTrue(worker.checkAiobSession(testMap));
    }

    @Test
    void checkAiobSessionShouldReturnFalseWhenSessionIdNotExists() {
        testMap.put("tenantId", "456");
        assertFalse(worker.checkAiobSession(testMap));
    }

    @Test
    void processDataShouldHandleMessageCorrectly() {
        String testData = "{\"sessionId\":\"123\"}";
        worker.processData(testData);
    }

    @Test
    void checkAiobRecordShouldReturnFalseWhenEvtSequenceNumberNotExists() {
        assertFalse(worker.checkAiobRecord(testMap));
    }

    @Test
    void checkAiobSessionShouldReturnFalseWhenSessionIdMissing() {
        testMap.put("tenantId", "456");
        assertFalse(worker.checkAiobSession(testMap));
    }



    @Test
    void checkAiobSessionShouldReturnFalseWhenTenantIdMissing() {
        testMap.put("sessionId", "123");
        assertTrue(worker.checkAiobSession(testMap));
    }

    @Test
    void convertAiobRecordShouldProcessFieldsCorrectly() {
        testMap.put("tenantId", "123");
        testMap.put("sessionId", "456");
        testMap.put("EvtSequenceNumber", "789");
        assertDoesNotThrow(() -> worker.convertAiobRecord(testMap));
    }



    @Test
    void processDataShouldHandleValidJson() {
        String validJson = "{\"key\":\"value\"}";
        assertDoesNotThrow(() -> worker.processData(validJson));
    }



    @Test
    void handleAiobMoileFieldShouldSuccessWhenSecretTypeIsNull() {
        testMap.put("mobile", "13800138000");
        testMap.put("tenantId", "123");
        when(commonService.getAiobSecretKey(anyInt(), anyLong(), any())).thenReturn(null);
    
        assertEquals("13800138000", testMap.get("mobile"));
    }





    @Test
    void handleAiobMoileFieldShouldDecryptMobileWhenSecretTypeIsNotNull() {
        testMap.put("mobile", "encryptedMobile");
        testMap.put("tenantId", "123");
        testMap.put("secretType", 1);
        String key = "testKey";
        String decryptedMobile = "13800138000";
        Map<String, String> encryptFields = new HashMap<>();

        when(commonService.getAiobSecretKey(1, 123L, "123")).thenReturn(key);
        when(commonService.getEncryptFields(1L)).thenReturn(encryptFields);
        try (MockedStatic<AESUtils> aesUtilsMock = mockStatic(AESUtils.class)) {
            aesUtilsMock.when(() -> AESUtils.decrypt("encryptedMobile", key))
                .thenReturn(decryptedMobile);

        }
    }
    void handleAiobMoileFieldShouldThrowExceptionWhenSecretKeyIsNull() {
        testMap.put("mobile", "encryptedMobile");
        testMap.put("tenantId", "123");
        testMap.put("secretType", 3);
        when(commonService.getAiobSecretKey(anyInt(), anyLong(), any())).thenReturn(null);
        try {
            worker.handleAiobMoileField(testMap, 1L);
        } catch (Exception e) {

        }

    }

    @Test
    void handleAiobMoileFieldShouldThrowExceptionWhenSecretKey() {
        testMap.put("mobile", "encryptedMobile");
        testMap.put("tenantId", "123");
        testMap.put("secretType", 1);
        testMap.put("secretKey", "1");
        // when(commonService.getAiobSecretKey(anyInt(), anyLong(), any())).thenReturn("1");
        try {
            worker.handleAiobMoileField(testMap, 1L);
        } catch (Exception e) {

        }

    }

    @Test
    void handleAiobMoileFieldShouldThrowExceptionWhenSecretKeyIS2() {
        testMap.put("mobile", "encryptedMobile");
        testMap.put("tenantId", "123");
        testMap.put("secretType", 2);
        when(commonService.getAiobSecretKey(anyInt(), anyLong(), any())).thenReturn("2");
        try {
            worker.handleAiobMoileField(testMap, 1L);
        } catch (Exception e) {

        }

    }

    @Test
    void handleAiobMoileFieldShouldThrowExceptionWhenSecretKeyIS4() {
        testMap.put("mobile", "encryptedMobile");
        testMap.put("tenantId", "123");
        testMap.put("secretType", 4);
        when(commonService.getAiobSecretKey(anyInt(), anyLong(), any())).thenReturn("2");
        try {
            worker.handleAiobMoileField(testMap, 1L);
        } catch (Exception e) {

        }

    }

    @Test
    void handleAiobMoileFieldShouldThrowExceptionWhenSecretKeyIS3() {
        testMap.put("mobile", "encryptedMobile");
        testMap.put("tenantId", "123");
        testMap.put("secretType", 3);
        when(commonService.getAiobSecretKey(anyInt(), anyLong(), any())).thenReturn("3");
        try {
            worker.handleAiobMoileField(testMap, 1L);
        } catch (Exception e) {

        }

    }

    @Test
    void handleAiobMoileFieldShouldThrowExceptionWhenSecre() {
        testMap.put("mobile", "encryptedMobile");
        testMap.put("tenantId", "123");
        testMap.put("secretType", 3);
        when(commonService.getAiobSecretKey(anyInt(), anyLong(), any())).thenReturn(null);
        try {
            worker.handleAiobMoileField(testMap, 1L);
        } catch (Exception e) {

        }

    }


    @Test
    void convertAiobRecordShouldSetRoleTypeWhenRoleExists() {
        testMap.put("role", "admin");
        worker.convertAiobRecord(testMap);
        assertEquals("admin", testMap.get("roleType"));
    }

    @Test
    void processDataShouldSkipWhenCodeNotInSet1() throws Exception {
        String data = "{\"code\":\"invalid\",\"data\":{}}";
        worker.processData(data);
        verify(kafkaTemplate, never()).send(anyString(), anyString());
    }

    @Test
    void processDataShouldSkipWhenTenantIdBlank1() throws Exception {
        String data = "{\"code\":\"record\",\"data\":{\"tenantId\":\"\"}}";
        worker.processData(data);
        verify(kafkaTemplate, never()).send(anyString(), anyString());
    }

    @Test
    void processDataShouldSkipWhenTenantIdBlank2() throws Exception {
        String data = "{\"code\":\"user\",\"data\":{\"tenantId\":\"tenant123\",\"mobile\":\"123\"}}";
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(new TenantInfo());
        when(commonService.getDataTableDetail(anyString(), anyString())).thenReturn(new DataTableInfo());
        when(commonService.getRequiredFields(anyLong())).thenReturn(List.of());
        when(commonService.getFieldsType(anyLong())).thenReturn(Map.of());
        worker.processData(data);
        verify(kafkaTemplate, never()).send(anyString(), anyString());
    }

    @Test
    void processDataShouldProcessRecordWhenValid1() throws Exception {
        String data = "{\"code\":\"record\",\"data\":{\"tenantId\":\"tenant123\",\"EvtSequenceNumber\":\"123\"}}";
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(new TenantInfo());
        when(commonService.getDataTableDetail(anyString(), anyString())).thenReturn(new DataTableInfo());
        when(commonService.getRequiredFields(anyLong())).thenReturn(List.of());
        when(commonService.getFieldsType(anyLong())).thenReturn(Map.of());
        
        worker.processData(data);
        
    }

    @Test
    void checkAiobSessionShouldReturnFalseWhenSessionIdMissing1() {
        assertFalse(worker.checkAiobSession(testMap));
    }



    @Test
    void checkAiobSessionShouldReturnFalseWhenSecretType3() {
        testMap.put("sessionId", "session123");
        testMap.put("secretType", 3);
        assertFalse(worker.checkAiobSession(testMap));
    }

    @Test
    void checkAiobUserShouldReturnFalseWhenMobileBlank() {
        assertFalse(worker.checkAiobUser(testMap));
    }

    @Test
    void checkAiobSessionShouldReturnTrueWhenValidSession() {
        testMap.put("sessionId", "session123");
        assertTrue(worker.checkAiobSession(testMap));
    }

    @Test
    void checkAiobSessionShouldReturnTrueWhenValidSession1() {
        testMap.put("sessionId", "session123");
        testMap.put("taskId", "1");
        assertFalse(worker.checkAiobSession(testMap));
    }
    
    @Test
    void processDataShouldSkipWhenTenantNotExist() throws Exception {
        String data = "{\"code\":\"record\",\"data\":{\"tenantId\":\"tenant123\"}}";
        when(tenantInfoService.queryTenantInfo(anyString())).thenReturn(null);
        worker.processData(data);
        verify(kafkaTemplate, never()).send(anyString(), anyString());
    }

    @Test
    void convertAiobRecordShouldDoNothingWhenRoleNotExists() {
        Map<String, Object> originalMap = new HashMap<>(testMap);
        worker.convertAiobRecord(testMap);
        assertEquals(originalMap, testMap);
    }

    @Test
    void checkAiobUserShouldHandleNullSecretType() {
        testMap.put("mobile", "13800138000");
        testMap.put("tenantId", "123");
        testMap.put("tenantId", "123");

        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        
        when(commonService.getDataTableDetail(anyString())).thenReturn(dataTableInfo);
        when(commonService.getEncryptFields(anyLong())).thenReturn(new HashMap<>());
        
        assertTrue(worker.checkAiobUser(testMap));
        verify(commonService, never()).getAiobSecretKey(anyInt(), anyLong(), any());
    }

    @Test
    void checkAiobUserShouldReturnFalseWhenMobileIsBlank() {
        testMap.put("mobile", "");
        assertFalse(worker.checkAiobUser(testMap));
    }

    @Test
    void checkAiobUserShouldReturnFalseWhenSecretTypeIs3() {
        testMap.put("mobile", "13800138000");
        testMap.put("secretType", 3);
        assertFalse(worker.checkAiobUser(testMap));
    }

    @Test
    void getQueryIdByNodeInfoShouldHandleMissingNodeInfoGracefully() {
    // Arrange
    Map<String, Object> testMap = new HashMap<>();
    
    // Act & Assert (should not throw exception)
    assertDoesNotThrow(() -> worker.getQueryIdByNodeInfo(testMap));
    }

    @Test
    void getQueryIdByNodeInfoShouldAddQueryIdWhenNodeInfoContainsQueryId() throws Exception {
    // Arrange
    String queryId = "testQueryId";
    String jsonContent = "{\"queryId\":\"" + queryId + "\"}";
    String base64NodeInfo = Base64.getEncoder().encodeToString(jsonContent.getBytes());
    
    Map<String, Object> testMap = new HashMap<>();
    testMap.put("nodeInfo", base64NodeInfo);
    
    // Act
    worker.getQueryIdByNodeInfo(testMap);
    
    // Assert
    assertEquals(queryId, testMap.get("queryId"));
    }

    @Test
    void getQueryIdByNodeInfoShouldNotAddQueryIdWhenNodeInfoDoesNotContainQueryId() throws Exception {
    // Arrange
    String jsonContent = "{\"otherField\":\"value\"}";
    String base64NodeInfo = Base64.getEncoder().encodeToString(jsonContent.getBytes());
    
    Map<String, Object> testMap = new HashMap<>();
    testMap.put("nodeInfo", base64NodeInfo);
    
    // Act
    worker.getQueryIdByNodeInfo(testMap);
    
    // Assert
    assertFalse(testMap.containsKey("queryId"));
    }

    @Test
    void getQueryIdByNodeInfoShouldHandleMalformedJsonGracefully() {
    // Arrange
    String invalidJson = "invalid json";
    String base64NodeInfo = Base64.getEncoder().encodeToString(invalidJson.getBytes());
    
    Map<String, Object> testMap = new HashMap<>();
    testMap.put("nodeInfo", base64NodeInfo);
    
    // Act & Assert (should not throw exception)
    assertDoesNotThrow(() -> worker.getQueryIdByNodeInfo(testMap));
    }

    @Test
    void getQueryIdByNodeInfoShouldNotAddQueryIdWhenNodeInfoDoesNotContainQueryId1() throws Exception {
        testMap = new HashMap<>();
        ReflectionTestUtils.setField(worker, "commonService", commonService);
        ReflectionTestUtils.setField(worker, "objectMapper", objectMapper);
    
        String json = "{\"otherKey\":\"value\"}";
        String encodedNodeInfo = Base64.getEncoder().encodeToString(json.getBytes());
        testMap.put("nodeInfo", encodedNodeInfo);
    
        Map<String, Object> decodedMap = new HashMap<>();
        when(objectMapper.readValue(anyString(), eq(Map.class))).thenReturn(decodedMap);
    
        worker.getQueryIdByNodeInfo(testMap);
    
        assertFalse(testMap.containsKey("queryId"));
    }

    @Test
    void getQueryIdByNodeInfoShouldHandleDecodingErrorGracefully() throws Exception {
        testMap = new HashMap<>();
        ReflectionTestUtils.setField(worker, "commonService", commonService);
        ReflectionTestUtils.setField(worker, "objectMapper", objectMapper);
    
        testMap.put("nodeInfo", "invalidBase64");
    
        when(objectMapper.readValue(anyString(), eq(Map.class))).thenThrow(new RuntimeException("Test exception"));
    
        assertFalse(testMap.containsKey("queryId"));
    }

    @Test
    void getQueryIdByNodeInfoShouldAddQueryIdWhenNodeInfoContainsQueryId1() throws Exception {
        testMap = new HashMap<>();
        ReflectionTestUtils.setField(worker, "commonService", commonService);
        ReflectionTestUtils.setField(worker, "objectMapper", objectMapper);
    
        String queryId = "testQueryId";
        String json = "{\"queryId\":\"" + queryId + "\"}";
        String encodedNodeInfo = Base64.getEncoder().encodeToString(json.getBytes());
        testMap.put("nodeInfo", encodedNodeInfo);
    
        Map<String, Object> decodedMap = new HashMap<>();
        decodedMap.put("queryId", queryId);
        when(objectMapper.readValue(anyString(), eq(Map.class))).thenReturn(decodedMap);
    
        worker.getQueryIdByNodeInfo(testMap);
    
        assertEquals(queryId, testMap.get("queryId"));
    }



    @Test
    void convertAiobSessionShouldHandleNullDidOwner() throws IOException {
        testMap = new HashMap<>();
        testMap.put("tenantId", "123");
        testMap.put("sessionId", "session123");
        testMap.put("taskId", "task123");
        testMap.put("mobile", "DE986CBEA5513AAF2E671ABD04D275FD");
        ReflectionTestUtils.setField(worker, "commonService", commonService);
    
        // Setup
        testMap.remove("didOwner");
        
        // Mock data
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);
        
        Map<String, String> encryptFields = new HashMap<>();
        encryptFields.put("field1", "type1");
    
        // Mock behavior
        when(commonService.getDataTableDetail(anyString())).thenReturn(dataTableInfo);
        when(commonService.getAiobSecretKey(anyInt(), anyLong(), any())).thenReturn("ac1pppC3c2JdZD86");
        when(commonService.getEncryptFields(anyLong())).thenReturn(encryptFields);

        ReflectionTestUtils.setField(worker, "client", client);
        ReflectionTestUtils.setField(worker, "aiobSOPService", aiobSOPService);

        CountResponse response = new CountResponse(1L, false, null);
        when(client.count(any(CountRequest.class), any(RequestOptions.class))).thenReturn(response);

        SopUserConfig userConfig = new SopUserConfig();
        userConfig.setTaskId("task1");
        userConfig.setTenantId("tenant1");
        userConfig.setCoreMetric((byte) 1);
        userConfig.setWarningThreshold(5);
        userConfig.setDel(DelEnum.NOT_DELETED.getBoolean());
        when(sopUserConfigMapper.selectByExample(any(SopUserConfigCriteria.class)))
                .thenReturn(Collections.singletonList(userConfig));

        // Execute
        worker.convertAiobSession(testMap);
        worker.convertAiobSession(testMap);
        worker.convertAiobSession(testMap);
        worker.convertAiobSession(testMap);
        worker.convertAiobSession(testMap);
        worker.convertAiobSession(testMap);
    
        // Verify
        assertNull(testMap.get("didOwner"));
    }

    @Test
    void convertAiobSessionShouldMappingReason() throws IOException {
        testMap = new HashMap<>();
        testMap.put("tenantId", "123");
        testMap.put("sessionId", "session123");
        testMap.put("taskId", "task123");
        testMap.put("mobile", "DE986CBEA5513AAF2E671ABD04D275FD");
        testMap.put("endType", 0);
        testMap.put("endTypeReason", "cantbeconnected");
        ReflectionTestUtils.setField(worker, "commonService", commonService);

        // Setup
        testMap.remove("didOwner");

        // Mock data
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setId(1L);

        Map<String, String> encryptFields = new HashMap<>();
        encryptFields.put("field1", "type1");

        // Mock behavior
        when(commonService.getDataTableDetail(anyString())).thenReturn(dataTableInfo);
        when(commonService.getAiobSecretKey(anyInt(), anyLong(), any())).thenReturn("ac1pppC3c2JdZD86");
        when(commonService.getEncryptFields(anyLong())).thenReturn(encryptFields);

        ReflectionTestUtils.setField(worker, "client", client);
        ReflectionTestUtils.setField(worker, "aiobSOPService", aiobSOPService);

        CountResponse response = new CountResponse(1L, false, null);
        when(client.count(any(CountRequest.class), any(RequestOptions.class))).thenReturn(response);

        SopUserConfig userConfig = new SopUserConfig();
        userConfig.setTaskId("task1");
        userConfig.setTenantId("tenant1");
        userConfig.setCoreMetric((byte) 1);
        userConfig.setWarningThreshold(5);
        userConfig.setDel(DelEnum.NOT_DELETED.getBoolean());
        when(sopUserConfigMapper.selectByExample(any(SopUserConfigCriteria.class)))
                .thenReturn(Collections.singletonList(userConfig));

        // Execute
        worker.convertAiobSession(testMap);

        // Verify
        assertNull(testMap.get("didOwner"));
        assertEquals(testMap.get("dicName"), "无法接通");
    }

    private CountResponse mockCountResponse(long count) {
        CountResponse response = mock(CountResponse.class);
        when(response.getCount()).thenReturn(count);
        return response;
    }

    @Test
    void addRobotVersionShouldDoNothingWhenRobotSceneNot5() {
        testMap = new HashMap<>();
        ReflectionTestUtils.setField(worker, "aiobRobotVersionService", aiobRobotVersionService);

        testMap.put("robotScene", 4);
        worker.addRobotVersion(testMap);
        verify(aiobRobotVersionService, never()).getAiobRobotVersion(any(), any(), any());
        verify(aiobRobotVersionService, never()).saveAiobRobotVersion(any(), any(), any(), any(), any());
    }

    @Test
    void addRobotVersionShouldDoNothingWhenRobotVersionExists() {
        testMap = new HashMap<>();
        ReflectionTestUtils.setField(worker, "aiobRobotVersionService", aiobRobotVersionService);

        testMap.put("robotScene", 5);
        testMap.put("tenantId", "tenant1");
        testMap.put("robotId", "robot1");
        testMap.put("botVersionId", "version1");
        testMap.put("botVersionName", "name1");
        testMap.put("sipCode", "200");

        when(aiobRobotVersionService.getAiobRobotVersion("tenant1", "robot1", "version1")).thenReturn(true);

        worker.addRobotVersion(testMap);

        verify(aiobRobotVersionService).getAiobRobotVersion("tenant1", "robot1", "version1");
        verify(aiobRobotVersionService, never()).saveAiobRobotVersion(any(), any(), any(), any(), any());
    }

    @Test
    void addRobotVersionShouldSaveNewVersionWhenRobotVersionNotExists() {
        testMap = new HashMap<>();
        ReflectionTestUtils.setField(worker, "aiobRobotVersionService", aiobRobotVersionService);

        testMap.put("robotScene", 5);
        testMap.put("tenantId", "tenant1");
        testMap.put("sipCode", "200");
        testMap.put("robotId", "robot1");
        testMap.put("botVersionId", "version1");
        testMap.put("botVersionName", "name1");
        testMap.put("publishTime", 1747742608000L);

        when(aiobRobotVersionService.getAiobRobotVersion("tenant1", "robot1", "version1")).thenReturn(false);

        worker.addRobotVersion(testMap);

        verify(aiobRobotVersionService).getAiobRobotVersion("tenant1", "robot1", "version1");
        verify(aiobRobotVersionService).saveAiobRobotVersion("tenant1", "robot1", "version1", "name1", 1747742608000L);
    }

    @Test
    void checkAiobSessionShouldReturnTrueWhenAllConditionsAreMet() {
    Map<String, Object> map = new HashMap<>();
    map.put("sessionId", "validSessionId");
    assertTrue(worker.checkAiobSession(map));
    }

    @Test
    void checkAiobSessionShouldReturnTrueWhenSecretTypeIsNot3() {
    Map<String, Object> map = new HashMap<>();
    map.put("sessionId", "validSessionId");
    map.put("secretType", 2);
    assertTrue(worker.checkAiobSession(map));
    }

    @Test
    void checkAiobSessionShouldReturnTrueWhenSecretTypeIsNull() {
    Map<String, Object> map = new HashMap<>();
    map.put("sessionId", "validSessionId");
    map.put("secretType", null);
    assertTrue(worker.checkAiobSession(map));
    }

    @Test
    void checkAiobSessionShouldReturnTrueWhenTaskTypeIsNot4() {
    Map<String, Object> map = new HashMap<>();
    map.put("sessionId", "validSessionId");
    map.put("taskType", "1");
    assertTrue(worker.checkAiobSession(map));
    }

    @Test
    void checkAiobSessionShouldReturnFalseWhenSessionIdIsBlank() {
    Map<String, Object> map = new HashMap<>();
    map.put("sessionId", "");
    assertFalse(worker.checkAiobSession(map));
    }

    @Test
    void checkAiobSessionShouldReturnFalseWhenTaskTypeIs4() {
    Map<String, Object> map = new HashMap<>();
    map.put("sessionId", "validSessionId");
    map.put("taskType", "4");
    assertFalse(worker.checkAiobSession(map));
    }

    @Test
    void checkAiobSessionShouldReturnFalseWhenSecretTypeIs31() {
    Map<String, Object> map = new HashMap<>();
    map.put("sessionId", "validSessionId");
    map.put("secretType", 3);
    assertFalse(worker.checkAiobSession(map));
    }



    @Test
    void isDuplicateDataShouldReturnFalseWhenTypeIsInvalid() {
        Map<String, Object> map = new HashMap<>();
        assertFalse(worker.isDuplicateData(map, "invalid_type", "a"));
    }
    @Test
    public void testIsDuplicateData_RecordType_NotDuplicate() {
        Map<String, Object> data = new HashMap<>();
        data.put("EvtSequenceNumber", "123");
        ReflectionTestUtils.setField(worker, "dorisService", dorisService);
        when(dorisService.getCount(anyString())).thenReturn(0L);

        Boolean result = worker.isDuplicateData(data, "record", "aa");
        assertTrue(result);
    }

    @Test
    public void testIsDuplicateData_SessionType_Duplicate() {
        Map<String, Object> data = new HashMap<>();
        data.put("sessionId", "abc-session");
        ReflectionTestUtils.setField(worker, "dorisService", dorisService);

        when(dorisService.getCount(anyString())).thenReturn(1L);

        Boolean result = worker.isDuplicateData(data, "session", "aa");
        assertFalse(result);
    }


    @Test
    public void testIsDuplicateData_UserType_NotDuplicate() {
        Map<String, Object> data = new HashMap<>();
        data.put("mobile", "13800138000");
        ReflectionTestUtils.setField(worker, "dorisService", dorisService);
        ReflectionTestUtils.setField(worker, "redisson", redisson);
        RBucket bucket = mock(RBucket.class);
        when(redisson.getBucket(anyString())).thenReturn(bucket);

        Boolean result = worker.isDuplicateData(data, "user", "aaa");
    }

    @Test
    public void testIsDuplicateData_UserType_Duplicate() {
        Map<String, Object> data = new HashMap<>();
        data.put("mobile", "13800138000");
        ReflectionTestUtils.setField(worker, "dorisService", dorisService);
        ReflectionTestUtils.setField(worker, "redisson", redisson);
        RBucket bucket = mock(RBucket.class);
        when(redisson.getBucket(anyString())).thenReturn(bucket);
        when(bucket.trySet("1", 10 , TimeUnit.MINUTES)).thenReturn(true);

        Boolean result = worker.isDuplicateData(data, "user", "aaa");
    }



    @Test
    void convertAiobRecordShouldPutRoleTypeWhenRoleExists() {
        Map<String, Object> map = new HashMap<>();
        map.put("role", "testRole");
        worker.convertAiobRecord(map);
        assertEquals("testRole", map.get("roleType"));
    }

    @Test
    void checkAiobSessionShouldReturnFalseWhenTaskTypeIs41() {
        Map<String, Object> map = new HashMap<>();
        map.put("sessionId", "123");
        map.put("taskType", "4");
        assertFalse(worker.checkAiobSession(map));
    }



    @Test
    void processDataShouldReturnWhenCodeNotInCodesSet() throws Exception {
        String data = "{\"code\":\"invalid\",\"data\":{}}";
        worker.processData(data);
        verify(kafkaTemplate, never()).send(anyString(), anyString());
    }


    @Test
    void checkAiobUserShouldReturnFalseWhenMobileIsBlank1() {
        Map<String, Object> map = new HashMap<>();
        assertFalse(worker.checkAiobUser(map));
    }

    @Test
    void initShouldAddDefaultTaskIdsWhenDefaultTaskIdStrIsNotEmpty() {
        worker.init();
        Set<String> expected = Set.of("1", "2", "3");
    }

    @Test
    void initShouldNotAddDefaultTaskIdsWhenDefaultTaskIdStrIsBlank() {
        ReflectionTestUtils.setField(worker, "defaultTaskIdStr", "");
        worker.init();
    }

    @Test
    void initShouldHandleSingleTaskId() {
        ReflectionTestUtils.setField(worker, "defaultTaskIdStr", "42");
        worker.init();
    }





}