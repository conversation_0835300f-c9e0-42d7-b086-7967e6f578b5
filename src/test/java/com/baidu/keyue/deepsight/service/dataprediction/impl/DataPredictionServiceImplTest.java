package com.baidu.keyue.deepsight.service.dataprediction.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.models.predict.PredictDataSet;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.kybase.commons.utils.JsonUtil;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import com.google.gson.reflect.TypeToken;

@ExtendWith(MockitoExtension.class)
public class DataPredictionServiceImplTest{

    private static final String VALID_JSON = "[{\"datasetId\":1,\"datasetName\":\"test\"}]";

    private static final String EMPTY_JSON = "[]";

    @Mock
    private DataTableInfoMapper dataTableInfoMapper;

    @InjectMocks
    private DataPredictionServiceImpl dataPredictionService;

    private static final String TENANT_ID = "testTenant";

    @Test
    void filterDataSourceListShouldReturnEmptyListWhenInputJsonIsEmpty() {
        List<PredictDataSet> result = dataPredictionService.filterDataSourceList(EMPTY_JSON, TENANT_ID);
        assertTrue(result.isEmpty());
    }

    @Test
    void filterDataSourceListShouldReturnFilteredListWhenValidDataExists() {
        try (MockedStatic<JsonUtil> mockedJsonUtil = mockStatic(JsonUtil.class)) {
            List<PredictDataSet> mockSourceList = List.of(new PredictDataSet(1L, "test", 1L, "", false));
            mockedJsonUtil.when(() -> JsonUtil.fromJson(eq(VALID_JSON), any(TypeToken.class)))
                    .thenReturn(mockSourceList);

            DataTableInfo dataTableInfo = new DataTableInfo();
            dataTableInfo.setId(1L);
            when(dataTableInfoMapper.selectByExample(any(DataTableInfoCriteria.class)))
                    .thenReturn(List.of(dataTableInfo));
    
            List<PredictDataSet> result = dataPredictionService.filterDataSourceList(VALID_JSON, TENANT_ID);
            assertEquals(1, result.size());
            assertEquals(1L, result.get(0).getDatasetId());
        }
    }

    @Test
    void filterDataSourceListShouldReturnOriginalListWhenNoValidDataFound() {
        try (MockedStatic<JsonUtil> mockedJsonUtil = mockStatic(JsonUtil.class)) {
            List<PredictDataSet> mockSourceList = List.of(new PredictDataSet(1L, "test", 1L, "", false));
            mockedJsonUtil.when(() -> JsonUtil.fromJson(eq(VALID_JSON), any(TypeToken.class)))
                    .thenReturn(mockSourceList);
    
            when(dataTableInfoMapper.selectByExample(any(DataTableInfoCriteria.class)))
                    .thenReturn(Collections.emptyList());
    
            List<PredictDataSet> result = dataPredictionService.filterDataSourceList(VALID_JSON, TENANT_ID);
            assertEquals(0, result.size());
        }
    }

    @Test
    void filterDataSourceListShouldReturnOriginalListWhenExceptionOccurs() {
        try (MockedStatic<JsonUtil> mockedJsonUtil = mockStatic(JsonUtil.class)) {
            List<PredictDataSet> mockSourceList = List.of(new PredictDataSet(1L, "test", 1L, "", false));
            mockedJsonUtil.when(() -> JsonUtil.fromJson(eq(VALID_JSON), any(TypeToken.class)))
                    .thenReturn(mockSourceList);
    
            when(dataTableInfoMapper.selectByExample(any(DataTableInfoCriteria.class)))
                    .thenThrow(new RuntimeException("Test exception"));
    
            List<PredictDataSet> result = dataPredictionService.filterDataSourceList(VALID_JSON, TENANT_ID);
            assertEquals(0, result.size());
        }
    }

}