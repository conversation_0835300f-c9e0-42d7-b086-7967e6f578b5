package com.baidu.keyue.deepsight.service.impl;

import com.baidu.keyue.deepsight.BaseTest;
import com.baidu.keyue.deepsight.database.service.DorisService;
import jakarta.annotation.Resource;
import org.junit.Test;

/**
 * @ClassName DorisServiceImplTest
 * @Description DorisServiceImplTest
 * <AUTHOR>
 * @Date 2025/3/19 2:01 PM
 */
public class DorisServiceImplTest extends BaseTest {
    @Resource
    private DorisService dorisService;
    
    @Test
    public void testOperationSchema(){
        dorisService.operationSchema("ALTER TABLE mock_user_9527 RENAME COLUMN `device_id` `DEVICEID`;");
    }
}
