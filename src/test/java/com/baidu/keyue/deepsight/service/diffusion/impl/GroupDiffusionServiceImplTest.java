package com.baidu.keyue.deepsight.service.diffusion.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DiffusionFilterEnum;
import com.baidu.keyue.deepsight.enums.FeatureSelectEnum;
import com.baidu.keyue.deepsight.enums.JudgeCriteriaEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.models.customer.request.CustomerDiffusionTaskCreateRequest;
import com.baidu.keyue.deepsight.models.customer.request.CustomerDiffusionTaskRetryRequest;
import com.baidu.keyue.deepsight.models.customer.response.CustomerDiffusionTaskCreateRes;
import com.baidu.keyue.deepsight.models.customer.response.CustomerDiffusionTaskRes;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTaskCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.mysqldb.mapper.CustomerDiffusionTaskMapper;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionCalculateService;
import com.baidu.keyue.deepsight.service.operation.OperationService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anySet;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.argThat;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = "customer-group.seed-proportion=0.5")
public class GroupDiffusionServiceImplTest {


    @Mock
    private GroupDiffusionCalculateService groupDiffusionCalculateService;
    @Mock
    private RLock lock;

    @InjectMocks
    private GroupDiffusionServiceImpl groupDiffusionService;

    private static final String TENANT_ID = "1";

    private static final String USER_ID = "9527";
    private static final String USER_NAME = "mock_test";

    @Mock
    private TaskInfoService taskInfoService;

    @Mock
    private CustomerGroupService customerGroupService;

    @Mock
    private CustomerDiffusionTaskMapper customerDiffusionTaskMapper;

    @Mock
    private RedissonClient redissonClient;
    @Mock
    private DorisService dorisService;

    @Mock
    private OperationService operationService;

    @BeforeEach
    void setUp() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(USER_ID);
            mocked.when(WebContextHolder::getUserName).thenReturn(USER_NAME);
        }
    }

    private CustomerDiffusionTaskCreateRequest buildValidRequest() {
        CustomerDiffusionTaskCreateRequest request = new CustomerDiffusionTaskCreateRequest();
        request.setTaskName("test-task");
        request.setSeedGroup(1L);
        request.setPredictGroup(new HashSet<>(Arrays.asList(2L, 3L)));
        request.setFeatureSelect(FeatureSelectEnum.COVER_CUSTOMIZE);
        request.setThreshold(0.5f);
        request.setJudgeCriteria(JudgeCriteriaEnum.SIMILARITY);
        request.setSimilarity(0.8f);
        request.setTriggerMod(TriggerModeEnum.REALTIME);
        request.setFilterRule(DiffusionFilterEnum.NOT_FILTER);
        return request;
    }

    private CustomerDiffusionTaskCreateRequest buildValidCronRequest() {
        CustomerDiffusionTaskCreateRequest request = new CustomerDiffusionTaskCreateRequest();
        request.setTaskName("test-task");
        request.setSeedGroup(1L);
        request.setPredictGroup(new HashSet<>(Arrays.asList(2L, 3L)));
        request.setFeatureSelect(FeatureSelectEnum.COVER_CUSTOMIZE);
        request.setThreshold(0.5f);
        request.setJudgeCriteria(JudgeCriteriaEnum.SIMILARITY);
        request.setSimilarity(0.8f);
        request.setTriggerMod(TriggerModeEnum.CRON);
        request.setTriggerFrequency(TriggerFrequencyEnum.DAY);
        request.setTriggerFrequencyValue(new TriggerFrequencyValue());
        request.setFilterRule(DiffusionFilterEnum.NOT_FILTER);
        return request;
    }

    @Test
    void createTaskSuccess() {
        ReflectionTestUtils.setField(groupDiffusionService, "seedProportion", 0.5);
        ReflectionTestUtils.setField(groupDiffusionService, "dorisService", dorisService);
        ReflectionTestUtils.setField(groupDiffusionService, "systemRecommend", 30F);
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(1L);
        WebContextHolder.setDeepSightWebContext(new DeepSightWebContext(userAuthInfo));
        CustomerDiffusionTaskCreateRequest request = buildValidRequest();
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TENANT_ID)))
                .thenReturn(buildCustomerGroups(request));
        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(request.getSeedGroup()))).thenReturn(100L);
        when(customerGroupService.getUserCount(eq(TENANT_ID), anySet())).thenReturn(1000L);
        when(customerDiffusionTaskMapper.countByExample(any())).thenReturn(0L);
        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(taskInfoService.createTask(any(), any(), any(), any())).thenReturn(1L);
        when(customerDiffusionTaskMapper.insertSelective(any())).thenReturn(1);
        CustomerDiffusionTaskCreateRes result = groupDiffusionService.createTask(request);
        assertNotNull(result);
        // 特征筛选-系统推荐
        request.setFeatureSelect(FeatureSelectEnum.SYSTEM_RECOMMEND);
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TENANT_ID)))
                .thenReturn(buildCustomerGroups(request));
        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(request.getSeedGroup()))).thenReturn(100L);
        when(customerGroupService.getUserCount(eq(TENANT_ID), anySet())).thenReturn(1000L);
        when(customerDiffusionTaskMapper.countByExample(any())).thenReturn(0L);
        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(taskInfoService.createTask(any(), any(), any(), any())).thenReturn(1L);
        when(customerDiffusionTaskMapper.insertSelective(any())).thenReturn(1);
        CustomerDiffusionTaskCreateRes result2 = groupDiffusionService.createTask(request);
        assertNotNull(result2);
    }

    @Test
    void createTaskFail() {
        CustomerDiffusionTaskCreateRequest request = buildValidRequest();
        ReflectionTestUtils.setField(groupDiffusionService, "seedProportion", 0.5);
        ReflectionTestUtils.setField(groupDiffusionService, "dorisService", dorisService);
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(1L);
        WebContextHolder.setDeepSightWebContext(new DeepSightWebContext(userAuthInfo));
        // 覆盖率异常
        request.setThreshold(0F);
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TENANT_ID)))
                .thenReturn(buildCustomerGroups(request));
        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(request.getSeedGroup()))).thenReturn(100L);
        when(customerGroupService.getUserCount(eq(TENANT_ID), anySet())).thenReturn(1000L);
        when(customerDiffusionTaskMapper.countByExample(any())).thenReturn(0L);
        when(taskInfoService.createTask(any(), any(), any(), any())).thenReturn(1L);
        when(customerDiffusionTaskMapper.insertSelective(any())).thenReturn(1);
        DeepSightException.ParamsErrorException coverError = assertThrows(DeepSightException.ParamsErrorException.class,
                () -> groupDiffusionService.createTask(request));
        assertEquals("覆盖率不能为空且必须大于0", coverError.getMessage());

        request.setThreshold(null);
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TENANT_ID)))
                .thenReturn(buildCustomerGroups(request));
        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(request.getSeedGroup()))).thenReturn(100L);
        when(customerGroupService.getUserCount(eq(TENANT_ID), anySet())).thenReturn(1000L);
        when(customerDiffusionTaskMapper.countByExample(any())).thenReturn(0L);
        when(taskInfoService.createTask(any(), any(), any(), any())).thenReturn(1L);
        when(customerDiffusionTaskMapper.insertSelective(any())).thenReturn(1);
        DeepSightException.ParamsErrorException coverError2 = assertThrows(DeepSightException.ParamsErrorException.class,
                () -> groupDiffusionService.createTask(request));
        assertEquals("覆盖率不能为空且必须大于0", coverError2.getMessage());

        // 人群数量校验
        request.setThreshold(1F);
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TENANT_ID)))
                .thenReturn(buildCustomerGroups(request));
        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(request.getSeedGroup()))).thenReturn(0L);
        when(customerGroupService.getUserCount(eq(TENANT_ID), anySet())).thenReturn(1000L);
        when(customerDiffusionTaskMapper.countByExample(any())).thenReturn(0L);
        when(taskInfoService.createTask(any(), any(), any(), any())).thenReturn(1L);
        when(customerDiffusionTaskMapper.insertSelective(any())).thenReturn(1);
        DeepSightException.ParamsErrorException countError = assertThrows(DeepSightException.ParamsErrorException.class,
                () -> groupDiffusionService.createTask(request));
        assertEquals("种子人群数不能为0", countError.getMessage());

        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TENANT_ID)))
                .thenReturn(buildCustomerGroups(request));
        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(request.getSeedGroup()))).thenReturn(10L);
        when(customerGroupService.getUserCount(eq(TENANT_ID), anySet())).thenReturn(0L);
        when(customerDiffusionTaskMapper.countByExample(any())).thenReturn(0L);
        when(taskInfoService.createTask(any(), any(), any(), any())).thenReturn(1L);
        when(customerDiffusionTaskMapper.insertSelective(any())).thenReturn(1);
        DeepSightException.ParamsErrorException countError2 = assertThrows(DeepSightException.ParamsErrorException.class,
                () -> groupDiffusionService.createTask(request));
        assertEquals("预测总人数不能为0", countError2.getMessage());

        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TENANT_ID)))
                .thenReturn(buildCustomerGroups(request));
        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(request.getSeedGroup()))).thenReturn(10L);
        when(customerGroupService.getUserCount(eq(TENANT_ID), anySet())).thenReturn(19L);
        when(customerDiffusionTaskMapper.countByExample(any())).thenReturn(0L);
        when(taskInfoService.createTask(any(), any(), any(), any())).thenReturn(1L);
        when(customerDiffusionTaskMapper.insertSelective(any())).thenReturn(1);
        DeepSightException.ParamsErrorException countError3 = assertThrows(DeepSightException.ParamsErrorException.class,
                () -> groupDiffusionService.createTask(request));
        assertEquals("种子人群人数占全部人群人数比例需要小于50%", countError3.getMessage());

        // 种子人群覆盖为0
        request.setThreshold(1F);
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TENANT_ID)))
                .thenReturn(buildCustomerGroups(request));
        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(request.getSeedGroup()))).thenReturn(100L);
        when(customerGroupService.getUserCount(eq(TENANT_ID), anySet())).thenReturn(1000L);
        when(customerDiffusionTaskMapper.countByExample(any())).thenReturn(0L);
        when(dorisService.getCount("""
                SELECT COUNT(*) FROM (
                    SELECT oneId,
                        () AS non_null_count
                    FROM mock_user_1
                    WHERE `process_customer_1` = '1'
                    order by deepsight_datetime
                ) AS subquery
                WHERE oneId != '' AND subquery.non_null_count >= 0""")).thenReturn(0L);
        when(taskInfoService.createTask(any(), any(), any(), any())).thenReturn(1L);
        when(customerDiffusionTaskMapper.insertSelective(any())).thenReturn(1);
        DeepSightException.ParamsErrorException seedCoverError = assertThrows(DeepSightException.ParamsErrorException.class,
                () -> groupDiffusionService.createTask(request));
        assertEquals("种子人群特征覆盖率不满足要求", seedCoverError.getMessage());

        // 预测人群覆盖为0
        request.setThreshold(1F);
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TENANT_ID)))
                .thenReturn(buildCustomerGroups(request));
        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(request.getSeedGroup()))).thenReturn(100L);
        when(customerGroupService.getUserCount(eq(TENANT_ID), anySet())).thenReturn(1000L);
        when(customerDiffusionTaskMapper.countByExample(any())).thenReturn(0L);
        when(dorisService.getCount("""
                SELECT COUNT(*) FROM (
                    SELECT oneId,
                        () AS non_null_count
                    FROM mock_user_1
                    WHERE `process_customer_1` = '1'
                    order by deepsight_datetime
                ) AS subquery
                WHERE oneId != '' AND subquery.non_null_count >= 0""")).thenReturn(1L);
        when(dorisService.getCount("""
                SELECT COUNT(*) FROM (
                    SELECT oneId,
                        () AS non_null_count
                    FROM mock_user_1
                    WHERE `process_customer_2` = '1' OR `process_customer_3` = '1'
                    order by deepsight_datetime
                ) AS subquery
                WHERE oneId != '' AND subquery.non_null_count >= 0""")).thenReturn(0L);
        when(taskInfoService.createTask(any(), any(), any(), any())).thenReturn(1L);
        when(customerDiffusionTaskMapper.insertSelective(any())).thenReturn(1);
        DeepSightException.ParamsErrorException prdictCoverError = assertThrows(DeepSightException.ParamsErrorException.class,
                () -> groupDiffusionService.createTask(request));
        assertEquals("预测人群特征覆盖率不满足要求", prdictCoverError.getMessage());

    }

    @Test
    void createCronTaskSuccess() {
        ReflectionTestUtils.setField(groupDiffusionService, "seedProportion", 0.5);
        ReflectionTestUtils.setField(groupDiffusionService, "dorisService", dorisService);
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(1L);
        WebContextHolder.setDeepSightWebContext(new DeepSightWebContext(userAuthInfo));
        CustomerDiffusionTaskCreateRequest request = buildValidCronRequest();
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TENANT_ID)))
                .thenReturn(buildCustomerGroups(request));
        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(request.getSeedGroup()))).thenReturn(100L);
        when(customerGroupService.getUserCount(eq(TENANT_ID), anySet())).thenReturn(1000L);
        when(customerDiffusionTaskMapper.countByExample(any())).thenReturn(0L);
        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(dorisService.getCount(anyString())).thenReturn(1L);
        when(taskInfoService.createTask(any(), any(), any(), any())).thenReturn(1L);
        when(customerDiffusionTaskMapper.insertSelective(any())).thenReturn(1);

        CustomerDiffusionTaskCreateRes result = groupDiffusionService.createTask(request);

        assertNotNull(result);
    }

    private List<CustomerGroup> buildCustomerGroups(CustomerDiffusionTaskCreateRequest request) {
        List<CustomerGroup> groups = new ArrayList<>();
        CustomerGroup seedGroup = new CustomerGroup();
        seedGroup.setId(request.getSeedGroup());
        seedGroup.setCustomerGroupName("seed");
        groups.add(seedGroup);
        request.getPredictGroup().forEach(id -> {
            CustomerGroup customerGroup = new CustomerGroup();
            customerGroup.setId(id);
            customerGroup.setCustomerGroupName("predict");
            groups.add(customerGroup);
        });
        return groups;
    }

    @Test
    void retryTaskWhenTaskNotFailed() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(USER_ID);
        }
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        CustomerDiffusionTaskRetryRequest request = new CustomerDiffusionTaskRetryRequest();
        request.setId(1L);

        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setTriggerMod(TriggerModeEnum.MANUAL.getCode());
        task.setCalStatus(TaskExecStatusEnum.SUCCESS.getCode());

        when(customerDiffusionTaskMapper.selectByExample(any()))
                .thenReturn(Collections.emptyList());

        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> groupDiffusionService.retryTask(request, USER_ID, TENANT_ID, USER_NAME));
    }

    @Test
    void retryTaskSuccess1() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(USER_ID);
        }
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        CustomerDiffusionTaskRetryRequest request = new CustomerDiffusionTaskRetryRequest();
        request.setId(1L);

        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setTriggerMod(TriggerModeEnum.MANUAL.getCode());
        task.setCalStatus(TaskExecStatusEnum.FAILED.getCode());

        when(customerDiffusionTaskMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(task));
        when(customerDiffusionTaskMapper.updateByPrimaryKeySelective(any())).thenReturn(1);

        groupDiffusionService.retryTask(request, USER_ID, TENANT_ID, USER_NAME);

        verify(customerDiffusionTaskMapper).updateByPrimaryKeySelective(any());
    }

    @Test
    void retryTaskWhenLockFailed() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(USER_ID);
        }
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        CustomerDiffusionTaskRetryRequest request = new CustomerDiffusionTaskRetryRequest();
        request.setId(1L);

        when(lock.tryLock()).thenReturn(false);

        assertThrows(DeepSightException.BusyRequestException.class,
                () -> groupDiffusionService.retryTask(request, USER_ID, TENANT_ID, USER_NAME));
    }

    @Test
    void retryTaskWhenTaskNotFound() {
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(USER_ID);
        }
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        CustomerDiffusionTaskRetryRequest request = new CustomerDiffusionTaskRetryRequest();
        request.setId(1L);

        when(customerDiffusionTaskMapper.selectByExample(any()))
                .thenReturn(Collections.emptyList());

        assertThrows(DeepSightException.ParamsErrorException.class,
                () -> groupDiffusionService.retryTask(request, USER_ID, TENANT_ID, USER_NAME));
    }

    @Test
    void getByIdAndTenantId_shouldReturnTaskWhenExists() {
        // Arrange
        Long taskId = 1L;
        String tenantId = "tenant1";
        CustomerDiffusionTask task = buildCustomerDiffusionTask();

        CustomerDiffusionTaskCriteria criteria = new CustomerDiffusionTaskCriteria();
        when(customerDiffusionTaskMapper.selectByExample(any(CustomerDiffusionTaskCriteria.class)))
                .thenReturn(List.of(task));

        // Act
        CustomerDiffusionTaskRes result = groupDiffusionService.getByIdAndTenantId(taskId, tenantId);

        // Assert
        assertNotNull(result);
        assertEquals(taskId, result.getId());
        verify(customerDiffusionTaskMapper).selectByExample(any(CustomerDiffusionTaskCriteria.class));
    }

    @Test
    void getByIdAndTenantId_shouldReturnNullWhenNotExists() {
        // Arrange
        Long taskId = 1L;
        String tenantId = "tenant1";

        when(customerDiffusionTaskMapper.selectByExample(any(CustomerDiffusionTaskCriteria.class)))
                .thenReturn(List.of());

        // Act
        CustomerDiffusionTaskRes result = groupDiffusionService.getByIdAndTenantId(taskId, tenantId);

        // Assert
        assertNull(result);
        verify(customerDiffusionTaskMapper).selectByExample(any(CustomerDiffusionTaskCriteria.class));
    }

    public CustomerDiffusionTask buildCustomerDiffusionTask() {
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setId(1L);
        task.setTenantId("1");
        task.setPredictGroup("11");
        task.setTriggerFrequencyValue(JSONUtil.toJsonStr(new TriggerFrequencyValue()));
        return task;
    }

    @Test
    void execByManual_TaskNotExist() {
        Long taskId = 1L;
        CustomerDiffusionTaskRetryRequest request = new CustomerDiffusionTaskRetryRequest();
        request.setId(taskId);

        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerDiffusionTaskMapper.selectByExample(any())).thenReturn(List.of());

        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(() -> WebContextHolder.getTenantId()).thenReturn(TENANT_ID);

            DeepSightException.ParamsErrorException exception = assertThrows(
                    DeepSightException.ParamsErrorException.class,
                    () -> groupDiffusionService.execByManual(request, USER_ID, TENANT_ID, USER_NAME)
            );
            assertEquals("任务不存在", exception.getMessage());
        }
    }

    @Test
    void execByManual_TaskRunning() {
        Long taskId = 1L;
        CustomerDiffusionTaskRetryRequest request = new CustomerDiffusionTaskRetryRequest();
        request.setId(taskId);
        CustomerDiffusionTask task = buildCustomerDiffusionTask();
        task.setTaskId(taskId);
        task.setCalStatus(TaskExecStatusEnum.RUNNING.getCode());
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerDiffusionTaskMapper.selectByExample(any()))
                .thenReturn(List.of(task));

        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(() -> WebContextHolder.getTenantId()).thenReturn(TENANT_ID);

            DeepSightException.ParamsErrorException exception = assertThrows(
                    DeepSightException.ParamsErrorException.class,
                    () -> groupDiffusionService.execByManual(request, USER_ID, TENANT_ID, USER_NAME)
            );
            assertEquals("任务计算中无法执行", exception.getMessage());
        }
    }

    @Test
    void execByManual_LockFailed() {
        Long taskId = 1L;
        CustomerDiffusionTaskRetryRequest request = new CustomerDiffusionTaskRetryRequest();
        request.setId(taskId);

        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(false);

        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(() -> WebContextHolder.getTenantId()).thenReturn(TENANT_ID);

            assertThrows(
                    DeepSightException.BusyRequestException.class,
                    () -> groupDiffusionService.execByManual(request, USER_ID, TENANT_ID, USER_NAME)
            );
        }
    }

    @Test
    void execByManual_Success() {
        // Prepare test data
        Long taskId = 1L;
        CustomerDiffusionTaskRetryRequest request = new CustomerDiffusionTaskRetryRequest();
        request.setId(taskId);

        CustomerDiffusionTaskRes taskRes = new CustomerDiffusionTaskRes();
        taskRes.setId(taskId);
        taskRes.setCalStatus(TaskExecStatusEnum.PENDING);

        CustomerDiffusionTask task1 = buildCustomerDiffusionTask();
        task1.setTaskId(taskId);


        // Mock dependencies
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(customerDiffusionTaskMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        doNothing().when(groupDiffusionCalculateService).execGroupDiffusion(eq(taskId), eq(USER_ID));

        // Mock getByIdAndTenantId to return our test task
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(() -> WebContextHolder.getTenantId()).thenReturn(TENANT_ID);
            mocked.when(() -> WebContextHolder.getUserId()).thenReturn(USER_ID);

            when(customerDiffusionTaskMapper.selectByExample(any()))
                    .thenReturn(List.of(task1));
            // Execute method
            groupDiffusionService.execByManual(request, USER_ID, TENANT_ID, USER_NAME);
            // Verify interactions
            verify(lock).tryLock();
            verify(customerDiffusionTaskMapper).updateByPrimaryKeySelective(argThat(task ->
                    task.getId().equals(taskId) &&
                            task.getModifier().equals(USER_ID) &&
                            task.getCalStatus().equals(TaskExecStatusEnum.RUNNING.getCode())
            ));
        }
    }

    @Test
    void getTaskDetail_ShouldIncludeGroupCounts_WhenNeedCountIsTrue() {
        Long taskId = 1L;
        Long seedGroupId = 10L;
        Long predictGroupId1 = 20L;
        Long predictGroupId2 = 30L;

        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setId(taskId);
        task.setTenantId(TENANT_ID);
        task.setDel(false);
        task.setSeedGroup(seedGroupId);
        task.setPredictGroup(StrUtil.join(",", predictGroupId1, predictGroupId2));

        when(customerDiffusionTaskMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(task));

        CustomerGroup seedGroup = new CustomerGroup();
        seedGroup.setId(seedGroupId);
        seedGroup.setCustomerGroupName("seed");

        CustomerGroup predictGroup1 = new CustomerGroup();
        predictGroup1.setId(predictGroupId1);
        predictGroup1.setCustomerGroupName("predict1");

        CustomerGroup predictGroup2 = new CustomerGroup();
        predictGroup2.setId(predictGroupId2);
        predictGroup2.setCustomerGroupName("predict2");

        when(customerGroupService.retrieveCustomerGroupWithIds(anyList(), eq(TENANT_ID)))
                .thenReturn(Arrays.asList(seedGroup, predictGroup1, predictGroup2));

        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(seedGroupId)))
                .thenReturn(100L);
        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(predictGroupId1)))
                .thenReturn(200L);
        when(customerGroupService.getUserCount(eq(TENANT_ID), eq(predictGroupId2)))
                .thenReturn(300L);

        CustomerDiffusionTaskRes result = groupDiffusionService.getTaskDetail(taskId, TENANT_ID, true);

        assertNotNull(result);
        assertEquals(taskId, result.getId());
        assertNotNull(result.getSeedGroupDetail());
        assertEquals(100L, result.getSeedGroupDetail().getCount());
        assertEquals(2, result.getPredictGroupDetail().size());
        assertEquals(200L, result.getPredictGroupDetail().get(0).getCount());
        assertEquals(300L, result.getPredictGroupDetail().get(1).getCount());
    }

    @Test
    void getTaskDetail_ShouldSetGroupPackage_WhenCustomerGroupExists() {
        Long taskId = 1L;
        Long customerGroupId = 100L;

        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setId(taskId);
        task.setTenantId(TENANT_ID);
        task.setDel(false);
        task.setPredictGroup("111");
        task.setCustomerGroupId(customerGroupId);

        when(customerDiffusionTaskMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(task));
        when(customerGroupService.getByIdAndTenantId(customerGroupId, TENANT_ID))
                .thenReturn(new CustomerGroup());

        CustomerDiffusionTaskRes result = groupDiffusionService.getTaskDetail(taskId, TENANT_ID, false);

        assertNotNull(result);
        assertTrue(result.getGroupPackage());
    }

    @Test
    void getTaskDetail_ShouldNotSetGroupPackage_WhenCustomerGroupNotExists() {
        Long taskId = 1L;
        Long customerGroupId = 100L;

        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setId(taskId);
        task.setTenantId(TENANT_ID);
        task.setDel(false);
        task.setPredictGroup("111");
        task.setCustomerGroupId(customerGroupId);

        when(customerDiffusionTaskMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(task));
        when(customerGroupService.getByIdAndTenantId(customerGroupId, TENANT_ID))
                .thenReturn(null);

        CustomerDiffusionTaskRes result = groupDiffusionService.getTaskDetail(taskId, TENANT_ID, false);

        assertNotNull(result);
        assertFalse(result.getGroupPackage());
    }

    @Test
    void getTaskDetail_ShouldReturnNull_WhenTaskNotExist() {
        Long taskId = 1L;
        when(customerDiffusionTaskMapper.selectByExample(any()))
                .thenReturn(Collections.emptyList());

        CustomerDiffusionTaskRes result = groupDiffusionService.getTaskDetail(taskId, TENANT_ID, false);

        assertNull(result);
    }

    @Test
    void getTaskDetail_ShouldReturnBasicTaskInfo_WhenNeedCountIsFalse() {
        Long taskId = 1L;
        CustomerDiffusionTask task = new CustomerDiffusionTask();
        task.setId(taskId);
        task.setTenantId(TENANT_ID);
        task.setDel(false);
        task.setPredictGroup("111");
        when(customerDiffusionTaskMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(task));

        CustomerDiffusionTaskRes result = groupDiffusionService.getTaskDetail(taskId, TENANT_ID, false);

        assertNotNull(result);
        assertEquals(taskId, result.getId());
        verify(customerGroupService, never()).retrieveCustomerGroupWithIds(any(), any());
        verify(customerGroupService, never()).getUserCount(any(), anyLong());
    }
}