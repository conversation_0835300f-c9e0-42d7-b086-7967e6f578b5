package com.baidu.keyue.deepsight.service.datamanage.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobRobotVersion;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobRobotVersionCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.AiobRobotVersionMapper;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@ExtendWith(MockitoExtension.class)
public class AiobRobotVersionServiceImplTest{


    private final String publishTimeStr = "2024-03-20 10:03:28";

    private final Long publishTime = 1747742608000L;
    @Mock
    private AiobRobotVersionMapper aiobRobotVersionMapper;

    @InjectMocks
    private AiobRobotVersionServiceImpl aiobRobotVersionService;

    private final String tenantId = "testTenant";

    private final String robotId = "testRobot";

    private final String botVersionId = "testVersion";

    private final String botVersionName = "testVersionName";

    @Test
    void getAiobRobotVersionShouldReturnTrueWhenVersionExists() {
        // Arrange
        when(aiobRobotVersionMapper.selectByExample(any(AiobRobotVersionCriteria.class)))
                .thenReturn(List.of(new AiobRobotVersion()));
    
        // Act
        boolean result = aiobRobotVersionService.getAiobRobotVersion(tenantId, robotId, botVersionId);
    
        // Assert
        assertTrue(result);
        verify(aiobRobotVersionMapper).selectByExample(any(AiobRobotVersionCriteria.class));
    }

    @Test
    void getAiobRobotVersionShouldReturnFalseWhenVersionNotExists() {
        // Arrange
        when(aiobRobotVersionMapper.selectByExample(any(AiobRobotVersionCriteria.class)))
                .thenReturn(Collections.emptyList());
    
        // Act
        boolean result = aiobRobotVersionService.getAiobRobotVersion(tenantId, robotId, botVersionId);
    
        // Assert
        assertFalse(result);
        verify(aiobRobotVersionMapper).selectByExample(any(AiobRobotVersionCriteria.class));
    }

    @Test
    void saveAiobRobotVersionShouldInsertRecordWithCorrectParameters() {
        try (var mockedDatetimeUtils = mockStatic(DatetimeUtils.class)) {
            // Arrange
            DateTime expectedDate = DateUtil.date(publishTime);
            mockedDatetimeUtils.when(() -> DatetimeUtils.fromTimestamp(publishTime))
                    .thenReturn(expectedDate);
            
            // Act
            aiobRobotVersionService.saveAiobRobotVersion(tenantId, robotId, botVersionId, botVersionName, publishTime);
            
            // Assert
            verify(aiobRobotVersionMapper).insert(argThat(version -> {
                assertEquals(tenantId, version.getTenantId());
                assertEquals(robotId, version.getRobotId());
                assertEquals(botVersionId, version.getRobotVersion());
                assertEquals(botVersionName, version.getRobotVersionName());
                assertEquals(expectedDate, version.getCreateTime());
                return true;
            }));
            mockedDatetimeUtils.verify(() -> 
                DatetimeUtils.fromTimestamp(publishTime));
        }
    }

    @Test
    void getAiobRobotVersionListShouldReturnEmptyListWhenNoVersionsExist() {
        // Arrange
        when(aiobRobotVersionMapper.selectByExample(any(AiobRobotVersionCriteria.class)))
                .thenReturn(List.of());
    
        // Act
        List<SopWholeRobotVersionResponse> result = 
            aiobRobotVersionService.getAiobRobotVersionList(tenantId, robotId);
    
        // Assert
        assertTrue(result.isEmpty());
        verify(aiobRobotVersionMapper).selectByExample(any(AiobRobotVersionCriteria.class));
    }

    @Test
    void getAiobRobotVersionListShouldReturnVersionListWhenVersionsExist() {
        // Arrange
        AiobRobotVersion version = new AiobRobotVersion();
        version.setRobotVersion(botVersionId);
        version.setRobotVersionName(botVersionName);
        version.setCreateTime(new Date());
    
        when(aiobRobotVersionMapper.selectByExample(any(AiobRobotVersionCriteria.class)))
                .thenReturn(List.of(version));
    
        // Act
        List<SopWholeRobotVersionResponse> result = 
            aiobRobotVersionService.getAiobRobotVersionList(tenantId, robotId);
    
        // Assert
        assertEquals(1, result.size());
        SopWholeRobotVersionResponse response = result.get(0);
        assertEquals(botVersionId, response.getRobotVersionId());
        assertEquals(botVersionName, response.getRobotVersionName());
        verify(aiobRobotVersionMapper).selectByExample(any(AiobRobotVersionCriteria.class));
    }

    @Test
    void saveDiagramRobotVersionShouldInsertNewRecord() {
        try (MockedStatic<DatetimeUtils> mockedDatetimeUtils = mockStatic(DatetimeUtils.class)) {
            // Arrange
            Date expectedDate = DateUtil.parse(publishTimeStr, DatetimeUtils.DATE_TIME_FORMATTER);
            mockedDatetimeUtils.when(() -> DatetimeUtils.fromDatetimeStr(eq(publishTimeStr), 
                    eq(DatetimeUtils.DATE_TIME_FORMATTER)))
                    .thenReturn(expectedDate);
            
            // Act
            aiobRobotVersionService.saveDiagramRobotVersion(tenantId, robotId, botVersionId, botVersionName, publishTimeStr);
            
            // Assert
            verify(aiobRobotVersionMapper).insert(argThat(version -> 
                version.getTenantId().equals(tenantId) &&
                version.getRobotId().equals(robotId) &&
                version.getRobotVersion().equals(botVersionId) &&
                version.getRobotVersionName().equals(botVersionName) &&
                version.getCreateTime().equals(expectedDate)
            ));
            mockedDatetimeUtils.verify(() -> 
                DatetimeUtils.fromDatetimeStr(eq(publishTimeStr), eq(DatetimeUtils.DATE_TIME_FORMATTER))
            );
        }
    }

}