package com.baidu.keyue.deepsight.service.dataprediction.impl;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baidu.keyue.deepsight.models.bsc.basic.BscKafkaConfig;

import com.baidu.keyue.deepsight.config.DataPredictCalculateConfiguration;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.PredictTypeEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.dataprediction.DataPredictionService;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.anyString;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.kafka.core.KafkaTemplate;


@ExtendWith(MockitoExtension.class)
public class DataPredictionCalServiceImplTest {

    @Mock
    private KafkaTemplate<String, String> kafkaTemplate;

    @Mock
    private DataPredictCalculateConfiguration dataPredictCalculateConfiguration;

    private DataTableInfo userTableDetail;

    private List<DataPredictionConfig> onPredictConfig;

    @Mock
    private DataTableManageService dataTableManageService;

    @Mock
    private DataPredictionService dataPredictionService;

    @Mock
    private DorisService dorisService;

    @InjectMocks
    private DataPredictionCalServiceImpl dataPredictionCalService;

    private final String tenantId = "123456";

    private final String userTableName = "mock_user_123456";

    private List<Map<String, Object>> recordList;
    private Map<String, String> fieldEncryptInfo;
    private BscKafkaConfig kafkaConfig;

    @Mock
    private TableRecordCommonService tableRecordCommonService;

    @BeforeEach
    void setUp() {
        userTableDetail = new DataTableInfo();
        userTableDetail.setId(1L);
        userTableDetail.setTenantid("123456");

        DataPredictionConfig config1 = new DataPredictionConfig();
        config1.setPredictionType(PredictTypeEnum.AGE_GROUP.getCode());
        DataPredictionConfig config2 = new DataPredictionConfig();
        config2.setPredictionType(PredictTypeEnum.INTERESTS.getCode());
        onPredictConfig = Arrays.asList(config1, config2);

        Map<String, Object> record1 = new HashMap<>();
        record1.put("oneId", "oneId1");
        record1.put("mobile", "D491C87C1CB19A0B492B9B9E350461E9");
        Map<String, Object> record2 = new HashMap<>();
        record2.put("oneId", "oneId2");
        record2.put("mobile", "D491C87C1CB19A0B492B9B9E350461E9");
        recordList = Arrays.asList(record1, record2);

        fieldEncryptInfo = new HashMap<>();
        fieldEncryptInfo.put("mobile", "ac1pppC3c2JdZD86");

        kafkaConfig = new BscKafkaConfig();
        kafkaConfig.setKafkaTopic("topic");
    }

    @Test
    void makeUpPredictByTenantWhenOnPredictConfigIsEmptyShouldDoNothing() {
        when(dataTableManageService.getTableDetailWithTableName(userTableName)).thenReturn(userTableDetail);
        when(dataPredictionService.getOnPredictConfig(tenantId)).thenReturn(Collections.emptyList());

        dataPredictionCalService.makeUpPredictByTenant(tenantId);

        verify(dataTableManageService).getTableDetailWithTableName(userTableName);
        verify(dataPredictionService).getOnPredictConfig(tenantId);
        verifyNoMoreInteractions(dorisService, kafkaTemplate);
    }

    @Test
    void makeUpPredictByTenantWhenDorisServiceThrowsExceptionShouldDoNothing() {
        when(dataTableManageService.getTableDetailWithTableName(userTableName)).thenReturn(userTableDetail);
        when(dataPredictionService.getOnPredictConfig(tenantId)).thenReturn(onPredictConfig);
        when(tableRecordCommonService.getEncryptFields(userTableDetail.getId())).thenReturn(Collections.emptyMap());
        when(dorisService.selectList(anyString())).thenThrow(new RuntimeException("Mock DB error"));

        dataPredictionCalService.makeUpPredictByTenant(tenantId);

        verify(dorisService).selectList(anyString());
        verifyNoInteractions(kafkaTemplate);
    }

    @Test
    void makeUpPredictByTenantWhenAllConditionsMetShouldSendKafkaMessages() {
        when(dataTableManageService.getTableDetailWithTableName(userTableName)).thenReturn(userTableDetail);
        when(dataPredictionService.getOnPredictConfig(tenantId)).thenReturn(onPredictConfig);
        when(tableRecordCommonService.getEncryptFields(userTableDetail.getId())).thenReturn(Collections.emptyMap());
        when(dorisService.selectList(anyString())).thenReturn(recordList);
        when(dataPredictCalculateConfiguration.getModelUrl()).thenReturn("modelUrl");
        when(dataPredictCalculateConfiguration.getKafkaConfig()).thenReturn(kafkaConfig);
//        when(MobileProcessUtils.decryptMobile(anyString(), anyMap())).thenReturn("decryptedMobile");

        dataPredictionCalService.makeUpPredictByTenant(tenantId);

        verify(kafkaTemplate, times(2)).send(anyString(), anyString());
    }

    @Test
    void makeUpPredictByTenantWhenUserTableIsNullShouldDoNothing() {
        when(dataTableManageService.getTableDetailWithTableName(userTableName)).thenReturn(null);

        dataPredictionCalService.makeUpPredictByTenant(tenantId);

        verify(dataTableManageService).getTableDetailWithTableName(userTableName);
        verifyNoMoreInteractions(dataPredictionService, dorisService, kafkaTemplate);
    }

}