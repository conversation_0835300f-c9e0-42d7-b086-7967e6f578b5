package com.baidu.keyue.deepsight.service.impl;

import com.baidu.keyue.deepsight.BaseTest;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableContentListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableFieldRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.TableFieldDetailResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.kybase.commons.utils.JsonUtil;
import org.junit.Test;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

public class DataTableManageServiceCustomerTest extends BaseTest {
    @Autowired
    private DataTableManageService dataTableManageService;

    @Test
    public void getTableFieldList() {
        MDC.put(Constants.REQUEST_TENANT_ID_FIELD, "test_122865401");
        GetTableFieldRequest request = new GetTableFieldRequest();
        request.setDataTableId(1806L);
        BasePageResponse.Page<TableFieldDetailResponse> includeBaidu = dataTableManageService.getTableFieldList(request, true);
        System.out.println(JsonUtil.toJson(includeBaidu));
        BasePageResponse.Page<TableFieldDetailResponse> excludeBaidu = dataTableManageService.getTableFieldList(request, false);
        System.out.println(JsonUtil.toJson(excludeBaidu));
    }

    @Test
    public void getTableContentFilterProperties() {
        MDC.put(Constants.REQUEST_TENANT_ID_FIELD, "test_122865401");
        List<DatasetPropertiesResult> includeBaidu = dataTableManageService.getTableContentFilterProperties(1806L, true);
        System.out.println(JsonUtil.toJson(includeBaidu));
        List<DatasetPropertiesResult> excludeBaidu = dataTableManageService.getTableContentFilterProperties(1806L, false);
        System.out.println(JsonUtil.toJson(excludeBaidu));
    }

    @Test
    public void getVisibleFields() {
        MDC.put(Constants.REQUEST_TENANT_ID_FIELD, "test_122865401");
        List<VisibleFieldResponse> includeBaidu = dataTableManageService.getVisibleFields(1806L, true);
        System.out.println(JsonUtil.toJson(includeBaidu));
        List<VisibleFieldResponse> excludeBaidu = dataTableManageService.getVisibleFields(1806L, false);
        System.out.println(JsonUtil.toJson(excludeBaidu));
    }

    @Test
    public void getTableContent() {
        MDC.put(Constants.REQUEST_TENANT_ID_FIELD, "test_122865401");
        GetTableContentListRequest request = new GetTableContentListRequest();
        request.setDataTableId(1806L);
        try {
            BasePageResponse.Page<Map<String, String>> includeBaidu = dataTableManageService.getTableContent(request, true);
            System.out.println(JsonUtil.toJson(includeBaidu));
            BasePageResponse.Page<Map<String, String>> excludeBaidu = dataTableManageService.getTableContent(request, false);
            System.out.println(JsonUtil.toJson(excludeBaidu));
        } catch (Exception e) {
        }
    }
}
