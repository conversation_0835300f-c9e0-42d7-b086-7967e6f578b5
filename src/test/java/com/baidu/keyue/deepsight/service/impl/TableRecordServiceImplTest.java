package com.baidu.keyue.deepsight.service.impl;

import cn.hutool.json.JSONUtil;
import com.baidu.keyue.deepsight.BaseTest;
import com.baidu.keyue.deepsight.models.datamanage.dto.FileDetailDto;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.FileImportTaskResponse;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordServiceImpl;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName TableRecordServiceImplTest
 * @Description 
 * <AUTHOR>
 * @Date 2025/2/25 10:16 AM
 */
@Slf4j
public class TableRecordServiceImplTest extends BaseTest {
    @Resource
    private TableRecordServiceImpl tableRecordService;
    
    @Test
    public void testCheckData(){
        List<FileDetailDto> files = new ArrayList<>();
        FileDetailDto detailDto = new FileDetailDto("es.xlsx", "/test/20250320/c4392cd907fd4436bac92df17d9195bb.xlsx");
        files.add(detailDto);
        Long dataTableId = 2094L; 
        String groupId = "252b3f98e90b472fad0afc62ee8a0f65";
        String tenantId = "39120146944873472";
        tableRecordService.checkFileData(files, dataTableId, groupId, tenantId);
    }
    
    @Test
    public void testImport(){
        FileImportRequest importRequest = JSONUtil.toBean("""
                {
                   "dataTableId": 2106,
                   "importTypeEnum": "INCREMENT",
                   "mappingTypeEnum": "EQUAL_NAME",
                   "groupId": "dcfe560deb3e410c84a0916478c1bfb1",
                   "fieldMappings": [
                     {
                       "sourceEnName": "int_a",
                       "sourceCnName": "物料ID",
                       "sourceType": "boolean",
                       "tagEnName": "int_a",
                       "tagCnName": "物料ID",
                       "tagType": "int"
                     },
                     {
                       "sourceEnName": "tinyint_a",
                       "sourceCnName": "物料类型",
                       "sourceType": "boolean",
                       "tagEnName": "tinyint_a",
                       "tagCnName": "物料类型",
                       "tagType": "tinyint"
                     },
                     {
                       "sourceEnName": "bigint_a",
                       "sourceCnName": "物料总数",
                       "sourceType": "int",
                       "tagEnName": "bigint_a",
                       "tagCnName": "物料总数",
                       "tagType": "bigint"
                     },
                     {
                       "sourceEnName": "varchar_a",
                       "sourceCnName": "物料名称",
                       "sourceType": "string",
                       "tagEnName": "varchar_a",
                       "tagCnName": "物料名称",
                       "tagType": "varchar"
                     },
                     {
                       "tagEnName": "json_a",
                       "tagCnName": "json结构",
                       "tagType": "json",
                       "sourceEnName": "array_a",
                       "sourceCnName": "多值"
                     },
                     {
                       "sourceEnName": "text_a",
                       "sourceCnName": "物料描述",
                       "sourceType": "string",
                       "tagEnName": "text_a",
                       "tagCnName": "文本",
                       "tagType": "text"
                     },
                     {
                       "sourceEnName": "datetime_a",
                       "sourceCnName": "开始日期",
                       "sourceType": "string",
                       "tagEnName": "datetime_a",
                       "tagCnName": "开始时间",
                       "tagType": "datetime"
                     },
                     {
                       "sourceEnName": "datetime_b",
                       "sourceCnName": "结束日期",
                       "sourceType": "string",
                       "tagEnName": "datetime_b",
                       "tagCnName": "结束时间",
                       "tagType": "datetime"
                     },
                     {
                       "sourceEnName": "array_a",
                       "sourceCnName": "多值",
                       "sourceType": "array",
                       "tagEnName": "array_a",
                       "tagCnName": "城市字段",
                       "tagType": "array"
                     },
                     {
                       "sourceEnName": "bool_a",
                       "sourceCnName": "是否是优质内容",
                       "sourceType": "boolean",
                       "tagEnName": "bool_a",
                       "tagCnName": "是否是优质内容",
                       "tagType": "boolean"
                     },
                     {
                       "tagEnName": "oneId",
                       "tagCnName": "唯一ID",
                       "tagType": "varchar"
                     },
                     {
                       "sourceEnName": "mobile",
                       "sourceCnName": "手机号",
                       "sourceType": "string",
                       "tagEnName": "mobile",
                       "tagCnName": "手机号",
                       "tagType": "varchar"
                     },
                     {
                       "tagEnName": "Device_model",
                       "tagCnName": "设备模型",
                       "tagType": "varchar"
                     },
                     {
                       "tagEnName": "wechat_id",
                       "tagCnName": "微信ID",
                       "tagType": "varchar"
                     }
                   ],
                   "fileIds": [
                     232
                   ]
                 }""", FileImportRequest.class);
        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setUserId(8185436775268354L);
        userAuthInfo.setTenantId(39544141016060928L);
        userAuthInfo.setUserName("all_test_op");
        FileImportTaskResponse res = tableRecordService.fileImport(importRequest, userAuthInfo);
        
    }
}
