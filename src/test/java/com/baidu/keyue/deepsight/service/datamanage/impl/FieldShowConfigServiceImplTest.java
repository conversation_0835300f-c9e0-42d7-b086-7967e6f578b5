package com.baidu.keyue.deepsight.service.datamanage.impl;

import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigSaveRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldShowConfigResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldShowConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldShowConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.FieldShowConfigMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class FieldShowConfigServiceImplTest {


    @Mock
    private RLock lock;
    @Mock
    private RedissonClient redissonClient;

    @InjectMocks
    private FieldShowConfigServiceImpl fieldShowConfigService;

    @Mock
    private FieldShowConfigMapper fieldShowConfigMapper;

    @Test
    void getByTableIdAndTenantIdAndPageIdShouldReturnNullWhenNoConfigFound() {
        // Arrange
        Long tableId = 1L;
        String tenantId = "tenant1";
        when(fieldShowConfigMapper.selectByExampleWithBLOBs(any(FieldShowConfigCriteria.class)))
                .thenReturn(Collections.emptyList());

        // Act
        FieldShowConfigResponse result = fieldShowConfigService.getByTableIdAndTenantIdAndPageId(tableId, tenantId);

        // Assert
        assertNull(result);
        verify(fieldShowConfigMapper).selectByExampleWithBLOBs(any(FieldShowConfigCriteria.class));
    }

    @Test
    void getByTableIdAndTenantIdAndPageIdShouldReturnConfigWhenFound() {
        // Arrange
        Long tableId = 1L;
        String tenantId = "tenant1";
        String pageId = "page1";
        FieldShowConfig config = new FieldShowConfig();
        config.setDataTableId(tableId);
        config.setTenantId(tenantId);
        when(fieldShowConfigMapper.selectByExampleWithBLOBs(any(FieldShowConfigCriteria.class)))
                .thenReturn(List.of(config));

        // Act
        FieldShowConfigResponse result = fieldShowConfigService.getByTableIdAndTenantIdAndPageId(tableId, tenantId);

        // Assert
        assertNotNull(result);
        assertEquals(tableId, result.getDataTableId());
        verify(fieldShowConfigMapper).selectByExampleWithBLOBs(any(FieldShowConfigCriteria.class));
    }

    @Test
    void saveOrUpdateByIdShouldInsertWhenNoExistingConfig() throws Exception {
        // Arrange
        FieldShowConfigSaveRequest request = new FieldShowConfigSaveRequest();
        request.setDataTableId(1L);
        request.setShowFields(List.of("field1", "field2"));
        String tenantId = "tenant1";
        String userName = "user1";

        RLock lock = mock(RLock.class);
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(fieldShowConfigMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of());
        
        FieldShowConfig capturedConfig = new FieldShowConfig();
        doAnswer(invocation -> {
            FieldShowConfig config = invocation.getArgument(0);
            config.setId(100L);
            return null;
        }).when(fieldShowConfigMapper).insertSelective(any(FieldShowConfig.class));
        when(lock.isLocked()).thenReturn(true);
        // Act
        FieldShowConfigResponse result = fieldShowConfigService.saveOrUpdateById(request, tenantId, userName);
    
        // Assert
        assertNotNull(result);
        assertEquals(100L, result.getId());
        verify(fieldShowConfigMapper).insertSelective(any(FieldShowConfig.class));
        verify(lock).unlock();
    }

    @Test
    void saveOrUpdateByIdShouldUpdateWhenExistingConfigFound() throws Exception {
        // Arrange
        FieldShowConfigSaveRequest request = new FieldShowConfigSaveRequest();
        request.setDataTableId(1L);
        request.setShowFields(List.of("field1", "field2"));
        String tenantId = "tenant1";
        String userName = "user1";

        RLock lock = mock(RLock.class);
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        FieldShowConfig existingConfig = new FieldShowConfig();
        existingConfig.setId(100L);
        existingConfig.setDataTableId(1L);
        existingConfig.setTenantId(tenantId);
    
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(fieldShowConfigMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(existingConfig));
        when(lock.isLocked()).thenReturn(true);

        // Act
        FieldShowConfigResponse result = fieldShowConfigService.saveOrUpdateById(request, tenantId, userName);
    
        // Assert
        assertNotNull(result);
        verify(fieldShowConfigMapper).updateByPrimaryKeySelective(any(FieldShowConfig.class));
        verify(lock).unlock();
    }

    @Test
    void saveOrUpdateByIdShouldThrowExceptionWhenLockFailed() {
        // Arrange
        FieldShowConfigSaveRequest request = new FieldShowConfigSaveRequest();
        request.setDataTableId(1L);
        String tenantId = "tenant1";
        String userName = "user1";
    
        when(redissonClient.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(false);
    
        // Act & Assert
        assertThrows(DeepSightException.BusinessException.class, () -> {
            fieldShowConfigService.saveOrUpdateById(request, tenantId, userName);
        });
        verify(lock, never()).unlock();
    }


}