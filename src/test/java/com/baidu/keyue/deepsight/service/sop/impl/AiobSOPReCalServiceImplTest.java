package com.baidu.keyue.deepsight.service.sop.impl;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.idmapping.dto.DatasetKafkaMsgDTO;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@TestPropertySource(properties = {"deepSight.kafka.topic.sop=test_topic"})
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
class AiobSOPReCalServiceImplTest {

    @InjectMocks
    private AiobSOPReCalServiceImpl aiobSOPReCalService;

    @Mock
    DorisService dorisService;

    @Mock
    KafkaTemplate<String, String> kafkaTemplate;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void buildKafkaMsgDtoFromSession() {
        String sessionTableName = "aiob_conversation_session_1";
        Map<String, Object> row = new HashMap<>();
        row.put("oneId", "oneId");
        row.put("sessionId", "sessionId");
        assertDoesNotThrow(() -> aiobSOPReCalService.buildKafkaMsgDtoFromSession(sessionTableName, row));
    }

    // testBuildKafkaMsgDtoFromSessionStartTime 用于测试 buildKafkaMsgDtoFromSession
    // generated by Comate
    @Test
    public void testBuildKafkaMsgDtoFromSessionStartTime() {
        // Arrange
        String sessionTableName = "testTable";
        Map<String, Object> session = new HashMap<>();

        // Act
        DatasetKafkaMsgDTO result = aiobSOPReCalService.buildKafkaMsgDtoFromSession(sessionTableName, session);

        // Assert
        assertEquals(sessionTableName, result.getCode());


        session.put("startTime", LocalDateTime.now());
        result = aiobSOPReCalService.buildKafkaMsgDtoFromSession(sessionTableName, session);
        assertEquals(sessionTableName, result.getCode());
    }

    // testBuildKafkaMsgDtoFromSessionCustomTagList 用于测试 buildKafkaMsgDtoFromSession
    // generated by Comate
    @Test
    public void testBuildKafkaMsgDtoFromSessionCustomTagList() {
        String sessionTableName = "testTable";
        Map<String, Object> session = new HashMap<>();
        session.put("customTagList", Arrays.asList("tag1", "tag2"));
        DatasetKafkaMsgDTO result = aiobSOPReCalService.buildKafkaMsgDtoFromSession(sessionTableName, session);
        assertEquals(sessionTableName, result.getCode());
        assertEquals(Arrays.asList("tag1", "tag2"), result.getData().get("customTagList"));
    }

    @Test
    void reRunQuickSOP() {
        assertDoesNotThrow(() -> aiobSOPReCalService.reRunQuickSOP("tenantId", "taskId"));
    }

    @Test
    void clearMetricsShouldDoNothingWhenParamsBlank() {
        assertDoesNotThrow(() -> aiobSOPReCalService.clearMetrics(null, null));
        assertDoesNotThrow(() -> aiobSOPReCalService.clearMetrics("", ""));
        assertDoesNotThrow(() -> aiobSOPReCalService.clearMetrics("tenantId", null));
        assertDoesNotThrow(() -> aiobSOPReCalService.clearMetrics(null, "robotVer"));

        verifyNoInteractions(dorisService);
    }

    @Test
    void clearMetricsShouldExecuteSqlForBothNodeAndEdge() {
        String tenantId = "testTenant";
        String robotVer = "v1.0";
        String nodeTableName = "aiob_sop_node_testTenant";
        String edgeTableName = "aiob_sop_edge_testTenant";
        String nodeSql = "DELETE FROM aiob_sop_node_testTenant WHERE robot_ver = 'v1.0'";
        String edgeSql = "DELETE FROM aiob_sop_edge_testTenant WHERE robot_ver = 'v1.0'";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(nodeTableName);
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(edgeTableName);

            ormUtilsMock.when(() -> ORMUtils.generateClearSOPMetricDataSql(nodeTableName, robotVer))
                    .thenReturn(nodeSql);
            ormUtilsMock.when(() -> ORMUtils.generateClearSOPMetricDataSql(edgeTableName, robotVer))
                    .thenReturn(edgeSql);

            when(dorisService.execSql(nodeSql)).thenReturn(1);
            when(dorisService.execSql(edgeSql)).thenReturn(1);

            assertDoesNotThrow(() -> aiobSOPReCalService.clearMetrics(tenantId, robotVer));

        }
    }

    @Test
    void clearMetricsShouldHandleSqlExecutionException() {
        String tenantId = "testTenant";
        String robotVer = "v1.0";
        String nodeTableName = "aiob_sop_node_testTenant";
        String edgeTableName = "aiob_sop_edge_testTenant";
        String nodeSql = "DELETE FROM aiob_sop_node_testTenant WHERE robot_ver = 'v1.0'";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(nodeTableName);
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(edgeTableName);

            ormUtilsMock.when(() -> ORMUtils.generateClearSOPMetricDataSql(nodeTableName, robotVer))
                    .thenReturn(nodeSql);
            ormUtilsMock.when(() -> ORMUtils.generateClearSOPMetricDataSql(edgeTableName, robotVer))
                    .thenReturn("");

            when(dorisService.execSql(anyString()))
                    .thenThrow(new RuntimeException("SQL execution failed"));

            assertDoesNotThrow(() -> aiobSOPReCalService.clearMetrics(tenantId, robotVer));

        }
    }

}