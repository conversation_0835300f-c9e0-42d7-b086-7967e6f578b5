package com.baidu.keyue.deepsight.service.impl;

import java.util.List;

import com.baidu.keyue.deepsight.BaseTest;
import com.baidu.keyue.deepsight.models.meg.Attribute;
import com.baidu.keyue.deepsight.models.meg.MEGIdEnum;
import com.baidu.keyue.deepsight.models.meg.MEGIds;
import com.baidu.keyue.deepsight.service.meg.MEGService;
import com.baidu.keyue.deepsight.service.user.BaiduUserDataService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class BaiduUserDataServiceTest extends BaseTest {
    @Autowired
    private BaiduUserDataService baiduUserDataService;

    @Autowired
    private MEGService megService;

    private static final String ID_MAPPING_JSON = "{\"idsMap\":{\"CUID\":[\"44A554E40D8463EBA902F9B0F03F2E4FA9CD3CC3\",\"FB292A8B645ADD87F0EBA0E3AD6D809F3298A751COSILKRHMBI\",\"884E68C1DA9E2EBE7836B98091691057FCEED4A61OMQQDCQKMH\"],\"IDFA\":[\"133EB8C0-CC9E-4C2D-A462-8422ED656F70\",\"226C0EAC-129D-4BBC-AF5E-D95341241EE7\"],\"BAIDUID\":[\"FC8CA1ACA5408849B0A5D1F206BDCE1F\",\"F9A0D6C858D66B65BDA8770BFB430AC4\",\"326C44699EB4392BBD897962A44899A7\",\"FACD791431EF952F3538E20A21710B9B\",\"47FD2EF91C7A0D639AC2340B68A37820\"],\"UDWID\":[\"user713182880\"]}}";
    private static final String ATTRIBUTE_LIST_JSON = "[{\"description\":{\"name\":\"性别\"},\"item\":[{\"value\":\"男\",\"weight\":65}]},{\"description\":{\"name\":\"年龄\"},\"item\":[{\"value\":\"25-34\",\"weight\":47}]},{\"description\":{\"name\":\"教育水平\"},\"item\":[{\"value\":\"大专\",\"weight\":54}]},{\"description\":{\"name\":\"职业类别\"}},{\"description\":{\"name\":\"所在行业\"},\"item\":[{\"value\":\"IT通信电子\",\"weight\":6}]},{\"description\":{\"name\":\"人生阶段\"}},{\"description\":{\"name\":\"婚姻状况\"}},{\"description\":{\"name\":\"消费水平\"},\"item\":[{\"value\":\"中\",\"weight\":34}]},{\"description\":{\"name\":\"消费意愿\"}},{\"description\":{\"name\":\"兴趣关注\"}}]";

    @Test
    public void testIdMapping() {
        MEGIds baiduId = megService.getBaiduId("C7E798982D57D39578B7AE1B88FFA5E7");
        System.out.println(JsonUtils.toJsonWithOutException(baiduId));
        Assert.assertNotNull(baiduId);

        baiduId = megService.getBaiduId("15236120087");
        System.out.println(JsonUtils.toJsonWithOutException(baiduId));
        Assert.assertNotNull(baiduId);
    }

    @Test
    public void testUserAttribute() {
        List<Attribute> userAttribute = megService.getMegAttribute("44A554E40D8463EBA902F9B0F03F2E4FA9CD3CC3", MEGIdEnum.cuid);
        System.out.println(JsonUtils.toJsonWithOutException(userAttribute));
        Assert.assertNotNull(userAttribute);
    }

    @Test
    public void testGetAllTable() {
        List<String> allTable = baiduUserDataService.getAllUserTable();
        System.out.println(JsonUtils.toJsonWithOutException(allTable));
        Assert.assertNotNull(allTable);
    }

    @Test
    public void refreshSecretKey() {
        baiduUserDataService.refreshSecretKey();
    }
}
