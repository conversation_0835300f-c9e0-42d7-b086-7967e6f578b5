package com.baidu.keyue.deepsight.service.tool;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.baidu.kybase.sdk.message.enums.MessageTypeEnum;
import com.baidu.kybase.sdk.message.service.MessageApi;
import com.baidu.kybase.sdk.message.vo.MessageBody;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class MessageServiceTest{

    @Mock
    private MessageApi messageApi;

    @InjectMocks
    private MessageService messageService;

    @Test
    void pushMessageSuccess() {
        // Arrange
        String cloudId = "testCloudId";
        MessageBody messageBody = new MessageBody();
    
        // Act
        messageService.pushMessage(cloudId, messageBody);

        
    }



}