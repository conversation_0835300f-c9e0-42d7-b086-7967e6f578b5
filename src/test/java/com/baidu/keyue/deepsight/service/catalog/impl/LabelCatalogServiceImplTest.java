package com.baidu.keyue.deepsight.service.catalog.impl;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.catalog.CatalogDetail;
import com.baidu.keyue.deepsight.models.catalog.DeleteCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.EditCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogResponse;
import com.baidu.keyue.deepsight.models.catalog.MoveCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.NewCatalogRequest;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalogCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelCatalogMapper;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class LabelCatalogServiceImplTest {


    private final String catalogName = "testCatalog";
    @InjectMocks
    private LabelCatalogServiceImpl service;

    private final String tenantId = "testTenant";

    private final String userId = "testUser";

    @Mock
    private ExtendLabelCatalogMapper labelCatalogMapper;

    @Mock
    private RedissonClient redisson;

    @Mock
    private RLock lock;

    @Test
    void testD() {
        String result = service.d("test");
        assertNull(result);
    }

    @Test
    void testGetCatalogDetail() {
        LabelCatalog catalog = new LabelCatalog();
        catalog.setId(1L);
        catalog.setTenantId(tenantId);

        when(labelCatalogMapper.selectByExample(any())).thenReturn(List.of(catalog));

        LabelCatalog result = service.getCatalogDetail(1L, tenantId);
        assertNotNull(result);
        assertEquals(1L, result.getId());
    }

    @Test
    void testCreateLabelCatalogSuccess() {
        NewCatalogRequest request = new NewCatalogRequest();
        request.setCatalogName("testCatalog");
        request.setParentId(0L);

        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(labelCatalogMapper.countByExample(any())).thenReturn(0L);

        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            assertDoesNotThrow(() -> service.createLabelCatalog(request));
            verify(labelCatalogMapper).insert(any());
        }
    }

    @Test
    void testUpdateLabelCatalogSuccess() {
        EditCatalogRequest request = new EditCatalogRequest();
        request.setCatalogId(1L);
        request.setCatalogName("newName");

        LabelCatalog catalog = new LabelCatalog();
        catalog.setId(1L);
        catalog.setTenantId(tenantId);
        catalog.setParentId(0L);
        catalog.setCatalogName("oldName");

        when(labelCatalogMapper.selectByExample(any())).thenReturn(List.of(catalog));
        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(labelCatalogMapper.countByExample(any())).thenReturn(0L);

        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            assertDoesNotThrow(() -> service.updateLabelCatalog(request));
            verify(labelCatalogMapper).updateByPrimaryKey(any());
        }
    }

    @Test
    void testBuildCatalogList() {
        LabelCatalog parent = new LabelCatalog();
        parent.setId(1L);
        parent.setParentId(0L);
        parent.setCatalogName("parent");

        LabelCatalog child = new LabelCatalog();
        child.setId(2L);
        child.setParentId(1L);
        child.setCatalogName("child");

        Map<Long, List<LabelCatalog>> catalogMap = new HashMap<>();
        catalogMap.put(0L, List.of(parent));
        catalogMap.put(1L, List.of(child));

        List<CatalogDetail> result = service.buildCatalogList(catalogMap, 0L);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getChildren().size());
    }

    @Test
    void testDeleteLabelCatalogSuccess() {
        DeleteCatalogRequest request = new DeleteCatalogRequest();
        request.setCatalogId(1L);

        LabelCatalog catalog = new LabelCatalog();
        catalog.setId(1L);
        catalog.setTenantId(tenantId);
        catalog.setDel(false);

        when(labelCatalogMapper.selectByExample(any())).thenReturn(List.of(catalog));
        when(labelCatalogMapper.countByExample(any())).thenReturn(0L);

        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            assertDoesNotThrow(() -> service.deleteLabelCatalog(request));
            verify(labelCatalogMapper).updateByPrimaryKey(any());
        }
    }

    @Test
    void testG() {
        String result = service.g("test");
        assertEquals("Cached-Value", result);
    }

    @Test
    void testU() {
        String result = service.u("test");
        assertEquals("Updated-Cache-Value", result);
    }

    @Test
    void testCreateLabelCatalogDuplicateName() {
        NewCatalogRequest request = new NewCatalogRequest();
        request.setCatalogName("testCatalog");
        request.setParentId(0L);

        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(labelCatalogMapper.countByExample(any())).thenReturn(1L);

        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            assertThrows(DeepSightException.CatalogOperatorFailedException.class,
                    () -> service.createLabelCatalog(request));
        }
    }

    @Test
    void testRetrieveCatalogIds() {
        LabelCatalog parent = new LabelCatalog();
        parent.setId(1L);
        parent.setParentId(0L);

        LabelCatalog child = new LabelCatalog();
        child.setId(2L);
        child.setParentId(1L);

        when(labelCatalogMapper.selectByExample(any())).thenReturn(List.of(parent, child));

        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            List<Long> result = service.retrieveCatalogIds(1L);
            assertEquals(2, result.size());
            assertTrue(result.contains(1L));
            assertTrue(result.contains(2L));
        }
    }

    @Test
    void testMoveTopSuccess() {
        MoveCatalogRequest request = new MoveCatalogRequest();
        request.setCatalogId(2L);
        request.setAfter(0L);

        LabelCatalog catalog = new LabelCatalog();
        catalog.setId(2L);
        catalog.setParentId(1L);
        catalog.setSort(2L);

        when(labelCatalogMapper.selectByExample(any(LabelCatalogCriteria.class))).thenReturn(Lists.newArrayList(catalog));

        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            assertDoesNotThrow(() -> service.move(request));
            verify(labelCatalogMapper).updateByPrimaryKey(any());
        }
    }

    @Test
    void testListWithCatalogId() {
        ListCatalogRequest request = new ListCatalogRequest();
        request.setTenantId(tenantId);
        request.setCatalogId(1L);

        LabelCatalog catalog1 = new LabelCatalog();
        catalog1.setId(1L);
        catalog1.setParentId(0L);
        catalog1.setCatalogName("parent");
        catalog1.setSort(1L);

        LabelCatalog catalog2 = new LabelCatalog();
        catalog2.setId(2L);
        catalog2.setParentId(1L);
        catalog2.setCatalogName("child");
        catalog2.setSort(1L);

        when(labelCatalogMapper.selectByExample(any())).thenReturn(List.of(catalog1, catalog2));

        ListCatalogResponse response = service.list(request);
        assertNotNull(response);
        assertEquals(1, response.getResults().size());
    }

    @Test
    void testInitTenantCatalog() {
        when(labelCatalogMapper.countByExample(any())).thenReturn(0L);

        assertDoesNotThrow(() -> service.initTenantCatalog(tenantId));
        verify(labelCatalogMapper, atLeastOnce()).insert(any());
    }

    @Test
    void checkLabelCatalogShouldReturnCatalogWhenExists() {
        // Arrange
        LabelCatalog expectedCatalog = new LabelCatalog();
        expectedCatalog.setCatalogName(catalogName);
        expectedCatalog.setTenantId(tenantId);
        
        when(labelCatalogMapper.selectByExample(any(LabelCatalogCriteria.class)))
                .thenReturn(List.of(expectedCatalog));
    
        // Act
        LabelCatalog result = service.checkLabelCatalog(tenantId, catalogName);
    
        // Assert
        assertNotNull(result);
        assertEquals(catalogName, result.getCatalogName());
        assertEquals(tenantId, result.getTenantId());
    }

    @Test
    void checkLabelCatalogShouldThrowExceptionWhenNotExists() {
        // Arrange
        when(labelCatalogMapper.selectByExample(any(LabelCatalogCriteria.class)))
                .thenReturn(Collections.emptyList());
    
        // Act & Assert
        DeepSightException.CatalogOperatorFailedException exception = assertThrows(
                DeepSightException.CatalogOperatorFailedException.class,
                () -> service.checkLabelCatalog(tenantId, catalogName)
        );
        assertEquals(ErrorCode.NOT_FOUND, exception.getErrorCode());
        assertEquals("标签目录不存在", exception.getMessage());
    }

}