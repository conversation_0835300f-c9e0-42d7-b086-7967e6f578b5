package com.baidu.keyue.deepsight.service.label.impl;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.LabelValueSaveModEnum;
import com.baidu.keyue.deepsight.enums.LogicEnum;
import com.baidu.keyue.deepsight.enums.SortOrderEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseRecordResponse;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogResponse;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigQueryRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldShowConfigResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.label.GetLabelDetailRequest;
import com.baidu.keyue.deepsight.models.label.GetLabelDistributeRequest;
import com.baidu.keyue.deepsight.models.label.LabelDetail;
import com.baidu.keyue.deepsight.models.label.LabelDistribute;
import com.baidu.keyue.deepsight.models.label.LabelOriginalDetail;
import com.baidu.keyue.deepsight.models.label.LabelRule;
import com.baidu.keyue.deepsight.models.label.LabelUserListRequest;
import com.baidu.keyue.deepsight.models.label.LabelValueRule;
import com.baidu.keyue.deepsight.models.label.ListLabelBriefRequest;
import com.baidu.keyue.deepsight.models.label.NewLabelRequest;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.models.label.UpdateLabelRequest;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.Label;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskScheduler;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelMapper;
import com.baidu.keyue.deepsight.service.catalog.LabelCatalogService;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.DorisConfServiceImpl;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class LabelServiceImplTest {


    @Mock
    private DataTableManageService dataTableManageService;

    @Mock
    private RuleManagerService ruleManagerService;

    @Mock
    private DorisService dorisService;

    @Mock
    private DorisConfServiceImpl dorisConfService;
    @Mock
    private RedissonClient redisson;

    @Mock
    private RLock lock;

    @Mock
    private LabelCatalogService labelCatalogService;

    @Mock
    private TaskSchedulerService taskSchedulerService;
    @Mock
    private LabelFieldServiceImpl labelFieldService;

    @Mock
    private TaskInfoService taskInfoService;

    @InjectMocks
    private LabelServiceImpl labelService;

    @Mock
    private ExtendLabelMapper labelMapper;

    @Mock
    private RuleParseService ruleParseService;

    @Test
    void deleteLabelShouldUpdateLabelAndDeleteFieldAndTask() {
        // Arrange
        Label label = new Label();
        label.setId(123L);
        label.setField(456L);
        label.setTask(789L);

        // Act
        labelService.deleteLabel(label);

        // Assert
        verify(labelMapper).updateByPrimaryKeySelective(argThat(updatedLabel -> {
            return updatedLabel.getId().equals(123L) && updatedLabel.getDel();
        }));
        verify(labelFieldService).deleteFieldById(456L);
        verify(taskInfoService).deleteFieldById(789L);
    }

    @Test
    void createLabelShouldInsertNewLabel() {
        NewLabelRequest request = new NewLabelRequest();
        request.setLabelName("testLabel");
        request.setCatalogId(1L);
        List<LabelValueRule> labelValueRules = Lists.newArrayList(
                new LabelValueRule("labelValue", "labelValueDesc",
                        new RuleGroup(
                                "testRuleGroup", LogicEnum.AND,
                                Lists.newArrayList(new RuleGroup()),
                                Lists.newArrayList(new RuleNode())
                        )));
        request.setLabelRule(new LabelRule(labelValueRules));
        request.setUpdateMod(UpdateModEnum.REPLACE);
        request.setLabelValueSaveMod(LabelValueSaveModEnum.SINGLE);
        request.setTriggerMod(TriggerModeEnum.MANUAL);

        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        when(taskInfoService.createNewLabelCalTask(any(), any(), any())).thenReturn(1L);
        when(labelFieldService.createNewLabelField(any(), any())).thenReturn(2L);
        when(ruleParseService.parseRuleGroup(any(), any())).thenReturn(null);

        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            UserAuthInfo info = new UserAuthInfo();
            info.setUserName("test_user");
            webContextHolder.when((MockedStatic.Verification) WebContextHolder.getUserAuthInfo()).thenReturn(info);
            labelService.createLabel(request);
        }

        verify(labelMapper).insert(any(LabelWithBLOBs.class));
    }

    @Test
    void getLabelByTenantAndLabelIdShouldReturnLabel() {
        LabelWithBLOBs label = new LabelWithBLOBs();
        label.setId(1L);

        when(labelMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(label));

        LabelWithBLOBs result = labelService.getLabelByTenantAndLabelId("tenant", 1L);
        assert result.getId() == 1L;
    }

    @Test
    void getLabelDetailShouldReturnLabelDetail() {
        GetLabelDetailRequest request = new GetLabelDetailRequest();
        request.setLabelId(1L);

        LabelWithBLOBs label = new LabelWithBLOBs();
        label.setId(1L);
        label.setTask(1L);
        label.setTriggerFrequencyValue("{}");
        label.setLabelRule("{}");

        when(labelMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(label));
        when(taskSchedulerService.queryLatestFinishedRecord(any())).thenReturn(new TaskScheduler());

        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            LabelOriginalDetail result = labelService.getLabelDetail(request);
            assert result.getLabelId() == 1L;
        }
    }

    @Test
    void updateLabelShouldUpdateLabelAndTask() {
        UpdateLabelRequest request = new UpdateLabelRequest();
        request.setLabelId(1L);
        request.setLabelName("newName");
        request.setCatalogId(1L);
        List<LabelValueRule> labelValueRules = Lists.newArrayList(
                new LabelValueRule("labelValue", "labelValueDesc",
                        new RuleGroup(
                                "testRuleGroup", LogicEnum.AND,
                                Lists.newArrayList(new RuleGroup()),
                                Lists.newArrayList(new RuleNode())
                        )));
        request.setLabelRule(new LabelRule(labelValueRules));
        request.setUpdateMod(UpdateModEnum.MERGE);
        request.setLabelValueSaveMod(LabelValueSaveModEnum.SINGLE);
        request.setTriggerMod(TriggerModeEnum.MANUAL);
        request.setTriggerFrequency(TriggerFrequencyEnum.DAY);
        request.setTriggerFrequencyValue(new TriggerFrequencyValue());

        LabelWithBLOBs label = new LabelWithBLOBs();
        label.setId(1L);
        label.setTask(1L);
        label.setLabelCalStatus(TaskExecStatusEnum.PENDING.getCode());
        label.setLabelName("oldName");

        when(labelMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(label));
        when(redisson.getLock(anyString())).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);

        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            UserAuthInfo info = new UserAuthInfo();
            info.setUserName("test_user");
            webContextHolder.when((MockedStatic.Verification) WebContextHolder.getUserAuthInfo()).thenReturn(info);
            labelService.updateLabel(request);
        }

        verify(taskInfoService).updateLabelCalTaskTrigger(anyLong(), any(TriggerModeEnum.class), any(TriggerFrequencyEnum.class), any(TriggerFrequencyValue.class));
    }

    @Test
    void queryRunningLabelShouldReturnRunningLabels() {
        when(labelMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(new LabelWithBLOBs()));

        List<LabelWithBLOBs> result = labelService.queryRunningLabel();
        assert !result.isEmpty();
    }

    @Test
    void queryDeletedLabelShouldReturnDeletedLabels() {
        when(labelMapper.selectByExample(any())).thenReturn(List.of(new Label()));

        List<Label> result = labelService.queryDeletedLabel(3600);
        assert !result.isEmpty();
    }

    @Test
    void retrieveLabelWithFieldIdsShouldReturnLabels() {
        when(labelMapper.selectByExample(any())).thenReturn(List.of(new Label()));

        List<Label> result = labelService.retrieveLabelWithFieldIds(List.of(1L), "tenant");
        assert !result.isEmpty();
    }

    @Test
    void listLabelShouldReturnPagedLabels() {
        ListLabelBriefRequest request = new ListLabelBriefRequest();
        request.setPageNo(1);
        request.setPageSize(10);

        when(labelCatalogService.retrieveCatalogIds(any())).thenReturn(List.of(1L));
        when(labelMapper.countByExample(any())).thenReturn(1L);

        Label label = new Label();
        label.setId(1L);
        label.setTask(1L);
        when(labelMapper.selectByExample(any())).thenReturn(List.of(label));

        when(taskSchedulerService.queryTaskScheduler(any())).thenReturn(Map.of(1L, new TaskSchedulerWithBLOBs()));

        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            BasePageResponse.Page<LabelDetail> result = labelService.listLabel(request);
            assert result.getTotal() == 1L;
        }

    }

    @Test
    void deleteLabelShouldUpdateLabelAndDeleteFieldAndTask1() {
        Label label = new Label();
        label.setId(123L);
        label.setField(456L);
        label.setTask(789L);

        labelService.deleteLabel(label);

        verify(labelMapper).updateByPrimaryKeySelective(argThat(updatedLabel ->
                updatedLabel.getId().equals(123L) && updatedLabel.getDel()));
        verify(labelFieldService).deleteFieldById(456L);
        verify(taskInfoService).deleteFieldById(789L);
    }

    @Test
    void labelDistributionShouldReturnSortedDistribution() {
        GetLabelDistributeRequest request = new GetLabelDistributeRequest();
        request.setLabelId(1L);
        request.setSort(SortOrderEnum.DESC);

        LabelWithBLOBs label = new LabelWithBLOBs();
        label.setId(1L);
        label.setDistribution("{\"labels\":[{\"count\":1},{\"count\":2}]}");

        when(labelMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(label));
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            LabelDistribute result = labelService.labelDistribution(request);
            assert result.getLabels().get(0).getCount() == 2L;
        }
    }

    @Test
    void labelDistributionShouldReturnEmpty() {
        GetLabelDistributeRequest request = new GetLabelDistributeRequest();
        request.setLabelId(1L);
        request.setSort(SortOrderEnum.DESC);

        LabelWithBLOBs label = new LabelWithBLOBs();
        label.setId(1L);
        label.setLabelRule("{\"labelValueRules\":[{\"labelValue\":\"18以下\",\"labelValueDesc\":\"18以下\"}," +
                "{\"labelValue\":\"18-24\",\"labelValueDesc\":\"18-24\"}]}");

        when(labelMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(label));
        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            LabelDistribute result = labelService.labelDistribution(request);
            assertNotNull(result.getLabels().get(0).getLabelName());
        }
    }

    @Test
    void listLabelTreeShouldReturnCatalogWithLabels() {
        ListCatalogRequest request = new ListCatalogRequest();
        when(labelCatalogService.list(request)).thenReturn(new ListCatalogResponse(Lists.newArrayList()));

        try (MockedStatic<WebContextHolder> webContextHolder = mockStatic(WebContextHolder.class)) {
            webContextHolder.when(WebContextHolder::getTenantId).thenReturn("test_tenant");
            webContextHolder.when(WebContextHolder::getUserId).thenReturn("test_user");
            ListCatalogResponse result = labelService.listLabelTree(request, false);
            assert result != null;
        }
    }

    @Test
    void getWaitExecLabelShouldReturnLabels() {
        Set<Long> taskIds = Set.of(1L);
        when(labelMapper.selectByExampleWithBLOBs(any())).thenReturn(List.of(new LabelWithBLOBs()));

        List<LabelWithBLOBs> result = labelService.getWaitExecLabel(taskIds);
        assert !result.isEmpty();
    }

    @Test
    void labelUserListShouldReturnCorrectResponse() {
        // Arrange
        String tenantId = "1";
        String mockTableName = "mock_user_1";
        Long tableId = 1L;
        Long labelId = 123L;
        String labelFieldName = "label_field";
        String primaryKey = "id";
        int pageNo = 1;
        int pageSize = 10;
        long count = 5;

        LabelUserListRequest request = new LabelUserListRequest();
        request.setLabelId(labelId);
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);

        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setId(tableId);

        VisibleFieldResponse fieldResponse = new VisibleFieldResponse();
        fieldResponse.setEnName("field1");
        List<VisibleFieldResponse> visibleFields = List.of(fieldResponse);

        FieldShowConfigResponse fieldShowConfig = new FieldShowConfigResponse();

        TableFieldMetaInfo primaryKeyField = new TableFieldMetaInfo();
        primaryKeyField.setEnField(primaryKey);

        DqlParseResult dqlParseResult = new DqlParseResult();
        dqlParseResult.setFrom("from_table");

        Map<String, Object> dorisData = Map.of("field1", "value1");
        List<Map<String, Object>> tableContentList = List.of(dorisData);
        Map<String, String> convertedData = Map.of("field1", "value1");

        try (MockedStatic<TenantUtils> tenantUtilsMock = Mockito.mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn(mockTableName);
            List<LabelWithBLOBs> list = new ArrayList<>();
            list.add(new LabelWithBLOBs());
            when(labelMapper.selectByExampleWithBLOBs(any(LabelCriteria.class))).thenReturn(list);
            when(dataTableManageService.getTableDetailWithTableName(mockTableName))
                    .thenReturn(tableInfo);
            when(dataTableManageService.getVisibleFields(tableId, false))
                    .thenReturn(visibleFields);
            when(dataTableManageService.getFieldShowConfig(eq(tenantId), any(FieldShowConfigQueryRequest.class)))
                    .thenReturn(fieldShowConfig);
            when(labelFieldService.genFieldName(labelId))
                    .thenReturn(labelFieldName);
            when(ruleManagerService.parseRuleGroup(any(RuleGroup.class), any(AtomicInteger.class)))
                    .thenReturn(dqlParseResult);
            when(dorisService.getCount(anyString()))
                    .thenReturn(count);
            when(dataTableManageService.queryTableFieldByTag(tableId, TableFieldTagEnum.PRIMARY))
                    .thenReturn(primaryKeyField);
            when(dorisService.selectList(anyString()))
                    .thenReturn(tableContentList);
            when(dorisConfService.dorisDataConvertToShowData(eq(tableId), eq(dorisData), eq(visibleFields)))
                    .thenReturn(convertedData);

            // Act
            BaseRecordResponse response = labelService.labelUserList(request, tenantId);

            // Assert
            assertNotNull(response);
            assertEquals(visibleFields, response.getFields());
            assertEquals(fieldShowConfig, response.getFieldShowConfig());
            assertEquals(pageNo, response.getContents().getPageNo());
            assertEquals(pageSize, response.getContents().getPageSize());
            assertEquals(count, response.getContents().getTotal());
            assertEquals(1, response.getContents().getResults().size());
            assertEquals("value1", response.getContents().getResults().get(0).get("field1"));
        }
    }


    @Test
    void labelUserListShouldThrowWhenTableNotExist() {
        // Arrange
        String tenantId = "1";
        LabelUserListRequest request = new LabelUserListRequest();

        try (MockedStatic<TenantUtils> tenantUtilsMock = Mockito.mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn("mock_user_1");

            // Act & Assert
            assertThrows(RuntimeException.class, () -> labelService.labelUserList(request, tenantId));
        }
    }

    @Test
    void labelUserListShouldThrowWhenNoVisibleFields() {
        // Arrange
        String tenantId = "1";
        LabelUserListRequest request = new LabelUserListRequest();
        request.setLabelId(1L);
        DataTableInfo tableInfo = new DataTableInfo();

        try (MockedStatic<TenantUtils> tenantUtilsMock = Mockito.mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn("mock_table_1");
            List<LabelWithBLOBs> list = new ArrayList<>();
            list.add(new LabelWithBLOBs());
            when(labelMapper.selectByExampleWithBLOBs(any(LabelCriteria.class))).thenReturn(list);
            when(dataTableManageService.getTableDetailWithTableName(anyString()))
                    .thenReturn(tableInfo);
            when(dataTableManageService.getVisibleFields(anyLong(), eq(false)))
                    .thenReturn(List.of());

            // Act & Assert
            assertThrows(RuntimeException.class, () -> labelService.labelUserList(request, tenantId));
        }
    }

    @Test
    void labelUserListShouldReturnEmptyWhenCountZero() {
        // Arrange
        String tenantId = "1";
        LabelUserListRequest request = new LabelUserListRequest();
        request.setLabelId(1L);
        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setId(1L);

        VisibleFieldResponse fieldResponse = new VisibleFieldResponse();
        List<VisibleFieldResponse> visibleFields = List.of(fieldResponse);

        try (MockedStatic<TenantUtils> tenantUtilsMock = Mockito.mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(() -> TenantUtils.generateMockUserTableName(tenantId))
                    .thenReturn("mock_table_1");
            List<LabelWithBLOBs> list = new ArrayList<>();
            list.add(new LabelWithBLOBs());
            when(labelMapper.selectByExampleWithBLOBs(any(LabelCriteria.class))).thenReturn(list);
            when(dataTableManageService.getTableDetailWithTableName(anyString()))
                    .thenReturn(tableInfo);
            when(dataTableManageService.getVisibleFields(anyLong(), eq(false)))
                    .thenReturn(visibleFields);
            when(dataTableManageService.getFieldShowConfig(eq(tenantId), any(FieldShowConfigQueryRequest.class)))
                    .thenReturn(new FieldShowConfigResponse());
            when(labelFieldService.genFieldName(anyLong()))
                    .thenReturn("field");
            when(ruleManagerService.parseRuleGroup(any(RuleGroup.class), any(AtomicInteger.class)))
                    .thenReturn(new DqlParseResult());
            when(dorisService.getCount(anyString()))
                    .thenReturn(0L);

            // Act
            BaseRecordResponse response = labelService.labelUserList(request, tenantId);

            // Assert
            assertNotNull(response);
            assertEquals(0, response.getContents().getTotal());
            assertTrue(response.getContents().getResults().isEmpty());
        }
    }
}