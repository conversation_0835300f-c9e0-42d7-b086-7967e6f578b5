package com.baidu.keyue.deepsight.service.tenant;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {
    "user_memory=classpath:datamanage/memory_extract_field_info.json",
    "customer_talk_field_info=classpath:datamanage/keyue_record_field_info.json",
    "aiob_talk_field_info=classpath:datamanage/aiob_record_field_info.json",
    "usern_field_info=classpath:datamanage/user_field_info.txt",
    "aiob_session_field_info=classpath:datamanage/aiob_session_service_field_info.txt"
})
public class TenantV1BetaUpgradeHandlerTest{

    @Mock
    private TenantInfoMapper tenantInfoMapper;

    @InjectMocks
    private TenantV1BetaUpgradeHandler tenantV1BetaUpgradeHandler;

    private UserAuthInfo userAuthInfo;

    @BeforeEach
    void setUp() {
        userAuthInfo = new UserAuthInfo();
        userAuthInfo.setAccountId("testAccount");
        userAuthInfo.setUserName("testUser");
        userAuthInfo.setUserId(123L);
        ReflectionTestUtils.setField(tenantV1BetaUpgradeHandler, "tenantInfoMapper", tenantInfoMapper);
    }

    @Test
    void initTenantInfoLoginTypeSuccess() {
        // Arrange
        when(tenantInfoMapper.insertSelective(any(TenantInfo.class))).thenReturn(1);
    
        // Act
        TenantInfo result = tenantV1BetaUpgradeHandler.initTenantInfo("tenant1", userAuthInfo, Constants.TENANT_LOGIN_TYPE);
    
        // Assert
        assertNotNull(result);
        assertEquals("tenant1", result.getTenantid());
        assertEquals("testAccount", result.getAccountid());
        assertEquals("testUser", result.getUsername());
        assertEquals("123", result.getUserId());
        assertEquals(Constants.TENANT_LOGIN_TYPE, result.getTenantSource());
        assertNotNull(result.getCreateTime());
        assertNotNull(result.getUpdateTime());
        verify(tenantInfoMapper, times(1)).insertSelective(any(TenantInfo.class));
    }

    @Test
    void initTenantInfoAiobTypeSuccess() {
        // Arrange
        when(tenantInfoMapper.insertSelective(any(TenantInfo.class))).thenReturn(1);
    
        // Act
        TenantInfo result = tenantV1BetaUpgradeHandler.initTenantInfo("tenant2", userAuthInfo, Constants.TENANT_AIOB_TYPE);
    
        // Assert
        assertNotNull(result);
        assertEquals("tenant2", result.getTenantid());
        assertEquals(Constants.DEFAULT_USER_ID, result.getUserId());
        assertEquals(Constants.TENANT_AIOB_TYPE, result.getTenantSource());
        assertNotNull(result.getCreateTime());
        assertNotNull(result.getUpdateTime());
        verify(tenantInfoMapper, times(1)).insertSelective(any(TenantInfo.class));
    }

    @Test
    void initTenantInfoDefaultTypeSuccess() {
        // Arrange
        when(tenantInfoMapper.insertSelective(any(TenantInfo.class))).thenReturn(1);
    
        // Act
        TenantInfo result = tenantV1BetaUpgradeHandler.initTenantInfo("tenant3", userAuthInfo, Constants.TENANT_DEFAULT_TYPE);
    
        // Assert
        assertNotNull(result);
        assertEquals("tenant3", result.getTenantid());
        assertEquals(Constants.AIOB_USER_ID, result.getUserId());
        assertEquals(Constants.TENANT_DEFAULT_TYPE, result.getTenantSource());
        assertNotNull(result.getCreateTime());
        assertNotNull(result.getUpdateTime());
        verify(tenantInfoMapper, times(1)).insertSelective(any(TenantInfo.class));
    }

    @Test
    void initTenantInfoNullUserIdLoginTypeSuccess() {
        // Arrange
        userAuthInfo.setUserId(null);
        when(tenantInfoMapper.insertSelective(any(TenantInfo.class))).thenReturn(1);
    
        // Act
        TenantInfo result = tenantV1BetaUpgradeHandler.initTenantInfo("tenant4", userAuthInfo, Constants.TENANT_LOGIN_TYPE);
    
        // Assert
        assertNotNull(result);
        assertEquals("tenant4", result.getTenantid());
        assertNull(result.getUserId());
        verify(tenantInfoMapper, times(1)).insertSelective(any(TenantInfo.class));
    }

}