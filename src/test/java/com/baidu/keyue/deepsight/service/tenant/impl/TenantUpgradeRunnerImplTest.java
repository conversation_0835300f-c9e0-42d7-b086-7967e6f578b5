package com.baidu.keyue.deepsight.service.tenant.impl;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.utils.ThreadPoolUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.ApplicationArguments;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {
    "switch.tenantUpgradeRunner=true",
    "app-version=1"
})
public class TenantUpgradeRunnerImplTest{

    @InjectMocks
    private TenantUpgradeRunnerImpl tenantUpgradeRunner;

    @Mock
    private TenantInfoService tenantInfoService;

    @Mock
    private DorisService dorisService;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private RLock lock;

    @Mock
    private ApplicationArguments args;
    
    private String KEY = "TENANT_AUTO_UPGRADE";

    @Mock
    private ThreadPoolExecutor threadPoolExecutor;

    @BeforeEach
    void setUp() {
    }

    @Test
    void runShouldSkipWhenAutoUpgradeDisabled() throws Exception {
        ReflectionTestUtils.setField(tenantUpgradeRunner, "tenantUpgradeRunner", false);
        tenantUpgradeRunner.run(args);
    }

    @Test
    void runShouldSkipWhenLockNotAcquired() throws Exception {
        ReflectionTestUtils.setField(tenantUpgradeRunner, "tenantUpgradeRunner", true);
        when(redissonClient.getLock(KEY)).thenReturn(lock);
        when(lock.tryLock()).thenReturn(false);
        tenantUpgradeRunner.run(args);
        verify(tenantInfoService, never()).getUpgradeTenant(anyInt());
    }

    @Test
    void runShouldExecuteUpgradeWhenLockAcquired() throws Exception {
        ReflectionTestUtils.setField(tenantUpgradeRunner, "tenantUpgradeRunner", true);
        ReflectionTestUtils.setField(tenantUpgradeRunner, "tenantInfoService", tenantInfoService);
        when(redissonClient.getLock(KEY)).thenReturn(lock);
        when(lock.tryLock()).thenReturn(true);
        List<TenantInfo> mockTenants = List.of(new TenantInfo(), new TenantInfo());
        when(tenantInfoService.getUpgradeTenant(null)).thenReturn(mockTenants);
    
        try (MockedStatic<ThreadPoolUtils> mocked = mockStatic(ThreadPoolUtils.class)) {
            mocked.when(ThreadPoolUtils::getSingleThreadPool).thenReturn(threadPoolExecutor);
            doAnswer(invocation -> {
                Runnable task = invocation.getArgument(0);
                task.run();
                return null;
            }).when(threadPoolExecutor).execute(any(Runnable.class));
    
            tenantUpgradeRunner.run(args);
    
            verify(tenantInfoService, times(mockTenants.size())).initOrUpgradeTenant(any());
        }
    }

    @Test
    void upgradeTenantShouldSuccessWhenAllParametersValid() {
        // Arrange
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("1");
        tenantInfo.setTenantSource("testSource");
        tenantInfo.setUserId("123");
        tenantInfo.setAccountid("account123");
        tenantInfo.setUsername("testUser");
        // Act
        tenantUpgradeRunner.upgradeTenant(tenantInfo);
        // Assert
        verify(tenantInfoService, times(1)).initOrUpgradeTenant(any(TenantDTO.class));
    }

    @Test
    void upgradeTenantShouldHandleNullUserId() {
        // Arrange
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("1");
        tenantInfo.setTenantSource("testSource");
        tenantInfo.setUserId(null);
        tenantInfo.setAccountid("account123");
        tenantInfo.setUsername("testUser");
        // Act
        tenantUpgradeRunner.upgradeTenant(tenantInfo);
        // Assert
        verify(tenantInfoService, times(1)).initOrUpgradeTenant(any(TenantDTO.class));
    }

    @Test
    void upgradeTenantShouldHandleException() {
        // Arrange
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantid("1");
        tenantInfo.setTenantSource("testSource");
        tenantInfo.setUserId("123");
        tenantInfo.setAccountid("account123");
        tenantInfo.setUsername("testUser");
        doThrow(new RuntimeException("Test exception")).when(tenantInfoService).initOrUpgradeTenant(any(TenantDTO.class));
        // Act
        tenantUpgradeRunner.upgradeTenant(tenantInfo);
        // Assert
        verify(tenantInfoService, times(1)).initOrUpgradeTenant(any(TenantDTO.class));
    }
}