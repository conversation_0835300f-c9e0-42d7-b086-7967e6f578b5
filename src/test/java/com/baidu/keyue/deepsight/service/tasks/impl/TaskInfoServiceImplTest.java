package com.baidu.keyue.deepsight.service.tasks.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;

import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendTaskInfoMapper;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
public class TaskInfoServiceImplTest{

    @Mock
    private ExtendTaskInfoMapper taskInfoMapper;

    @InjectMocks
    private TaskInfoServiceImpl taskInfoService;

    @Test
    void getTaskDetailWithIdsShouldReturnNullWhenIdsIsEmpty() {
        List<Long> emptyIds = Collections.emptyList();
        List<TaskInfo> result = taskInfoService.getTaskDetailWithIds(TaskTypeEnum.LABEL_DWS_TASK, emptyIds);
        assertNull(result);
    }

    @Test
    void getTaskDetailWithIdsShouldReturnTaskListWhenIdsNotEmpty() {
        List<Long> ids = List.of(1L, 2L, 3L);
        TaskTypeEnum taskType = TaskTypeEnum.LABEL_DWS_TASK;
        List<TaskInfo> expectedTasks = List.of(new TaskInfo(), new TaskInfo());
    
        when(taskInfoMapper.selectByExample(any(TaskInfoCriteria.class))).thenReturn(expectedTasks);
    
        List<TaskInfo> result = taskInfoService.getTaskDetailWithIds(taskType, ids);
    
        assertNotNull(result);
        assertEquals(expectedTasks.size(), result.size());
        
        verify(taskInfoMapper).selectByExample(any(TaskInfoCriteria.class));
    }

    @Test
    void getTaskDetailWithIdsShouldSetCorrectCriteria() {
        List<Long> ids = List.of(1L, 2L, 3L);
        TaskTypeEnum taskType = TaskTypeEnum.LABEL_DWS_TASK;
        List<TaskInfo> expectedTasks = List.of(new TaskInfo());
    
        when(taskInfoMapper.selectByExample(any(TaskInfoCriteria.class))).thenAnswer(invocation -> {
            TaskInfoCriteria criteria = invocation.getArgument(0);
            assertEquals(taskType.getCode(), criteria.getOredCriteria().get(0).getAllCriteria().get(0).getValue());
            assertEquals(DelEnum.NOT_DELETED.getBoolean(), criteria.getOredCriteria().get(0).getAllCriteria().get(1).getValue());
            assertEquals(ids, criteria.getOredCriteria().get(0).getAllCriteria().get(2).getValue());
            return expectedTasks;
        });
    
        List<TaskInfo> result = taskInfoService.getTaskDetailWithIds(taskType, ids);
    
        assertNotNull(result);
        assertEquals(expectedTasks, result);
    }

}