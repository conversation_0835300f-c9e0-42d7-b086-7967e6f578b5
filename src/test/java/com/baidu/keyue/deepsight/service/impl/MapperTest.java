package com.baidu.keyue.deepsight.service.impl;

import com.baidu.keyue.deepsight.BaseTest;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelCatalogMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelFieldMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendMemoryExtractMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendTaskInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendTaskSchedulerMapper;
import jakarta.annotation.Resource;
import org.apache.ibatis.annotations.Param;
import org.junit.Test;

import java.util.Date;
import java.util.List;

/**
 * @ClassName MapperTest
 * @Description mapper代码维护测试
 * <AUTHOR>
 * @Date 2025/4/3 5:47 PM
 */
public class MapperTest extends BaseTest {
    @Resource
    private ExtendLabelCatalogMapper extendLabelCatalogMapper;
    @Resource
    private ExtendLabelFieldMapper extendLabelFieldMapper;
    @Resource
    private ExtendLabelMapper extendLabelMapper;
    @Resource
    private ExtendMemoryExtractMapper extendMemoryExtractMapper;
    @Resource
    private ExtendTaskInfoMapper extendTaskInfoMapper;
    @Resource
    private ExtendTaskSchedulerMapper extendTaskSchedulerMapper;

    @Test
    public void extendLabelCatalogMapperTest() {
        List<Long> ids = List.of(1L, 2L);
        Long id = 1L;
        Byte number = 0;
        String modifier = "9527";
        Date date = new Date();
        extendLabelCatalogMapper.catalogMoveDown(ids, new Date(), modifier);
        extendLabelMapper.labelUpdate(id, id, "测试标签", number, number, number,
                number, number, "{}", "测试", modifier, true, date);
        extendLabelMapper.updateLabelCalTaskResult(id, number, "test", date, false);
        extendLabelMapper.updateLabelCalTaskStatus(id, number, "test");
        extendMemoryExtractMapper.updateCalTaskStatus(id, number);
        extendTaskInfoMapper.taskTriggerUpdate(10000L,"0 1 * * *", date, modifier, date, number);
        extendTaskInfoMapper.updateTaskNextExecDate(10000L, date);
        extendTaskSchedulerMapper.updateSchedulerStatus(id, number, date);
        extendTaskSchedulerMapper.queryLatestByTaskIds(ids);
    }
}
