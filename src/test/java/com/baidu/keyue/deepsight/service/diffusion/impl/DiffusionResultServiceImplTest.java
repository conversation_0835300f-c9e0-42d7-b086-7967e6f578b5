package com.baidu.keyue.deepsight.service.diffusion.impl;

import com.baidu.keyue.deepsight.database.service.impl.DorisServiceImpl;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.diffusion.result.request.CustomerDiffusionContentListRequest;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.CustomerDiffusionTaskMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TaskSchedulerMapper;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.DorisConfServiceImpl;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class DiffusionResultServiceImplTest {



    private CustomerDiffusionTask diffusionTask;

    private TaskSchedulerWithBLOBs taskScheduler;

    private TableFieldMetaInfo scoreFieldMetaInfo;
    private DataTableInfo userDataTableInfo;
    @Mock
    private CustomerDiffusionTaskMapper diffusionTaskMapper;

    @Mock
    private TaskSchedulerMapper taskSchedulerMapper;

    @Mock
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Mock
    private DataTableInfoMapper dataTableInfoMapper;

    @Mock
    private DorisConfServiceImpl dorisConfServiceImpl;

    @Mock
    private RuleManagerService ruleManagerService;

    @Mock
    private DorisServiceImpl dorisService;

    @Mock
    private DataTableManageService dataTableManageService;

    @InjectMocks
    private DiffusionResultServiceImpl diffusionResultService;

    private static final String TENANT_ID = "1";

    private static final String USER_ID = "9527";
    private static final String USER_NAME = "mock_test";

    @BeforeEach
    public void setUp() {
        // Setup code if needed
        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            mocked.when(WebContextHolder::getUserId).thenReturn(USER_ID);
            mocked.when(WebContextHolder::getUserName).thenReturn(USER_NAME);
        }
    }

    @Test
    public void testContentListSuccess() {
        Long diffusionTaskId = 1L;
        CustomerDiffusionTask diffusionTask = new CustomerDiffusionTask();
        diffusionTask.setId(diffusionTaskId);
        diffusionTask.setTaskId(1L);
        diffusionTask.setTenantId(TENANT_ID);

        TaskSchedulerWithBLOBs taskScheduler = new TaskSchedulerWithBLOBs();
        taskScheduler.setStatus((byte) 1);

        DataTableInfo userDataTableInfo = new DataTableInfo();
        userDataTableInfo.setId(1L);

        VisibleFieldResponse visibleField = new VisibleFieldResponse();
        VisibleFieldResponse visibleField1 = new VisibleFieldResponse();
        visibleField1.setEnName("oneId");
        visibleField1.setCnName("oneId");

        visibleField.setEnName("score");
        visibleField.setCnName("score");
        List<VisibleFieldResponse> visibleFields = Arrays.asList(
                visibleField1,
                visibleField
        );

        DqlParseResult dqlParseResult = new DqlParseResult();
        dqlParseResult.setSelect("select *");

        UserAuthInfo userAuthInfo = new UserAuthInfo();
        userAuthInfo.setTenantId(1L);
        WebContextHolder.setDeepSightWebContext(new DeepSightWebContext(userAuthInfo));

        when(diffusionTaskMapper.selectByPrimaryKey(diffusionTaskId)).thenReturn(diffusionTask);
        TableFieldMetaInfo scoreFieldMetaInfo = new TableFieldMetaInfo();
        scoreFieldMetaInfo.setEnField("score");
        when(tableFieldMetaInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(scoreFieldMetaInfo));
        when(taskSchedulerMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.singletonList(taskScheduler));
        when(dataTableInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(userDataTableInfo));
        when(dataTableManageService.getVisibleFields(anyLong(), anyBoolean())).thenReturn(visibleFields);
        when(ruleManagerService.innerJoin(any())).thenReturn(dqlParseResult);
        when(dorisService.getCount(anyString())).thenReturn(100L);
        when(dorisService.selectList(anyString())).thenReturn(Collections.singletonList(new HashMap<>()));
        when(dorisConfServiceImpl.dorisDataConvertToShowData(anyLong(), anyMap(), anyList())).thenReturn(new HashMap<>());

        CustomerDiffusionContentListRequest request = new CustomerDiffusionContentListRequest();

        request.setCustomerDiffusionTaskId(diffusionTaskId);
        RuleFilter ruleFilter = new RuleFilter();
        ruleFilter.setFiled("oneId");
        request.setFilters(Collections.singletonList(ruleFilter));
        BasePageResponse.Page<Map<String, String>> result = diffusionResultService.contentList(request);

        assertEquals(100, result.getTotal());
    }

    @Test
    public void testCheckTenantPermission_Success() {
        // Arrange
        Long taskId = 1L;
        CustomerDiffusionTask mockTask = new CustomerDiffusionTask();
        mockTask.setTenantId(TENANT_ID);

        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            when(diffusionTaskMapper.selectByPrimaryKey(taskId)).thenReturn(mockTask);

            // Act & Assert
            assertDoesNotThrow(() -> diffusionResultService.checkTenantPermission(taskId));
        }
    }

    @Test
    public void testCheckTenantPermission_TaskNotFound() {
        // Arrange
        Long taskId = 1L;

        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            when(diffusionTaskMapper.selectByPrimaryKey(taskId)).thenReturn(null);

            // Act & Assert
            DeepSightException.ParamsErrorException exception = assertThrows(
                DeepSightException.ParamsErrorException.class,
                () -> diffusionResultService.checkTenantPermission(taskId)
            );
            assertEquals("扩散任务不存在", exception.getMessage());
        }
    }

    @Test
    public void testCheckTenantPermission_TenantMismatch() {
        // Arrange
        Long taskId = 1L;
        CustomerDiffusionTask mockTask = new CustomerDiffusionTask();
        mockTask.setTenantId("2"); // Different tenant

        try (MockedStatic<WebContextHolder> mocked = mockStatic(WebContextHolder.class)) {
            mocked.when(WebContextHolder::getTenantId).thenReturn(TENANT_ID);
            when(diffusionTaskMapper.selectByPrimaryKey(taskId)).thenReturn(mockTask);

            // Act & Assert
            DeepSightException.ParamsErrorException exception = assertThrows(
                DeepSightException.ParamsErrorException.class,
                () -> diffusionResultService.checkTenantPermission(taskId)
            );
            assertEquals("扩散任务不属于该租户", exception.getMessage());
        }
    }
}