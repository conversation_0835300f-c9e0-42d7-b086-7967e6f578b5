package com.baidu.keyue.deepsight.service.tasks.impl;


import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskScheduler;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendTaskSchedulerMapper;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@ExtendWith(MockitoExtension.class)
public class TaskSchedulerServiceImplTest {

    @Mock
    private ExtendTaskSchedulerMapper schedulerMapper;

    @InjectMocks
    private TaskSchedulerServiceImpl taskSchedulerService;

    @Test
    void queryRunningSchedulerShouldReturnLatestRunningRecords() {
        // Prepare test data
        List<Long> taskIds = List.of(1L, 2L, 3L);
        TaskSchedulerWithBLOBs record1 = new TaskSchedulerWithBLOBs();
        record1.setId(1L);
        record1.setTaskId(1L);
        TaskSchedulerWithBLOBs record2 = new TaskSchedulerWithBLOBs();
        record2.setId(2L);
        record2.setTaskId(2L);
        TaskSchedulerWithBLOBs record3 = new TaskSchedulerWithBLOBs();
        record3.setId(3L);
        record3.setTaskId(3L);
        List<TaskSchedulerWithBLOBs> mockRecords = List.of(record1, record2, record3);

        // Mock mapper behavior
        when(schedulerMapper.selectByExampleWithBLOBs(any(TaskSchedulerCriteria.class)))
                .thenReturn(mockRecords);

        // Execute method
        List<TaskSchedulerWithBLOBs> result = taskSchedulerService.queryRunningScheduler(taskIds);

        // Verify results
        assertNotNull(result);
        assertEquals(3, result.size());
        Map<Long, TaskSchedulerWithBLOBs> resultMap = result.stream()
                .collect(Collectors.toMap(TaskScheduler::getTaskId, Function.identity()));
        assertTrue(resultMap.containsKey(1L));
        assertTrue(resultMap.containsKey(2L));
        assertTrue(resultMap.containsKey(3L));

        // Verify interactions
        verify(schedulerMapper, times(1)).selectByExampleWithBLOBs(any(TaskSchedulerCriteria.class));
    }

    @Test
    void queryRunningSchedulerShouldHandleEmptyInput() {
        // Execute with empty input
        List<TaskSchedulerWithBLOBs> result = taskSchedulerService.queryRunningScheduler(List.of());

        // Verify results
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void queryRunningSchedulerShouldHandleDuplicateRecords() {
        // Prepare test data with duplicate taskIds
        List<Long> taskIds = List.of(1L, 2L, 3L);
        TaskSchedulerWithBLOBs record1 = new TaskSchedulerWithBLOBs();
        record1.setId(1L);
        record1.setTaskId(1L);
        TaskSchedulerWithBLOBs record2 = new TaskSchedulerWithBLOBs();
        record2.setId(2L);
        record2.setTaskId(1L); // Duplicate taskId
        TaskSchedulerWithBLOBs record3 = new TaskSchedulerWithBLOBs();
        record3.setId(3L);
        record3.setTaskId(2L);
        List<TaskSchedulerWithBLOBs> mockRecords = List.of(record1, record2, record3);

        // Mock mapper behavior
        when(schedulerMapper.selectByExampleWithBLOBs(any(TaskSchedulerCriteria.class)))
                .thenReturn(mockRecords);

        // Execute method
        List<TaskSchedulerWithBLOBs> result = taskSchedulerService.queryRunningScheduler(taskIds);

        // Verify results - should keep the first record for duplicate taskId
        assertNotNull(result);
        assertEquals(2, result.size()); // Should have 2 unique taskIds
        Map<Long, TaskSchedulerWithBLOBs> resultMap = result.stream()
                .collect(Collectors.toMap(TaskScheduler::getTaskId, Function.identity()));
        assertEquals(1L, resultMap.get(1L).getId()); // Should keep record with id=1
        assertEquals(3L, resultMap.get(2L).getId());
    }

    @Test
    void queryRunningSchedulerShouldFilterByRunningStatus() {
        // Prepare test data
        List<Long> taskIds = List.of(1L, 2L, 3L);
        TaskSchedulerWithBLOBs record1 = new TaskSchedulerWithBLOBs();
        record1.setId(1L);
        record1.setTaskId(1L);
        record1.setStatus(TaskExecStatusEnum.RUNNING.getCode());
        TaskSchedulerWithBLOBs record2 = new TaskSchedulerWithBLOBs();
        record2.setId(2L);
        record2.setTaskId(2L);
        record2.setStatus(TaskExecStatusEnum.SUCCESS.getCode()); // Not running
        List<TaskSchedulerWithBLOBs> mockRecords = List.of(record1, record2);

        // Mock mapper behavior
        when(schedulerMapper.selectByExampleWithBLOBs(any(TaskSchedulerCriteria.class)))
                .thenReturn(mockRecords);

        // Execute method
        List<TaskSchedulerWithBLOBs> result = taskSchedulerService.queryRunningScheduler(taskIds);

        // Verify results - should only include running records
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1L, result.get(0).getTaskId());
    }

    @Test
    void queryLatestFinishedRecordShouldReturnNullWhenNoRecordsFound() {
        // Setup
        Long taskId = 1L;
        when(schedulerMapper.selectByExample(any(TaskSchedulerCriteria.class)))
                .thenReturn(Collections.emptyList());

        // Execute
        TaskScheduler result = taskSchedulerService.queryLatestFinishedRecord(taskId);

        // Verify
        assertNull(result);
        verify(schedulerMapper).selectByExample(any(TaskSchedulerCriteria.class));
    }

    @Test
    void queryLatestFinishedRecordShouldReturnLatestRecordWhenFound() {
        // Setup
        Long taskId = 1L;
        TaskScheduler expectedRecord = new TaskScheduler();
        expectedRecord.setId(1L);
        expectedRecord.setTaskId(taskId);
        expectedRecord.setStatus(TaskExecStatusEnum.SUCCESS.getCode());

        when(schedulerMapper.selectByExample(any(TaskSchedulerCriteria.class)))
                .thenReturn(List.of(expectedRecord));

        // Execute
        TaskScheduler result = taskSchedulerService.queryLatestFinishedRecord(taskId);

        // Verify
        assertNotNull(result);
        assertEquals(expectedRecord, result);
        assertEquals(taskId, result.getTaskId());
        assertEquals(TaskExecStatusEnum.SUCCESS.getCode(), result.getStatus());
        verify(schedulerMapper).selectByExample(any(TaskSchedulerCriteria.class));
    }

    @Test
    void queryLatestFinishedRecordShouldReturnFirstRecordWhenMultipleFound() {
        // Setup
        Long taskId = 1L;
        TaskScheduler expectedRecord = new TaskScheduler();
        expectedRecord.setId(1L);
        expectedRecord.setTaskId(taskId);
        expectedRecord.setStatus(TaskExecStatusEnum.SUCCESS.getCode());

        TaskScheduler anotherRecord = new TaskScheduler();
        anotherRecord.setId(2L);
        anotherRecord.setTaskId(taskId);
        anotherRecord.setStatus(TaskExecStatusEnum.SUCCESS.getCode());

        when(schedulerMapper.selectByExample(any(TaskSchedulerCriteria.class)))
                .thenReturn(List.of(expectedRecord, anotherRecord));

        // Execute
        TaskScheduler result = taskSchedulerService.queryLatestFinishedRecord(taskId);

        // Verify
        assertNotNull(result);
        assertEquals(expectedRecord.getId(), result.getId());
        verify(schedulerMapper).selectByExample(any(TaskSchedulerCriteria.class));
    }

    @Test
    void queryTaskSchedulerShouldReturnEmptyMapWhenInputIsEmpty() {
        Map<Long, TaskSchedulerWithBLOBs> result = taskSchedulerService.queryTaskScheduler(Collections.emptyList());
        assertTrue(result.isEmpty());
    }

    @Test
    void queryTaskSchedulerShouldReturnEmptyMapWhenNoRecordsFound() {
        when(schedulerMapper.queryLatestByTaskIds(anyList())).thenReturn(Collections.emptyList());
        
        Map<Long, TaskSchedulerWithBLOBs> result = taskSchedulerService.queryTaskScheduler(List.of(1L, 2L));
        assertTrue(result.isEmpty());
    }

    @Test
    void queryTaskSchedulerShouldReturnMappedRecords() {
        // Prepare test data
        TaskSchedulerWithBLOBs record1 = new TaskSchedulerWithBLOBs();
        record1.setId(1L);
        record1.setTaskId(101L);
        TaskSchedulerWithBLOBs record2 = new TaskSchedulerWithBLOBs();
        record2.setId(2L);
        record2.setTaskId(102L);
        
        // Mock mapper behavior
        when(schedulerMapper.queryLatestByTaskIds(List.of(101L, 102L)))
            .thenReturn(List.of(record1, record2));
        when(schedulerMapper.selectByExampleWithBLOBs(any()))
            .thenReturn(List.of(record1, record2));
    
        // Execute method
        Map<Long, TaskSchedulerWithBLOBs> result = taskSchedulerService.queryTaskScheduler(List.of(101L, 102L));
    
        // Verify results
        assertEquals(2, result.size());
        assertTrue(result.containsKey(101L));
        assertTrue(result.containsKey(102L));
    }

    @Test
    void queryTaskSchedulerShouldFilterNullAndZeroIds() {
        // Prepare test data
        TaskSchedulerWithBLOBs record = new TaskSchedulerWithBLOBs();
        record.setId(1L);
        record.setTaskId(101L);
        
        // Mock mapper behavior
        when(schedulerMapper.queryLatestByTaskIds(List.of(101L)))
            .thenReturn(List.of(record));
        when(schedulerMapper.selectByExampleWithBLOBs(any()))
            .thenReturn(List.of(record));
    
        // Execute method with mixed valid and invalid inputs
        Map<Long, TaskSchedulerWithBLOBs> result = taskSchedulerService.queryTaskScheduler(List.of(0L, 101L));
    
        // Verify results
        assertEquals(1, result.size());
        assertTrue(result.containsKey(101L));
    }

}