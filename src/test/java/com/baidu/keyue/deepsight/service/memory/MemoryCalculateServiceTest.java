package com.baidu.keyue.deepsight.service.memory;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.google.common.collect.Lists;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.enums.*;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.memory.cal.MemoryCalculateContext;
import com.baidu.keyue.deepsight.mysqldb.entity.*;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendMemoryExtractMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

@MockitoSettings(strictness = Strictness.LENIENT)
@ExtendWith(MockitoExtension.class)
public class MemoryCalculateServiceTest {

    @Mock
    private ExtendMemoryExtractMapper memoryExtractMapper;

    @Mock
    private DataTableInfoMapper dataTableInfoMapper;

    @Mock
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Mock
    private TaskInfoService taskInfoService;

    @Mock
    private TaskSchedulerService taskSchedulerService;

    @InjectMocks
    private MemoryCalculateService memoryCalculateService;

    private MemoryExtractWithBLOBs memoryExtract;

    @Mock
    private RuleManagerService ruleManagerService;

    @Mock
    private DorisService dorisService;

    private TaskInfo taskInfo;

    @BeforeEach
    void setUp() {
        memoryExtract = new MemoryExtractWithBLOBs();
        memoryExtract.setTask(1L);
        memoryExtract.setTriggerMod(TriggerModeEnum.REALTIME.getCode());
        memoryExtract.setDel(DelEnum.NOT_DELETED.getBoolean());
        memoryExtract.setStatus(SwitchEnum.ON.getBoolean());

        taskInfo = new TaskInfo();
        taskInfo.setId(1L);
        taskInfo.setTaskType(TaskTypeEnum.MEMORY_EXTRACT.getCode());
    }

    @Test
    void pullWaitExecMemoryTaskRealtimeModeEmptyList() {
        when(memoryExtractMapper.selectByExampleWithBLOBs(any(MemoryExtractCriteria.class)))
                .thenReturn(Collections.emptyList());

        List<Pair<MemoryExtractWithBLOBs, TaskInfo>> result =
                memoryCalculateService.pullWaitExecMemoryTask(TriggerModeEnum.REALTIME);

        assertTrue(result.isEmpty());
        verify(taskInfoService, never()).getTaskDetailWithIds(any(), any());
    }

    @Test
    void pullWaitExecMemoryTaskRealtimeModeWithData() {
        when(memoryExtractMapper.selectByExampleWithBLOBs(any(MemoryExtractCriteria.class)))
                .thenReturn(List.of(memoryExtract));
        when(taskInfoService.getTaskDetailWithIds(any(TaskTypeEnum.class), anyList()))
                .thenReturn(List.of(taskInfo));

        List<Pair<MemoryExtractWithBLOBs, TaskInfo>> result =
                memoryCalculateService.pullWaitExecMemoryTask(TriggerModeEnum.REALTIME);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(memoryExtract, result.get(0).getLeft());
        assertEquals(taskInfo, result.get(0).getRight());
    }

    @Test
    void pullWaitExecMemoryTaskCronModeEmptyList() {
        when(taskInfoService.pullWaitExecTask(eq(TaskTypeEnum.MEMORY_EXTRACT), any()))
                .thenReturn(Collections.emptyList());

        List<Pair<MemoryExtractWithBLOBs, TaskInfo>> result =
                memoryCalculateService.pullWaitExecMemoryTask(TriggerModeEnum.CRON);

        assertTrue(result.isEmpty());
        verify(memoryExtractMapper, never()).selectByExampleWithBLOBs(any());
    }

    @Test
    void pullWaitExecMemoryTaskCronModeWithData() {
        when(taskInfoService.pullWaitExecTask(eq(TaskTypeEnum.MEMORY_EXTRACT), any(TriggerModeEnum.class)))
                .thenReturn(List.of(taskInfo));
        when(memoryExtractMapper.selectByExampleWithBLOBs(any(MemoryExtractCriteria.class)))
                .thenReturn(List.of(memoryExtract));

        List<Pair<MemoryExtractWithBLOBs, TaskInfo>> result =
                memoryCalculateService.pullWaitExecMemoryTask(TriggerModeEnum.CRON);

        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(memoryExtract, result.get(0).getLeft());
        assertEquals(taskInfo, result.get(0).getRight());
    }

    @Test
    void onFinishedUpdatesStatusAndCount() {
        MemoryCalculateContext context = new MemoryCalculateContext();
        memoryExtract.setId(1L);
        memoryExtract.setCalStatus(TaskExecStatusEnum.SUCCESS.getCode());
        context.setMemory(memoryExtract);
        context.setCount(10);
        context.setExecId(1L);
        ;

        memoryCalculateService.onFinished(context, taskInfo);

        verify(memoryExtractMapper).updateCalTaskStatus(anyLong(), anyByte());
        verify(taskSchedulerService).updateScheduler(any(MemoryCalculateContext.class), any(TaskExecStatusEnum.class));
        assertEquals(TaskExecStatusEnum.SUCCESS.getCode(), memoryExtract.getCalStatus());
    }

    @Test
    void execBySchedulerWithTableInfoNotFound() {
        memoryExtract.setDataTableId(1L);
        memoryExtract.setFieldId(1L);
        MemoryCalculateContext context = new MemoryCalculateContext();
        context.setMemory(memoryExtract);
        context.setErr(new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.INTERNAL_ERROR, "数据源不存在"));

        when(memoryCalculateService.getDataSetTableDetail(anyLong())).thenReturn(null);

        memoryCalculateService.execByScheduler(memoryExtract, taskInfo);

        assertTrue(Objects.nonNull(context.getErr()));
    }

    @Test
    void onProcessingUpdatesStatus() {
        MemoryCalculateContext context = new MemoryCalculateContext();
        context.setMemory(memoryExtract);
        memoryExtract.setCalStatus(TaskExecStatusEnum.RUNNING.getCode());

        memoryCalculateService.onProcessing(context);

        assertEquals(TaskExecStatusEnum.RUNNING.getCode(), memoryExtract.getCalStatus());
    }

    @Test
    void onFailedUpdatesStatus() {
        MemoryExtractWithBLOBs memoryExtract = new MemoryExtractWithBLOBs();
        memoryExtract.setId(1L);
        memoryExtract.setTask(1L);
        memoryExtract.setTriggerMod(TriggerModeEnum.CRON.getCode());
        memoryExtract.setDel(DelEnum.NOT_DELETED.getBoolean());
        memoryExtract.setStatus(SwitchEnum.ON.getBoolean());
        memoryExtract.setCalStatus(TaskExecStatusEnum.FAILED.getCode());

        MemoryCalculateContext context = new MemoryCalculateContext();
        context.setMemory(memoryExtract);
        context.setErr(new DeepSightException.MemoryExtractOperatorFailedException(ErrorCode.INTERNAL_ERROR, "Test error"));

        memoryCalculateService.onFailed(context);

        verify(memoryExtractMapper).updateCalTaskStatus(eq(1L), eq(TaskExecStatusEnum.FAILED.getCode()));
//        verify(memoryExtractMapper).updateByPrimaryKeySelective(memoryExtract);
        assertEquals(TaskExecStatusEnum.FAILED.getCode(), memoryExtract.getCalStatus());
    }

    @Test
    void execRightNowSuccess() {
        memoryExtract.setDataTableId(1L);
        memoryExtract.setFieldId(1L);
        when(memoryExtractMapper.selectByPrimaryKey(eq(1L))).thenReturn(memoryExtract);
        when(taskInfoService.getTaskDetailWithId(eq(1L))).thenReturn(taskInfo);
        DataTableInfo tableInfo = new DataTableInfo();
        tableInfo.setTableName("aiob_conversation_record_111");
        when(dataTableInfoMapper.selectByPrimaryKey(eq(1L))).thenReturn(tableInfo);

        when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(Collections.emptyList());

        memoryCalculateService.execRightNow(1L);

        verify(memoryExtractMapper).selectByPrimaryKey(1L);
        verify(taskInfoService).getTaskDetailWithId(1L);
    }

    @Test
    void execByScheduler_RealtimeMode_Success() {
    // Prepare test data
    memoryExtract.setTriggerMod(TriggerModeEnum.REALTIME.getCode());
    memoryExtract.setDataFilterRule("[]");
    memoryExtract.setDataTableId(1L);
    memoryExtract.setFieldId(1L);
    
    DataTableInfo tableInfo = new DataTableInfo();
    tableInfo.setTableName("test_table");
    TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
    
    // Mock dependencies
    when(memoryCalculateService.getDataSetTableDetail(anyLong())).thenReturn(tableInfo);
    when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(Lists.newArrayList(fieldMetaInfo));
    when(taskSchedulerService.newTaskScheduler(anyLong(), anyString())).thenReturn(1L);
    DqlParseResult dqlParseResult = mock(DqlParseResult.class);
    when(ruleManagerService.parseRuleNode(any(RuleNode.class))).thenReturn(dqlParseResult);
    when(dqlParseResult.parseDorisSql()).thenReturn("SELECT * FROM DB");
    when(dorisService.selectList(anyString())).thenReturn(Collections.emptyList());
    
    // Execute
    memoryCalculateService.execByScheduler(memoryExtract, taskInfo);
    
    // Verify
    verify(memoryExtractMapper).updateCalTaskStatus(eq(memoryExtract.getId()), eq(TaskExecStatusEnum.RUNNING.getCode()));
    verify(memoryExtractMapper).updateCalTaskStatus(eq(memoryExtract.getId()), eq(TaskExecStatusEnum.SUCCESS.getCode()));
    verify(taskInfoService, never()).updateNextCalDate(any());
    }

    @Test
    void execByScheduler_CronMode_Success() {
    // Prepare test data
    memoryExtract.setTriggerMod(TriggerModeEnum.CRON.getCode());
    memoryExtract.setDataFilterRule("[]");
    memoryExtract.setDataTableId(1L);
    memoryExtract.setFieldId(1L);
    
    DataTableInfo tableInfo = new DataTableInfo();
    tableInfo.setTableName("test_table");
    TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
    
    // Mock dependencies
    when(memoryCalculateService.getDataSetTableDetail(anyLong())).thenReturn(tableInfo);
    when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(Lists.newArrayList(fieldMetaInfo));
    when(taskSchedulerService.newTaskScheduler(anyLong(), anyString())).thenReturn(1L);

    DqlParseResult dqlParseResult = mock(DqlParseResult.class);
    when(ruleManagerService.parseRuleNode(any(RuleNode.class))).thenReturn(dqlParseResult);
    when(dqlParseResult.parseDorisSql()).thenReturn("SELECT * FROM DB");
    when(dorisService.selectList(anyString())).thenReturn(Collections.emptyList());
    
    // Execute
    memoryCalculateService.execByScheduler(memoryExtract, taskInfo);
    
    // Verify
    verify(memoryExtractMapper).updateCalTaskStatus(eq(memoryExtract.getId()), eq(TaskExecStatusEnum.RUNNING.getCode()));
    verify(memoryExtractMapper).updateCalTaskStatus(eq(memoryExtract.getId()), eq(TaskExecStatusEnum.SUCCESS.getCode()));
    verify(taskInfoService).updateNextCalDate(any());
    verify(taskSchedulerService).updateScheduler(any(), eq(TaskExecStatusEnum.RUNNING));
    verify(taskSchedulerService).updateScheduler(any(), eq(TaskExecStatusEnum.SUCCESS));
    }

    @Test
    void execByScheduler_TableInfoNull_Failure() {
    // Prepare test data
    memoryExtract.setTriggerMod(TriggerModeEnum.REALTIME.getCode());
    memoryExtract.setDataTableId(1L);
    
    // Mock dependencies
    when(memoryCalculateService.getDataSetTableDetail(anyLong())).thenReturn(null);
    
    // Execute
    memoryCalculateService.execByScheduler(memoryExtract, taskInfo);
    
    // Verify
    verify(memoryExtractMapper).updateCalTaskStatus(eq(memoryExtract.getId()), eq(TaskExecStatusEnum.FAILED.getCode()));
    verify(taskInfoService, never()).updateNextCalDate(any());
    }

    @Test
    void execByScheduler_FieldMetaInfoNull_Failure() {
    // Prepare test data
    memoryExtract.setTriggerMod(TriggerModeEnum.REALTIME.getCode());
    memoryExtract.setDataTableId(1L);
    memoryExtract.setFieldId(1L);
    
    DataTableInfo tableInfo = new DataTableInfo();
    
    // Mock dependencies
    when(memoryCalculateService.getDataSetTableDetail(anyLong())).thenReturn(tableInfo);
    when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(Lists.newArrayList());

    // Execute
    memoryCalculateService.execByScheduler(memoryExtract, taskInfo);
    
    // Verify
    verify(memoryExtractMapper).updateCalTaskStatus(eq(memoryExtract.getId()), eq(TaskExecStatusEnum.FAILED.getCode()));
    verify(taskInfoService, never()).updateNextCalDate(any());
    }

    @Test
    void execByScheduler_WithDataFilterRule_Success() {
    // Prepare test data
    memoryExtract.setTriggerMod(TriggerModeEnum.REALTIME.getCode());
    memoryExtract.setDataFilterRule("[{\"field\":\"test\",\"operator\":\"=\",\"value\":\"value\"}]");
    memoryExtract.setDataTableId(1L);
    memoryExtract.setFieldId(1L);
    
    DataTableInfo tableInfo = new DataTableInfo();
    tableInfo.setTableName("test_table");
    TableFieldMetaInfo fieldMetaInfo = new TableFieldMetaInfo();
    
    // Mock dependencies
    when(memoryCalculateService.getDataSetTableDetail(anyLong())).thenReturn(tableInfo);
    when(tableFieldMetaInfoMapper.selectByExample(any(TableFieldMetaInfoCriteria.class))).thenReturn(Lists.newArrayList(fieldMetaInfo));
    DqlParseResult dqlParseResult = mock(DqlParseResult.class);
    when(ruleManagerService.parseRuleNode(any(RuleNode.class))).thenReturn(dqlParseResult);
    when(dqlParseResult.parseDorisSql()).thenReturn("SELECT * FROM DB");
    when(dorisService.selectList(anyString())).thenReturn(Collections.emptyList());

    // Execute
    memoryCalculateService.execByScheduler(memoryExtract, taskInfo);
    
    // Verify
    verify(memoryExtractMapper).updateCalTaskStatus(eq(memoryExtract.getId()), eq(TaskExecStatusEnum.SUCCESS.getCode()));
    }

}