package com.baidu.keyue.deepsight.service.sop.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.sop.SopEdge;
import com.baidu.keyue.deepsight.models.sop.SopIntent;
import com.baidu.keyue.deepsight.models.sop.SopNode;
import com.baidu.keyue.deepsight.models.sop.SopNodeDailyDistribute;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class AiobSopMetricServiceTest {

    @InjectMocks
    private AiobSopMetricService aiobSopMetricService;

    @Mock
    private DorisService dorisService;

    @Test
    void wholeEdgeMetricShouldReturnEmptyListWhenQueryReturnsEmpty() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_edge_sql";
            ormUtilsMock.when(() -> ORMUtils.generateWholeEdgeMetric(expectedTableName, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());

            // Act
            List<SopEdge> result = aiobSopMetricService.wholeEdgeMetric(tenantId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void wholeEdgeMetricShouldReturnEmptyListWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_edge_sql";
            ormUtilsMock.when(() -> ORMUtils.generateWholeEdgeMetric(expectedTableName, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            List<SopEdge> result = aiobSopMetricService.wholeEdgeMetric(tenantId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void wholeEdgeMetricShouldReturnSortedEdgeListWhenQuerySuccess() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_edge_sql";
            ormUtilsMock.when(() -> ORMUtils.generateWholeEdgeMetric(expectedTableName, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            List<Map<String, Object>> mockRecords = new ArrayList<>();
            Map<String, Object> record1 = new HashMap<>();
            record1.put("from_node", "node1");
            record1.put("end_node", "node2");
            record1.put("edge_count", 10L);
            mockRecords.add(record1);

            Map<String, Object> record2 = new HashMap<>();
            record2.put("from_node", "node3");
            record2.put("end_node", "node4");
            record2.put("edge_count", 20L);
            mockRecords.add(record2);

            when(dorisService.selectList(expectedSql)).thenReturn(mockRecords);

            // Act
            List<SopEdge> result = aiobSopMetricService.wholeEdgeMetric(tenantId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertEquals(2, result.size());
            assertEquals("node3", result.get(0).getFromNodeId());
            assertEquals("node4", result.get(0).getEndNodeId());
            assertEquals(20L, result.get(0).getWeight());
            assertEquals("node1", result.get(1).getFromNodeId());
            assertEquals("node2", result.get(1).getEndNodeId());
            assertEquals(10L, result.get(1).getWeight());
        }
    }

    @Test
    void getNodeUvByTaskIdSQLShouldReturnDataWhenDorisServiceReturnsData() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_uv_sql";
            ormUtilsMock.when(() -> ORMUtils.generateNodeUVSQL(expectedTableName, taskId, startTime, endTime))
                    .thenReturn(expectedSql);

            List<Map<String, Object>> expectedResult = new ArrayList<>();
            expectedResult.add(new HashMap<>());
            when(dorisService.selectList(expectedSql)).thenReturn(expectedResult);

            // Act
            List<Map<String, Object>> result = aiobSopMetricService.getNodeUvByTaskIdSQL(tenantId, taskId, startTime, endTime);

            // Assert
            assertEquals(expectedResult, result);
            verify(dorisService).selectList(expectedSql);
        }
    }

    @Test
    void getNodeUvByTaskIdSQLShouldReturnEmptyListWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_uv_sql";
            ormUtilsMock.when(() -> ORMUtils.generateNodeUVSQL(expectedTableName, taskId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("DB error"));

            // Act
            List<Map<String, Object>> result = aiobSopMetricService.getNodeUvByTaskIdSQL(tenantId, taskId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void getNodeHangUpByTaskIdSQLShouldReturnDataWhenDorisServiceReturnsData() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        Date startTime = new Date();
        Date endTime = new Date();
        String expectedTableName = "aiob_sop_node_testTenant";
        String expectedSql = "generated_hangup_sql";
        List<Map<String, Object>> expectedResult = List.of(Map.of("node_id", "node1", "hangup_count", 5));

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            ormUtilsMock.when(() -> ORMUtils.generateNodeHangUpSQL(expectedTableName, taskId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenReturn(expectedResult);

            // Act
            List<Map<String, Object>> result = aiobSopMetricService.getNodeHangUpByTaskIdSQL(tenantId, taskId, startTime, endTime);

            // Assert
            assertEquals(expectedResult, result);
            tenantUtilsMock.verify(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId));
            ormUtilsMock.verify(() -> ORMUtils.generateNodeHangUpSQL(expectedTableName, taskId, startTime, endTime));
            verify(dorisService).selectList(expectedSql);
        }
    }

    @Test
    void getNodeHangUpByTaskIdSQLShouldReturnEmptyListWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        Date startTime = new Date();
        Date endTime = new Date();
        String expectedTableName = "aiob_sop_node_testTenant";
        String expectedSql = "generated_hangup_sql";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            ormUtilsMock.when(() -> ORMUtils.generateNodeHangUpSQL(expectedTableName, taskId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("DB error"));

            // Act
            List<Map<String, Object>> result = aiobSopMetricService.getNodeHangUpByTaskIdSQL(tenantId, taskId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
            tenantUtilsMock.verify(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId));
            ormUtilsMock.verify(() -> ORMUtils.generateNodeHangUpSQL(expectedTableName, taskId, startTime, endTime));
            verify(dorisService).selectList(expectedSql);
        }
    }

    @Test
    void getAllPreviewNodesShouldReturnEmptyListWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        String currNodeId = "node1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateGetAllFromNodesSQL(expectedTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            List<String> result = aiobSopMetricService.getAllPreviewNodes(tenantId, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void getAllFromNodesShouldReturnPreviewNodesListWhenQuerySucceeds() {
        // Arrange
        String tenantId = "testTenant";
        String currNodeId = "node1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();
        List<Map<String, Object>> mockRecords = List.of(
                Map.of("from_node", "nodeA"),
                Map.of("from_node", "nodeB")
        );

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateGetAllFromNodesSQL(expectedTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenReturn(mockRecords);

            // Act
            List<String> result = aiobSopMetricService.getAllPreviewNodes(tenantId, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertEquals(2, result.size());
            assertTrue(result.containsAll(List.of("nodeA", "nodeB")));
        }
    }

    @Test
    void getAllPreviewNodesShouldReturnEmptyListWhenNoRecordsFound() {
        // Arrange
        String tenantId = "testTenant";
        String currNodeId = "node1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateGetAllFromNodesSQL(expectedTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());

            // Act
            List<String> result = aiobSopMetricService.getAllPreviewNodes(tenantId, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void currNodeToForwardNodeEdgeMetricShouldReturnEmptyListWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        String currNodeId = "node1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_edge_sql";
            ormUtilsMock.when(() -> ORMUtils.generateCurrNodeToForwardNodeEdgeMetricSQL(
                            expectedTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            List<SopNode> result = aiobSopMetricService.currNodeToForwardNodeEdgeMetric(
                    tenantId, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void currNodeToForwardNodeEdgeMetricShouldReturnSortedNodesWhenQuerySuccess() {
        // Arrange
        String tenantId = "testTenant";
        String currNodeId = "node1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_edge_sql";
            ormUtilsMock.when(() -> ORMUtils.generateCurrNodeToForwardNodeEdgeMetricSQL(
                            expectedTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            List<Map<String, Object>> mockRecords = new ArrayList<>();
            Map<String, Object> record1 = new HashMap<>();
            record1.put("end_node", "node2");
            record1.put("node_uv", 100L);
            record1.put("node_pv", 200L);
            Map<String, Object> record2 = new HashMap<>();
            record2.put("end_node", "node3");
            record2.put("node_uv", 150L);
            record2.put("node_pv", 300L);
            mockRecords.add(record1);
            mockRecords.add(record2);

            when(dorisService.selectList(expectedSql)).thenReturn(mockRecords);

            // Act
            List<SopNode> result = aiobSopMetricService.currNodeToForwardNodeEdgeMetric(
                    tenantId, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertEquals(2, result.size());
            assertEquals("node3", result.get(0).getNodeId());
            assertEquals(300L, result.get(0).getPv());
            assertEquals(150L, result.get(0).getUv());
            assertEquals("node2", result.get(1).getNodeId());
            assertEquals(200L, result.get(1).getPv());
            assertEquals(100L, result.get(1).getUv());
        }
    }

    @Test
    void currNodeToForwardNodeEdgeMetricShouldReturnEmptyListWhenNoRecords() {
        // Arrange
        String tenantId = "testTenant";
        String currNodeId = "node1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_edge_sql";
            ormUtilsMock.when(() -> ORMUtils.generateCurrNodeToForwardNodeEdgeMetricSQL(
                            expectedTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());

            // Act
            List<SopNode> result = aiobSopMetricService.currNodeToForwardNodeEdgeMetric(
                    tenantId, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void previewNodeToCurrNodeEdgeMetricShouldReturnEmptyListWhenNoRecords() {
        // Arrange
        String tenantId = "testTenant";
        List<String> previewNodeIds = List.of("node1", "node2");
        String currNodeId = "currNode";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generatePreviewNodeToCurrNodeEdgeMetricSQL(
                            expectedTableName, previewNodeIds, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());

            // Act
            List<SopEdge> result = aiobSopMetricService.previewNodeToCurrNodeEdgeMetric(
                    tenantId, previewNodeIds, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void previewNodeToCurrNodeEdgeMetricShouldReturnEmptyListWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        List<String> previewNodeIds = List.of("node1", "node2");
        String currNodeId = "currNode";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generatePreviewNodeToCurrNodeEdgeMetricSQL(
                            expectedTableName, previewNodeIds, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            List<SopEdge> result = aiobSopMetricService.previewNodeToCurrNodeEdgeMetric(
                    tenantId, previewNodeIds, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void previewNodeToCurrNodeEdgeMetricShouldReturnSortedEdgesWhenQuerySuccess() {
        // Arrange
        String tenantId = "testTenant";
        List<String> previewNodeIds = List.of("node1", "node2");
        String currNodeId = "currNode";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generatePreviewNodeToCurrNodeEdgeMetricSQL(
                            expectedTableName, previewNodeIds, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            List<Map<String, Object>> mockRecords = List.of(
                    Map.of("from_node", "node1", "edge_count", 10L),
                    Map.of("from_node", "node2", "edge_count", 20L)
            );
            when(dorisService.selectList(expectedSql)).thenReturn(mockRecords);

            // Act
            List<SopEdge> result = aiobSopMetricService.previewNodeToCurrNodeEdgeMetric(
                    tenantId, previewNodeIds, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertEquals(2, result.size());
            assertEquals("node2", result.get(0).getFromNodeId());
            assertEquals(20L, result.get(0).getWeight());
            assertEquals("node1", result.get(1).getFromNodeId());
            assertEquals(10L, result.get(1).getWeight());
        }
    }

    @Test
    void dailyCurrNodeToForwardNodeEdgeMetricShouldReturnEmptyListWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        String currNodeId = "node1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_edge_sql";
            ormUtilsMock.when(() -> ORMUtils.generateDailyCurrNodeToForwardNodeEdgeMetricSQL(
                            expectedTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            List<SopNodeDailyDistribute> result = aiobSopMetricService.dailyCurrNodeToForwardNodeEdgeMetric(
                    tenantId, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void dailyCurrNodeToForwardNodeEdgeMetricShouldReturnSortedDistributions() {
        // Arrange
        String tenantId = "testTenant";
        String currNodeId = "node1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_edge_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPEdgeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_edge_sql";
            ormUtilsMock.when(() -> ORMUtils.generateDailyCurrNodeToForwardNodeEdgeMetricSQL(
                            expectedTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            List<Map<String, Object>> mockRecords = new ArrayList<>();
            Map<String, Object> record1 = new HashMap<>();
            record1.put("day", "2023-01-02");
            record1.put("end_node", "node2");
            record1.put("node_uv", 10L);
            record1.put("node_pv", 20L);
            mockRecords.add(record1);

            Map<String, Object> record2 = new HashMap<>();
            record2.put("day", "2023-01-01");
            record2.put("end_node", "node3");
            record2.put("node_uv", 5L);
            record2.put("node_pv", 15L);
            mockRecords.add(record2);

            Map<String, Object> record3 = new HashMap<>();
            record3.put("day", "2023-01-01");
            record3.put("end_node", "node1");
            record3.put("node_uv", 8L);
            record3.put("node_pv", 18L);
            mockRecords.add(record3);

            when(dorisService.selectList(expectedSql)).thenReturn(mockRecords);

            // Act
            List<SopNodeDailyDistribute> result = aiobSopMetricService.dailyCurrNodeToForwardNodeEdgeMetric(
                    tenantId, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertEquals(2, result.size());
            assertEquals("2023-01-01", result.get(0).getDate());
            assertEquals("2023-01-02", result.get(1).getDate());

            List<SopNode> day1Nodes = result.get(0).getNodes();
            assertEquals(2, day1Nodes.size());
            assertEquals("node1", day1Nodes.get(0).getNodeId());
            assertEquals("node3", day1Nodes.get(1).getNodeId());

            List<SopNode> day2Nodes = result.get(1).getNodes();
            assertEquals(1, day2Nodes.size());
            assertEquals("node2", day2Nodes.get(0).getNodeId());
        }
    }

    @Test
    void countSessionConnectedCallShouldReturnCountWhenSuccess() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String expectedTableName = "aiob_session_testTenant";
        String expectedSql = "generated_count_sql";
        long expectedCount = 10L;

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            tenantUtilsMock.when(() -> TenantUtils.generateAiobSessionTableName(tenantId))
                    .thenReturn(expectedTableName);
            ormUtilsMock.when(() -> ORMUtils.generateCountSessionConnectedCallSQL(expectedTableName, taskId, robotId, robotVer))
                    .thenReturn(expectedSql);
            when(dorisService.getCount(expectedSql)).thenReturn(expectedCount);

            // Act
            Long result = aiobSopMetricService.countSessionConnectedCall(tenantId, taskId, robotId, robotVer);

            // Assert
            assertEquals(expectedCount, result);
            verify(dorisService).getCount(expectedSql);
        }
    }

    @Test
    void countSessionConnectedCallShouldReturnZeroWhenExceptionOccurs() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String expectedTableName = "aiob_session_testTenant";
        String expectedSql = "generated_count_sql";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            tenantUtilsMock.when(() -> TenantUtils.generateAiobSessionTableName(tenantId))
                    .thenReturn(expectedTableName);
            ormUtilsMock.when(() -> ORMUtils.generateCountSessionConnectedCallSQL(expectedTableName, taskId, robotId, robotVer))
                    .thenReturn(expectedSql);
            when(dorisService.getCount(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            Long result = aiobSopMetricService.countSessionConnectedCall(tenantId, taskId, robotId, robotVer);

            // Assert
            assertEquals(0L, result);
            verify(dorisService).getCount(expectedSql);
        }
    }

    @Test
    void countAnalysedMetricSQLShouldReturnCountWhenSuccess() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        long expectedCount = 100L;

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSessionTableName = "aiob_sop_node_testTenant2";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSessionTableName(tenantId))
                    .thenReturn(expectedSessionTableName);

            String expectedSql = "generated_count_sql";
            ormUtilsMock.when(() -> ORMUtils.generateCountAnalysedMetricSQL(expectedSessionTableName, expectedTableName, taskId, robotId, robotVer))
                    .thenReturn(expectedSql);

            when(dorisService.getCount(expectedSql)).thenReturn(expectedCount);

            // Act
            Long result = aiobSopMetricService.countAnalysedMetricSQL(tenantId, taskId, robotId, robotVer);

            // Assert
            assertEquals(expectedCount, result);
        }
    }

    @Test
    void countAnalysedMetricSQLShouldReturnZeroWhenExceptionOccurs() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_count_sql";
            ormUtilsMock.when(() -> ORMUtils.generateCountAnalysedMetricSQL(expectedTableName, expectedTableName, taskId, robotId, robotVer))
                    .thenReturn(expectedSql);

            when(dorisService.getCount(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            Long result = aiobSopMetricService.countAnalysedMetricSQL(tenantId, taskId, robotId, robotVer);

            // Assert
            assertEquals(0L, result);
        }
    }

    @Test
    void dailyCurrNodeMetricShouldReturnEmptyMapWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        String currNodeId = "node1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateDailyCurrNodeMetric(expectedTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            Map<String, SopNode> result = aiobSopMetricService.dailyCurrNodeMetric(tenantId, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void dailyCurrNodeMetricShouldReturnMapWithSopNodesWhenDorisServiceReturnsRecords() {
        // Arrange
        String tenantId = "testTenant";
        String currNodeId = "node1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateDailyCurrNodeMetric(expectedTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            List<Map<String, Object>> mockRecords = new ArrayList<>();
            Map<String, Object> record1 = new HashMap<>();
            record1.put("day", "2023-01-01");
            record1.put("node_uv", 100L);
            record1.put("node_pv", 200L);
            mockRecords.add(record1);

            Map<String, Object> record2 = new HashMap<>();
            record2.put("day", "2023-01-02");
            record2.put("node_uv", 150L);
            record2.put("node_pv", 250L);
            mockRecords.add(record2);

            when(dorisService.selectList(expectedSql)).thenReturn(mockRecords);

            // Act
            Map<String, SopNode> result = aiobSopMetricService.dailyCurrNodeMetric(tenantId, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertEquals(2, result.size());
            assertTrue(result.containsKey("2023-01-01"));
            assertEquals(100L, result.get("2023-01-01").getUv());
            assertEquals(200L, result.get("2023-01-01").getPv());
            assertEquals(currNodeId, result.get("2023-01-01").getNodeId());

            assertTrue(result.containsKey("2023-01-02"));
            assertEquals(150L, result.get("2023-01-02").getUv());
            assertEquals(250L, result.get("2023-01-02").getPv());
            assertEquals(currNodeId, result.get("2023-01-02").getNodeId());
        }
    }

    @Test
    void dailyCurrNodeMetricShouldReturnEmptyMapWhenDorisServiceReturnsEmptyList() {
        // Arrange
        String tenantId = "testTenant";
        String currNodeId = "node1";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateDailyCurrNodeMetric(expectedTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());

            // Act
            Map<String, SopNode> result = aiobSopMetricService.dailyCurrNodeMetric(tenantId, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void wholeNodeHangupMetricShouldReturnEmptyMapWhenNoDataReturned() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateWholeNodeHangupMetric(expectedTableName, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());

            // Act
            Map<String, SopNode> result = aiobSopMetricService.wholeNodeHangupMetric(tenantId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void wholeNodeHangupMetricShouldReturnEmptyMapWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateWholeNodeHangupMetric(expectedTableName, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            Map<String, SopNode> result = aiobSopMetricService.wholeNodeHangupMetric(tenantId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void wholeNodeHangupMetricShouldReturnCorrectMapWhenDataExists() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateWholeNodeHangupMetric(expectedTableName, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            List<Map<String, Object>> mockRecords = new ArrayList<>();
            Map<String, Object> record1 = new HashMap<>();
            record1.put("node_id", "node1");
            record1.put("node_uv", 100L);
            record1.put("node_pv", 200L);
            mockRecords.add(record1);

            Map<String, Object> record2 = new HashMap<>();
            record2.put("node_id", "node2");
            record2.put("node_uv", 150L);
            record2.put("node_pv", 250L);
            mockRecords.add(record2);

            when(dorisService.selectList(expectedSql)).thenReturn(mockRecords);

            // Act
            Map<String, SopNode> result = aiobSopMetricService.wholeNodeHangupMetric(tenantId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertEquals(2, result.size());
            assertEquals(100L, result.get("node1").getUv());
            assertEquals(200L, result.get("node1").getPv());
            assertEquals(150L, result.get("node2").getUv());
            assertEquals(250L, result.get("node2").getPv());
        }
    }

    @Test
    void wholeIntentMetricShouldReturnEmptyListWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        List<String> endNodeIds = List.of("node1", "node2");
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_intent_sql";
            ormUtilsMock.when(() -> ORMUtils.generateWholeIntentMetric(expectedTableName, endNodeIds, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            List<SopIntent> result = aiobSopMetricService.wholeIntentMetric(tenantId, endNodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void wholeIntentMetricShouldReturnSortedListWhenDataIsValid() {
        // Arrange
        String tenantId = "testTenant";
        List<String> endNodeIds = List.of("node1", "node2");
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_intent_sql";
            ormUtilsMock.when(() -> ORMUtils.generateWholeIntentMetric(expectedTableName, endNodeIds, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            List<Map<String, Object>> mockRecords = new ArrayList<>();
            Map<String, Object> record1 = new HashMap<>();
            record1.put("node_id", "node1");
            record1.put("tag", "intent1");
            record1.put("node_pv", 100L);
            record1.put("node_uv", 50L);
            mockRecords.add(record1);

            Map<String, Object> record2 = new HashMap<>();
            record2.put("node_id", "node2");
            record2.put("tag", "intent2");
            record2.put("node_pv", 200L);
            record2.put("node_uv", 100L);
            mockRecords.add(record2);

            Map<String, Object> record3 = new HashMap<>();
            record3.put("node_id", "node3");
            record3.put("tag", "");
            record3.put("node_pv", 300L);
            record3.put("node_uv", 150L);
            mockRecords.add(record3);

            when(dorisService.selectList(expectedSql)).thenReturn(mockRecords);

            // Act
            List<SopIntent> result = aiobSopMetricService.wholeIntentMetric(tenantId, endNodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertEquals(2, result.size());
            assertEquals("intent2", result.get(0).getIntent());
            assertEquals(200L, result.get(0).getPv());
            assertEquals("intent1", result.get(1).getIntent());
            assertEquals(100L, result.get(1).getPv());
        }
    }

    @Test
    void wholeIntentMetricShouldReturnEmptyListWhenNoValidIntents() {
        // Arrange
        String tenantId = "testTenant";
        List<String> endNodeIds = List.of("node1", "node2");
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_intent_sql";
            ormUtilsMock.when(() -> ORMUtils.generateWholeIntentMetric(expectedTableName, endNodeIds, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            List<Map<String, Object>> mockRecords = new ArrayList<>();
            Map<String, Object> record1 = new HashMap<>();
            record1.put("node_id", "node1");
            record1.put("tag", "");
            record1.put("node_pv", 100L);
            record1.put("node_uv", 50L);
            mockRecords.add(record1);

            when(dorisService.selectList(expectedSql)).thenReturn(mockRecords);

            // Act
            List<SopIntent> result = aiobSopMetricService.wholeIntentMetric(tenantId, endNodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void nodeMetricWithIdsShouldReturnDefaultWhenNodeIdsIsEmpty() {
        // Arrange
        String tenantId = "testTenant";
        List<String> emptyNodeIds = Collections.emptyList();
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        // Act
        Long[] result = aiobSopMetricService.nodeMetricWithIds(tenantId, emptyNodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);

        // Assert
        assertArrayEquals(new Long[]{0L, 0L}, result);
    }

    @Test
    void nodeMetricWithIdsShouldReturnDefaultWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        List<String> nodeIds = List.of("node1", "node2");
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateNodeMetricWithIdsSQL(expectedTableName, nodeIds, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            Long[] result = aiobSopMetricService.nodeMetricWithIds(tenantId, nodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertArrayEquals(new Long[]{0L, 0L}, result);
        }
    }

    @Test
    void nodeMetricWithIdsShouldReturnDefaultWhenRecordListIsEmpty() {
        // Arrange
        String tenantId = "testTenant";
        List<String> nodeIds = List.of("node1", "node2");
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateNodeMetricWithIdsSQL(expectedTableName, nodeIds, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());

            // Act
            Long[] result = aiobSopMetricService.nodeMetricWithIds(tenantId, nodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertArrayEquals(new Long[]{0L, 0L}, result);
        }
    }

    @Test
    void nodeMetricWithIdsShouldReturnMetricsWhenRecordListHasData() {
        // Arrange
        String tenantId = "testTenant";
        List<String> nodeIds = List.of("node1", "node2");
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateNodeMetricWithIdsSQL(expectedTableName, nodeIds, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            Map<String, Object> record = new HashMap<>();
            record.put("node_uv", 100L);
            record.put("node_pv", 200L);
            when(dorisService.selectList(expectedSql)).thenReturn(List.of(record));

            // Act
            Long[] result = aiobSopMetricService.nodeMetricWithIds(tenantId, nodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertArrayEquals(new Long[]{100L, 200L}, result);
        }
    }

    @Test
    void flexibleIdTransferShouldReturnRobotIdAndVersionWhenQuerySuccess() {
        // Arrange
        String tenantId = "testTenant";
        String agentId = "agent1";
        String versionId = "v1";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_debug_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobDebugTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "flexible_id_transfer_sql";
            ormUtilsMock.when(() -> ORMUtils.generateFlexibleIdTransferSql(expectedTableName, agentId, versionId))
                    .thenReturn(expectedSql);

            Map<String, Object> mockRecord = new HashMap<>();
            mockRecord.put("robot_id", "robot1");
            mockRecord.put("robot_ver", "v1.0");
            List<Map<String, Object>> mockResult = Collections.singletonList(mockRecord);
            when(dorisService.selectList(expectedSql)).thenReturn(mockResult);

            // Act
            List<String> result = aiobSopMetricService.flexibleIdTransfer(tenantId, agentId, versionId);

            // Assert
            assertEquals(2, result.size());
            assertEquals("robot1", result.get(0));
            assertEquals("v1.0", result.get(1));
        }
    }

    @Test
    void flexibleIdTransferShouldReturnEmptyListWhenQueryThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        String agentId = "agent1";
        String versionId = "v1";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_debug_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobDebugTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "flexible_id_transfer_sql";
            ormUtilsMock.when(() -> ORMUtils.generateFlexibleIdTransferSql(expectedTableName, agentId, versionId))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("DB error"));

            // Act
            List<String> result = aiobSopMetricService.flexibleIdTransfer(tenantId, agentId, versionId);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void flexibleIdTransferShouldReturnEmptyListWhenQueryResultIsEmpty() {
        // Arrange
        String tenantId = "testTenant";
        String agentId = "agent1";
        String versionId = "v1";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_debug_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobDebugTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "flexible_id_transfer_sql";
            ormUtilsMock.when(() -> ORMUtils.generateFlexibleIdTransferSql(expectedTableName, agentId, versionId))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());

            // Act
            List<String> result = aiobSopMetricService.flexibleIdTransfer(tenantId, agentId, versionId);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void checkTaskSceneTypeShouldReturnNullWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_session_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSessionTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateQueryTaskScene(expectedTableName, taskId))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            Integer result = aiobSopMetricService.checkTaskSceneType(tenantId, taskId);

            // Assert
            assertNull(result);
        }
    }

    @Test
    void checkTaskSceneTypeShouldReturnNullWhenRecordListIsEmpty() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_session_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSessionTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateQueryTaskScene(expectedTableName, taskId))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());

            // Act
            Integer result = aiobSopMetricService.checkTaskSceneType(tenantId, taskId);

            // Assert
            assertNull(result);
        }
    }

    @Test
    void checkTaskSceneTypeShouldReturnRobotSceneWhenRecordExists() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        Integer expectedScene = 1;

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_session_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSessionTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateQueryTaskScene(expectedTableName, taskId))
                    .thenReturn(expectedSql);

            Map<String, Object> record = new HashMap<>();
            record.put("robotScene", expectedScene);
            when(dorisService.selectList(expectedSql)).thenReturn(List.of(record));

            // Act
            Integer result = aiobSopMetricService.checkTaskSceneType(tenantId, taskId);

            // Assert
            assertEquals(expectedScene, result);
        }
    }

    @Test
    void countFlexibleSessionConnectedCallShouldReturnCountWhenSuccess() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedSessionTable = "aiob_session_testTenant";
            String expectedDebugTable = "aiob_debug_testTenant";
            String expectedSql = "generated_count_sql";
            long expectedCount = 10L;

            tenantUtilsMock.when(() -> TenantUtils.generateAiobSessionTableName(tenantId))
                    .thenReturn(expectedSessionTable);
            tenantUtilsMock.when(() -> TenantUtils.generateAiobDebugTableName(tenantId))
                    .thenReturn(expectedDebugTable);

            ormUtilsMock.when(() -> ORMUtils.generateCountFlexibleSessionConnectedCallSQL(
                            expectedSessionTable, expectedDebugTable, taskId, robotId, robotVer))
                    .thenReturn(expectedSql);

            when(dorisService.getCount(expectedSql)).thenReturn(expectedCount);

            // Act
            Long result = aiobSopMetricService.countFlexibleSessionConnectedCall(tenantId, taskId, robotId, robotVer);

            // Assert
            assertEquals(expectedCount, result);
        }
    }

    @Test
    void countFlexibleSessionConnectedCallShouldReturnZeroWhenExceptionOccurs() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedSessionTable = "aiob_session_testTenant";
            String expectedDebugTable = "aiob_debug_testTenant";
            String expectedSql = "generated_count_sql";

            tenantUtilsMock.when(() -> TenantUtils.generateAiobSessionTableName(tenantId))
                    .thenReturn(expectedSessionTable);
            tenantUtilsMock.when(() -> TenantUtils.generateAiobDebugTableName(tenantId))
                    .thenReturn(expectedDebugTable);

            ormUtilsMock.when(() -> ORMUtils.generateCountFlexibleSessionConnectedCallSQL(
                            expectedSessionTable, expectedDebugTable, taskId, robotId, robotVer))
                    .thenReturn(expectedSql);

            when(dorisService.getCount(expectedSql)).thenThrow(new RuntimeException("DB error"));

            // Act
            Long result = aiobSopMetricService.countFlexibleSessionConnectedCall(tenantId, taskId, robotId, robotVer);

            // Assert
            assertEquals(0L, result);
        }
    }

    @Test
    void countFlexibleProcessedCallShouldReturnCountWhenSuccessful() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedSessionTable = "aiob_session_testTenant";
            String expectedNodeTable = "aiob_sop_node_testTenant";
            String expectedSql = "generated_sql";
            long expectedCount = 10L;

            tenantUtilsMock.when(() -> TenantUtils.generateAiobSessionTableName(tenantId))
                    .thenReturn(expectedSessionTable);
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedNodeTable);

            ormUtilsMock.when(() -> ORMUtils.generateCountFlexibleProcessedCallSQL(
                            expectedSessionTable, expectedNodeTable, taskId, robotId, robotVer))
                    .thenReturn(expectedSql);

            when(dorisService.getCount(expectedSql)).thenReturn(expectedCount);

            // Act
            Long result = aiobSopMetricService.countFlexibleProcessedCall(tenantId, taskId, robotId, robotVer);

            // Assert
            assertEquals(expectedCount, result);
        }
    }

    @Test
    void countFlexibleProcessedCallShouldReturnZeroWhenExceptionOccurs() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedSessionTable = "aiob_session_testTenant";
            String expectedNodeTable = "aiob_sop_node_testTenant";
            String expectedSql = "generated_sql";

            tenantUtilsMock.when(() -> TenantUtils.generateAiobSessionTableName(tenantId))
                    .thenReturn(expectedSessionTable);
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedNodeTable);

            ormUtilsMock.when(() -> ORMUtils.generateCountFlexibleProcessedCallSQL(
                            expectedSessionTable, expectedNodeTable, taskId, robotId, robotVer))
                    .thenReturn(expectedSql);

            when(dorisService.getCount(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            Long result = aiobSopMetricService.countFlexibleProcessedCall(tenantId, taskId, robotId, robotVer);

            // Assert
            assertEquals(0L, result);
        }
    }

    @Test
    void getAgentIdSQLShouldReturnExpectedResultWhenDorisServiceReturnsData() {
        // Arrange
        String tenantId = "testTenant";
        String sessionId = "session1";
        String nodeId = "node1";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_debug_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobDebugTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateQueryAgentIdBySQL(expectedTableName, sessionId, nodeId))
                    .thenReturn(expectedSql);

            List<Map<String, Object>> expectedResult = new ArrayList<>();
            Map<String, Object> mockData = new HashMap<>();
            mockData.put("agent_id", "agent1");
            expectedResult.add(mockData);

            when(dorisService.selectList(expectedSql)).thenReturn(expectedResult);

            // Act
            List<Map<String, Object>> result = aiobSopMetricService.getAgentIdSQL(tenantId, sessionId, nodeId);

            // Assert
            assertEquals(expectedResult, result);
            verify(dorisService).selectList(expectedSql);
        }
    }

    @Test
    void getAgentIdSQLShouldReturnEmptyListWhenDorisServiceThrowsException() {
        // Arrange
        String tenantId = "testTenant";
        String sessionId = "session1";
        String nodeId = "node1";

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_debug_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobDebugTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_sql";
            ormUtilsMock.when(() -> ORMUtils.generateQueryAgentIdBySQL(expectedTableName, sessionId, nodeId))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("Mock DB error"));

            // Act
            List<Map<String, Object>> result = aiobSopMetricService.getAgentIdSQL(tenantId, sessionId, nodeId);

            // Assert
            assertTrue(result.isEmpty());
            verify(dorisService).selectList(expectedSql);
        }
    }

    @Test
    void wholeNodeMetricShouldReturnEmptyListWhenQueryReturnsEmpty() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_node_sql";
            ormUtilsMock.when(() -> ORMUtils.generateWholeNodeMetric(expectedTableName, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenReturn(Collections.emptyList());

            // Act
            List<SopNode> result = aiobSopMetricService.wholeNodeMetric(tenantId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void wholeNodeMetricShouldReturnSortedNodesWhenQueryReturnsValidData() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_node_sql";
            ormUtilsMock.when(() -> ORMUtils.generateWholeNodeMetric(expectedTableName, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            List<Map<String, Object>> mockRecords = new ArrayList<>();
            Map<String, Object> record1 = new HashMap<>();
            record1.put("node_id", "node1");
            record1.put("node_pv", 100L);
            record1.put("node_uv", 50L);
            mockRecords.add(record1);

            Map<String, Object> record2 = new HashMap<>();
            record2.put("node_id", "node2");
            record2.put("node_pv", 200L);
            record2.put("node_uv", 100L);
            mockRecords.add(record2);

            Map<String, Object> invalidRecord = new HashMap<>();
            invalidRecord.put("node_id", "");
            invalidRecord.put("node_pv", 300L);
            invalidRecord.put("node_uv", 150L);
            mockRecords.add(invalidRecord);

            when(dorisService.selectList(expectedSql)).thenReturn(mockRecords);

            // Act
            List<SopNode> result = aiobSopMetricService.wholeNodeMetric(tenantId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertEquals(2, result.size());
            assertEquals("node2", result.get(0).getNodeId());
            assertEquals(200L, result.get(0).getPv());
            assertEquals(100L, result.get(0).getUv());
            assertEquals("node1", result.get(1).getNodeId());
            assertEquals(100L, result.get(1).getPv());
            assertEquals(50L, result.get(1).getUv());
        }
    }

    @Test
    void wholeNodeMetricShouldReturnEmptyListWhenExceptionOccurs() {
        // Arrange
        String tenantId = "testTenant";
        String taskId = "task1";
        String robotId = "robot1";
        String robotVer = "v1";
        String topicId = "topic1";
        Date startTime = new Date();
        Date endTime = new Date();

        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<ORMUtils> ormUtilsMock = mockStatic(ORMUtils.class)) {

            String expectedTableName = "aiob_sop_node_testTenant";
            tenantUtilsMock.when(() -> TenantUtils.generateAiobSOPNodeTableName(tenantId))
                    .thenReturn(expectedTableName);

            String expectedSql = "generated_node_sql";
            ormUtilsMock.when(() -> ORMUtils.generateWholeNodeMetric(expectedTableName, taskId, robotId, robotVer, topicId, startTime, endTime))
                    .thenReturn(expectedSql);

            when(dorisService.selectList(expectedSql)).thenThrow(new RuntimeException("Database error"));

            // Act
            List<SopNode> result = aiobSopMetricService.wholeNodeMetric(tenantId, taskId, robotId, robotVer, topicId, startTime, endTime);

            // Assert
            assertTrue(result.isEmpty());
        }
    }

}