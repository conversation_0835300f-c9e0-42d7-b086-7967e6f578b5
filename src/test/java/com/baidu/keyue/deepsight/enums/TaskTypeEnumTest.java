package com.baidu.keyue.deepsight.enums;

import static org.junit.Assert.*;

import org.junit.Test;

public class TaskTypeEnumTest{

    // testGetDescByCodeValidCode 用于测试 getDescByCode
    // generated by Comate
    @Test
    public void testGetDescByCodeValidCode() {
        // Test with a valid code
        Byte validCode = 0;
        TaskTypeEnum result = TaskTypeEnum.getDescByCode(validCode);
        assertNotNull(result);
        assertEquals("贴源层数据任务", result.getDesc());
    }

    // testGetDescByCodeInvalidCode 用于测试 getDescByCode
    // generated by Comate
    @Test
    public void testGetDescByCodeInvalidCode() {
        // Test with an invalid code
        Byte invalidCode = 99;
        TaskTypeEnum result = TaskTypeEnum.getDescByCode(invalidCode);
        assertNull(result);
    }

}