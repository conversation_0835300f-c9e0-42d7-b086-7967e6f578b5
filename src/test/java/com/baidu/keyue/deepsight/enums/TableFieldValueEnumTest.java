package com.baidu.keyue.deepsight.enums;

import static org.junit.Assert.*;

import org.junit.Test;

public class TableFieldValueEnumTest{

    // testGetByValueType 用于测试 getByValueType
    // generated by Comate
    @Test
    public void testGetByValueType() {
        // Test with valid value types
        assertEquals(TableFieldValueEnum.NUMBER, TableFieldValueEnum.getByValueType("number"));
        assertEquals(TableFieldValueEnum.ENUM, TableFieldValueEnum.getByValueType("enum"));
        assertEquals(TableFieldValueEnum.TEXT, TableFieldValueEnum.getByValueType("text"));
    
        // Test with invalid value types
        assertNull(TableFieldValueEnum.getByValueType("invalid"));
    }

}