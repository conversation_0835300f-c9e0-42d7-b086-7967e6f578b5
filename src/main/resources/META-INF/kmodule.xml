<kmodule xmlns="http://www.drools.org/xsd/kmodule">
    <!--
        kbase 可以存在多个
        name: 指定kbase的名字，需要是唯一的
        packages: 包名，可以理解为到src/main/resources目录下查找这个包名下的规则文件,多个包使用逗号分割
        default: 当前kbase是否是默认的kbase
    -->
    <kbase name="filter-kabse" packages="com.baidu.rules" default="false">
        <!--
            ksession 可以存在多个
            name: 指定ksession 的名字，需要唯一
            defalut: 当前ksession在这个kbase下是否是默认的
            type: 指定当前ksession是否是有状态的 stateless表示是无状态的
        -->
        <ksession name="filter-ksession-stateful" default="false" type="stateful"/>
    </kbase>
</kmodule>