#!/usr/bin/env sh

CONTAINER_MEM_LIMIT=$(cat /sys/fs/cgroup/memory/memory.limit_in_bytes)
MAX_MEMORY=$(($CONTAINER_MEM_LIMIT * 70 / 100 / 1024 / 1024))m

bin=`pwd`/`dirname $script`
app='deep-sight-platform'

APPLICATION=${bin}${app}.jar
LOGBACK_FILE_PATH=logback-spring.xml
SPRING_CONFIG_FILE=application.yml

if [ "$JVM_DEBUG" = "debug" ];then
   JVM_OPTS="-Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=8977 ${JVM_OPTS}"
else
   JVM_OPTS="${JVM_OPTS}"
fi

java -server -Xms$MAX_MEMORY -Xmx$MAX_MEMORY -Xss360K -XX:MetaspaceSize=256m \
-Xlog:gc*,safepoint:gc.log:time,uptime:filecount=20,filesize=50M -Xlog:async \
-Dlogging.config=$LOGBACK_FILE_PATH \
-Dspring.config.location=$SPRING_CONFIG_FILE \
-javaagent:/home/<USER>/deep-sight-platform/rasp/boot.jar -Drasp.app.id=70c84751ba3800c5173cf0da18cabd0a0e0dbf64 -Drasp.user.tag=image_iregistry.baidu-int.com/deepsight/deep-sight-platform:icode_baidu/keyue-deep-sight/deep-sight-platform -Drasp.cloud.endpoint=http://rasp-cloud.baidubce.com:10088 \
-Dfile.encoding=UTF-8 ${JVM_OPTS} \
-XX:+UseZGC -XX:+UseCompressedOops -XX:+UseCompressedClassPointers \
-XX:+SegmentedCodeCache -XX:+ExplicitGCInvokesConcurrent \
-jar ${APPLICATION}
