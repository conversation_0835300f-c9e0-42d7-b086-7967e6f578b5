<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!--    配置要执行的sql文件，按先后顺序执行，每个迭代按照init，ddl，dml顺序配置执行顺序-->
    <!--    数据库初始化sql-->
    <!--    mvp version-->
    <include file="classpath:/db.changelog/mvp/V1_init.xml" relativeToChangelogFile="false"/>






</databaseChangeLog>