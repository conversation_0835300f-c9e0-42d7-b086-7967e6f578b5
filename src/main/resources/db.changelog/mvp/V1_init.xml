<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">

    <!--    配置要执行的sql文件，按先后顺序执行，每个迭代按照init，ddl，dml顺序配置执行顺序-->
    <!--    数据库初始化sql-->
    <!--    mvp version-->
    <changeSet id="1" author="luoweiwei01">
        <sql>
            CREATE TABLE IF NOT EXISTS `tenant_info` (
                                           `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                           `tenantId` varchar(255) NOT NULL COMMENT '租户id',
                                           `accountId` varchar(255) DEFAULT NULL COMMENT '账户id',
                                           `userName` varchar(255) DEFAULT NULL COMMENT '用户名称',
                                           `tenant_source` varchar(255) NOT NULL COMMENT '租户来源:login/aiob',
                                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                           `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                           `user_id` varchar(20) DEFAULT NULL,
                                           PRIMARY KEY (`id`),
                                           UNIQUE KEY `tenantId` (`tenantId`),
                                           UNIQUE KEY `accountId` (`accountId`),
                                           KEY `idx_tenantId` (`tenantId`),
                                           KEY `idx_userName` (`userName`)
            ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='租户信息表';
        </sql>
        <sql>
            CREATE TABLE IF NOT EXISTS `field_encry_config` (
                                                  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                  `table_en_name` varchar(255) NOT NULL COMMENT '数据表名称（加密）',
                                                  `en_field` varchar(255) NOT NULL COMMENT '数据字段英文名称（加密）',
                                                  `secret_key` varchar(255) NOT NULL COMMENT '加密密钥',
                                                  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                  `data_table_id` varchar(255) DEFAULT NULL COMMENT '数据表唯一id',
                                                  PRIMARY KEY (`id`),
                                                  KEY `idx_table_name` (`table_en_name`)
            ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='加密信息表';
        </sql>
        <sql>
            CREATE TABLE IF NOT EXISTS `datatable_meta_info` (
                                                   `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                                   `table_en_name` varchar(255) NOT NULL COMMENT '数据表名称',
                                                   `data_table_id` bigint(20) NOT NULL COMMENT '数据表唯一id',
                                                   `en_field` varchar(255) NOT NULL COMMENT '数据字段英文名称',
                                                   `cn_field` varchar(255) NOT NULL COMMENT '数据字段中文名称',
                                                   `field_type` varchar(50) NOT NULL COMMENT '数据类型',
                                                   `description` varchar(255) DEFAULT NULL COMMENT '数据描述',
                                                   `is_filter_criteria` tinyint(1) NOT NULL DEFAULT '0' COMMENT '作为筛选条件',
                                                   `is_required` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否必填 0非必填 1必填',
                                                   `is_secrete` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否加密',
                                                   `is_visable` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否可见 0：可见 1：不可见',
                                                   `field_tag` int(10) NOT NULL DEFAULT '0' COMMENT '字段标记 0：无 1：主键 2：度量 3：敏感 4：分区',
                                                   `config_infos` text COMMENT '配置信息',
                                                   `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                                   `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                                   `value_type` varchar(255) NOT NULL COMMENT '取值类型如string：enum/text',
                                                   `data_type` varchar(64) NOT NULL COMMENT '数据类型',
                                                   PRIMARY KEY (`id`),
                                                   KEY `idx_table_name` (`table_en_name`),
                                                   KEY `idx_table_id` (`data_table_id`),
                                                   KEY `idx_update_time` (`update_time`),
                                                   KEY `en_field_index` (`en_field`)
            ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='字段元数据信息表';
        </sql>
        <sql>
            CREATE TABLE IF NOT EXISTS
                `label` (
                            `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '标签id',
                            `user_id` VARCHAR(128) NOT NULL COMMENT '用户 ID',
                            `catalog_id` BIGINT(20) NOT NULL COMMENT '标签目录id',
                            `label_name` VARCHAR(128) NOT NULL COMMENT '标签名称',
                            `label_value_update_mod` tinyint(4) NOT NULL COMMENT '标签更新取值逻辑:0:每次重新计算,1:合并历史值',
                            `label_value_save_mod` tinyint(4) NOT NULL COMMENT '标签值保存类型:0:单值,1:多值',
                            `trigger_mod` tinyint(4) NOT NULL COMMENT '标签更新触发类型:0:定时触发,1:手动触发',
                            `trigger_frequency` tinyint(4) DEFAULT NULL COMMENT '执行频率:0:每天,1:每周,2:每月',
                            `trigger_frequency_value` VARCHAR(256) DEFAULT NULL COMMENT '执行频率json',
                            `label_rule` text NOT NULL COMMENT '标签值规则json',
                            `exec_mod` tinyint(4) DEFAULT '0' COMMENT '生产方式:0:业务规则,1:SQL,2:业务模式',
                            `field` BIGINT(20) DEFAULT NULL COMMENT '字段',
                            `distribution` text COMMENT '标签分布统计结果json',
                            `label_cal_status` tinyint(4) DEFAULT '0' COMMENT '标签计算状态: 0:待计算,1:计算中,2:计算成功,3:计算失败,4:计算取消',
                            `last_cal_date` TIMESTAMP NULL DEFAULT NULL COMMENT '上一次执行时间',
                            `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
                            `creator` VARCHAR(128) NOT NULL COMMENT '创建者',
                            `modifier` VARCHAR(128) NOT NULL COMMENT '修改者',
                            `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                            `update_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                            `task` BIGINT(20) DEFAULT NULL COMMENT '任务ID',
                            `tenant_id` VARCHAR(128) NOT NULL COMMENT '租户 ID',
                            `recalculate` tinyint(1) DEFAULT '0' COMMENT '是否需要覆盖更新',
                            PRIMARY KEY(`id`)
            ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='标签表';
        </sql>
        <sql>
            CREATE TABLE IF NOT EXISTS
                `label_catalog` (
                                    `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '标签目录id',
                                    `parent_id` BIGINT(20) NOT NULL COMMENT '父级标签目录id',
                                    `catalog_name` VARCHAR(128) NOT NULL COMMENT '标签目录名称',
                                    `sort` BIGINT(20) NOT NULL COMMENT '排序值',
                                    `user_id` VARCHAR(128) NOT NULL COMMENT '用户 ID',
                                    `del` tinyint(1) NOT NULL COMMENT '删除标识, 0:未删除, 1: 已删除',
                                    `creator` VARCHAR(128) NOT NULL COMMENT '创建者',
                                    `modifier` VARCHAR(128) NOT NULL COMMENT '修改者',
                                    `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                    `update_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                    `label_count` INT(11) NOT NULL DEFAULT '0' COMMENT '标签数量',
                                    `tenant_id` VARCHAR(128) NOT NULL COMMENT '租户 ID',
                                    PRIMARY KEY(`id`)
            ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4;
        </sql>
        <sql>
            CREATE TABLE IF NOT EXISTS
                `label_field` (
                                  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '字段 ID',
                                  `field_type` VARCHAR(32) NOT NULL COMMENT '字段类型, STRING/INT/ARRAY...等',
                                  `field_desc` VARCHAR(256) DEFAULT NULL COMMENT '字段描述',
                                  `label_table` VARCHAR(128) NOT NULL COMMENT '宽表名',
                                  `table_space` VARCHAR(128) NOT NULL COMMENT '宽表空间',
                                  `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
                                  `creator` VARCHAR(128) NOT NULL COMMENT '创建者',
                                  `modifier` VARCHAR(128) NOT NULL COMMENT '修改者',
                                  `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `update_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                  PRIMARY KEY(`id`)
            ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='标签字段表';
        </sql>
        <sql>
            CREATE TABLE IF NOT EXISTS `customer_group` (
                                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '客群id',
                                              `user_id` varchar(128) NOT NULL COMMENT '用户 ID',
                                              `customer_group_name` varchar(128) NOT NULL COMMENT '客群名称',
                                              `customer_group_description` varchar(500) DEFAULT NULL COMMENT '客群描述',
                                              `customer_group_value_update_mod` tinyint(4) NOT NULL COMMENT '更新取值逻辑:0:每次重新计算,1:合并历史值',
                                              `trigger_mod` tinyint(4) NOT NULL COMMENT '更新触发类型:0:定时触发,1:手动触发',
                                              `trigger_frequency` tinyint(4) DEFAULT NULL COMMENT '执行频率:0:每天,1:每周,2:每月',
                                              `trigger_frequency_value` varchar(256) DEFAULT NULL COMMENT '执行频率json',
                                              `customer_group_rule` text NOT NULL COMMENT '客群值规则json',
                                              `cal_status` tinyint(4) DEFAULT '0' COMMENT '计算状态: 0:待计算,1:计算中,2:计算成功,3:计算失败,4:计算取消',
                                              `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
                                              `creator` varchar(128) NOT NULL COMMENT '创建者',
                                              `modifier` varchar(128) NOT NULL COMMENT '修改者',
                                              `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                              `last_cal_date` timestamp NULL DEFAULT NULL COMMENT '上一次执行时间',
                                              `task` bigint(20) DEFAULT NULL COMMENT '任务ID',
                                              `tenant_id` varchar(128) DEFAULT NULL COMMENT '租户id',
                                              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='客群表';
        </sql>
        <sql>
            CREATE TABLE IF NOT EXISTS `datatable_info` (
                                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
                                              `cn_name` varchar(255) NOT NULL COMMENT '数据表名称',
                                              `en_name` varchar(255) NOT NULL COMMENT '数据表英文名称',
                                              `data_type` int(10) NOT NULL COMMENT '数据类型',
                                              `data_source` varchar(255) NOT NULL COMMENT '数据源',
                                              `status` tinyint(4) NOT NULL COMMENT '状态',
                                              `is_visable` tinyint(1) NOT NULL COMMENT '是否可见 0：可见 1：不可见',
                                              `is_preset` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 预置 1：新建',
                                              `is_del` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0: 正常 1：已删除',
                                              `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
                                              `table_name` varchar(255) NOT NULL,
                                              `tenantId` varchar(128) NOT NULL COMMENT '租户id',
                                              PRIMARY KEY (`id`),
                                              KEY `idx_table_name` (`en_name`),
                                              KEY `idx_update_time` (`update_time`)
            ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='数据表管理表';
        </sql>
        <sql>
            CREATE TABLE IF NOT EXISTS `task_info` (
                                         `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
                                         `task_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '任务类型, 0:贴源层集成任务, 1:中间层指标加工任务',
                                         `task_desc` varchar(256) DEFAULT NULL COMMENT '任务描述',
                                         `task_conf` text COMMENT '任务配置信息, JSON 格式',
                                         `trigger_cron` varchar(64) DEFAULT '' COMMENT '定时任务 cron 表达式',
                                         `next_exec_date` timestamp NULL DEFAULT NULL COMMENT '下一次执行时间',
                                         `del` tinyint(1) NOT NULL COMMENT '删除标识, 0:未删除, 1:已删除',
                                         `creator` varchar(128) NOT NULL COMMENT '创建者',
                                         `modifier` varchar(128) NOT NULL COMMENT '修改者',
                                         `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         PRIMARY KEY (`id`)
            ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='任务表';
        </sql>
        <sql>
            CREATE TABLE IF NOT EXISTS `task_scheduler` (
                                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务执行ID',
                                              `task_id` bigint(20) NOT NULL COMMENT '任务ID,全局唯一',
                                              `external_id` varchar(256) DEFAULT NULL COMMENT '外部执行ID',
                                              `body` text COMMENT '额外信息',
                                              `message` text COMMENT '结果信息',
                                              `status` tinyint(4) NOT NULL COMMENT '任务执行状态',
                                              `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
                                              `creator` varchar(128) NOT NULL COMMENT '创建者',
                                              `modifier` varchar(128) NOT NULL COMMENT '修改者',
                                              `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                              PRIMARY KEY (`id`)
            ) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='任务调度执行表';
        </sql>
    </changeSet>
</databaseChangeLog>