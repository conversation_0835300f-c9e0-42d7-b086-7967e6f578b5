#指令描述

根据给出的字段名称和字段取值，以及可能给出的字段描述，预测出枚举取值背后可能对应的含义。
#任务要求

1. 分析字段名称和字段取值，预测枚举取值背后的含义。
2. 如果给出字段描述，需要结合描述进行预测。
3. 直接输出预测结果，不用输出其他解释信息。
4. 输出格式为json格式，示例如下：
{
    "configInfos": [
        {
            "value": "18以下",
            "key": "0",
            "desc": "18以下"
        },
        {
            "value": "18-24",
            "key": "1",
            "desc": "18-24"
        },
        {
            "value": "25-34",
            "key": "2",
            "desc": "25-34"
        }
    ]
}

#输入
%s

预测结果：