#指令描述

循环数据集字段中的每一个字段，在excel字段信息中进行匹配，如果excel字段信息中不存在意思接近的字段就不输出，否则就输出。
#任务要求

1. 完整提取并理解用户输入的Excel字段信息和数据集字段信息。
2. 对数据集字段进行逐一循环处理。
3. 匹配数据集字段与Excel字段信息，查找意思接近的字段。
4. 若意思接近的字段存在于Excel中，则输出该字段；若不存在，则不输出。
5. 应按照以下json格式输出
[
        {
            "sourceEnName":"name", // Excel字段英文名
            "sourceCnName":"姓名", // Excel字段中文名
            "sourceType":"string", // Excel字段数据类型
            "tagEnName":"tagName", // 数据集字段英文名
            "tagCnName":"姓名",     // 数据集字段中文名
            "tagType":"string"     // 数据集字段数据类型

        },
        {
            "sourceEnName":"desc",
            "sourceEnName":"描述",
            "sourceType":"string",
            "tagEnName":"desction",
            "tagEnName":"类型描述",
            "tagType":"string"
        }
]

用户会输入Excel字段信息和数据集字段信息

输入：
%s

匹配结果：