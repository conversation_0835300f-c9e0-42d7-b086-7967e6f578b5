package com.baidu.rules

import java.util.List
import com.baidu.keyue.deepsight.enums.FilterTypeEnum
import com.baidu.keyue.deepsight.enums.FuncEnum
import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult
import com.baidu.keyue.deepsight.database.common.func.AggFunc
import com.baidu.keyue.deepsight.database.common.func.AggFunc
import com.baidu.keyue.deepsight.database.common.func.Func
import org.apache.commons.lang3.StringUtils;


// ---------------- 非聚合的函数构建

rule "contain"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.CONTAIN)
        $ruleNode: RuleNode()
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getWhere().add(Func.contain(filed, params, $ruleNode));
end

rule "not contain"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.NOT_CONTAIN)
        $ruleNode: RuleNode()
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getWhere().add(Func.notContain(filed, params, $ruleNode));
end

rule "like"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.LIKE)
        $ruleNode: RuleNode()
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getWhere().add(Func.like(filed, params.get(0), $ruleNode));
end

rule "least"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.LEAST)
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getWhere().add(Func.least(filed, params.get(0)));
end

rule "is null"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.IS_NULL && getAggregator() == null)
        $ruleNode: RuleNode()
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        $dqlParseResult.getWhere().add(Func.isNull(filed, $ruleNode));
end

rule "not null"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.IS_NOT_NULL && getAggregator() == null)
        $ruleNode: RuleNode()
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        $dqlParseResult.getWhere().add(Func.notNull(filed, $ruleNode));
end

rule "equal"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.EQUALS && getAggregator() == null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getWhere().add(Func.eq(filed, params.get(0)));
end

rule "not equal"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.NOT_EQUALS && getAggregator() == null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getWhere().add(Func.notEq(filed, params.get(0)));
end

rule "gt"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.GREATER_THAN && getAggregator() == null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getWhere().add(Func.gt(filed, params.get(0)));
end

rule "ge"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.GREATER_EQUALS && getAggregator() == null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getWhere().add(Func.ge(filed, params.get(0)));
end

rule "lt"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.LESS_THAN && getAggregator() == null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        List<String> params = $ruleFilter.getParams();
        // 修复：添加缺失的SQL生成代码
        $dqlParseResult.getWhere().add(Func.lt(filed, params.get(0)));
end

rule "le"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.LESS_EQUALS && getAggregator() == null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getWhere().add(Func.le(filed, params.get(0)));
end

rule "between"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.BETWEEN && getAggregator() == null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = $ruleFilter.getFiled();
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getWhere().add(Func.between(filed, params.get(0), params.get(1)));
end


// ---------------- 聚合的函数构建

rule "agg is null"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.IS_NULL && getAggregator() != null)
        $ruleNode: RuleNode()
        $dqlParseResult: DqlParseResult()
    then
        String filed = AggFunc.agg($ruleFilter.getAggregator(), $ruleFilter.getFiled());
        $dqlParseResult.getHaving().add(Func.isNull(filed, $ruleNode));
end

rule "agg not null"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.IS_NOT_NULL && getAggregator() != null)
        $ruleNode: RuleNode()
        $dqlParseResult: DqlParseResult()
    then
        String filed = AggFunc.agg($ruleFilter.getAggregator(), $ruleFilter.getFiled());
        $dqlParseResult.getHaving().add(Func.notNull(filed, $ruleNode));
end

rule "agg equal"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.EQUALS && getAggregator() != null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = AggFunc.agg($ruleFilter.getAggregator(), $ruleFilter.getFiled());
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getHaving().add(Func.eq(filed, params.get(0)));
end

rule "agg not equal"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.NOT_EQUALS && getAggregator() != null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = AggFunc.agg($ruleFilter.getAggregator(), $ruleFilter.getFiled());
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getHaving().add(Func.notEq(filed, params.get(0)));
end

rule "agg gt"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.GREATER_THAN && getAggregator() != null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = AggFunc.agg($ruleFilter.getAggregator(), $ruleFilter.getFiled());
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getHaving().add(Func.gt(filed, params.get(0)));
end

rule "agg ge"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.GREATER_EQUALS && getAggregator() != null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = AggFunc.agg($ruleFilter.getAggregator(), $ruleFilter.getFiled());
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getHaving().add(Func.ge(filed, params.get(0)));
end

rule "agg lt"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.LESS_THAN && getAggregator() != null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = AggFunc.agg($ruleFilter.getAggregator(), $ruleFilter.getFiled());
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getHaving().add(Func.lt(filed, params.get(0)));
end

rule "agg le"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.LESS_EQUALS && getAggregator() != null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = AggFunc.agg($ruleFilter.getAggregator(), $ruleFilter.getFiled());
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getHaving().add(Func.le(filed, params.get(0)));
end

rule "agg between"
    when
        $ruleFilter: RuleFilter(getFunction() == FuncEnum.BETWEEN && getAggregator() != null)
        $dqlParseResult: DqlParseResult()
    then
        String filed = AggFunc.agg($ruleFilter.getAggregator(), $ruleFilter.getFiled());
        List<String> params = $ruleFilter.getParams();
        $dqlParseResult.getHaving().add(Func.between(filed, params.get(0), params.get(1)));
end