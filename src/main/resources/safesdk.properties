# éå®åå¨å±ç½ååï¼ä»¥åå·åé
redirect.whitelist=www.baidu.com;localhost;qianfan.baidubce.com
# æ¯å¦å¼å¯éå®åéå¶ï¼true ä¸ºæ¯ï¼å¶ä»ä¸ºå¦
redirect.limit=true
# ä¸ä¼ æä»¶åå­ç¬¦æ°éå¶ï¼å¼å°äº 0 ä¸ºæ éå¶
file.name.length=-1
# ä¸ä¼ æä»¶åå®¹é¿åº¦éå¶ï¼åä½ä¸º byteï¼å¼å°äº 0 ä¸ºæ éå¶
file.content.length=-1
# åç¼ç½ååï¼ä»¥åå·åé [å¿é¡»éç½®ï¼å¦åä¼å¯¼è´ä¸ä¼ å¤±è´¥]
file.extension.whitelist=txt;jpg;xls;xlsx;csv
# æ¯å¦åè®¸æ²¡æåç¼çæä»¶éè¿ï¼true ä¸ºæ¯ï¼å¶ä»ä¸ºå¦
file.allow.no.extension=true
# æ°æ®åºè¡¨ååååå­ç¬¦ç½ååï¼åå·åé
sql.character.whitelist=
# ååºååç½ååï¼ä¸º JEP290 è§åä¸²
# éè¯»ç« èï¼http://wiki.baidu.com/pages/viewpage.action?pageId=799324431#Java%E5%AE%89%E5%85%A8%E5%9F%BA%E7%A1%80%E5%BA%93%E4%BD%BF%E7%94%A8%E6%89%8B%E5%86%8C(Web)-SafeObjectInputStream
jdk.serialFilter=