[{"fieldTag": 1, "isFilterCriteria": true, "isRequired": true, "cnField": "对话id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "queryId", "description": "同一个sessionid内，每次对话都有一个对应的queryid", "updateTime": 1739873858781, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1739873858781, "valueType": "text", "dataTableId": 4, "fieldType": "string", "number": 1}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "会话id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "sessionId", "description": "首轮会返回sessionId,后续对话需使用返回的sessionId", "updateTime": 1739873858781, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1739873858781, "valueType": "text", "dataTableId": 4, "fieldType": "string", "number": 2}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "用户提问时间", "dataType": "datetime", "enField": "queryTime", "description": "用户请求的时间", "updateTime": 1739873858781, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1739873858781, "valueType": "time", "dataTableId": 4, "fieldType": "time", "number": 3}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "用户问题", "dataType": "text", "enField": "queryText", "description": "请求的问题", "updateTime": 1739873858781, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1739873858781, "valueType": "text", "dataTableId": 4, "fieldType": "string", "number": 4}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "回答内容", "dataType": "text", "enField": "answerText", "description": "机器人回答内容", "updateTime": 1739873858781, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1739873858781, "valueType": "text", "dataTableId": 4, "fieldType": "string", "number": 5}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "用户id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "user_id", "description": "用户唯一标识", "updateTime": 1739873858781, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1739873858781, "valueType": "text", "dataTableId": 4, "fieldType": "string", "number": 6}, {"fieldTag": 0, "isFilterCriteria": false, "isRequired": false, "cnField": "用户名称", "dataType": "<PERSON><PERSON><PERSON>", "enField": "username", "description": "用户名称", "updateTime": 1739873858781, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1739873858781, "valueType": "text", "dataTableId": 4, "fieldType": "string", "number": 7}, {"fieldTag": 0, "isFilterCriteria": false, "isRequired": false, "cnField": "变量", "dataType": "json", "enField": "variables", "description": "Map格式，key 是变量名称，value是变量值。对话中使用的变量，如果传了会覆盖当前中对话中已有的变量值", "updateTime": 1739873858781, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1739873858781, "valueType": "text", "dataTableId": 4, "fieldType": "string", "number": 8}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "租户id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "tenantId", "description": "租户的唯一标识", "updateTime": 1739873858781, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1739873858781, "valueType": "text", "dataTableId": 4, "fieldType": "string", "number": 9}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "回答结束时间", "dataType": "datetime", "enField": "endTime", "description": "回答结束时间", "updateTime": 1739873858781, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1739873858781, "valueType": "time", "dataTableId": 4, "fieldType": "time", "number": 10}, {"fieldTag": 0, "isFilterCriteria": false, "isRequired": false, "cnField": "意图", "dataType": "json", "enField": "intent", "description": "此次会话涉及的意图", "updateTime": 1739873858781, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1739873858781, "valueType": "text", "dataTableId": 4, "fieldType": "string", "number": 11}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "机器人id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "agentId", "description": "客服机器人的唯一标识", "updateTime": 1739873858781, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1739873858781, "valueType": "text", "dataTableId": 4, "fieldType": "string", "number": 12}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "全局oneId", "dataType": "<PERSON><PERSON><PERSON>", "enField": "oneId", "description": "全局oneId", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 4, "fieldType": "string", "number": 13}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "写入时间", "dataType": "datetime", "enField": "deepsight_datetime", "description": "写入时间 例如2024-09-03 20:55:40", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1737173264000, "valueType": "time", "dataTableId": 4, "fieldType": "time", "number": 14}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "更新时间", "dataType": "datetime", "enField": "deepsight_update_datetime", "description": "更新时间 例如2024-09-03 20:55:40", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_customer_talk", "isSecrete": false, "createTime": 1737173264000, "valueType": "time", "dataTableId": 4, "fieldType": "time", "number": 15}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "百度域浏览器cookie", "dataType": "<PERSON><PERSON><PERSON>", "enField": "BAIDUID", "description": "百度域浏览器cookie", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 16}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "微信公众号用户UnionID", "dataType": "<PERSON><PERSON><PERSON>", "enField": "UNIONID", "description": "微信公众号用户UnionID", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 17}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "国际移动设备身份码", "dataType": "<PERSON><PERSON><PERSON>", "enField": "IMEI", "description": "国际移动设备身份码", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 18}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "百度定义移动设备唯一标示", "dataType": "<PERSON><PERSON><PERSON>", "enField": "CUID", "description": "百度定义移动设备唯一标示", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 19}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "Mac地址", "dataType": "<PERSON><PERSON><PERSON>", "enField": "MAC", "description": "Mac地址", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 20}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "匿名访客ID", "dataType": "<PERSON><PERSON><PERSON>", "enField": "anonymous_id", "description": "匿名访客ID", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 21}]