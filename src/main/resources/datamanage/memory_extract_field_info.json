[{"fieldTag": 1, "isFilterCriteria": true, "isRequired": true, "cnField": "唯一ID", "dataType": "bigint", "enField": "id", "description": "唯一id", "updateTime": 1739874459035, "isVisable": true, "tableEnName": "global_default_memory", "isSecrete": false, "createTime": 1739874459035, "valueType": "number", "dataTableId": 5, "fieldType": "number", "number": 1}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": true, "cnField": "记忆id", "dataType": "bigint", "enField": "memory_id", "description": "每条记忆的唯一id", "updateTime": 1739874459035, "isVisable": true, "tableEnName": "global_default_memory", "isSecrete": false, "createTime": 1739874459035, "valueType": "text", "dataTableId": 5, "fieldType": "string", "number": 2}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "用户id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "user_id", "description": "用户身份唯一标识", "updateTime": 1739874459035, "isVisable": true, "tableEnName": "global_default_memory", "isSecrete": false, "createTime": 1739874459035, "valueType": "text", "dataTableId": 5, "fieldType": "string", "number": 3}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": true, "cnField": "抽取时间", "dataType": "datetime", "enField": "extract_date", "description": "该记忆抽取的时间", "updateTime": 1739874459035, "isVisable": true, "tableEnName": "global_default_memory", "isSecrete": false, "createTime": 1739874459035, "valueType": "text", "dataTableId": 5, "fieldType": "string", "number": 4}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": true, "cnField": "记忆内容", "dataType": "text", "enField": "memory_content", "description": "抽取的记忆片段内容", "updateTime": 1739874459035, "isVisable": true, "tableEnName": "global_default_memory", "isSecrete": false, "createTime": 1739874459035, "valueType": "text", "dataTableId": 5, "fieldType": "string", "number": 5}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": true, "cnField": "记忆类型", "dataType": "<PERSON><PERSON><PERSON>", "enField": "memory_type", "description": "抽取的记忆片段分类，有重要事件、基础属性、兴趣爱好3种", "updateTime": 1739874459035, "isVisable": true, "tableEnName": "global_default_memory", "isSecrete": false, "createTime": 1739874459035, "valueType": "enum", "dataTableId": 5, "fieldType": "string", "configInfos": [{"key": "event", "value": "重要事件", "desc": "重要事件"}, {"key": "attribute", "value": "基础属性", "desc": "基础属性"}, {"key": "interest", "value": "兴趣爱好", "desc": "兴趣爱好"}], "number": 6}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": true, "cnField": "数据来源", "dataType": "bigint", "enField": "dataset_id", "description": "客服数据集、外呼数据集", "updateTime": 1739874459035, "isVisable": true, "tableEnName": "global_default_memory", "isSecrete": false, "createTime": 1739874459035, "valueType": "text", "dataTableId": 5, "fieldType": "string", "number": 7}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "外部ID", "dataType": "<PERSON><PERSON><PERSON>", "enField": "external_id", "description": "外部ID", "updateTime": 1739874459035, "isVisable": false, "tableEnName": "global_default_memory", "isSecrete": false, "createTime": 1739874459035, "valueType": "text", "dataTableId": 5, "fieldType": "string", "number": 8}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "全局oneId", "dataType": "<PERSON><PERSON><PERSON>", "enField": "oneId", "description": "全局oneId", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_memory", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 5, "fieldType": "string", "number": 9}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "写入时间", "dataType": "datetime", "enField": "deepsight_datetime", "description": "写入时间 例如2024-09-03 20:55:40", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_memory", "isSecrete": false, "createTime": 1737173264000, "valueType": "time", "dataTableId": 5, "fieldType": "time", "number": 10}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "更新时间", "dataType": "datetime", "enField": "deepsight_update_datetime", "description": "更新时间 例如2024-09-03 20:55:40", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_memory", "isSecrete": false, "createTime": 1737173264000, "valueType": "time", "dataTableId": 5, "fieldType": "time", "number": 11}]