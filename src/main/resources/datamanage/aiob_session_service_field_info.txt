[{"fieldTag": 1, "isFilterCriteria": true, "isRequired": true, "cnField": "通话id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "sessionId", "description": "对于一通电话的唯一表示", "updateTime": 1737173255000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173255000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 1}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "号码组ID", "dataType": "<PERSON><PERSON><PERSON>", "enField": "memberId", "description": "号码组ID", "updateTime": 1737173256000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173256000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 2}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "任务名称", "dataType": "<PERSON><PERSON><PERSON>", "enField": "taskName", "description": "任务名称", "updateTime": 1737173256000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173256000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 3}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "任务id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "taskId", "description": "任务id", "updateTime": 1737173256000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173256000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 4}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "负责人id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "taskType", "description": "任务类型", "updateTime": 1737173256000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173256000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 5}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "处理时间", "dataType": "<PERSON><PERSON><PERSON>", "enField": "taskTypeDesc", "description": "任务来源描述", "updateTime": 1737173256000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173256000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 6}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "录音记录", "dataType": "tinyint", "enField": "soundRecord", "updateTime": 1737173256000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173256000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 7}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "会话类型", "dataType": "<PERSON><PERSON><PERSON>", "enField": "sessionType", "description": "0-未知 1-呼入 2-呼出", "updateTime": 1737173257000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173257000, "valueType": "enum", "dataTableId": 1, "fieldType": "string", "configInfos": [{"key": "0", "value": "未知", "desc": "未知"}, {"key": "1", "value": "呼入", "desc": "呼入"}, {"key": "2", "value": "呼出", "desc": "呼出"}], "number": 8}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "机器人id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "robotId", "description": "机器人id", "updateTime": 1737173257000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173257000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 9}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "机器人名称", "dataType": "<PERSON><PERSON><PERSON>", "enField": "robotName", "description": "机器人名称", "updateTime": 1737173257000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173257000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 10}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "状态", "dataType": "<PERSON><PERSON><PERSON>", "enField": "cmdStatus", "description": "状态", "updateTime": 1737173257000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173257000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 11}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "被叫号码 (已加密)", "dataType": "<PERSON><PERSON><PERSON>", "enField": "mobile", "description": "被叫号码_加密", "updateTime": 1737173257000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173257000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 12}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "被叫号码 省", "dataType": "<PERSON><PERSON><PERSON>", "enField": "mobileProvince", "description": "被叫号码省", "updateTime": 1737173257000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173257000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 13}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "被叫号码 市", "dataType": "<PERSON><PERSON><PERSON>", "enField": "mobileCity", "description": "被叫号码市", "updateTime": 1737173258000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173258000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 14}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "号码所属", "dataType": "<PERSON><PERSON><PERSON>", "enField": "<PERSON><PERSON><PERSON><PERSON>", "description": "0-平台，1-客户自有", "updateTime": 1737173258000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173258000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 15}, {"fieldTag": 2, "isFilterCriteria": true, "isRequired": false, "cnField": "动作", "dataType": "json", "enField": "action", "description": "动作", "updateTime": 1737173258000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173258000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 16}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "是否响铃", "dataType": "boolean", "enField": "isRinging", "description": "是否响铃", "updateTime": 1737173258000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173258000, "valueType": "number", "dataTableId": 1, "fieldType": "number", "number": 17}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "是否回复", "dataType": "boolean", "enField": "isAnswer", "description": "是否回复", "updateTime": 1737173258000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173258000, "valueType": "number", "dataTableId": 1, "fieldType": "number", "number": 18}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "挂断原因", "dataType": "<PERSON><PERSON><PERSON>", "enField": "hangupReason", "description": "挂断原因", "updateTime": 1737173258000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173258000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 19}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "意图", "dataType": "<PERSON><PERSON><PERSON>", "enField": "intent", "description": "意图", "updateTime": 1737173258000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173258000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 20}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "号码维度呼叫次序", "dataType": "int", "enField": "callTimes", "description": "号码维度呼叫次序", "updateTime": 1737173258000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173258000, "valueType": "time", "dataTableId": 1, "fieldType": "time", "number": 21}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "呼叫类型", "dataType": "int", "enField": "callType", "description": "0首次呼叫，1重试呼叫，2预约呼叫, 3实时呼叫", "updateTime": 1737173258000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173258000, "valueType": "enum", "dataTableId": 1, "fieldType": "string", "configInfos": [{"key": "0", "value": "首次呼叫", "desc": "首次呼叫"}, {"key": "1", "value": "重试呼叫", "desc": "重试呼叫"}, {"key": "2", "value": "预约呼叫", "desc": "预约呼叫"}, {"key": "3", "value": "实时呼叫", "desc": "实时呼叫"}], "number": 22}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "电话结果", "dataType": "int", "enField": "completeType", "description": "3, 未完成 2, 已完成", "updateTime": 1737173259000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173259000, "valueType": "enum", "dataTableId": 1, "fieldType": "string", "configInfos": [{"key": "3", "value": "未完成", "desc": "未完成"}, {"key": "2", "value": "已完成", "desc": "已完成"}], "number": 23}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "拨打持续时长", "dataType": "int", "enField": "durationTimeLen", "description": "拨打持续时长", "updateTime": 1737173259000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173259000, "valueType": "time", "dataTableId": 1, "fieldType": "time", "number": 24}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "电话结果", "dataType": "tinyint", "enField": "endType", "description": "电话结果，0-已完成 1-待呼叫 2-未完成", "updateTime": 1737173259000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173259000, "valueType": "enum", "dataTableId": 1, "fieldType": "string", "configInfos": [{"key": "0", "value": "已完成", "desc": "已完成"}, {"key": "1", "value": "待呼叫", "desc": "待呼叫"}, {"key": "2", "value": "未完成", "desc": "未完成"}], "number": 25}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "触发电话结果原因", "dataType": "<PERSON><PERSON><PERSON>", "enField": "endTypeReason", "description": "触发电话结果原因", "updateTime": 1737173259000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173259000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 26}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "策略", "dataType": "json", "enField": "executiveStrategy", "description": "策略完成节点", "updateTime": 1737173259000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173259000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 27}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "导入文件的唯一标识", "dataType": "largeint", "enField": "fileId", "description": "导入文件的唯一标识", "updateTime": 1737173259000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173259000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 28}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "是否是完成节点", "dataType": "boolean", "enField": "isComplete", "description": "是否是完成节点", "updateTime": 1737173259000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173259000, "valueType": "number", "dataTableId": 1, "fieldType": "number", "number": 29}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "是否机器人挂机", "dataType": "boolean", "enField": "isRobotHangup", "description": "是否机器人挂机", "updateTime": 1737173260000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173260000, "valueType": "number", "dataTableId": 1, "fieldType": "number", "number": 30}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "短信状态", "dataType": "tinyint", "enField": "smsStatus", "updateTime": 1737173260000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173260000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 31}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "短信变量信息", "dataType": "json", "enField": "smsVar", "updateTime": 1737173260000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173260000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 32}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "转人工状态", "dataType": "tinyint", "enField": "transResult", "updateTime": 1737173260000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173260000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 33}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "任务类型", "dataType": "tinyint", "enField": "sysType", "updateTime": 1737173260000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173260000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 34}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "开始时间", "dataType": "datetime", "enField": "startTime", "description": "开始时间", "updateTime": 1737173260000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173260000, "valueType": "time", "dataTableId": 1, "fieldType": "time", "number": 35}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "挂断时间", "dataType": "datetime", "enField": "endTime", "description": "挂断时间", "updateTime": 1737173260000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173260000, "valueType": "time", "dataTableId": 1, "fieldType": "time", "number": 36}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "接通时间", "dataType": "datetime", "enField": "talkingStartTime", "description": "接通时间", "updateTime": 1737173260000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173260000, "valueType": "time", "dataTableId": 1, "fieldType": "time", "number": 37}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "通话时长", "dataType": "int", "enField": "talkingTimeLen", "description": "通话时长", "updateTime": 1737173260000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173260000, "valueType": "number", "dataTableId": 1, "fieldType": "number", "number": 38}, {"fieldTag": 3, "isFilterCriteria": true, "isRequired": false, "cnField": "对话轮次", "dataType": "int", "enField": "talkingTurn", "description": "对话轮次", "updateTime": 1737173260000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173260000, "valueType": "number", "dataTableId": 1, "fieldType": "number", "number": 39}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "录音地址", "dataType": "<PERSON><PERSON><PERSON>", "enField": "audioCallPath", "description": "录音地址", "updateTime": 1737173261000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173261000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 40}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "振铃时间", "dataType": "datetime", "enField": "ringStartTime", "description": "振铃时间", "updateTime": 1737173261000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173261000, "valueType": "time", "dataTableId": 1, "fieldType": "time", "number": 41}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "振铃时长", "dataType": "int", "enField": "ringingTimeLen", "description": "振铃时长", "updateTime": 1737173261000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173261000, "valueType": "time", "dataTableId": 1, "fieldType": "time", "number": 42}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "创建时间", "dataType": "datetime", "enField": "createTime", "updateTime": 1737173261000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173261000, "valueType": "time", "dataTableId": 1, "fieldType": "time", "number": 43}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "更新时间", "dataType": "datetime", "enField": "updateTime", "description": "更新时间", "updateTime": 1737173261000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173261000, "valueType": "time", "dataTableId": 1, "fieldType": "time", "number": 44}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "创建时间后续用来做拉数据处理", "dataType": "bigint", "enField": "createTimePoint", "description": "创建时间后续用来做拉数据处理", "updateTime": 1737173261000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173261000, "valueType": "number", "dataTableId": 1, "fieldType": "number", "number": 45}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "机器人场景", "dataType": "tinyint", "enField": "robotScene", "description": "机器人场景", "updateTime": 1737173261000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173261000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 46}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "是否机器人场景", "dataType": "<PERSON><PERSON><PERSON>", "enField": "robotSceneName", "description": "是否机器人场景", "updateTime": 1737173261000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173261000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 47}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "来源", "dataType": "<PERSON><PERSON><PERSON>", "enField": "fromSource", "description": "来源", "updateTime": 1737173261000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173261000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 48}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "机器人标签抽取信息", "dataType": "json", "enField": "tagExtractInfo", "description": "抽取标签", "updateTime": 1737173261000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173261000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 49}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "对话内容", "dataType": "text", "enField": "conversationContent", "description": "对话内容", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 50}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": true, "cnField": "通话轮数", "dataType": "int", "enField": "recordCount", "description": "会话数", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "number", "dataTableId": 1, "fieldType": "number", "number": 51}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "电话号码MD5", "dataType": "<PERSON><PERSON><PERSON>", "enField": "mobileMD5", "description": "手机号md5", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 52}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "租户id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "tenantId", "updateTime": 1737173262000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 53}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "租户名称", "dataType": "<PERSON><PERSON><PERSON>", "enField": "tenantName", "updateTime": 1737173262000, "isVisable": false, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 54}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "全局oneId", "dataType": "<PERSON><PERSON><PERSON>", "enField": "oneId", "description": "全局oneId", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 55}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "写入时间", "dataType": "datetime", "enField": "deepsight_datetime", "description": "写入时间 例如2024-09-03 20:55:40", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173264000, "valueType": "time", "dataTableId": 1, "fieldType": "time", "number": 56}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "更新时间", "dataType": "datetime", "enField": "deepsight_update_datetime", "description": "更新时间 例如2024-09-03 20:55:40", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173264000, "valueType": "time", "dataTableId": 1, "fieldType": "time", "number": 57}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "百度域浏览器cookie", "dataType": "<PERSON><PERSON><PERSON>", "enField": "BAIDUID", "description": "百度域浏览器cookie", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 58}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "国际移动设备身份码", "dataType": "<PERSON><PERSON><PERSON>", "enField": "IMEI", "description": "国际移动设备身份码", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 59}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "百度定义移动设备唯一标示", "dataType": "<PERSON><PERSON><PERSON>", "enField": "CUID", "description": "百度定义移动设备唯一标示", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 60}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "Mac地址", "dataType": "<PERSON><PERSON><PERSON>", "enField": "MAC", "description": "Mac地址", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 61}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "接通状态", "dataType": "<PERSON><PERSON><PERSON>", "enField": "sipCode", "description": "接通状态", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 62}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "机器人版本", "dataType": "<PERSON><PERSON><PERSON>", "enField": "botVersionId", "description": "机器人版本", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 63}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "意向", "dataType": "array", "enField": "customTagList", "description": "意向", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "strings", "number": 64}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "主叫号码", "dataType": "<PERSON><PERSON><PERSON>", "enField": "callerNum", "description": "主叫号码", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 65}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "字典类别", "dataType": "<PERSON><PERSON><PERSON>", "enField": "dicCate<PERSON><PERSON>", "description": "字典类别-未接通类别", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 66}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "字典名称", "dataType": "<PERSON><PERSON><PERSON>", "enField": "dicName", "description": "字典名称-未接通名称", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 67}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "是否为小秘书", "dataType": "tinyint", "enField": "is_auto_answer", "description": "是否为小秘书：1是，0否", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "number", "dataTableId": 1, "fieldType": "number", "number": 68}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "号线状态", "dataType": "<PERSON><PERSON><PERSON>", "enField": "lineStatus", "description": "ALL(全部状态)/ENABLED(启用中)/BANNED(已禁止)/ARREARS(已欠费)/DISABLED(已停用)", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 69}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "外呼任务状态", "dataType": "tinyint", "enField": "taskStatus", "description": "外呼任务:1-待启动/2-执行中/3-已暂停/4-已完成/5-已终止", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "number", "dataTableId": 1, "fieldType": "number", "number": 70}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "主叫号码城市", "dataType": "<PERSON><PERSON><PERSON>", "enField": "callerCity", "description": "主叫号码城市", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_session", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 1, "fieldType": "string", "number": 71}]