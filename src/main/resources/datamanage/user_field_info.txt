[{"fieldTag": 1, "isFilterCriteria": true, "isRequired": true, "cnField": "用户id", "dataType": "<PERSON><PERSON><PERSON>", "enField": "user_id", "description": "一个用户唯一标识，不能包含中文", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string", "number": 1}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "用户昵称", "dataType": "<PERSON><PERSON><PERSON>", "enField": "user_name", "description": "用户昵称或者姓名", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string", "number": 2}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "用户年龄", "dataType": "int", "enField": "age", "description": "用作特征或者筛选条件", "updateTime": 1737173263000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173263000, "valueType": "number", "dataTableId": 2, "fieldType": "number", "number": 3}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "用户年龄段", "dataType": "<PERSON><PERSON><PERSON>", "enField": "age_group", "description": "用作特征或者筛选条件", "updateTime": 1737173263000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173263000, "valueType": "enum", "number": 4, "dataTableId": 2, "fieldType": "string", "configInfos": [{"key": "0", "value": "18以下", "desc": "18以下"}, {"key": "1", "value": "18-24", "desc": "18-24"}, {"key": "2", "value": "25-34", "desc": "25-34"}, {"key": "3", "value": "35-44", "desc": "35-44"}, {"key": "4", "value": "45-54", "desc": "45-54"}, {"key": "5", "value": "55-64", "desc": "55-64"}, {"key": "6", "value": "65以上", "desc": "65以上"}]}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "用户城市", "dataType": "<PERSON><PERSON><PERSON>", "enField": "city", "description": "用户所在城市，中文名称表示，用作特征或者筛选条件", "updateTime": 1737173263000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173263000, "valueType": "text", "number": 5, "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "用户性别", "dataType": "<PERSON><PERSON><PERSON>", "enField": "gender", "description": "用作特征或者筛选条件，例如：男性、女性", "updateTime": 1737173263000, "isVisable": true, "number": 6, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173263000, "valueType": "enum", "dataTableId": 2, "fieldType": "string", "configInfos": [{"key": "0", "value": "女性", "desc": "女性"}, {"key": "1", "value": "男性", "desc": "男性"}, {"key": "2", "value": "未知", "desc": "未知"}]}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "注册时间", "dataType": "datetime", "number": 7, "enField": "register_time", "description": "用户注册时间 例如2024-09-03 20:55:40", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173264000, "valueType": "time", "dataTableId": 2, "fieldType": "time"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "更新时间", "dataType": "datetime", "enField": "update_time", "number": 8, "description": "用户更新时间 例如2024-09-03 20:55:40", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173264000, "valueType": "time", "dataTableId": 2, "fieldType": "time"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "用户标签", "dataType": "<PERSON><PERSON><PERSON>", "enField": "tags", "number": 9, "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173264000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "商圈", "dataType": "<PERSON><PERSON><PERSON>", "enField": "area", "number": 10, "description": "用户所在商圈，中文名称表示，用作特征或者筛选条件", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173264000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "用户国家", "dataType": "<PERSON><PERSON><PERSON>", "number": 11, "enField": "country", "description": "用作特征或者筛选条件", "updateTime": 1737173265000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173265000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "设备标识", "dataType": "<PERSON><PERSON><PERSON>", "number": 12, "enField": "DEVICEID", "description": "用户使用设备id，用作特征或者筛选条件", "updateTime": 1737173265000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173265000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "设备型号", "number": 13, "dataType": "<PERSON><PERSON><PERSON>", "enField": "device_model", "description": "如HONER，用作特征或者筛选条件", "updateTime": 1737173265000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173265000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "用户所在区县", "dataType": "<PERSON><PERSON><PERSON>", "number": 14, "enField": "district", "description": "用作特征或者筛选条件", "updateTime": 1737173265000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173265000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "会员等级", "dataType": "<PERSON><PERSON><PERSON>", "number": 15, "enField": "membership_level", "description": "用户会员等级，用作特征或者筛选条件", "updateTime": 1737173265000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173265000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "操作系统", "dataType": "<PERSON><PERSON><PERSON>", "enField": "os", "number": 16, "description": "用户设备操作系统，用作特征或者筛选条件", "updateTime": 1737173265000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173265000, "valueType": "enum", "dataTableId": 2, "fieldType": "string", "configInfos": [{"key": "1", "value": "IOS", "desc": "IOS"}, {"key": "2", "value": "Android", "desc": "Android"}, {"key": "3", "value": "Windows", "desc": "Windows"}, {"key": "4", "value": "其他", "desc": "其他"}]}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "用户省份", "dataType": "<PERSON><PERSON><PERSON>", "enField": "province", "number": 17, "description": "用作特征或者筛选条件", "updateTime": 1737173265000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173265000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "用户类型", "dataType": "<PERSON><PERSON><PERSON>", "number": 18, "enField": "user_type", "description": "用作特征或者筛选条件", "updateTime": 1737173265000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173265000, "valueType": "enum", "dataTableId": 2, "fieldType": "string", "configInfos": [{"key": "1", "value": "登陆用户", "desc": "登陆用户"}, {"key": "2", "value": "访客", "desc": "访客"}]}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "收入水平", "dataType": "<PERSON><PERSON><PERSON>", "number": 19, "enField": "income_level", "updateTime": 1737173265000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173265000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "教育水平", "dataType": "<PERSON><PERSON><PERSON>", "number": 20, "enField": "education_level", "description": "如本科、硕士研究生、博士研究生等，用作特征或者筛选条件", "updateTime": 1737173266000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173266000, "valueType": "enum", "dataTableId": 2, "fieldType": "string", "configInfos": [{"key": "1", "value": "高中及以下", "desc": "高中及以下"}, {"key": "2", "value": "大专", "desc": "大专"}, {"key": "3", "value": "本科", "desc": "本科"}, {"key": "4", "value": "硕士研究生", "desc": "硕士研究生"}, {"key": "5", "value": "博士研究生", "desc": "博士研究生"}]}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "人生阶段", "dataType": "<PERSON><PERSON><PERSON>", "number": 21, "enField": "life_stage", "description": "人生所处阶段，如高中生、大学生等，用作特征或者筛选条件", "updateTime": 1737173266000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173266000, "valueType": "enum", "dataTableId": 2, "fieldType": "string", "configInfos": [{"key": "10", "value": "初中生", "desc": "初中生"}, {"key": "20", "value": "高中生", "desc": "高中生"}, {"key": "30", "value": "高考", "desc": "高考"}, {"key": "40", "value": "大学生", "desc": "大学生"}, {"key": "50", "value": "考研", "desc": "考研"}, {"key": "60", "value": "研究生", "desc": "研究生"}, {"key": "70", "value": "征婚", "desc": "征婚"}, {"key": "80", "value": "备婚", "desc": "备婚"}, {"key": "90", "value": "备孕", "desc": "备孕"}, {"key": "100", "value": "孕期", "desc": "孕期"}, {"key": "200", "value": "待产", "desc": "待产"}, {"key": "300", "value": "育儿阶段", "desc": "育儿阶段"}, {"key": "400", "value": "家有0-1岁小孩", "desc": "家有0-1岁小孩"}, {"key": "500", "value": "家有1-3岁小孩", "desc": "家有1-3岁小孩"}, {"key": "600", "value": "家有3-6岁小孩", "desc": "家有3-6岁小孩"}, {"key": "700", "value": "家有小学生", "desc": "家有小学生"}, {"key": "800", "value": "家有初中生", "desc": "家有初中生"}, {"key": "900", "value": "家有高中生", "desc": "家有高中生"}, {"key": "1000", "value": "家有老人", "desc": "家有老人"}, {"key": "2000", "value": "退休", "desc": "退休"}, {"key": "3000", "value": "家有孕妇", "desc": "家有孕妇"}]}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "所在行业", "dataType": "<PERSON><PERSON><PERSON>", "enField": "industry", "description": "客户职业所在行业，根据国民经济行业分类标准划分，用作特征或者筛选条件", "updateTime": 1737173266000, "isVisable": true, "number": 22, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173266000, "valueType": "enum", "dataTableId": 2, "fieldType": "string", "configInfos": [{"key": "70110100", "value": "农林牧渔", "desc": "农林牧渔"}, {"key": "70110200", "value": "能源采矿化工", "desc": "能源采矿化工"}, {"key": "70110300", "value": "食品加工", "desc": "食品加工"}, {"key": "70110400", "value": "纺织服装", "desc": "纺织服装"}, {"key": "70110500", "value": "建材家居", "desc": "建材家居"}, {"key": "70110600", "value": "医药卫生", "desc": "医药卫生"}, {"key": "70110700", "value": "机械制造", "desc": "机械制造"}, {"key": "70110800", "value": "汽车", "desc": "汽车"}, {"key": "70110900", "value": "IT通信电子", "desc": "IT通信电子"}, {"key": "70111000", "value": "建筑房地产", "desc": "建筑房地产"}, {"key": "70111100", "value": "交通运输和仓储邮政", "desc": "交通运输和仓储邮政"}, {"key": "70111200", "value": "餐饮", "desc": "餐饮"}, {"key": "70111300", "value": "家电", "desc": "家电"}, {"key": "70111400", "value": "日化百货", "desc": "日化百货"}, {"key": "70111500", "value": "金融保险", "desc": "金融保险"}, {"key": "70111600", "value": "生活服务", "desc": "生活服务"}, {"key": "70111700", "value": "住宿旅游", "desc": "住宿旅游"}, {"key": "70111800", "value": "广告营销", "desc": "广告营销"}, {"key": "70111900", "value": "法律商务人力外贸", "desc": "法律商务人力外贸"}, {"key": "70112000", "value": "科学研究", "desc": "科学研究"}, {"key": "70112100", "value": "教育", "desc": "教育"}, {"key": "70112200", "value": "文化体育娱乐", "desc": "文化体育娱乐"}, {"key": "70112300", "value": "社会公共管理", "desc": "社会公共管理"}]}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "职业类型", "dataType": "<PERSON><PERSON><PERSON>", "number": 23, "enField": "occupation", "description": "职业类型类型，如专业技术人员，用作特征或者筛选条件", "updateTime": 1737173266000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173266000, "valueType": "enum", "dataTableId": 2, "fieldType": "string", "configInfos": [{"key": "70120100", "value": "生产操作人员", "desc": "生产操作人员"}, {"key": "70120200", "value": "文职人员", "desc": "文职人员"}, {"key": "70120300", "value": "专业技术人员", "desc": "专业技术人员"}, {"key": "70120400", "value": "管理者和企业主", "desc": "管理者和企业主"}, {"key": "70120500", "value": "个体经营业者", "desc": "个体经营业者"}, {"key": "70120600", "value": "服务人员", "desc": "服务人员"}]}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "常驻城市线级", "dataType": "<PERSON><PERSON><PERSON>", "enField": "city_level", "description": "常驻城市线级，如一线城市，用作特征或者筛选条件", "updateTime": 1737173266000, "isVisable": true, "number": 24, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173266000, "valueType": "enum", "dataTableId": 2, "fieldType": "string", "configInfos": [{"key": "0", "value": "未知", "desc": "未知"}, {"key": "1", "value": "一线城市", "desc": "一线城市"}, {"key": "2", "value": "二线城市", "desc": "二线城市"}, {"key": "3", "value": "三线城市", "desc": "三线城市"}, {"key": "4", "value": "四线城市", "desc": "四线城市"}, {"key": "5", "value": "五线城市", "desc": "五线城市"}]}, {"fieldTag": 4, "isFilterCriteria": true, "isRequired": false, "cnField": "手机", "dataType": "<PERSON><PERSON><PERSON>", "enField": "mobile", "number": 25, "description": "手机号码，用作外呼通话或者关联关系挖掘", "updateTime": 1737173266000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173266000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "邮箱", "dataType": "<PERSON><PERSON><PERSON>", "number": 26, "enField": "email_address", "description": "邮箱地址，用作邮件触达或者关联关系挖掘", "updateTime": 1737173266000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173266000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "微信", "dataType": "<PERSON><PERSON><PERSON>", "number": 27, "enField": "wechat_id", "description": "微信号码，用作维信触达或者关联关系挖掘", "updateTime": 1737173266000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173266000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "*用户性别", "dataType": "<PERSON><PERSON><PERSON>", "number": 28, "enField": "bd_gender", "description": "用户性别(百度)", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "*用户年龄段", "dataType": "<PERSON><PERSON><PERSON>", "number": 29, "enField": "bd_age_group", "description": "用户年龄段(百度)", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "*用户教育水平", "dataType": "<PERSON><PERSON><PERSON>", "number": 30, "enField": "bd_education_level", "description": "用户教育水平(百度)", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "*用户职业类别", "dataType": "<PERSON><PERSON><PERSON>", "number": 31, "enField": "bd_occupation", "description": "用户职业类别(百度)", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "*用户所在行业", "dataType": "<PERSON><PERSON><PERSON>", "enField": "bd_industry", "number": 32, "description": "用户所在行业(百度)", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "*用户人生阶段", "dataType": "<PERSON><PERSON><PERSON>", "enField": "bd_life_stage", "number": 33, "description": "用户人生阶段(百度)", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "*用户婚姻状况", "dataType": "<PERSON><PERSON><PERSON>", "number": 34, "enField": "bd_marriage_status", "description": "用户婚姻状况(百度)", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "number": 35, "cnField": "*用户消费水平", "dataType": "<PERSON><PERSON><PERSON>", "enField": "bd_consume_level", "description": "用户消费水平(百度)", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "*用户消费意愿", "dataType": "<PERSON><PERSON><PERSON>", "number": 36, "enField": "bd_consume_intent", "description": "用户消费意愿(百度)", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "*用户地理位置", "number": 37, "dataType": "<PERSON><PERSON><PERSON>", "enField": "bd_geographic_location", "description": "用户地理位置(百度)", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "*用户兴趣爱好", "number": 38, "dataType": "<PERSON><PERSON><PERSON>", "enField": "bd_interests", "description": "用户兴趣爱好(百度)", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": false, "isRequired": false, "cnField": "手机号", "number": 39, "dataType": "array", "enField": "mobile_list", "description": "工作台手机号,多值", "updateTime": 1737173262000, "isVisable": false, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "strings"}, {"fieldTag": 0, "isFilterCriteria": false, "isRequired": false, "cnField": "邮箱", "number": 40, "dataType": "array", "enField": "email_list", "description": "工作台邮箱,多值", "updateTime": 1737173262000, "isVisable": false, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "strings"}, {"fieldTag": 0, "isFilterCriteria": false, "isRequired": false, "cnField": "来源", "number": 41, "dataType": "<PERSON><PERSON><PERSON>", "enField": "source", "description": "不可编辑", "updateTime": 1737173262000, "isVisable": false, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "全局oneId", "number": 49, "dataType": "<PERSON><PERSON><PERSON>", "enField": "oneId", "description": "全局oneId", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "写入时间", "number": 50, "dataType": "datetime", "enField": "deepsight_datetime", "description": "写入时间 例如2024-09-03 20:55:40", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173264000, "valueType": "time", "dataTableId": 2, "fieldType": "time"}, {"fieldTag": 0, "isFilterCriteria": true, "isRequired": false, "cnField": "更新时间", "number": 51, "dataType": "datetime", "enField": "deepsight_update_datetime", "description": "更新时间 例如2024-09-03 20:55:40", "updateTime": 1737173264000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173264000, "valueType": "time", "dataTableId": 2, "fieldType": "time"}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "国际移动设备身份码", "number": 52, "dataType": "<PERSON><PERSON><PERSON>", "enField": "IMEI", "description": "国际移动设备身份码", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "百度域浏览器cookie", "dataType": "<PERSON><PERSON><PERSON>", "enField": "BAIDUID", "number": 53, "description": "百度域浏览器cookie", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "百度定义移动设备唯一标示", "number": 54, "dataType": "<PERSON><PERSON><PERSON>", "enField": "CUID", "description": "百度定义移动设备唯一标示", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "百度注册id", "number": 55, "dataType": "<PERSON><PERSON><PERSON>", "enField": "USERID", "description": "百度注册id", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "Mac地址", "number": 56, "dataType": "<PERSON><PERSON><PERSON>", "enField": "MAC", "description": "Mac地址", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "UNIONID", "number": 57, "dataType": "<PERSON><PERSON><PERSON>", "enField": "UNIONID", "description": "微信公众号用户UnionID", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "IDFA", "number": 58, "dataType": "<PERSON><PERSON><PERSON>", "enField": "IDFA", "description": "apple提供给广告主的设备唯一ID", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "OAID的设备标识", "number": 59, "dataType": "<PERSON><PERSON><PERSON>", "enField": "OAID", "description": "OAID的设备标识", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}, {"fieldTag": 6, "isFilterCriteria": true, "isRequired": false, "cnField": "匿名访客ID", "number": 60, "dataType": "<PERSON><PERSON><PERSON>", "enField": "anonymous_id", "description": "匿名访客ID", "updateTime": 1737173262000, "isVisable": true, "tableEnName": "global_default_user", "isSecrete": false, "createTime": 1737173262000, "valueType": "text", "dataTableId": 2, "fieldType": "string"}]