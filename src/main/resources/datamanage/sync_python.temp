"""
python调用示例
"""

import os

import time
import requests
import json

def post_m(dic):
    ctx = json.dumps(dic, ensure_ascii=False)
    ctx = ctx.encode("utf-8")
    rex = requests.post(url, ctx, headers=headers)
    return rex

Authorization = "${Authorization}"
url = "${url}"

headers = {"Content-Type": "application/json", "Authorization": Authorization}

demo = [${samples}]

begin = time.time()
rex = post_m(demo)
end = time.time()
print("using time: {}, status {}, ret content: {}".format(round(end - begin, 6), rex.status_code, rex.text))

