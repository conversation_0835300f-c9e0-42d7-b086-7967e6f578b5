<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (C) 2016 Baidu, Inc. All Rights Reserved.
  -->
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <!-- using your local class path -->

    <context id="generatorTables" targetRuntime="MyBatis3">

        <!-- 格式化java代码 -->
        <property name="javaFormatter" value="org.mybatis.generator.api.dom.DefaultJavaFormatter"/>
        <!-- 格式化XML代码 -->
        <property name="xmlFormatter" value="org.mybatis.generator.api.dom.DefaultXmlFormatter"/>

        <!-- <plugin type="org.mybatis.generator.plugins.MapperConfigPlugin"> <property name="fileName" value="mkt-mybatis-config.xml"
            /> <property name="targetPackage" value="/" /> <property name="targetProject" value="src/main/resources" /> </plugin> -->

        <!-- 此处是将Example改名为Criteria 当然 想改成什么都行 -->
        <plugin type="org.mybatis.generator.plugins.RenameExampleClassPlugin">
            <property name="searchString" value="Example"/>
            <property name="replaceString" value="Criteria"/>
        </plugin>

        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>

        <!-- commentGenerator 去除自动生成的注释 -->
        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <!-- 添加 db 表中字段的注释 -->
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <!--<jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="**************************************************************************************************************************" userId="root"
            password="MhxzKhl">
        </jdbcConnection>-->

        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="********************************************************************************************************************************"
                        userId="root"
                        password="znkf@2024">
                <property name="nullCatalogMeansCurrent" value="true" />
                <property name="remarksReporting" value="true"/>
        </jdbcConnection>
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- javaModelGenerator是模型的生成信息，这里将指定这些Java model类的生成路径； -->
        <javaModelGenerator targetPackage="com.baidu.keyue.deepsight.mysqldb.entity"
                            targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- sqlMapGenerator是mybatis 的sqlMapper XML文件的生成信息，包括生成路径等； -->
        <sqlMapGenerator targetPackage="mapper/mysqldb" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- javaClientGenerator是应用接口的生成信息； -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.baidu.keyue.deepsight.mysqldb.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!-- 生成数据库表对应代码 -->

<!--        <table tableName="datatable_info" domainObjectName="DataTableInfo">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="datatable_meta_info" domainObjectName="TableFieldMetaInfo">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="field_encry_config" domainObjectName="FieldEncryConfig">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="label_catalog" domainObjectName="LabelCatalog">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="label" domainObjectName="Label">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="label_field" domainObjectName="LabelField">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="customer_group" domainObjectName="CustomerGroup">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="task_info" domainObjectName="TaskInfo">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="task_scheduler" domainObjectName="TaskScheduler">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="task_file_import" domainObjectName="TaskFileImport">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="id_mapping_relation" domainObjectName="IdMappingRelation">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--            &lt;!&ndash; 筛选json字段&ndash;&gt;-->
<!--            <columnOverride column="en_fields" jdbcType="VARCHAR"></columnOverride>-->
<!--            <columnOverride column="cn_fields" jdbcType="VARCHAR"></columnOverride>-->
<!--        </table>-->
<!--        <table tableName="id_mapping_rule" domainObjectName="IdMappingRule">-->
<!--                <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--            </table>-->
<!--        <table tableName="id_mapping_datatable" domainObjectName="IdMappingDataTable">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="customer_diffusion_task" domainObjectName="CustomerDiffusionTask">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="field_show_config" domainObjectName="FieldShowConfig">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="aiob_sop_meta" domainObjectName="AiobSopMeta">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="sop_user_config" domainObjectName="SopUserConfig">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--            <table tableName="aiob_robot_version" domainObjectName="AiobRobotVersion">-->
<!--                <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--            </table>-->
<!--        <table tableName="alert_config" domainObjectName="AlertConfig">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
<!--        <table tableName="alert_record" domainObjectName="AlertRecord">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--        </table>-->
    </context>

</generatorConfiguration>