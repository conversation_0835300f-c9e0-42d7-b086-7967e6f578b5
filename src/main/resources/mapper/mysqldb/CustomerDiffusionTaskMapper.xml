<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.CustomerDiffusionTaskMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_name" jdbcType="VARCHAR" property="taskName" />
    <result column="seed_group" jdbcType="BIGINT" property="seedGroup" />
    <result column="predict_group" jdbcType="VARCHAR" property="predictGroup" />
    <result column="filter_rule" jdbcType="TINYINT" property="filterRule" />
    <result column="feature_select" jdbcType="TINYINT" property="featureSelect" />
    <result column="threshold" jdbcType="REAL" property="threshold" />
    <result column="judge_criteria" jdbcType="TINYINT" property="judgeCriteria" />
    <result column="similarity" jdbcType="REAL" property="similarity" />
    <result column="ranking" jdbcType="INTEGER" property="ranking" />
    <result column="trigger_mod" jdbcType="TINYINT" property="triggerMod" />
    <result column="trigger_frequency" jdbcType="TINYINT" property="triggerFrequency" />
    <result column="trigger_frequency_value" jdbcType="VARCHAR" property="triggerFrequencyValue" />
    <result column="cal_status" jdbcType="TINYINT" property="calStatus" />
    <result column="last_cal_date" jdbcType="TIMESTAMP" property="lastCalDate" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="group_package" jdbcType="BIT" property="groupPackage" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="customer_group_id" jdbcType="BIGINT" property="customerGroupId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="modifier_name" jdbcType="VARCHAR" property="modifierName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, task_name, seed_group, predict_group, filter_rule, feature_select, threshold, 
    judge_criteria, similarity, ranking, trigger_mod, trigger_frequency, trigger_frequency_value, 
    cal_status, last_cal_date, task_id, tenant_id, group_package, del, creator, modifier, 
    create_time, update_time, customer_group_id, creator_name, modifier_name
  </sql>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTaskCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from customer_diffusion_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from customer_diffusion_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from customer_diffusion_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTaskCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from customer_diffusion_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into customer_diffusion_task (task_name, seed_group, predict_group, 
      filter_rule, feature_select, threshold, 
      judge_criteria, similarity, ranking, 
      trigger_mod, trigger_frequency, trigger_frequency_value, 
      cal_status, last_cal_date, task_id, 
      tenant_id, group_package, del, 
      creator, modifier, create_time, 
      update_time, customer_group_id, creator_name, 
      modifier_name)
    values (#{taskName,jdbcType=VARCHAR}, #{seedGroup,jdbcType=BIGINT}, #{predictGroup,jdbcType=VARCHAR}, 
      #{filterRule,jdbcType=TINYINT}, #{featureSelect,jdbcType=TINYINT}, #{threshold,jdbcType=REAL}, 
      #{judgeCriteria,jdbcType=TINYINT}, #{similarity,jdbcType=REAL}, #{ranking,jdbcType=INTEGER}, 
      #{triggerMod,jdbcType=TINYINT}, #{triggerFrequency,jdbcType=TINYINT}, #{triggerFrequencyValue,jdbcType=VARCHAR}, 
      #{calStatus,jdbcType=TINYINT}, #{lastCalDate,jdbcType=TIMESTAMP}, #{taskId,jdbcType=BIGINT}, 
      #{tenantId,jdbcType=VARCHAR}, #{groupPackage,jdbcType=BIT}, #{del,jdbcType=BIT}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{customerGroupId,jdbcType=BIGINT}, #{creatorName,jdbcType=VARCHAR}, 
      #{modifierName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into customer_diffusion_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskName != null">
        task_name,
      </if>
      <if test="seedGroup != null">
        seed_group,
      </if>
      <if test="predictGroup != null">
        predict_group,
      </if>
      <if test="filterRule != null">
        filter_rule,
      </if>
      <if test="featureSelect != null">
        feature_select,
      </if>
      <if test="threshold != null">
        threshold,
      </if>
      <if test="judgeCriteria != null">
        judge_criteria,
      </if>
      <if test="similarity != null">
        similarity,
      </if>
      <if test="ranking != null">
        ranking,
      </if>
      <if test="triggerMod != null">
        trigger_mod,
      </if>
      <if test="triggerFrequency != null">
        trigger_frequency,
      </if>
      <if test="triggerFrequencyValue != null">
        trigger_frequency_value,
      </if>
      <if test="calStatus != null">
        cal_status,
      </if>
      <if test="lastCalDate != null">
        last_cal_date,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="groupPackage != null">
        group_package,
      </if>
      <if test="del != null">
        del,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="customerGroupId != null">
        customer_group_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="modifierName != null">
        modifier_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskName != null">
        #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="seedGroup != null">
        #{seedGroup,jdbcType=BIGINT},
      </if>
      <if test="predictGroup != null">
        #{predictGroup,jdbcType=VARCHAR},
      </if>
      <if test="filterRule != null">
        #{filterRule,jdbcType=TINYINT},
      </if>
      <if test="featureSelect != null">
        #{featureSelect,jdbcType=TINYINT},
      </if>
      <if test="threshold != null">
        #{threshold,jdbcType=REAL},
      </if>
      <if test="judgeCriteria != null">
        #{judgeCriteria,jdbcType=TINYINT},
      </if>
      <if test="similarity != null">
        #{similarity,jdbcType=REAL},
      </if>
      <if test="ranking != null">
        #{ranking,jdbcType=INTEGER},
      </if>
      <if test="triggerMod != null">
        #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequency != null">
        #{triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequencyValue != null">
        #{triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="calStatus != null">
        #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="lastCalDate != null">
        #{lastCalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="groupPackage != null">
        #{groupPackage,jdbcType=BIT},
      </if>
      <if test="del != null">
        #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerGroupId != null">
        #{customerGroupId,jdbcType=BIGINT},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modifierName != null">
        #{modifierName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTaskCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from customer_diffusion_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update customer_diffusion_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskName != null">
        task_name = #{record.taskName,jdbcType=VARCHAR},
      </if>
      <if test="record.seedGroup != null">
        seed_group = #{record.seedGroup,jdbcType=BIGINT},
      </if>
      <if test="record.predictGroup != null">
        predict_group = #{record.predictGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.filterRule != null">
        filter_rule = #{record.filterRule,jdbcType=TINYINT},
      </if>
      <if test="record.featureSelect != null">
        feature_select = #{record.featureSelect,jdbcType=TINYINT},
      </if>
      <if test="record.threshold != null">
        threshold = #{record.threshold,jdbcType=REAL},
      </if>
      <if test="record.judgeCriteria != null">
        judge_criteria = #{record.judgeCriteria,jdbcType=TINYINT},
      </if>
      <if test="record.similarity != null">
        similarity = #{record.similarity,jdbcType=REAL},
      </if>
      <if test="record.ranking != null">
        ranking = #{record.ranking,jdbcType=INTEGER},
      </if>
      <if test="record.triggerMod != null">
        trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      </if>
      <if test="record.triggerFrequency != null">
        trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="record.triggerFrequencyValue != null">
        trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="record.calStatus != null">
        cal_status = #{record.calStatus,jdbcType=TINYINT},
      </if>
      <if test="record.lastCalDate != null">
        last_cal_date = #{record.lastCalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.groupPackage != null">
        group_package = #{record.groupPackage,jdbcType=BIT},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=BIT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.customerGroupId != null">
        customer_group_id = #{record.customerGroupId,jdbcType=BIGINT},
      </if>
      <if test="record.creatorName != null">
        creator_name = #{record.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.modifierName != null">
        modifier_name = #{record.modifierName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update customer_diffusion_task
    set id = #{record.id,jdbcType=BIGINT},
      task_name = #{record.taskName,jdbcType=VARCHAR},
      seed_group = #{record.seedGroup,jdbcType=BIGINT},
      predict_group = #{record.predictGroup,jdbcType=VARCHAR},
      filter_rule = #{record.filterRule,jdbcType=TINYINT},
      feature_select = #{record.featureSelect,jdbcType=TINYINT},
      threshold = #{record.threshold,jdbcType=REAL},
      judge_criteria = #{record.judgeCriteria,jdbcType=TINYINT},
      similarity = #{record.similarity,jdbcType=REAL},
      ranking = #{record.ranking,jdbcType=INTEGER},
      trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      cal_status = #{record.calStatus,jdbcType=TINYINT},
      last_cal_date = #{record.lastCalDate,jdbcType=TIMESTAMP},
      task_id = #{record.taskId,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      group_package = #{record.groupPackage,jdbcType=BIT},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      customer_group_id = #{record.customerGroupId,jdbcType=BIGINT},
      creator_name = #{record.creatorName,jdbcType=VARCHAR},
      modifier_name = #{record.modifierName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update customer_diffusion_task
    <set>
      <if test="taskName != null">
        task_name = #{taskName,jdbcType=VARCHAR},
      </if>
      <if test="seedGroup != null">
        seed_group = #{seedGroup,jdbcType=BIGINT},
      </if>
      <if test="predictGroup != null">
        predict_group = #{predictGroup,jdbcType=VARCHAR},
      </if>
      <if test="filterRule != null">
        filter_rule = #{filterRule,jdbcType=TINYINT},
      </if>
      <if test="featureSelect != null">
        feature_select = #{featureSelect,jdbcType=TINYINT},
      </if>
      <if test="threshold != null">
        threshold = #{threshold,jdbcType=REAL},
      </if>
      <if test="judgeCriteria != null">
        judge_criteria = #{judgeCriteria,jdbcType=TINYINT},
      </if>
      <if test="similarity != null">
        similarity = #{similarity,jdbcType=REAL},
      </if>
      <if test="ranking != null">
        ranking = #{ranking,jdbcType=INTEGER},
      </if>
      <if test="triggerMod != null">
        trigger_mod = #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequency != null">
        trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequencyValue != null">
        trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="calStatus != null">
        cal_status = #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="lastCalDate != null">
        last_cal_date = #{lastCalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="groupPackage != null">
        group_package = #{groupPackage,jdbcType=BIT},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerGroupId != null">
        customer_group_id = #{customerGroupId,jdbcType=BIGINT},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modifierName != null">
        modifier_name = #{modifierName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update customer_diffusion_task
    set task_name = #{taskName,jdbcType=VARCHAR},
      seed_group = #{seedGroup,jdbcType=BIGINT},
      predict_group = #{predictGroup,jdbcType=VARCHAR},
      filter_rule = #{filterRule,jdbcType=TINYINT},
      feature_select = #{featureSelect,jdbcType=TINYINT},
      threshold = #{threshold,jdbcType=REAL},
      judge_criteria = #{judgeCriteria,jdbcType=TINYINT},
      similarity = #{similarity,jdbcType=REAL},
      ranking = #{ranking,jdbcType=INTEGER},
      trigger_mod = #{triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      cal_status = #{calStatus,jdbcType=TINYINT},
      last_cal_date = #{lastCalDate,jdbcType=TIMESTAMP},
      task_id = #{taskId,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      group_package = #{groupPackage,jdbcType=BIT},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      customer_group_id = #{customerGroupId,jdbcType=BIGINT},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      modifier_name = #{modifierName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>