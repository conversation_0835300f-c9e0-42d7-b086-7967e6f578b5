<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.ExtendIdMappingRelationMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="preset" jdbcType="BIT" property="preset" />
    <result column="data_table_id" jdbcType="BIGINT" property="dataTableId" />
    <result column="en_fields" jdbcType="VARCHAR" property="enFields" />
    <result column="cn_fields" jdbcType="VARCHAR" property="cnFields" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>

  <select id="countPublicId" resultType="java.lang.Long">
    select count(*) from id_mapping_relation
    <where>
      <if test="dataTableId != null">
        AND data_table_id != #{dataTableId}
      </if>
      <if test="tenantId != null">
        AND tenant_id = #{tenantId}
      </if>
      <if test="enFields != null and enFields.size() > 0">
        AND
        <foreach item="enField" collection="enFields" open="(" separator=" OR " close=")">
          JSON_CONTAINS(en_fields, CONCAT('"', #{enField}, '"'))
        </foreach>
      </if>
    </where>
  </select>

  <select id="listDelDataTableName" resultType="java.lang.String">
    SELECT * FROM datatable_info
    LEFT JOIN id_mapping_relation
    ON datatable_info.id = id_mapping_relation.data_table_id
    WHERE id_mapping_relation.del = 0 AND datatable_info.is_del = 1 AND datatable_info.tenantId = #{tenantId}
  </select>
</mapper>