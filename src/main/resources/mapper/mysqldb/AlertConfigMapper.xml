<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.AlertConfigMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="config_type" jdbcType="VARCHAR" property="configType" />
    <result column="config_target" jdbcType="VARCHAR" property="configTarget" />
    <result column="threshold_rate" jdbcType="DECIMAL" property="thresholdRate" />
    <result column="dial_count" jdbcType="INTEGER" property="dialCount" />
    <result column="alert_freq" jdbcType="INTEGER" property="alertFreq" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="alert_time" jdbcType="VARCHAR" property="alertTime" />
    <result column="next_check_time" jdbcType="TIMESTAMP" property="nextCheckTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, config_type, config_target, threshold_rate, dial_count, alert_freq, is_active, 
    update_time, tenant_id, alert_time, next_check_time
  </sql>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AlertConfigCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from alert_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from alert_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from alert_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AlertConfigCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from alert_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into alert_config (config_type, config_target, threshold_rate, 
      dial_count, alert_freq, is_active, 
      update_time, tenant_id, alert_time, 
      next_check_time)
    values (#{configType,jdbcType=VARCHAR}, #{configTarget,jdbcType=VARCHAR}, #{thresholdRate,jdbcType=DECIMAL}, 
      #{dialCount,jdbcType=INTEGER}, #{alertFreq,jdbcType=INTEGER}, #{isActive,jdbcType=BIT}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=VARCHAR}, #{alertTime,jdbcType=VARCHAR}, 
      #{nextCheckTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into alert_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="configType != null">
        config_type,
      </if>
      <if test="configTarget != null">
        config_target,
      </if>
      <if test="thresholdRate != null">
        threshold_rate,
      </if>
      <if test="dialCount != null">
        dial_count,
      </if>
      <if test="alertFreq != null">
        alert_freq,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="alertTime != null">
        alert_time,
      </if>
      <if test="nextCheckTime != null">
        next_check_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="configType != null">
        #{configType,jdbcType=VARCHAR},
      </if>
      <if test="configTarget != null">
        #{configTarget,jdbcType=VARCHAR},
      </if>
      <if test="thresholdRate != null">
        #{thresholdRate,jdbcType=DECIMAL},
      </if>
      <if test="dialCount != null">
        #{dialCount,jdbcType=INTEGER},
      </if>
      <if test="alertFreq != null">
        #{alertFreq,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="alertTime != null">
        #{alertTime,jdbcType=VARCHAR},
      </if>
      <if test="nextCheckTime != null">
        #{nextCheckTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AlertConfigCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from alert_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update alert_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.configType != null">
        config_type = #{record.configType,jdbcType=VARCHAR},
      </if>
      <if test="record.configTarget != null">
        config_target = #{record.configTarget,jdbcType=VARCHAR},
      </if>
      <if test="record.thresholdRate != null">
        threshold_rate = #{record.thresholdRate,jdbcType=DECIMAL},
      </if>
      <if test="record.dialCount != null">
        dial_count = #{record.dialCount,jdbcType=INTEGER},
      </if>
      <if test="record.alertFreq != null">
        alert_freq = #{record.alertFreq,jdbcType=INTEGER},
      </if>
      <if test="record.isActive != null">
        is_active = #{record.isActive,jdbcType=BIT},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.alertTime != null">
        alert_time = #{record.alertTime,jdbcType=VARCHAR},
      </if>
      <if test="record.nextCheckTime != null">
        next_check_time = #{record.nextCheckTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update alert_config
    set id = #{record.id,jdbcType=INTEGER},
      config_type = #{record.configType,jdbcType=VARCHAR},
      config_target = #{record.configTarget,jdbcType=VARCHAR},
      threshold_rate = #{record.thresholdRate,jdbcType=DECIMAL},
      dial_count = #{record.dialCount,jdbcType=INTEGER},
      alert_freq = #{record.alertFreq,jdbcType=INTEGER},
      is_active = #{record.isActive,jdbcType=BIT},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      alert_time = #{record.alertTime,jdbcType=VARCHAR},
      next_check_time = #{record.nextCheckTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update alert_config
    <set>
      <if test="configType != null">
        config_type = #{configType,jdbcType=VARCHAR},
      </if>
      <if test="configTarget != null">
        config_target = #{configTarget,jdbcType=VARCHAR},
      </if>
      <if test="thresholdRate != null">
        threshold_rate = #{thresholdRate,jdbcType=DECIMAL},
      </if>
      <if test="dialCount != null">
        dial_count = #{dialCount,jdbcType=INTEGER},
      </if>
      <if test="alertFreq != null">
        alert_freq = #{alertFreq,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=BIT},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="alertTime != null">
        alert_time = #{alertTime,jdbcType=VARCHAR},
      </if>
      <if test="nextCheckTime != null">
        next_check_time = #{nextCheckTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update alert_config
    set config_type = #{configType,jdbcType=VARCHAR},
      config_target = #{configTarget,jdbcType=VARCHAR},
      threshold_rate = #{thresholdRate,jdbcType=DECIMAL},
      dial_count = #{dialCount,jdbcType=INTEGER},
      alert_freq = #{alertFreq,jdbcType=INTEGER},
      is_active = #{isActive,jdbcType=BIT},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      alert_time = #{alertTime,jdbcType=VARCHAR},
      next_check_time = #{nextCheckTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>