<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.LabelMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.Label">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="catalog_id" jdbcType="BIGINT" property="catalogId" />
    <result column="label_name" jdbcType="VARCHAR" property="labelName" />
    <result column="label_value_update_mod" jdbcType="TINYINT" property="labelValueUpdateMod" />
    <result column="label_value_save_mod" jdbcType="TINYINT" property="labelValueSaveMod" />
    <result column="trigger_mod" jdbcType="TINYINT" property="triggerMod" />
    <result column="trigger_frequency" jdbcType="TINYINT" property="triggerFrequency" />
    <result column="trigger_frequency_value" jdbcType="VARCHAR" property="triggerFrequencyValue" />
    <result column="exec_mod" jdbcType="TINYINT" property="execMod" />
    <result column="field" jdbcType="BIGINT" property="field" />
    <result column="label_cal_status" jdbcType="TINYINT" property="labelCalStatus" />
    <result column="last_cal_date" jdbcType="TIMESTAMP" property="lastCalDate" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="task" jdbcType="BIGINT" property="task" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="recalculate" jdbcType="BIT" property="recalculate" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="label_rule" jdbcType="LONGVARCHAR" property="labelRule" />
    <result column="distribution" jdbcType="LONGVARCHAR" property="distribution" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, user_id, catalog_id, label_name, label_value_update_mod, label_value_save_mod, 
    trigger_mod, trigger_frequency, trigger_frequency_value, exec_mod, field, label_cal_status, 
    last_cal_date, del, creator, modifier, create_time, update_time, task, tenant_id, 
    recalculate
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    label_rule, distribution
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelCriteria" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from label
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from label
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from label
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from label
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from label
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into label (user_id, catalog_id, label_name, 
      label_value_update_mod, label_value_save_mod, 
      trigger_mod, trigger_frequency, trigger_frequency_value, 
      exec_mod, field, label_cal_status, 
      last_cal_date, del, creator, 
      modifier, create_time, update_time, 
      task, tenant_id, recalculate, 
      label_rule, distribution)
    values (#{userId,jdbcType=VARCHAR}, #{catalogId,jdbcType=BIGINT}, #{labelName,jdbcType=VARCHAR}, 
      #{labelValueUpdateMod,jdbcType=TINYINT}, #{labelValueSaveMod,jdbcType=TINYINT}, 
      #{triggerMod,jdbcType=TINYINT}, #{triggerFrequency,jdbcType=TINYINT}, #{triggerFrequencyValue,jdbcType=VARCHAR}, 
      #{execMod,jdbcType=TINYINT}, #{field,jdbcType=BIGINT}, #{labelCalStatus,jdbcType=TINYINT}, 
      #{lastCalDate,jdbcType=TIMESTAMP}, #{del,jdbcType=BIT}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{task,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, #{recalculate,jdbcType=BIT}, 
      #{labelRule,jdbcType=LONGVARCHAR}, #{distribution,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into label
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="catalogId != null">
        catalog_id,
      </if>
      <if test="labelName != null">
        label_name,
      </if>
      <if test="labelValueUpdateMod != null">
        label_value_update_mod,
      </if>
      <if test="labelValueSaveMod != null">
        label_value_save_mod,
      </if>
      <if test="triggerMod != null">
        trigger_mod,
      </if>
      <if test="triggerFrequency != null">
        trigger_frequency,
      </if>
      <if test="triggerFrequencyValue != null">
        trigger_frequency_value,
      </if>
      <if test="execMod != null">
        exec_mod,
      </if>
      <if test="field != null">
        field,
      </if>
      <if test="labelCalStatus != null">
        label_cal_status,
      </if>
      <if test="lastCalDate != null">
        last_cal_date,
      </if>
      <if test="del != null">
        del,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="task != null">
        task,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="recalculate != null">
        recalculate,
      </if>
      <if test="labelRule != null">
        label_rule,
      </if>
      <if test="distribution != null">
        distribution,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="catalogId != null">
        #{catalogId,jdbcType=BIGINT},
      </if>
      <if test="labelName != null">
        #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="labelValueUpdateMod != null">
        #{labelValueUpdateMod,jdbcType=TINYINT},
      </if>
      <if test="labelValueSaveMod != null">
        #{labelValueSaveMod,jdbcType=TINYINT},
      </if>
      <if test="triggerMod != null">
        #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequency != null">
        #{triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequencyValue != null">
        #{triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="execMod != null">
        #{execMod,jdbcType=TINYINT},
      </if>
      <if test="field != null">
        #{field,jdbcType=BIGINT},
      </if>
      <if test="labelCalStatus != null">
        #{labelCalStatus,jdbcType=TINYINT},
      </if>
      <if test="lastCalDate != null">
        #{lastCalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="del != null">
        #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="task != null">
        #{task,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="recalculate != null">
        #{recalculate,jdbcType=BIT},
      </if>
      <if test="labelRule != null">
        #{labelRule,jdbcType=LONGVARCHAR},
      </if>
      <if test="distribution != null">
        #{distribution,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from label
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.catalogId != null">
        catalog_id = #{record.catalogId,jdbcType=BIGINT},
      </if>
      <if test="record.labelName != null">
        label_name = #{record.labelName,jdbcType=VARCHAR},
      </if>
      <if test="record.labelValueUpdateMod != null">
        label_value_update_mod = #{record.labelValueUpdateMod,jdbcType=TINYINT},
      </if>
      <if test="record.labelValueSaveMod != null">
        label_value_save_mod = #{record.labelValueSaveMod,jdbcType=TINYINT},
      </if>
      <if test="record.triggerMod != null">
        trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      </if>
      <if test="record.triggerFrequency != null">
        trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="record.triggerFrequencyValue != null">
        trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="record.execMod != null">
        exec_mod = #{record.execMod,jdbcType=TINYINT},
      </if>
      <if test="record.field != null">
        field = #{record.field,jdbcType=BIGINT},
      </if>
      <if test="record.labelCalStatus != null">
        label_cal_status = #{record.labelCalStatus,jdbcType=TINYINT},
      </if>
      <if test="record.lastCalDate != null">
        last_cal_date = #{record.lastCalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=BIT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.task != null">
        task = #{record.task,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.recalculate != null">
        recalculate = #{record.recalculate,jdbcType=BIT},
      </if>
      <if test="record.labelRule != null">
        label_rule = #{record.labelRule,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.distribution != null">
        distribution = #{record.distribution,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=VARCHAR},
      catalog_id = #{record.catalogId,jdbcType=BIGINT},
      label_name = #{record.labelName,jdbcType=VARCHAR},
      label_value_update_mod = #{record.labelValueUpdateMod,jdbcType=TINYINT},
      label_value_save_mod = #{record.labelValueSaveMod,jdbcType=TINYINT},
      trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      exec_mod = #{record.execMod,jdbcType=TINYINT},
      field = #{record.field,jdbcType=BIGINT},
      label_cal_status = #{record.labelCalStatus,jdbcType=TINYINT},
      last_cal_date = #{record.lastCalDate,jdbcType=TIMESTAMP},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      task = #{record.task,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      recalculate = #{record.recalculate,jdbcType=BIT},
      label_rule = #{record.labelRule,jdbcType=LONGVARCHAR},
      distribution = #{record.distribution,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=VARCHAR},
      catalog_id = #{record.catalogId,jdbcType=BIGINT},
      label_name = #{record.labelName,jdbcType=VARCHAR},
      label_value_update_mod = #{record.labelValueUpdateMod,jdbcType=TINYINT},
      label_value_save_mod = #{record.labelValueSaveMod,jdbcType=TINYINT},
      trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      exec_mod = #{record.execMod,jdbcType=TINYINT},
      field = #{record.field,jdbcType=BIGINT},
      label_cal_status = #{record.labelCalStatus,jdbcType=TINYINT},
      last_cal_date = #{record.lastCalDate,jdbcType=TIMESTAMP},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      task = #{record.task,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      recalculate = #{record.recalculate,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="catalogId != null">
        catalog_id = #{catalogId,jdbcType=BIGINT},
      </if>
      <if test="labelName != null">
        label_name = #{labelName,jdbcType=VARCHAR},
      </if>
      <if test="labelValueUpdateMod != null">
        label_value_update_mod = #{labelValueUpdateMod,jdbcType=TINYINT},
      </if>
      <if test="labelValueSaveMod != null">
        label_value_save_mod = #{labelValueSaveMod,jdbcType=TINYINT},
      </if>
      <if test="triggerMod != null">
        trigger_mod = #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequency != null">
        trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequencyValue != null">
        trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="execMod != null">
        exec_mod = #{execMod,jdbcType=TINYINT},
      </if>
      <if test="field != null">
        field = #{field,jdbcType=BIGINT},
      </if>
      <if test="labelCalStatus != null">
        label_cal_status = #{labelCalStatus,jdbcType=TINYINT},
      </if>
      <if test="lastCalDate != null">
        last_cal_date = #{lastCalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="task != null">
        task = #{task,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="recalculate != null">
        recalculate = #{recalculate,jdbcType=BIT},
      </if>
      <if test="labelRule != null">
        label_rule = #{labelRule,jdbcType=LONGVARCHAR},
      </if>
      <if test="distribution != null">
        distribution = #{distribution,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label
    set user_id = #{userId,jdbcType=VARCHAR},
      catalog_id = #{catalogId,jdbcType=BIGINT},
      label_name = #{labelName,jdbcType=VARCHAR},
      label_value_update_mod = #{labelValueUpdateMod,jdbcType=TINYINT},
      label_value_save_mod = #{labelValueSaveMod,jdbcType=TINYINT},
      trigger_mod = #{triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      exec_mod = #{execMod,jdbcType=TINYINT},
      field = #{field,jdbcType=BIGINT},
      label_cal_status = #{labelCalStatus,jdbcType=TINYINT},
      last_cal_date = #{lastCalDate,jdbcType=TIMESTAMP},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      task = #{task,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      recalculate = #{recalculate,jdbcType=BIT},
      label_rule = #{labelRule,jdbcType=LONGVARCHAR},
      distribution = #{distribution,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.Label">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label
    set user_id = #{userId,jdbcType=VARCHAR},
      catalog_id = #{catalogId,jdbcType=BIGINT},
      label_name = #{labelName,jdbcType=VARCHAR},
      label_value_update_mod = #{labelValueUpdateMod,jdbcType=TINYINT},
      label_value_save_mod = #{labelValueSaveMod,jdbcType=TINYINT},
      trigger_mod = #{triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      exec_mod = #{execMod,jdbcType=TINYINT},
      field = #{field,jdbcType=BIGINT},
      label_cal_status = #{labelCalStatus,jdbcType=TINYINT},
      last_cal_date = #{lastCalDate,jdbcType=TIMESTAMP},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      task = #{task,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      recalculate = #{recalculate,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="labelUpdate">
    update label
    set catalog_id=#{catalogId},
        label_name=#{labelName},
        label_cal_status=#{labelCalStatus},
        label_value_update_mod=#{labelValueUpdateMod},
        label_value_save_mod=#{labelValueSaveMod},
        trigger_mod=#{triggerMod},
        trigger_frequency=#{triggerFrequency},
        trigger_frequency_value=#{triggerFrequencyValue},
        label_rule=#{labelRule},
        modifier=#{modifier},
        recalculate=#{recalculate},
        update_time=#{updateTime}
    where id = #{id}
  </update>
  <update id="updateLabelCalTaskResult">
    update label
    set label_cal_status=#{labelCalStatus},
        distribution=#{distribution},
        recalculate=#{recalculate},
        last_cal_date=#{lastCalDate}
    where id = #{id}
  </update>
  <update id="updateLabelCalTaskStatus">
    update label set label_cal_status=#{labelCalStatus}, distribution=#{distribution} where id = #{id}
  </update>
</mapper>