<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.CustomerGroupMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="customer_group_name" jdbcType="VARCHAR" property="customerGroupName" />
    <result column="customer_group_description" jdbcType="VARCHAR" property="customerGroupDescription" />
    <result column="customer_group_value_update_mod" jdbcType="TINYINT" property="customerGroupValueUpdateMod" />
    <result column="trigger_mod" jdbcType="TINYINT" property="triggerMod" />
    <result column="trigger_frequency" jdbcType="TINYINT" property="triggerFrequency" />
    <result column="trigger_frequency_value" jdbcType="VARCHAR" property="triggerFrequencyValue" />
    <result column="cal_status" jdbcType="TINYINT" property="calStatus" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="last_cal_date" jdbcType="TIMESTAMP" property="lastCalDate" />
    <result column="task" jdbcType="BIGINT" property="task" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="preset" jdbcType="BIT" property="preset" />
    <result column="grouping_type" jdbcType="TINYINT" property="groupingType" />
    <result column="config_tag" jdbcType="BIT" property="configTag" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="customer_group_rule" jdbcType="LONGVARCHAR" property="customerGroupRule" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, user_id, customer_group_name, customer_group_description, customer_group_value_update_mod, 
    trigger_mod, trigger_frequency, trigger_frequency_value, cal_status, del, creator, 
    modifier, create_time, update_time, last_cal_date, task, tenant_id, preset, grouping_type, 
    config_tag
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    customer_group_rule
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroupCriteria" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from customer_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroupCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from customer_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from customer_group
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from customer_group
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroupCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from customer_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into customer_group (user_id, customer_group_name, customer_group_description, 
      customer_group_value_update_mod, trigger_mod, 
      trigger_frequency, trigger_frequency_value, 
      cal_status, del, creator, 
      modifier, create_time, update_time, 
      last_cal_date, task, tenant_id, 
      preset, grouping_type, config_tag, 
      customer_group_rule)
    values (#{userId,jdbcType=VARCHAR}, #{customerGroupName,jdbcType=VARCHAR}, #{customerGroupDescription,jdbcType=VARCHAR}, 
      #{customerGroupValueUpdateMod,jdbcType=TINYINT}, #{triggerMod,jdbcType=TINYINT}, 
      #{triggerFrequency,jdbcType=TINYINT}, #{triggerFrequencyValue,jdbcType=VARCHAR}, 
      #{calStatus,jdbcType=TINYINT}, #{del,jdbcType=BIT}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{lastCalDate,jdbcType=TIMESTAMP}, #{task,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}, 
      #{preset,jdbcType=BIT}, #{groupingType,jdbcType=TINYINT}, #{configTag,jdbcType=BIT}, 
      #{customerGroupRule,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into customer_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="customerGroupName != null">
        customer_group_name,
      </if>
      <if test="customerGroupDescription != null">
        customer_group_description,
      </if>
      <if test="customerGroupValueUpdateMod != null">
        customer_group_value_update_mod,
      </if>
      <if test="triggerMod != null">
        trigger_mod,
      </if>
      <if test="triggerFrequency != null">
        trigger_frequency,
      </if>
      <if test="triggerFrequencyValue != null">
        trigger_frequency_value,
      </if>
      <if test="calStatus != null">
        cal_status,
      </if>
      <if test="del != null">
        del,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="lastCalDate != null">
        last_cal_date,
      </if>
      <if test="task != null">
        task,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="preset != null">
        preset,
      </if>
      <if test="groupingType != null">
        grouping_type,
      </if>
      <if test="configTag != null">
        config_tag,
      </if>
      <if test="customerGroupRule != null">
        customer_group_rule,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupName != null">
        #{customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupDescription != null">
        #{customerGroupDescription,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupValueUpdateMod != null">
        #{customerGroupValueUpdateMod,jdbcType=TINYINT},
      </if>
      <if test="triggerMod != null">
        #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequency != null">
        #{triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequencyValue != null">
        #{triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="calStatus != null">
        #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="del != null">
        #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastCalDate != null">
        #{lastCalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="task != null">
        #{task,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="preset != null">
        #{preset,jdbcType=BIT},
      </if>
      <if test="groupingType != null">
        #{groupingType,jdbcType=TINYINT},
      </if>
      <if test="configTag != null">
        #{configTag,jdbcType=BIT},
      </if>
      <if test="customerGroupRule != null">
        #{customerGroupRule,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroupCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from customer_group
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update customer_group
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.customerGroupName != null">
        customer_group_name = #{record.customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.customerGroupDescription != null">
        customer_group_description = #{record.customerGroupDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.customerGroupValueUpdateMod != null">
        customer_group_value_update_mod = #{record.customerGroupValueUpdateMod,jdbcType=TINYINT},
      </if>
      <if test="record.triggerMod != null">
        trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      </if>
      <if test="record.triggerFrequency != null">
        trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="record.triggerFrequencyValue != null">
        trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="record.calStatus != null">
        cal_status = #{record.calStatus,jdbcType=TINYINT},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=BIT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastCalDate != null">
        last_cal_date = #{record.lastCalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.task != null">
        task = #{record.task,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.preset != null">
        preset = #{record.preset,jdbcType=BIT},
      </if>
      <if test="record.groupingType != null">
        grouping_type = #{record.groupingType,jdbcType=TINYINT},
      </if>
      <if test="record.configTag != null">
        config_tag = #{record.configTag,jdbcType=BIT},
      </if>
      <if test="record.customerGroupRule != null">
        customer_group_rule = #{record.customerGroupRule,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update customer_group
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=VARCHAR},
      customer_group_name = #{record.customerGroupName,jdbcType=VARCHAR},
      customer_group_description = #{record.customerGroupDescription,jdbcType=VARCHAR},
      customer_group_value_update_mod = #{record.customerGroupValueUpdateMod,jdbcType=TINYINT},
      trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      cal_status = #{record.calStatus,jdbcType=TINYINT},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      last_cal_date = #{record.lastCalDate,jdbcType=TIMESTAMP},
      task = #{record.task,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      preset = #{record.preset,jdbcType=BIT},
      grouping_type = #{record.groupingType,jdbcType=TINYINT},
      config_tag = #{record.configTag,jdbcType=BIT},
      customer_group_rule = #{record.customerGroupRule,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update customer_group
    set id = #{record.id,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=VARCHAR},
      customer_group_name = #{record.customerGroupName,jdbcType=VARCHAR},
      customer_group_description = #{record.customerGroupDescription,jdbcType=VARCHAR},
      customer_group_value_update_mod = #{record.customerGroupValueUpdateMod,jdbcType=TINYINT},
      trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      cal_status = #{record.calStatus,jdbcType=TINYINT},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      last_cal_date = #{record.lastCalDate,jdbcType=TIMESTAMP},
      task = #{record.task,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      preset = #{record.preset,jdbcType=BIT},
      grouping_type = #{record.groupingType,jdbcType=TINYINT},
      config_tag = #{record.configTag,jdbcType=BIT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update customer_group
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupName != null">
        customer_group_name = #{customerGroupName,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupDescription != null">
        customer_group_description = #{customerGroupDescription,jdbcType=VARCHAR},
      </if>
      <if test="customerGroupValueUpdateMod != null">
        customer_group_value_update_mod = #{customerGroupValueUpdateMod,jdbcType=TINYINT},
      </if>
      <if test="triggerMod != null">
        trigger_mod = #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequency != null">
        trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequencyValue != null">
        trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="calStatus != null">
        cal_status = #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastCalDate != null">
        last_cal_date = #{lastCalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="task != null">
        task = #{task,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="preset != null">
        preset = #{preset,jdbcType=BIT},
      </if>
      <if test="groupingType != null">
        grouping_type = #{groupingType,jdbcType=TINYINT},
      </if>
      <if test="configTag != null">
        config_tag = #{configTag,jdbcType=BIT},
      </if>
      <if test="customerGroupRule != null">
        customer_group_rule = #{customerGroupRule,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update customer_group
    set user_id = #{userId,jdbcType=VARCHAR},
      customer_group_name = #{customerGroupName,jdbcType=VARCHAR},
      customer_group_description = #{customerGroupDescription,jdbcType=VARCHAR},
      customer_group_value_update_mod = #{customerGroupValueUpdateMod,jdbcType=TINYINT},
      trigger_mod = #{triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      cal_status = #{calStatus,jdbcType=TINYINT},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      last_cal_date = #{lastCalDate,jdbcType=TIMESTAMP},
      task = #{task,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      preset = #{preset,jdbcType=BIT},
      grouping_type = #{groupingType,jdbcType=TINYINT},
      config_tag = #{configTag,jdbcType=BIT},
      customer_group_rule = #{customerGroupRule,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update customer_group
    set user_id = #{userId,jdbcType=VARCHAR},
      customer_group_name = #{customerGroupName,jdbcType=VARCHAR},
      customer_group_description = #{customerGroupDescription,jdbcType=VARCHAR},
      customer_group_value_update_mod = #{customerGroupValueUpdateMod,jdbcType=TINYINT},
      trigger_mod = #{triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      cal_status = #{calStatus,jdbcType=TINYINT},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      last_cal_date = #{lastCalDate,jdbcType=TIMESTAMP},
      task = #{task,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      preset = #{preset,jdbcType=BIT},
      grouping_type = #{groupingType,jdbcType=TINYINT},
      config_tag = #{configTag,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>