<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.TaskInfoMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_type" jdbcType="TINYINT" property="taskType" />
    <result column="task_desc" jdbcType="VARCHAR" property="taskDesc" />
    <result column="trigger_cron" jdbcType="VARCHAR" property="triggerCron" />
    <result column="next_exec_date" jdbcType="TIMESTAMP" property="nextExecDate" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="trigger_mod" jdbcType="TINYINT" property="triggerMod" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="task_conf" jdbcType="LONGVARCHAR" property="taskConf" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, task_type, task_desc, trigger_cron, next_exec_date, del, creator, modifier, create_time, 
    update_time, trigger_mod
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    task_conf
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskInfoCriteria" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskInfoCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from task_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskInfoCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into task_info (task_type, task_desc, trigger_cron, 
      next_exec_date, del, creator, 
      modifier, create_time, update_time, 
      trigger_mod, task_conf)
    values (#{taskType,jdbcType=TINYINT}, #{taskDesc,jdbcType=VARCHAR}, #{triggerCron,jdbcType=VARCHAR}, 
      #{nextExecDate,jdbcType=TIMESTAMP}, #{del,jdbcType=BIT}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{triggerMod,jdbcType=TINYINT}, #{taskConf,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into task_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskType != null">
        task_type,
      </if>
      <if test="taskDesc != null">
        task_desc,
      </if>
      <if test="triggerCron != null">
        trigger_cron,
      </if>
      <if test="nextExecDate != null">
        next_exec_date,
      </if>
      <if test="del != null">
        del,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="triggerMod != null">
        trigger_mod,
      </if>
      <if test="taskConf != null">
        task_conf,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskType != null">
        #{taskType,jdbcType=TINYINT},
      </if>
      <if test="taskDesc != null">
        #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="triggerCron != null">
        #{triggerCron,jdbcType=VARCHAR},
      </if>
      <if test="nextExecDate != null">
        #{nextExecDate,jdbcType=TIMESTAMP},
      </if>
      <if test="del != null">
        #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="triggerMod != null">
        #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="taskConf != null">
        #{taskConf,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskInfoCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update task_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskType != null">
        task_type = #{record.taskType,jdbcType=TINYINT},
      </if>
      <if test="record.taskDesc != null">
        task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.triggerCron != null">
        trigger_cron = #{record.triggerCron,jdbcType=VARCHAR},
      </if>
      <if test="record.nextExecDate != null">
        next_exec_date = #{record.nextExecDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=BIT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.triggerMod != null">
        trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      </if>
      <if test="record.taskConf != null">
        task_conf = #{record.taskConf,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update task_info
    set id = #{record.id,jdbcType=BIGINT},
      task_type = #{record.taskType,jdbcType=TINYINT},
      task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      trigger_cron = #{record.triggerCron,jdbcType=VARCHAR},
      next_exec_date = #{record.nextExecDate,jdbcType=TIMESTAMP},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      task_conf = #{record.taskConf,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update task_info
    set id = #{record.id,jdbcType=BIGINT},
      task_type = #{record.taskType,jdbcType=TINYINT},
      task_desc = #{record.taskDesc,jdbcType=VARCHAR},
      trigger_cron = #{record.triggerCron,jdbcType=VARCHAR},
      next_exec_date = #{record.nextExecDate,jdbcType=TIMESTAMP},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      trigger_mod = #{record.triggerMod,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update task_info
    <set>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=TINYINT},
      </if>
      <if test="taskDesc != null">
        task_desc = #{taskDesc,jdbcType=VARCHAR},
      </if>
      <if test="triggerCron != null">
        trigger_cron = #{triggerCron,jdbcType=VARCHAR},
      </if>
      <if test="nextExecDate != null">
        next_exec_date = #{nextExecDate,jdbcType=TIMESTAMP},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="triggerMod != null">
        trigger_mod = #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="taskConf != null">
        task_conf = #{taskConf,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update task_info
    set task_type = #{taskType,jdbcType=TINYINT},
      task_desc = #{taskDesc,jdbcType=VARCHAR},
      trigger_cron = #{triggerCron,jdbcType=VARCHAR},
      next_exec_date = #{nextExecDate,jdbcType=TIMESTAMP},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      trigger_mod = #{triggerMod,jdbcType=TINYINT},
      task_conf = #{taskConf,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update task_info
    set task_type = #{taskType,jdbcType=TINYINT},
      task_desc = #{taskDesc,jdbcType=VARCHAR},
      trigger_cron = #{triggerCron,jdbcType=VARCHAR},
      next_exec_date = #{nextExecDate,jdbcType=TIMESTAMP},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      trigger_mod = #{triggerMod,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="taskTriggerUpdate">
    update task_info
    set trigger_cron=#{triggerCron},
        next_exec_date=#{nextExecDate},
        modifier=#{modifier},
        update_time=#{updateTime},
        trigger_mod=#{triggerMod}
    where id = #{id}
  </update>
  <update id="updateTaskNextExecDate">
    update task_info set next_exec_date=#{nextExecDate} where id=#{id}
  </update>
</mapper>