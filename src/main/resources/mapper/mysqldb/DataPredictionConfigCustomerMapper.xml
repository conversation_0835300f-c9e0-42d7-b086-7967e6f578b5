<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.DataPredictionConfigCustomerMapper">

  <insert id="batchInsert">
    INSERT INTO data_prediction_config (tenant_id, prediction_type, status,
      description, del, creator, modifier, create_time, update_time)
    VALUES
    <foreach collection="list" item="data" separator=",">
      (#{data.tenantId}, #{data.predictionType} , #{data.status}, #{data.description},
      #{data.del}, #{data.creator}, #{data.modifier}, #{data.createTime}, #{data.updateTime})
    </foreach>
  </insert>
</mapper>