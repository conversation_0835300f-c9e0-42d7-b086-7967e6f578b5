<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.LabelFieldMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.LabelField">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="field_type" jdbcType="VARCHAR" property="fieldType" />
    <result column="field_desc" jdbcType="VARCHAR" property="fieldDesc" />
    <result column="label_table" jdbcType="VARCHAR" property="labelTable" />
    <result column="table_space" jdbcType="VARCHAR" property="tableSpace" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, field_type, field_desc, label_table, table_space, del, creator, modifier, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelFieldCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from label_field
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from label_field
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from label_field
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelFieldCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from label_field
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelField" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into label_field (field_type, field_desc, label_table, 
      table_space, del, creator, 
      modifier, create_time, update_time
      )
    values (#{fieldType,jdbcType=VARCHAR}, #{fieldDesc,jdbcType=VARCHAR}, #{labelTable,jdbcType=VARCHAR}, 
      #{tableSpace,jdbcType=VARCHAR}, #{del,jdbcType=BIT}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelField" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into label_field
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fieldType != null">
        field_type,
      </if>
      <if test="fieldDesc != null">
        field_desc,
      </if>
      <if test="labelTable != null">
        label_table,
      </if>
      <if test="tableSpace != null">
        table_space,
      </if>
      <if test="del != null">
        del,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fieldType != null">
        #{fieldType,jdbcType=VARCHAR},
      </if>
      <if test="fieldDesc != null">
        #{fieldDesc,jdbcType=VARCHAR},
      </if>
      <if test="labelTable != null">
        #{labelTable,jdbcType=VARCHAR},
      </if>
      <if test="tableSpace != null">
        #{tableSpace,jdbcType=VARCHAR},
      </if>
      <if test="del != null">
        #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelFieldCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from label_field
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label_field
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.fieldType != null">
        field_type = #{record.fieldType,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldDesc != null">
        field_desc = #{record.fieldDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.labelTable != null">
        label_table = #{record.labelTable,jdbcType=VARCHAR},
      </if>
      <if test="record.tableSpace != null">
        table_space = #{record.tableSpace,jdbcType=VARCHAR},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=BIT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label_field
    set id = #{record.id,jdbcType=BIGINT},
      field_type = #{record.fieldType,jdbcType=VARCHAR},
      field_desc = #{record.fieldDesc,jdbcType=VARCHAR},
      label_table = #{record.labelTable,jdbcType=VARCHAR},
      table_space = #{record.tableSpace,jdbcType=VARCHAR},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelField">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label_field
    <set>
      <if test="fieldType != null">
        field_type = #{fieldType,jdbcType=VARCHAR},
      </if>
      <if test="fieldDesc != null">
        field_desc = #{fieldDesc,jdbcType=VARCHAR},
      </if>
      <if test="labelTable != null">
        label_table = #{labelTable,jdbcType=VARCHAR},
      </if>
      <if test="tableSpace != null">
        table_space = #{tableSpace,jdbcType=VARCHAR},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelField">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label_field
    set field_type = #{fieldType,jdbcType=VARCHAR},
      field_desc = #{fieldDesc,jdbcType=VARCHAR},
      label_table = #{labelTable,jdbcType=VARCHAR},
      table_space = #{tableSpace,jdbcType=VARCHAR},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>