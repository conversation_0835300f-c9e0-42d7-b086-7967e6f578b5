<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.ExtendIdMappingDataTableMapper">
  <resultMap id="IdMappingDataTableWithRule" type="com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTableWithRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_table_id" jdbcType="BIGINT" property="dataTableId" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="table_en_name" jdbcType="VARCHAR" property="tableEnName" />

    <collection property="idMappingRules" ofType="com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule">
      <id column="rule_id" jdbcType="BIGINT" property="id" />
      <result column="rule_en_field" jdbcType="VARCHAR" property="enField" />
      <result column="rule_cn_field" jdbcType="VARCHAR" property="cnField" />
      <result column="rule_description" jdbcType="VARCHAR" property="description" />
      <result column="rule_field_type" jdbcType="TINYINT" property="fieldType" />
      <result column="rule_preset" jdbcType="BIT" property="preset" />
      <result column="rule_merge_policy" jdbcType="TINYINT" property="mergePolicy" />
      <result column="rule_priority" jdbcType="INTEGER" property="priority" />
      <result column="rule_tenant_id" jdbcType="VARCHAR" property="tenantId" />
      <result column="rule_creator" jdbcType="VARCHAR" property="creator" />
      <result column="rule_modifier" jdbcType="VARCHAR" property="modifier" />
      <result column="rule_create_time" jdbcType="TIMESTAMP" property="createTime" />
      <result column="rule_update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </collection>
  </resultMap>
  <select id="selectIdMappingDataTableWithRule"
          resultMap="IdMappingDataTableWithRule">
    SELECT
      t2.table_en_name AS table_en_name,
      t1.id, t1.data_table_id, t1.del, t1.creator, t1.modifier, t1.create_time, t1.update_time, t1.tenant_id,
      t3.id AS rule_id, t3.en_field AS rule_en_field, t3.cn_field AS rule_cn_field, t3.description  AS rule_description,
      t3.field_type AS rule_field_type, t3.preset AS rule_preset, t3.merge_policy AS rule_merge_policy, t3.priority AS rule_priority,
      t3.tenant_id AS rule_tenant_id, t3.creator AS rule_creator, t3.modifier AS rule_modifier, t3.create_time AS rule_create_time, t3.update_time AS rule_update_time
    FROM id_mapping_datatable t1
           INNER JOIN datatable_meta_info t2 ON t1.data_table_id = t2.data_table_id
           INNER JOIN id_mapping_rule t3 ON t2.en_field = t3.en_field AND t1.tenant_id = t3.tenant_id
    WHERE t1.tenant_id = #{tenantId} AND t1.del = #{del}
    ORDER BY t3.priority ASC
  </select>

</mapper>