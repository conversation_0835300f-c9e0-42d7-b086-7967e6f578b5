<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.MemoryExtractMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtract">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="data_table_id" jdbcType="BIGINT" property="dataTableId" />
    <result column="dataset_name" jdbcType="VARCHAR" property="datasetName" />
    <result column="field_id" jdbcType="BIGINT" property="fieldId" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="prompt_type" jdbcType="TINYINT" property="promptType" />
    <result column="trigger_frequency" jdbcType="TINYINT" property="triggerFrequency" />
    <result column="trigger_frequency_value" jdbcType="VARCHAR" property="triggerFrequencyValue" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="status" jdbcType="BIT" property="status" />
    <result column="task" jdbcType="BIGINT" property="task" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="trigger_mod" jdbcType="TINYINT" property="triggerMod" />
    <result column="is_default" jdbcType="BIT" property="isDefault" />
    <result column="cal_status" jdbcType="TINYINT" property="calStatus" />
    <result column="field_name_en" jdbcType="VARCHAR" property="fieldNameEn" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="prompt" jdbcType="LONGVARCHAR" property="prompt" />
    <result column="data_filter_rule" jdbcType="LONGVARCHAR" property="dataFilterRule" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tenant_id, data_table_id, dataset_name, field_id, field_name, prompt_type, trigger_frequency, 
    trigger_frequency_value, description, status, task, del, creator, modifier, create_time, 
    update_time, trigger_mod, is_default, cal_status, field_name_en
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    prompt, data_filter_rule
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractCriteria" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from memory_extract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from memory_extract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from memory_extract
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from memory_extract
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from memory_extract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into memory_extract (tenant_id, data_table_id, dataset_name, 
      field_id, field_name, prompt_type, 
      trigger_frequency, trigger_frequency_value, 
      description, status, task, 
      del, creator, modifier, 
      create_time, update_time, trigger_mod, 
      is_default, cal_status, field_name_en, 
      prompt, data_filter_rule)
    values (#{tenantId,jdbcType=VARCHAR}, #{dataTableId,jdbcType=BIGINT}, #{datasetName,jdbcType=VARCHAR}, 
      #{fieldId,jdbcType=BIGINT}, #{fieldName,jdbcType=VARCHAR}, #{promptType,jdbcType=TINYINT}, 
      #{triggerFrequency,jdbcType=TINYINT}, #{triggerFrequencyValue,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{status,jdbcType=BIT}, #{task,jdbcType=BIGINT}, 
      #{del,jdbcType=BIT}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{triggerMod,jdbcType=TINYINT}, 
      #{isDefault,jdbcType=BIT}, #{calStatus,jdbcType=TINYINT}, #{fieldNameEn,jdbcType=VARCHAR}, 
      #{prompt,jdbcType=LONGVARCHAR}, #{dataFilterRule,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into memory_extract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="dataTableId != null">
        data_table_id,
      </if>
      <if test="datasetName != null">
        dataset_name,
      </if>
      <if test="fieldId != null">
        field_id,
      </if>
      <if test="fieldName != null">
        field_name,
      </if>
      <if test="promptType != null">
        prompt_type,
      </if>
      <if test="triggerFrequency != null">
        trigger_frequency,
      </if>
      <if test="triggerFrequencyValue != null">
        trigger_frequency_value,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="task != null">
        task,
      </if>
      <if test="del != null">
        del,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="triggerMod != null">
        trigger_mod,
      </if>
      <if test="isDefault != null">
        is_default,
      </if>
      <if test="calStatus != null">
        cal_status,
      </if>
      <if test="fieldNameEn != null">
        field_name_en,
      </if>
      <if test="prompt != null">
        prompt,
      </if>
      <if test="dataFilterRule != null">
        data_filter_rule,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="dataTableId != null">
        #{dataTableId,jdbcType=BIGINT},
      </if>
      <if test="datasetName != null">
        #{datasetName,jdbcType=VARCHAR},
      </if>
      <if test="fieldId != null">
        #{fieldId,jdbcType=BIGINT},
      </if>
      <if test="fieldName != null">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="promptType != null">
        #{promptType,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequency != null">
        #{triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequencyValue != null">
        #{triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="task != null">
        #{task,jdbcType=BIGINT},
      </if>
      <if test="del != null">
        #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="triggerMod != null">
        #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=BIT},
      </if>
      <if test="calStatus != null">
        #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="fieldNameEn != null">
        #{fieldNameEn,jdbcType=VARCHAR},
      </if>
      <if test="prompt != null">
        #{prompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="dataFilterRule != null">
        #{dataFilterRule,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from memory_extract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update memory_extract
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataTableId != null">
        data_table_id = #{record.dataTableId,jdbcType=BIGINT},
      </if>
      <if test="record.datasetName != null">
        dataset_name = #{record.datasetName,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldId != null">
        field_id = #{record.fieldId,jdbcType=BIGINT},
      </if>
      <if test="record.fieldName != null">
        field_name = #{record.fieldName,jdbcType=VARCHAR},
      </if>
      <if test="record.promptType != null">
        prompt_type = #{record.promptType,jdbcType=TINYINT},
      </if>
      <if test="record.triggerFrequency != null">
        trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="record.triggerFrequencyValue != null">
        trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=BIT},
      </if>
      <if test="record.task != null">
        task = #{record.task,jdbcType=BIGINT},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=BIT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.triggerMod != null">
        trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      </if>
      <if test="record.isDefault != null">
        is_default = #{record.isDefault,jdbcType=BIT},
      </if>
      <if test="record.calStatus != null">
        cal_status = #{record.calStatus,jdbcType=TINYINT},
      </if>
      <if test="record.fieldNameEn != null">
        field_name_en = #{record.fieldNameEn,jdbcType=VARCHAR},
      </if>
      <if test="record.prompt != null">
        prompt = #{record.prompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.dataFilterRule != null">
        data_filter_rule = #{record.dataFilterRule,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update memory_extract
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      data_table_id = #{record.dataTableId,jdbcType=BIGINT},
      dataset_name = #{record.datasetName,jdbcType=VARCHAR},
      field_id = #{record.fieldId,jdbcType=BIGINT},
      field_name = #{record.fieldName,jdbcType=VARCHAR},
      prompt_type = #{record.promptType,jdbcType=TINYINT},
      trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=BIT},
      task = #{record.task,jdbcType=BIGINT},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      is_default = #{record.isDefault,jdbcType=BIT},
      cal_status = #{record.calStatus,jdbcType=TINYINT},
      field_name_en = #{record.fieldNameEn,jdbcType=VARCHAR},
      prompt = #{record.prompt,jdbcType=LONGVARCHAR},
      data_filter_rule = #{record.dataFilterRule,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update memory_extract
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      data_table_id = #{record.dataTableId,jdbcType=BIGINT},
      dataset_name = #{record.datasetName,jdbcType=VARCHAR},
      field_id = #{record.fieldId,jdbcType=BIGINT},
      field_name = #{record.fieldName,jdbcType=VARCHAR},
      prompt_type = #{record.promptType,jdbcType=TINYINT},
      trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=BIT},
      task = #{record.task,jdbcType=BIGINT},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      is_default = #{record.isDefault,jdbcType=BIT},
      cal_status = #{record.calStatus,jdbcType=TINYINT},
      field_name_en = #{record.fieldNameEn,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update memory_extract
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="dataTableId != null">
        data_table_id = #{dataTableId,jdbcType=BIGINT},
      </if>
      <if test="datasetName != null">
        dataset_name = #{datasetName,jdbcType=VARCHAR},
      </if>
      <if test="fieldId != null">
        field_id = #{fieldId,jdbcType=BIGINT},
      </if>
      <if test="fieldName != null">
        field_name = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="promptType != null">
        prompt_type = #{promptType,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequency != null">
        trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequencyValue != null">
        trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=BIT},
      </if>
      <if test="task != null">
        task = #{task,jdbcType=BIGINT},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="triggerMod != null">
        trigger_mod = #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="isDefault != null">
        is_default = #{isDefault,jdbcType=BIT},
      </if>
      <if test="calStatus != null">
        cal_status = #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="fieldNameEn != null">
        field_name_en = #{fieldNameEn,jdbcType=VARCHAR},
      </if>
      <if test="prompt != null">
        prompt = #{prompt,jdbcType=LONGVARCHAR},
      </if>
      <if test="dataFilterRule != null">
        data_filter_rule = #{dataFilterRule,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update memory_extract
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      data_table_id = #{dataTableId,jdbcType=BIGINT},
      dataset_name = #{datasetName,jdbcType=VARCHAR},
      field_id = #{fieldId,jdbcType=BIGINT},
      field_name = #{fieldName,jdbcType=VARCHAR},
      prompt_type = #{promptType,jdbcType=TINYINT},
      trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      status = #{status,jdbcType=BIT},
      task = #{task,jdbcType=BIGINT},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      trigger_mod = #{triggerMod,jdbcType=TINYINT},
      is_default = #{isDefault,jdbcType=BIT},
      cal_status = #{calStatus,jdbcType=TINYINT},
      field_name_en = #{fieldNameEn,jdbcType=VARCHAR},
      prompt = #{prompt,jdbcType=LONGVARCHAR},
      data_filter_rule = #{dataFilterRule,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtract">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update memory_extract
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      data_table_id = #{dataTableId,jdbcType=BIGINT},
      dataset_name = #{datasetName,jdbcType=VARCHAR},
      field_id = #{fieldId,jdbcType=BIGINT},
      field_name = #{fieldName,jdbcType=VARCHAR},
      prompt_type = #{promptType,jdbcType=TINYINT},
      trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      status = #{status,jdbcType=BIT},
      task = #{task,jdbcType=BIGINT},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      trigger_mod = #{triggerMod,jdbcType=TINYINT},
      is_default = #{isDefault,jdbcType=BIT},
      cal_status = #{calStatus,jdbcType=TINYINT},
      field_name_en = #{fieldNameEn,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <update id="updateCalTaskStatus">
      update memory_extract set cal_status=#{labelCalStatus} where id = #{id}
    </update>
</mapper>