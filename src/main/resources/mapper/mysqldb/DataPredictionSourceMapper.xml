<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.DataPredictionSourceMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSource">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="prompt_type" jdbcType="TINYINT" property="promptType" />
    <result column="prediction_update_type" jdbcType="BIT" property="predictionUpdateType" />
    <result column="trigger_mod" jdbcType="TINYINT" property="triggerMod" />
    <result column="trigger_frequency" jdbcType="TINYINT" property="triggerFrequency" />
    <result column="trigger_frequency_value" jdbcType="VARCHAR" property="triggerFrequencyValue" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="task" jdbcType="BIGINT" property="task" />
    <result column="cal_status" jdbcType="TINYINT" property="calStatus" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="data_source_list" jdbcType="LONGVARCHAR" property="dataSourceList" />
    <result column="prompt" jdbcType="LONGVARCHAR" property="prompt" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tenant_id, prompt_type, prediction_update_type, trigger_mod, trigger_frequency, 
    trigger_frequency_value, description, task, cal_status, del, creator, modifier, create_time, 
    update_time
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    data_source_list, prompt
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceCriteria" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from data_prediction_source
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from data_prediction_source
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from data_prediction_source
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from data_prediction_source
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from data_prediction_source
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into data_prediction_source (tenant_id, prompt_type, prediction_update_type, 
      trigger_mod, trigger_frequency, trigger_frequency_value, 
      description, task, cal_status, 
      del, creator, modifier, 
      create_time, update_time, data_source_list, 
      prompt)
    values (#{tenantId,jdbcType=VARCHAR}, #{promptType,jdbcType=TINYINT}, #{predictionUpdateType,jdbcType=BIT}, 
      #{triggerMod,jdbcType=TINYINT}, #{triggerFrequency,jdbcType=TINYINT}, #{triggerFrequencyValue,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{task,jdbcType=BIGINT}, #{calStatus,jdbcType=TINYINT}, 
      #{del,jdbcType=BIT}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{dataSourceList,jdbcType=LONGVARCHAR}, 
      #{prompt,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into data_prediction_source
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="promptType != null">
        prompt_type,
      </if>
      <if test="predictionUpdateType != null">
        prediction_update_type,
      </if>
      <if test="triggerMod != null">
        trigger_mod,
      </if>
      <if test="triggerFrequency != null">
        trigger_frequency,
      </if>
      <if test="triggerFrequencyValue != null">
        trigger_frequency_value,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="task != null">
        task,
      </if>
      <if test="calStatus != null">
        cal_status,
      </if>
      <if test="del != null">
        del,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="dataSourceList != null">
        data_source_list,
      </if>
      <if test="prompt != null">
        prompt,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="promptType != null">
        #{promptType,jdbcType=TINYINT},
      </if>
      <if test="predictionUpdateType != null">
        #{predictionUpdateType,jdbcType=BIT},
      </if>
      <if test="triggerMod != null">
        #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequency != null">
        #{triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequencyValue != null">
        #{triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="task != null">
        #{task,jdbcType=BIGINT},
      </if>
      <if test="calStatus != null">
        #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="del != null">
        #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataSourceList != null">
        #{dataSourceList,jdbcType=LONGVARCHAR},
      </if>
      <if test="prompt != null">
        #{prompt,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from data_prediction_source
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update data_prediction_source
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.promptType != null">
        prompt_type = #{record.promptType,jdbcType=TINYINT},
      </if>
      <if test="record.predictionUpdateType != null">
        prediction_update_type = #{record.predictionUpdateType,jdbcType=BIT},
      </if>
      <if test="record.triggerMod != null">
        trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      </if>
      <if test="record.triggerFrequency != null">
        trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="record.triggerFrequencyValue != null">
        trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.task != null">
        task = #{record.task,jdbcType=BIGINT},
      </if>
      <if test="record.calStatus != null">
        cal_status = #{record.calStatus,jdbcType=TINYINT},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=BIT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.dataSourceList != null">
        data_source_list = #{record.dataSourceList,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.prompt != null">
        prompt = #{record.prompt,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update data_prediction_source
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      prompt_type = #{record.promptType,jdbcType=TINYINT},
      prediction_update_type = #{record.predictionUpdateType,jdbcType=BIT},
      trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      task = #{record.task,jdbcType=BIGINT},
      cal_status = #{record.calStatus,jdbcType=TINYINT},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      data_source_list = #{record.dataSourceList,jdbcType=LONGVARCHAR},
      prompt = #{record.prompt,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update data_prediction_source
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      prompt_type = #{record.promptType,jdbcType=TINYINT},
      prediction_update_type = #{record.predictionUpdateType,jdbcType=BIT},
      trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      task = #{record.task,jdbcType=BIGINT},
      cal_status = #{record.calStatus,jdbcType=TINYINT},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update data_prediction_source
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="promptType != null">
        prompt_type = #{promptType,jdbcType=TINYINT},
      </if>
      <if test="predictionUpdateType != null">
        prediction_update_type = #{predictionUpdateType,jdbcType=BIT},
      </if>
      <if test="triggerMod != null">
        trigger_mod = #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequency != null">
        trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequencyValue != null">
        trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="task != null">
        task = #{task,jdbcType=BIGINT},
      </if>
      <if test="calStatus != null">
        cal_status = #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dataSourceList != null">
        data_source_list = #{dataSourceList,jdbcType=LONGVARCHAR},
      </if>
      <if test="prompt != null">
        prompt = #{prompt,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update data_prediction_source
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      prompt_type = #{promptType,jdbcType=TINYINT},
      prediction_update_type = #{predictionUpdateType,jdbcType=BIT},
      trigger_mod = #{triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      task = #{task,jdbcType=BIGINT},
      cal_status = #{calStatus,jdbcType=TINYINT},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      data_source_list = #{dataSourceList,jdbcType=LONGVARCHAR},
      prompt = #{prompt,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSource">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update data_prediction_source
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      prompt_type = #{promptType,jdbcType=TINYINT},
      prediction_update_type = #{predictionUpdateType,jdbcType=BIT},
      trigger_mod = #{triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      task = #{task,jdbcType=BIGINT},
      cal_status = #{calStatus,jdbcType=TINYINT},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>