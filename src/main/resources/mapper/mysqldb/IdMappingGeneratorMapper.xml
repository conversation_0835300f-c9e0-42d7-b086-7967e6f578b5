<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingGeneratorMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGenerator">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="trigger_mod" jdbcType="TINYINT" property="triggerMod" />
    <result column="trigger_frequency" jdbcType="TINYINT" property="triggerFrequency" />
    <result column="trigger_frequency_value" jdbcType="VARCHAR" property="triggerFrequencyValue" />
    <result column="cal_status" jdbcType="TINYINT" property="calStatus" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="last_cal_date" jdbcType="TIMESTAMP" property="lastCalDate" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, trigger_mod, trigger_frequency, trigger_frequency_value, cal_status, del, creator, 
    modifier, create_time, update_time, last_cal_date, task_id, tenant_id
  </sql>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGeneratorCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from id_mapping_generator
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from id_mapping_generator
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from id_mapping_generator
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGeneratorCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from id_mapping_generator
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGenerator" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into id_mapping_generator (trigger_mod, trigger_frequency, trigger_frequency_value, 
      cal_status, del, creator, 
      modifier, create_time, update_time, 
      last_cal_date, task_id, tenant_id
      )
    values (#{triggerMod,jdbcType=TINYINT}, #{triggerFrequency,jdbcType=TINYINT}, #{triggerFrequencyValue,jdbcType=VARCHAR}, 
      #{calStatus,jdbcType=TINYINT}, #{del,jdbcType=BIT}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{lastCalDate,jdbcType=TIMESTAMP}, #{taskId,jdbcType=BIGINT}, #{tenantId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGenerator" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into id_mapping_generator
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="triggerMod != null">
        trigger_mod,
      </if>
      <if test="triggerFrequency != null">
        trigger_frequency,
      </if>
      <if test="triggerFrequencyValue != null">
        trigger_frequency_value,
      </if>
      <if test="calStatus != null">
        cal_status,
      </if>
      <if test="del != null">
        del,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="lastCalDate != null">
        last_cal_date,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="triggerMod != null">
        #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequency != null">
        #{triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequencyValue != null">
        #{triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="calStatus != null">
        #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="del != null">
        #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastCalDate != null">
        #{lastCalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGeneratorCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from id_mapping_generator
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update id_mapping_generator
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.triggerMod != null">
        trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      </if>
      <if test="record.triggerFrequency != null">
        trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="record.triggerFrequencyValue != null">
        trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="record.calStatus != null">
        cal_status = #{record.calStatus,jdbcType=TINYINT},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=BIT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastCalDate != null">
        last_cal_date = #{record.lastCalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update id_mapping_generator
    set id = #{record.id,jdbcType=BIGINT},
      trigger_mod = #{record.triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{record.triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{record.triggerFrequencyValue,jdbcType=VARCHAR},
      cal_status = #{record.calStatus,jdbcType=TINYINT},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      last_cal_date = #{record.lastCalDate,jdbcType=TIMESTAMP},
      task_id = #{record.taskId,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGenerator">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update id_mapping_generator
    <set>
      <if test="triggerMod != null">
        trigger_mod = #{triggerMod,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequency != null">
        trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      </if>
      <if test="triggerFrequencyValue != null">
        trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      </if>
      <if test="calStatus != null">
        cal_status = #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastCalDate != null">
        last_cal_date = #{lastCalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGenerator">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update id_mapping_generator
    set trigger_mod = #{triggerMod,jdbcType=TINYINT},
      trigger_frequency = #{triggerFrequency,jdbcType=TINYINT},
      trigger_frequency_value = #{triggerFrequencyValue,jdbcType=VARCHAR},
      cal_status = #{calStatus,jdbcType=TINYINT},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      last_cal_date = #{lastCalDate,jdbcType=TIMESTAMP},
      task_id = #{taskId,jdbcType=BIGINT},
      tenant_id = #{tenantId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>