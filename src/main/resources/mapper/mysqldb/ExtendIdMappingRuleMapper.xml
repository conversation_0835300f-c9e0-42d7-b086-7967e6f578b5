<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.ExtendIdMappingRuleMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="en_field" jdbcType="VARCHAR" property="enField" />
    <result column="cn_field" jdbcType="VARCHAR" property="cnField" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="field_type" jdbcType="TINYINT" property="fieldType" />
    <result column="preset" jdbcType="BIT" property="preset" />
    <result column="merge_policy" jdbcType="TINYINT" property="mergePolicy" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="list" useGeneratedKeys="true">
    INSERT INTO id_mapping_rule (
    en_field,
    cn_field,
    description,
    field_type,
    preset,
    merge_policy,
    priority,
    tenant_id,
    creator,
    modifier,
    create_time,
    update_time
    ) VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.enField},
      #{item.cnField},
      #{item.description},
      #{item.fieldType},
      #{item.preset},
      #{item.mergePolicy},
      #{item.priority},
      #{item.tenantId},
      #{item.creator},
      #{item.modifier},
      #{item.createTime},
      #{item.updateTime}
      )
    </foreach>
  </insert>
</mapper>