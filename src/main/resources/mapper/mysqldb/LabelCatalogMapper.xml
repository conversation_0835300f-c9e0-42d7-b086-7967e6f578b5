<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.LabelCatalogMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="parent_id" jdbcType="BIGINT" property="parentId" />
    <result column="catalog_name" jdbcType="VARCHAR" property="catalogName" />
    <result column="sort" jdbcType="BIGINT" property="sort" />
    <result column="user_id" jdbcType="VARCHAR" property="userId" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="label_count" jdbcType="INTEGER" property="labelCount" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, parent_id, catalog_name, sort, user_id, del, creator, modifier, create_time, 
    update_time, label_count, tenant_id
  </sql>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalogCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from label_catalog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from label_catalog
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from label_catalog
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalogCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from label_catalog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into label_catalog (parent_id, catalog_name, sort, 
      user_id, del, creator, modifier, 
      create_time, update_time, label_count, 
      tenant_id)
    values (#{parentId,jdbcType=BIGINT}, #{catalogName,jdbcType=VARCHAR}, #{sort,jdbcType=BIGINT}, 
      #{userId,jdbcType=VARCHAR}, #{del,jdbcType=BIT}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{labelCount,jdbcType=INTEGER}, 
      #{tenantId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into label_catalog
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="catalogName != null">
        catalog_name,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="del != null">
        del,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="labelCount != null">
        label_count,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        #{parentId,jdbcType=BIGINT},
      </if>
      <if test="catalogName != null">
        #{catalogName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="del != null">
        #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="labelCount != null">
        #{labelCount,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalogCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from label_catalog
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label_catalog
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=BIGINT},
      </if>
      <if test="record.catalogName != null">
        catalog_name = #{record.catalogName,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=VARCHAR},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=BIT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.labelCount != null">
        label_count = #{record.labelCount,jdbcType=INTEGER},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label_catalog
    set id = #{record.id,jdbcType=BIGINT},
      parent_id = #{record.parentId,jdbcType=BIGINT},
      catalog_name = #{record.catalogName,jdbcType=VARCHAR},
      sort = #{record.sort,jdbcType=BIGINT},
      user_id = #{record.userId,jdbcType=VARCHAR},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      label_count = #{record.labelCount,jdbcType=INTEGER},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label_catalog
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=BIGINT},
      </if>
      <if test="catalogName != null">
        catalog_name = #{catalogName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="labelCount != null">
        label_count = #{labelCount,jdbcType=INTEGER},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update label_catalog
    set parent_id = #{parentId,jdbcType=BIGINT},
      catalog_name = #{catalogName,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=BIGINT},
      user_id = #{userId,jdbcType=VARCHAR},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      label_count = #{labelCount,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="catalogMoveDown">
    update label_catalog
    set sort = sort + 2, update_time = #{updateTime}, `modifier` = #{modifier}
    where id in
    <foreach item='item' index='index' collection='ids' open='(' separator=',' close=')'>
      #{item}
    </foreach>
  </update>
</mapper>