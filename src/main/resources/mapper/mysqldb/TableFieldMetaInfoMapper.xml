<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="table_en_name" jdbcType="VARCHAR" property="tableEnName" />
    <result column="data_table_id" jdbcType="BIGINT" property="dataTableId" />
    <result column="en_field" jdbcType="VARCHAR" property="enField" />
    <result column="cn_field" jdbcType="VARCHAR" property="cnField" />
    <result column="field_type" jdbcType="VARCHAR" property="fieldType" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="is_filter_criteria" jdbcType="BIT" property="isFilterCriteria" />
    <result column="is_required" jdbcType="BIT" property="isRequired" />
    <result column="is_secrete" jdbcType="BIT" property="isSecrete" />
    <result column="from_baidu" jdbcType="BIT" property="fromBaidu" />
    <result column="is_visable" jdbcType="BIT" property="isVisable" />
    <result column="field_tag" jdbcType="INTEGER" property="fieldTag" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="value_type" jdbcType="VARCHAR" property="valueType" />
    <result column="data_type" jdbcType="VARCHAR" property="dataType" />
    <result column="is_show_value" jdbcType="BIT" property="isShowValue" />
    <result column="number" jdbcType="INTEGER" property="number" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="config_infos" jdbcType="LONGVARCHAR" property="configInfos" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, table_en_name, data_table_id, en_field, cn_field, field_type, description, is_filter_criteria, 
    is_required, is_secrete, from_baidu, is_visable, field_tag, create_time, update_time, 
    value_type, data_type, is_show_value, number
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    config_infos
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from datatable_meta_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datatable_meta_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from datatable_meta_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from datatable_meta_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from datatable_meta_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into datatable_meta_info (table_en_name, data_table_id, en_field, 
      cn_field, field_type, description, 
      is_filter_criteria, is_required, is_secrete, 
      from_baidu, is_visable, field_tag, 
      create_time, update_time, value_type, 
      data_type, is_show_value, number, 
      config_infos)
    values (#{tableEnName,jdbcType=VARCHAR}, #{dataTableId,jdbcType=BIGINT}, #{enField,jdbcType=VARCHAR}, 
      #{cnField,jdbcType=VARCHAR}, #{fieldType,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{isFilterCriteria,jdbcType=BIT}, #{isRequired,jdbcType=BIT}, #{isSecrete,jdbcType=BIT}, 
      #{fromBaidu,jdbcType=BIT}, #{isVisable,jdbcType=BIT}, #{fieldTag,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{valueType,jdbcType=VARCHAR}, 
      #{dataType,jdbcType=VARCHAR}, #{isShowValue,jdbcType=BIT}, #{number,jdbcType=INTEGER}, 
      #{configInfos,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into datatable_meta_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tableEnName != null">
        table_en_name,
      </if>
      <if test="dataTableId != null">
        data_table_id,
      </if>
      <if test="enField != null">
        en_field,
      </if>
      <if test="cnField != null">
        cn_field,
      </if>
      <if test="fieldType != null">
        field_type,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="isFilterCriteria != null">
        is_filter_criteria,
      </if>
      <if test="isRequired != null">
        is_required,
      </if>
      <if test="isSecrete != null">
        is_secrete,
      </if>
      <if test="fromBaidu != null">
        from_baidu,
      </if>
      <if test="isVisable != null">
        is_visable,
      </if>
      <if test="fieldTag != null">
        field_tag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="valueType != null">
        value_type,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="isShowValue != null">
        is_show_value,
      </if>
      <if test="number != null">
        number,
      </if>
      <if test="configInfos != null">
        config_infos,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tableEnName != null">
        #{tableEnName,jdbcType=VARCHAR},
      </if>
      <if test="dataTableId != null">
        #{dataTableId,jdbcType=BIGINT},
      </if>
      <if test="enField != null">
        #{enField,jdbcType=VARCHAR},
      </if>
      <if test="cnField != null">
        #{cnField,jdbcType=VARCHAR},
      </if>
      <if test="fieldType != null">
        #{fieldType,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="isFilterCriteria != null">
        #{isFilterCriteria,jdbcType=BIT},
      </if>
      <if test="isRequired != null">
        #{isRequired,jdbcType=BIT},
      </if>
      <if test="isSecrete != null">
        #{isSecrete,jdbcType=BIT},
      </if>
      <if test="fromBaidu != null">
        #{fromBaidu,jdbcType=BIT},
      </if>
      <if test="isVisable != null">
        #{isVisable,jdbcType=BIT},
      </if>
      <if test="fieldTag != null">
        #{fieldTag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valueType != null">
        #{valueType,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="isShowValue != null">
        #{isShowValue,jdbcType=BIT},
      </if>
      <if test="number != null">
        #{number,jdbcType=INTEGER},
      </if>
      <if test="configInfos != null">
        #{configInfos,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from datatable_meta_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update datatable_meta_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tableEnName != null">
        table_en_name = #{record.tableEnName,jdbcType=VARCHAR},
      </if>
      <if test="record.dataTableId != null">
        data_table_id = #{record.dataTableId,jdbcType=BIGINT},
      </if>
      <if test="record.enField != null">
        en_field = #{record.enField,jdbcType=VARCHAR},
      </if>
      <if test="record.cnField != null">
        cn_field = #{record.cnField,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldType != null">
        field_type = #{record.fieldType,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.isFilterCriteria != null">
        is_filter_criteria = #{record.isFilterCriteria,jdbcType=BIT},
      </if>
      <if test="record.isRequired != null">
        is_required = #{record.isRequired,jdbcType=BIT},
      </if>
      <if test="record.isSecrete != null">
        is_secrete = #{record.isSecrete,jdbcType=BIT},
      </if>
      <if test="record.fromBaidu != null">
        from_baidu = #{record.fromBaidu,jdbcType=BIT},
      </if>
      <if test="record.isVisable != null">
        is_visable = #{record.isVisable,jdbcType=BIT},
      </if>
      <if test="record.fieldTag != null">
        field_tag = #{record.fieldTag,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.valueType != null">
        value_type = #{record.valueType,jdbcType=VARCHAR},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=VARCHAR},
      </if>
      <if test="record.isShowValue != null">
        is_show_value = #{record.isShowValue,jdbcType=BIT},
      </if>
      <if test="record.number != null">
        number = #{record.number,jdbcType=INTEGER},
      </if>
      <if test="record.configInfos != null">
        config_infos = #{record.configInfos,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update datatable_meta_info
    set id = #{record.id,jdbcType=BIGINT},
      table_en_name = #{record.tableEnName,jdbcType=VARCHAR},
      data_table_id = #{record.dataTableId,jdbcType=BIGINT},
      en_field = #{record.enField,jdbcType=VARCHAR},
      cn_field = #{record.cnField,jdbcType=VARCHAR},
      field_type = #{record.fieldType,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      is_filter_criteria = #{record.isFilterCriteria,jdbcType=BIT},
      is_required = #{record.isRequired,jdbcType=BIT},
      is_secrete = #{record.isSecrete,jdbcType=BIT},
      from_baidu = #{record.fromBaidu,jdbcType=BIT},
      is_visable = #{record.isVisable,jdbcType=BIT},
      field_tag = #{record.fieldTag,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      value_type = #{record.valueType,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=VARCHAR},
      is_show_value = #{record.isShowValue,jdbcType=BIT},
      number = #{record.number,jdbcType=INTEGER},
      config_infos = #{record.configInfos,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update datatable_meta_info
    set id = #{record.id,jdbcType=BIGINT},
      table_en_name = #{record.tableEnName,jdbcType=VARCHAR},
      data_table_id = #{record.dataTableId,jdbcType=BIGINT},
      en_field = #{record.enField,jdbcType=VARCHAR},
      cn_field = #{record.cnField,jdbcType=VARCHAR},
      field_type = #{record.fieldType,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      is_filter_criteria = #{record.isFilterCriteria,jdbcType=BIT},
      is_required = #{record.isRequired,jdbcType=BIT},
      is_secrete = #{record.isSecrete,jdbcType=BIT},
      from_baidu = #{record.fromBaidu,jdbcType=BIT},
      is_visable = #{record.isVisable,jdbcType=BIT},
      field_tag = #{record.fieldTag,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      value_type = #{record.valueType,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=VARCHAR},
      is_show_value = #{record.isShowValue,jdbcType=BIT},
      number = #{record.number,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update datatable_meta_info
    <set>
      <if test="tableEnName != null">
        table_en_name = #{tableEnName,jdbcType=VARCHAR},
      </if>
      <if test="dataTableId != null">
        data_table_id = #{dataTableId,jdbcType=BIGINT},
      </if>
      <if test="enField != null">
        en_field = #{enField,jdbcType=VARCHAR},
      </if>
      <if test="cnField != null">
        cn_field = #{cnField,jdbcType=VARCHAR},
      </if>
      <if test="fieldType != null">
        field_type = #{fieldType,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="isFilterCriteria != null">
        is_filter_criteria = #{isFilterCriteria,jdbcType=BIT},
      </if>
      <if test="isRequired != null">
        is_required = #{isRequired,jdbcType=BIT},
      </if>
      <if test="isSecrete != null">
        is_secrete = #{isSecrete,jdbcType=BIT},
      </if>
      <if test="fromBaidu != null">
        from_baidu = #{fromBaidu,jdbcType=BIT},
      </if>
      <if test="isVisable != null">
        is_visable = #{isVisable,jdbcType=BIT},
      </if>
      <if test="fieldTag != null">
        field_tag = #{fieldTag,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valueType != null">
        value_type = #{valueType,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=VARCHAR},
      </if>
      <if test="isShowValue != null">
        is_show_value = #{isShowValue,jdbcType=BIT},
      </if>
      <if test="number != null">
        number = #{number,jdbcType=INTEGER},
      </if>
      <if test="configInfos != null">
        config_infos = #{configInfos,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update datatable_meta_info
    set table_en_name = #{tableEnName,jdbcType=VARCHAR},
      data_table_id = #{dataTableId,jdbcType=BIGINT},
      en_field = #{enField,jdbcType=VARCHAR},
      cn_field = #{cnField,jdbcType=VARCHAR},
      field_type = #{fieldType,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      is_filter_criteria = #{isFilterCriteria,jdbcType=BIT},
      is_required = #{isRequired,jdbcType=BIT},
      is_secrete = #{isSecrete,jdbcType=BIT},
      from_baidu = #{fromBaidu,jdbcType=BIT},
      is_visable = #{isVisable,jdbcType=BIT},
      field_tag = #{fieldTag,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      value_type = #{valueType,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=VARCHAR},
      is_show_value = #{isShowValue,jdbcType=BIT},
      number = #{number,jdbcType=INTEGER},
      config_infos = #{configInfos,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update datatable_meta_info
    set table_en_name = #{tableEnName,jdbcType=VARCHAR},
      data_table_id = #{dataTableId,jdbcType=BIGINT},
      en_field = #{enField,jdbcType=VARCHAR},
      cn_field = #{cnField,jdbcType=VARCHAR},
      field_type = #{fieldType,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      is_filter_criteria = #{isFilterCriteria,jdbcType=BIT},
      is_required = #{isRequired,jdbcType=BIT},
      is_secrete = #{isSecrete,jdbcType=BIT},
      from_baidu = #{fromBaidu,jdbcType=BIT},
      is_visable = #{isVisable,jdbcType=BIT},
      field_tag = #{fieldTag,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      value_type = #{valueType,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=VARCHAR},
      is_show_value = #{isShowValue,jdbcType=BIT},
      number = #{number,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>