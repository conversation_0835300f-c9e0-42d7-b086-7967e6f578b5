<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.TaskFileImportMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="source_name" jdbcType="VARCHAR" property="sourceName" />
    <result column="bos_name" jdbcType="VARCHAR" property="bosName" />
    <result column="bos_err_name" jdbcType="VARCHAR" property="bosErrName" />
    <result column="suffix_name" jdbcType="VARCHAR" property="suffixName" />
    <result column="write_type" jdbcType="VARCHAR" property="writeType" />
    <result column="mapping_type" jdbcType="VARCHAR" property="mappingType" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="data_table_id" jdbcType="BIGINT" property="dataTableId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="message" jdbcType="VARCHAR" property="message" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="field_mapping" jdbcType="LONGVARBINARY" property="fieldMapping" />
    <result column="first_row_data" jdbcType="LONGVARBINARY" property="firstRowData" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, group_id, source_name, bos_name, bos_err_name, suffix_name, write_type, mapping_type, 
    tenant_id, data_table_id, status, del, creator, modifier, operator_name, create_time, 
    update_time, message
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    field_mapping, first_row_data
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportCriteria" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_file_import
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from task_file_import
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from task_file_import
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from task_file_import
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from task_file_import
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportWithBLOBs" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into task_file_import (group_id, source_name, bos_name, 
      bos_err_name, suffix_name, write_type, 
      mapping_type, tenant_id, data_table_id, 
      status, del, creator, modifier, 
      operator_name, create_time, update_time, 
      message, field_mapping, first_row_data
      )
    values (#{groupId,jdbcType=VARCHAR}, #{sourceName,jdbcType=VARCHAR}, #{bosName,jdbcType=VARCHAR}, 
      #{bosErrName,jdbcType=VARCHAR}, #{suffixName,jdbcType=VARCHAR}, #{writeType,jdbcType=VARCHAR}, 
      #{mappingType,jdbcType=VARCHAR}, #{tenantId,jdbcType=VARCHAR}, #{dataTableId,jdbcType=BIGINT}, 
      #{status,jdbcType=TINYINT}, #{del,jdbcType=BIT}, #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, 
      #{operatorName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{message,jdbcType=VARCHAR}, #{fieldMapping,jdbcType=LONGVARBINARY}, #{firstRowData,jdbcType=LONGVARBINARY}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportWithBLOBs" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into task_file_import
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        group_id,
      </if>
      <if test="sourceName != null">
        source_name,
      </if>
      <if test="bosName != null">
        bos_name,
      </if>
      <if test="bosErrName != null">
        bos_err_name,
      </if>
      <if test="suffixName != null">
        suffix_name,
      </if>
      <if test="writeType != null">
        write_type,
      </if>
      <if test="mappingType != null">
        mapping_type,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="dataTableId != null">
        data_table_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="del != null">
        del,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="message != null">
        message,
      </if>
      <if test="fieldMapping != null">
        field_mapping,
      </if>
      <if test="firstRowData != null">
        first_row_data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="sourceName != null">
        #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="bosName != null">
        #{bosName,jdbcType=VARCHAR},
      </if>
      <if test="bosErrName != null">
        #{bosErrName,jdbcType=VARCHAR},
      </if>
      <if test="suffixName != null">
        #{suffixName,jdbcType=VARCHAR},
      </if>
      <if test="writeType != null">
        #{writeType,jdbcType=VARCHAR},
      </if>
      <if test="mappingType != null">
        #{mappingType,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="dataTableId != null">
        #{dataTableId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="del != null">
        #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="fieldMapping != null">
        #{fieldMapping,jdbcType=LONGVARBINARY},
      </if>
      <if test="firstRowData != null">
        #{firstRowData,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from task_file_import
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update task_file_import
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.groupId != null">
        group_id = #{record.groupId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceName != null">
        source_name = #{record.sourceName,jdbcType=VARCHAR},
      </if>
      <if test="record.bosName != null">
        bos_name = #{record.bosName,jdbcType=VARCHAR},
      </if>
      <if test="record.bosErrName != null">
        bos_err_name = #{record.bosErrName,jdbcType=VARCHAR},
      </if>
      <if test="record.suffixName != null">
        suffix_name = #{record.suffixName,jdbcType=VARCHAR},
      </if>
      <if test="record.writeType != null">
        write_type = #{record.writeType,jdbcType=VARCHAR},
      </if>
      <if test="record.mappingType != null">
        mapping_type = #{record.mappingType,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.dataTableId != null">
        data_table_id = #{record.dataTableId,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=BIT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.operatorName != null">
        operator_name = #{record.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.message != null">
        message = #{record.message,jdbcType=VARCHAR},
      </if>
      <if test="record.fieldMapping != null">
        field_mapping = #{record.fieldMapping,jdbcType=LONGVARBINARY},
      </if>
      <if test="record.firstRowData != null">
        first_row_data = #{record.firstRowData,jdbcType=LONGVARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update task_file_import
    set id = #{record.id,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      source_name = #{record.sourceName,jdbcType=VARCHAR},
      bos_name = #{record.bosName,jdbcType=VARCHAR},
      bos_err_name = #{record.bosErrName,jdbcType=VARCHAR},
      suffix_name = #{record.suffixName,jdbcType=VARCHAR},
      write_type = #{record.writeType,jdbcType=VARCHAR},
      mapping_type = #{record.mappingType,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      data_table_id = #{record.dataTableId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      operator_name = #{record.operatorName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      message = #{record.message,jdbcType=VARCHAR},
      field_mapping = #{record.fieldMapping,jdbcType=LONGVARBINARY},
      first_row_data = #{record.firstRowData,jdbcType=LONGVARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update task_file_import
    set id = #{record.id,jdbcType=BIGINT},
      group_id = #{record.groupId,jdbcType=VARCHAR},
      source_name = #{record.sourceName,jdbcType=VARCHAR},
      bos_name = #{record.bosName,jdbcType=VARCHAR},
      bos_err_name = #{record.bosErrName,jdbcType=VARCHAR},
      suffix_name = #{record.suffixName,jdbcType=VARCHAR},
      write_type = #{record.writeType,jdbcType=VARCHAR},
      mapping_type = #{record.mappingType,jdbcType=VARCHAR},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      data_table_id = #{record.dataTableId,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      operator_name = #{record.operatorName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      message = #{record.message,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update task_file_import
    <set>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="sourceName != null">
        source_name = #{sourceName,jdbcType=VARCHAR},
      </if>
      <if test="bosName != null">
        bos_name = #{bosName,jdbcType=VARCHAR},
      </if>
      <if test="bosErrName != null">
        bos_err_name = #{bosErrName,jdbcType=VARCHAR},
      </if>
      <if test="suffixName != null">
        suffix_name = #{suffixName,jdbcType=VARCHAR},
      </if>
      <if test="writeType != null">
        write_type = #{writeType,jdbcType=VARCHAR},
      </if>
      <if test="mappingType != null">
        mapping_type = #{mappingType,jdbcType=VARCHAR},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="dataTableId != null">
        data_table_id = #{dataTableId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=VARCHAR},
      </if>
      <if test="fieldMapping != null">
        field_mapping = #{fieldMapping,jdbcType=LONGVARBINARY},
      </if>
      <if test="firstRowData != null">
        first_row_data = #{firstRowData,jdbcType=LONGVARBINARY},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update task_file_import
    set group_id = #{groupId,jdbcType=VARCHAR},
      source_name = #{sourceName,jdbcType=VARCHAR},
      bos_name = #{bosName,jdbcType=VARCHAR},
      bos_err_name = #{bosErrName,jdbcType=VARCHAR},
      suffix_name = #{suffixName,jdbcType=VARCHAR},
      write_type = #{writeType,jdbcType=VARCHAR},
      mapping_type = #{mappingType,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      data_table_id = #{dataTableId,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      message = #{message,jdbcType=VARCHAR},
      field_mapping = #{fieldMapping,jdbcType=LONGVARBINARY},
      first_row_data = #{firstRowData,jdbcType=LONGVARBINARY}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImport">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update task_file_import
    set group_id = #{groupId,jdbcType=VARCHAR},
      source_name = #{sourceName,jdbcType=VARCHAR},
      bos_name = #{bosName,jdbcType=VARCHAR},
      bos_err_name = #{bosErrName,jdbcType=VARCHAR},
      suffix_name = #{suffixName,jdbcType=VARCHAR},
      write_type = #{writeType,jdbcType=VARCHAR},
      mapping_type = #{mappingType,jdbcType=VARCHAR},
      tenant_id = #{tenantId,jdbcType=VARCHAR},
      data_table_id = #{dataTableId,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      message = #{message,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>