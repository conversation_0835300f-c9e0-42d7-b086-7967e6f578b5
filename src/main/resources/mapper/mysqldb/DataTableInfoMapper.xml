<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="cn_name" jdbcType="VARCHAR" property="cnName" />
    <result column="en_name" jdbcType="VARCHAR" property="enName" />
    <result column="data_type" jdbcType="INTEGER" property="dataType" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="is_visable" jdbcType="BIT" property="isVisable" />
    <result column="is_preset" jdbcType="TINYINT" property="isPreset" />
    <result column="is_del" jdbcType="TINYINT" property="isDel" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="table_name" jdbcType="VARCHAR" property="tableName" />
    <result column="tenantId" jdbcType="VARCHAR" property="tenantid" />
    <result column="db_type" jdbcType="VARCHAR" property="dbType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="description" jdbcType="LONGVARCHAR" property="description" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, cn_name, en_name, data_type, status, is_visable, is_preset, is_del, create_time, 
    update_time, table_name, tenantId, db_type
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    description
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from datatable_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datatable_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from datatable_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from datatable_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from datatable_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into datatable_info (cn_name, en_name, data_type, 
      status, is_visable, is_preset, 
      is_del, create_time, update_time, 
      table_name, tenantId, db_type, 
      description)
    values (#{cnName,jdbcType=VARCHAR}, #{enName,jdbcType=VARCHAR}, #{dataType,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{isVisable,jdbcType=BIT}, #{isPreset,jdbcType=TINYINT}, 
      #{isDel,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{tableName,jdbcType=VARCHAR}, #{tenantid,jdbcType=VARCHAR}, #{dbType,jdbcType=VARCHAR}, 
      #{description,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into datatable_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cnName != null">
        cn_name,
      </if>
      <if test="enName != null">
        en_name,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isVisable != null">
        is_visable,
      </if>
      <if test="isPreset != null">
        is_preset,
      </if>
      <if test="isDel != null">
        is_del,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tableName != null">
        table_name,
      </if>
      <if test="tenantid != null">
        tenantId,
      </if>
      <if test="dbType != null">
        db_type,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cnName != null">
        #{cnName,jdbcType=VARCHAR},
      </if>
      <if test="enName != null">
        #{enName,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="isVisable != null">
        #{isVisable,jdbcType=BIT},
      </if>
      <if test="isPreset != null">
        #{isPreset,jdbcType=TINYINT},
      </if>
      <if test="isDel != null">
        #{isDel,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tableName != null">
        #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="tenantid != null">
        #{tenantid,jdbcType=VARCHAR},
      </if>
      <if test="dbType != null">
        #{dbType,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from datatable_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update datatable_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.cnName != null">
        cn_name = #{record.cnName,jdbcType=VARCHAR},
      </if>
      <if test="record.enName != null">
        en_name = #{record.enName,jdbcType=VARCHAR},
      </if>
      <if test="record.dataType != null">
        data_type = #{record.dataType,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.isVisable != null">
        is_visable = #{record.isVisable,jdbcType=BIT},
      </if>
      <if test="record.isPreset != null">
        is_preset = #{record.isPreset,jdbcType=TINYINT},
      </if>
      <if test="record.isDel != null">
        is_del = #{record.isDel,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.tableName != null">
        table_name = #{record.tableName,jdbcType=VARCHAR},
      </if>
      <if test="record.tenantid != null">
        tenantId = #{record.tenantid,jdbcType=VARCHAR},
      </if>
      <if test="record.dbType != null">
        db_type = #{record.dbType,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update datatable_info
    set id = #{record.id,jdbcType=BIGINT},
      cn_name = #{record.cnName,jdbcType=VARCHAR},
      en_name = #{record.enName,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      is_visable = #{record.isVisable,jdbcType=BIT},
      is_preset = #{record.isPreset,jdbcType=TINYINT},
      is_del = #{record.isDel,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      table_name = #{record.tableName,jdbcType=VARCHAR},
      tenantId = #{record.tenantid,jdbcType=VARCHAR},
      db_type = #{record.dbType,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update datatable_info
    set id = #{record.id,jdbcType=BIGINT},
      cn_name = #{record.cnName,jdbcType=VARCHAR},
      en_name = #{record.enName,jdbcType=VARCHAR},
      data_type = #{record.dataType,jdbcType=INTEGER},
      status = #{record.status,jdbcType=TINYINT},
      is_visable = #{record.isVisable,jdbcType=BIT},
      is_preset = #{record.isPreset,jdbcType=TINYINT},
      is_del = #{record.isDel,jdbcType=TINYINT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      table_name = #{record.tableName,jdbcType=VARCHAR},
      tenantId = #{record.tenantid,jdbcType=VARCHAR},
      db_type = #{record.dbType,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update datatable_info
    <set>
      <if test="cnName != null">
        cn_name = #{cnName,jdbcType=VARCHAR},
      </if>
      <if test="enName != null">
        en_name = #{enName,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="isVisable != null">
        is_visable = #{isVisable,jdbcType=BIT},
      </if>
      <if test="isPreset != null">
        is_preset = #{isPreset,jdbcType=TINYINT},
      </if>
      <if test="isDel != null">
        is_del = #{isDel,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tableName != null">
        table_name = #{tableName,jdbcType=VARCHAR},
      </if>
      <if test="tenantid != null">
        tenantId = #{tenantid,jdbcType=VARCHAR},
      </if>
      <if test="dbType != null">
        db_type = #{dbType,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update datatable_info
    set cn_name = #{cnName,jdbcType=VARCHAR},
      en_name = #{enName,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      is_visable = #{isVisable,jdbcType=BIT},
      is_preset = #{isPreset,jdbcType=TINYINT},
      is_del = #{isDel,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      table_name = #{tableName,jdbcType=VARCHAR},
      tenantId = #{tenantid,jdbcType=VARCHAR},
      db_type = #{dbType,jdbcType=VARCHAR},
      description = #{description,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update datatable_info
    set cn_name = #{cnName,jdbcType=VARCHAR},
      en_name = #{enName,jdbcType=VARCHAR},
      data_type = #{dataType,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      is_visable = #{isVisable,jdbcType=BIT},
      is_preset = #{isPreset,jdbcType=TINYINT},
      is_del = #{isDel,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      table_name = #{tableName,jdbcType=VARCHAR},
      tenantId = #{tenantid,jdbcType=VARCHAR},
      db_type = #{dbType,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>