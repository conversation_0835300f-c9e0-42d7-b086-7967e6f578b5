<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.baidu.keyue.deepsight.mysqldb.mapper.AiobSopMetaMapper">
  <resultMap id="BaseResultMap" type="com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="task_id" jdbcType="VARCHAR" property="taskId" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="step_id" jdbcType="VARCHAR" property="stepId" />
    <result column="step_name" jdbcType="VARCHAR" property="stepName" />
    <result column="node_id" jdbcType="VARCHAR" property="nodeId" />
    <result column="node_name" jdbcType="VARCHAR" property="nodeName" />
    <result column="manual_check" jdbcType="BIT" property="manualCheck" />
    <result column="del" jdbcType="BIT" property="del" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <result column="task_rule" jdbcType="LONGVARCHAR" property="taskRule" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, tenant_id, task_id, version, step_id, step_name, node_id, node_name, manual_check, 
    del, creator, modifier, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    task_rule
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMetaCriteria" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from aiob_sop_meta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMetaCriteria" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from aiob_sop_meta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from aiob_sop_meta
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from aiob_sop_meta
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMetaCriteria">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from aiob_sop_meta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into aiob_sop_meta (tenant_id, task_id, version, 
      step_id, step_name, node_id, 
      node_name, manual_check, del, 
      creator, modifier, create_time, 
      update_time, task_rule)
    values (#{tenantId,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, 
      #{stepId,jdbcType=VARCHAR}, #{stepName,jdbcType=VARCHAR}, #{nodeId,jdbcType=VARCHAR}, 
      #{nodeName,jdbcType=VARCHAR}, #{manualCheck,jdbcType=BIT}, #{del,jdbcType=BIT}, 
      #{creator,jdbcType=VARCHAR}, #{modifier,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{taskRule,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into aiob_sop_meta
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="stepId != null">
        step_id,
      </if>
      <if test="stepName != null">
        step_name,
      </if>
      <if test="nodeId != null">
        node_id,
      </if>
      <if test="nodeName != null">
        node_name,
      </if>
      <if test="manualCheck != null">
        manual_check,
      </if>
      <if test="del != null">
        del,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="taskRule != null">
        task_rule,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="stepId != null">
        #{stepId,jdbcType=VARCHAR},
      </if>
      <if test="stepName != null">
        #{stepName,jdbcType=VARCHAR},
      </if>
      <if test="nodeId != null">
        #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="nodeName != null">
        #{nodeName,jdbcType=VARCHAR},
      </if>
      <if test="manualCheck != null">
        #{manualCheck,jdbcType=BIT},
      </if>
      <if test="del != null">
        #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskRule != null">
        #{taskRule,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMetaCriteria" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from aiob_sop_meta
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update aiob_sop_meta
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.tenantId != null">
        tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=VARCHAR},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=VARCHAR},
      </if>
      <if test="record.stepId != null">
        step_id = #{record.stepId,jdbcType=VARCHAR},
      </if>
      <if test="record.stepName != null">
        step_name = #{record.stepName,jdbcType=VARCHAR},
      </if>
      <if test="record.nodeId != null">
        node_id = #{record.nodeId,jdbcType=VARCHAR},
      </if>
      <if test="record.nodeName != null">
        node_name = #{record.nodeName,jdbcType=VARCHAR},
      </if>
      <if test="record.manualCheck != null">
        manual_check = #{record.manualCheck,jdbcType=BIT},
      </if>
      <if test="record.del != null">
        del = #{record.del,jdbcType=BIT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.modifier != null">
        modifier = #{record.modifier,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.taskRule != null">
        task_rule = #{record.taskRule,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update aiob_sop_meta
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      task_id = #{record.taskId,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      step_id = #{record.stepId,jdbcType=VARCHAR},
      step_name = #{record.stepName,jdbcType=VARCHAR},
      node_id = #{record.nodeId,jdbcType=VARCHAR},
      node_name = #{record.nodeName,jdbcType=VARCHAR},
      manual_check = #{record.manualCheck,jdbcType=BIT},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      task_rule = #{record.taskRule,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update aiob_sop_meta
    set id = #{record.id,jdbcType=BIGINT},
      tenant_id = #{record.tenantId,jdbcType=VARCHAR},
      task_id = #{record.taskId,jdbcType=VARCHAR},
      version = #{record.version,jdbcType=VARCHAR},
      step_id = #{record.stepId,jdbcType=VARCHAR},
      step_name = #{record.stepName,jdbcType=VARCHAR},
      node_id = #{record.nodeId,jdbcType=VARCHAR},
      node_name = #{record.nodeName,jdbcType=VARCHAR},
      manual_check = #{record.manualCheck,jdbcType=BIT},
      del = #{record.del,jdbcType=BIT},
      creator = #{record.creator,jdbcType=VARCHAR},
      modifier = #{record.modifier,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update aiob_sop_meta
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="stepId != null">
        step_id = #{stepId,jdbcType=VARCHAR},
      </if>
      <if test="stepName != null">
        step_name = #{stepName,jdbcType=VARCHAR},
      </if>
      <if test="nodeId != null">
        node_id = #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="nodeName != null">
        node_name = #{nodeName,jdbcType=VARCHAR},
      </if>
      <if test="manualCheck != null">
        manual_check = #{manualCheck,jdbcType=BIT},
      </if>
      <if test="del != null">
        del = #{del,jdbcType=BIT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taskRule != null">
        task_rule = #{taskRule,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update aiob_sop_meta
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      task_id = #{taskId,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      step_id = #{stepId,jdbcType=VARCHAR},
      step_name = #{stepName,jdbcType=VARCHAR},
      node_id = #{nodeId,jdbcType=VARCHAR},
      node_name = #{nodeName,jdbcType=VARCHAR},
      manual_check = #{manualCheck,jdbcType=BIT},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      task_rule = #{taskRule,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update aiob_sop_meta
    set tenant_id = #{tenantId,jdbcType=VARCHAR},
      task_id = #{taskId,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      step_id = #{stepId,jdbcType=VARCHAR},
      step_name = #{stepName,jdbcType=VARCHAR},
      node_id = #{nodeId,jdbcType=VARCHAR},
      node_name = #{nodeName,jdbcType=VARCHAR},
      manual_check = #{manualCheck,jdbcType=BIT},
      del = #{del,jdbcType=BIT},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>