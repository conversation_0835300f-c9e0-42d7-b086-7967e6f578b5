CREATE TABLE
    IF NOT EXISTS `aiob_sop_node_metric_^&` (
    `cal_date` DATETIME NULL COMMENT '计算时间',
    `task_id` VARCHAR(255) default '' COMMENT '任务 id',
    `robot_id` VARCHAR(255) default '' COMMENT '机器人 id',
    `robot_ver` VARCHAR(255) default '' COMMENT '机器人版本',
    `topic_id` VARCHAR(255) default '' COMMENT '主题id',
    `node_id` VARCHAR(255) default '' COMMENT '当前节点 id',
    `oneId` VARCHAR(255) default '' COMMENT 'oneId',
    `hangup` INT default '0' COMMENT '异常挂断电话',
    `intent` ARRAY<VARCHAR(128)> DEFAULT '[]' COMMENT '意图节点',
    `sessionId`  varchar(255) default '' COMMENT '通话id'
    ) ENGINE=OLAP COMMENT '外呼 sop 节点统计明细表' DISTRIBUTED BY HASH(`task_id`) BUCKETS 10 PROPERTIES(
        "replication_allocation"="tag.location.default: 3",
        "is_being_synced"="false",
        "storage_medium"="hdd",
        "storage_format"="V2",
        "light_schema_change"="true",
        "disable_auto_compaction"="false",
        "enable_single_replica_compaction"="false",
        "enable_mow_light_delete"="false"
    );
CREATE TABLE
    IF NOT EXISTS `aiob_sop_edge_metric_^&` (
    `cal_date` DATETIME default NULL COMMENT '计算时间',
    `task_id` VARCHAR(255) default '' COMMENT '任务 id',
    `robot_id` VARCHAR(255) default '' COMMENT '机器人 id',
    `robot_ver` VARCHAR(255) default '' COMMENT '机器人版本',
    `topic_id` VARCHAR(255) default '' COMMENT '主题id',
    `from_node` VARCHAR(255) default '' COMMENT '边起始节点 id',
    `end_node` VARCHAR(255) default '' COMMENT '边到达节点 id',
    `oneId` VARCHAR(255) default '' COMMENT 'oneId',
    `sessionId`  varchar(255) default '' COMMENT '通话id'
    ) ENGINE=OLAP COMMENT '外呼 sop 边统计明细表' DISTRIBUTED BY HASH(`task_id`) BUCKETS 10 PROPERTIES(
      "replication_allocation"="tag.location.default: 3",
      "is_being_synced"="false",
      "storage_medium"="hdd",
      "storage_format"="V2",
      "light_schema_change"="true",
      "disable_auto_compaction"="false",
      "enable_single_replica_compaction"="false",
      "enable_mow_light_delete"="false"
  );
CREATE TABLE
    IF NOT EXISTS `aiob_conversation_record_debug_^&` (
    `queryId` VARCHAR(80) NULL COMMENT 'debug info id',
    `sessionId` VARCHAR(80) NULL COMMENT '通话 id',
    `topicId` VARCHAR(255) NULL COMMENT '主题id',
    `nodeId` VARCHAR(255) NULL COMMENT '节点id',
    `robot_id` VARCHAR(255) NULL COMMENT '机器人 id',
    `robot_ver` VARCHAR(255) NULL COMMENT '机器人版本',
    `chunkId` VARCHAR(255) NULL COMMENT '节点顺序',
    `intent` ARRAY<VARCHAR(128)> NULL DEFAULT '[]' COMMENT '意图',
    `agent_id` VARCHAR(255) NULL COMMENT 'agent_id',
    `version_id` VARCHAR(255) NULL COMMENT 'version_id'
    ) ENGINE=OLAP COMMENT '外呼 sop 边统计明细表' DISTRIBUTED BY HASH(`queryId`) BUCKETS 10 PROPERTIES(
      "replication_allocation"="tag.location.default: 3",
      "is_being_synced"="false",
      "storage_medium"="hdd",
      "storage_format"="V2",
      "light_schema_change"="true",
      "disable_auto_compaction"="false",
      "enable_single_replica_compaction"="false",
      "enable_mow_light_delete"="false"
  );
ALTER TABLE `aiob_conversation_record_^&` ADD COLUMN `start` varchar(255) NULL DEFAULT '' COMMENT '对话相对时间';
ALTER TABLE `aiob_conversation_record_^&` ADD COLUMN `queryId` varchar(255) NULL DEFAULT '' COMMENT 'debug查询ID';
ALTER TABLE `aiob_conversation_session_service_^&` ADD COLUMN `sipCode` varchar(255) NULL COMMENT '接通状态';
ALTER TABLE `aiob_conversation_session_service_^&` ADD COLUMN `botVersionId` varchar(255) NULL DEFAULT '' COMMENT '机器人版本';
ALTER TABLE `aiob_conversation_session_service_^&` ADD COLUMN `customTagList` ARRAY<VARCHAR(128)> NULL DEFAULT '[]' COMMENT '意向';