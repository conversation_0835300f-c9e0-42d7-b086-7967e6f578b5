ALTER TABLE global_default_user ADD COLUMN `deepsight_datetime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '写入时间';
ALTER TABLE global_default_user ADD COLUMN `deepsight_update_datetime` datetime NULL COMMENT '更新时间';
ALTER TABLE global_default_user ADD COLUMN `mobile_list` ARRAY<VARCHAR(128)> NULL default '[]'  COMMENT '手机号，多值';
ALTER TABLE global_default_user ADD COLUMN `email_list` ARRAY<VARCHAR(128)> NULL default '[]'  COMMENT '邮箱，多值';
ALTER TABLE global_default_user ADD COLUMN `source` VARCHAR(128) NULL COMMENT '来源';
ALTER TABLE global_default_user RENAME COLUMN `device_id` `DEVICEID`;
ALTER TABLE global_default_user ADD COLUMN `IMEI` varchar(255) NULL DEFAULT '' COMMENT '国际移动设备身份码';
ALTER TABLE global_default_user ADD COLUMN `BAIDUID` varchar(255) NULL DEFAULT '' COMMENT '百度域浏览器cookie';
ALTER TABLE global_default_user ADD COLUMN `CUID` varchar(255) NULL DEFAULT '' COMMENT '百度定义移动设备唯一标示';
ALTER TABLE global_default_user ADD COLUMN `USERID` varchar(255) NULL DEFAULT '' COMMENT '百度注册id';
ALTER TABLE global_default_user ADD COLUMN `MAC` varchar(255) NULL DEFAULT '' COMMENT 'Mac 地址';
ALTER TABLE global_default_user ADD COLUMN `UNIONID` varchar(255) NULL DEFAULT '' COMMENT '微信公众号用户UnionID';
ALTER TABLE global_default_user ADD COLUMN `IDFA` varchar(255) NULL DEFAULT '' COMMENT 'apple提供给广告主的设备唯一ID';
ALTER TABLE global_default_user ADD COLUMN `OAID` varchar(255) NULL DEFAULT '' COMMENT 'OAID的设备标识';
ALTER TABLE global_default_user ADD COLUMN `anonymous_id` varchar(255) NULL DEFAULT '' COMMENT '匿名访客ID';
ALTER TABLE global_default_user ADD COLUMN `oneId` varchar(255) NULL DEFAULT '' COMMENT '唯一ID';
ALTER TABLE global_default_keyue_conversation_record_info ADD COLUMN `deepsight_update_datetime` datetime NULL COMMENT '更新时间';
ALTER TABLE global_default_keyue_conversation_record_info ADD COLUMN `BAIDUID` varchar(255) NULL DEFAULT '' COMMENT '百度域浏览器cookie';
ALTER TABLE global_default_keyue_conversation_record_info ADD COLUMN `UNIONID` varchar(255) NULL DEFAULT '' COMMENT '微信公众号用户UnionID';
ALTER TABLE global_default_keyue_conversation_record_info ADD COLUMN `IMEI` varchar(255) NULL DEFAULT '' COMMENT '国际移动设备身份码';
ALTER TABLE global_default_keyue_conversation_record_info ADD COLUMN `CUID` varchar(255) NULL DEFAULT '' COMMENT '百度定义移动设备唯一标示';
ALTER TABLE global_default_keyue_conversation_record_info ADD COLUMN `MAC` varchar(255) NULL DEFAULT '' COMMENT 'Mac地址';
ALTER TABLE global_default_keyue_conversation_record_info ADD COLUMN `anonymous_id` varchar(255) NULL DEFAULT '' COMMENT '匿名访客ID';
ALTER TABLE global_default_keyue_conversation_record_info ADD COLUMN `oneId` varchar(255) NULL DEFAULT '' COMMENT '唯一ID';
ALTER TABLE global_default_keyue_conversation_record_info RENAME COLUMN `uid` `user_id`;
ALTER TABLE global_default_session ADD COLUMN `deepsight_update_datetime` datetime NULL COMMENT '更新时间';
ALTER TABLE global_default_session ADD COLUMN `BAIDUID` varchar(255) NULL DEFAULT '' COMMENT '百度域浏览器cookie';
ALTER TABLE global_default_session ADD COLUMN `IMEI` varchar(255) NULL DEFAULT '' COMMENT '国际移动设备身份码';
ALTER TABLE global_default_session ADD COLUMN `CUID` varchar(255) NULL DEFAULT '' COMMENT '百度定义移动设备唯一标示';
ALTER TABLE global_default_session ADD COLUMN `MAC` varchar(255) NULL DEFAULT '' COMMENT 'Mac 地址';
ALTER TABLE global_default_session ADD COLUMN `oneId` varchar(255) NULL DEFAULT '' COMMENT '唯一ID';
ALTER TABLE global_default_aiob_conversation_record_info ADD COLUMN `deepsight_update_datetime` datetime NULL COMMENT '更新时间';
ALTER TABLE global_default_aiob_conversation_record_info ADD COLUMN `oneId` varchar(255) NULL DEFAULT '' COMMENT '唯一ID';
ALTER TABLE global_default_memory_info ADD COLUMN `deepsight_update_datetime` datetime NULL COMMENT '更新时间';
ALTER TABLE global_default_memory_info ADD COLUMN `oneId` varchar(255) NULL DEFAULT '' COMMENT '唯一ID';


