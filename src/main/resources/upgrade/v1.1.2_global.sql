ALTER TABLE `global_default_aiob_conversation_record_info` ADD COLUMN `start` varchar(255) NULL DEFAULT '' COMMENT '对话相对时间';
ALTER TABLE `global_default_aiob_conversation_record_info` ADD COLUMN `queryId` varchar(255) NULL DEFAULT '' COMMENT 'debug查询ID';
ALTER TABLE `global_default_session` ADD COLUMN `sipCode` varchar(255) NULL COMMENT '接通状态';
ALTER TABLE `global_default_session` ADD COLUMN `botVersionId` varchar(255) NULL DEFAULT '' COMMENT '机器人版本';
ALTER TABLE `global_default_session` ADD COLUMN `customTagList` ARRAY<VARCHAR(128)> NULL DEFAULT '[]' COMMENT '意向';