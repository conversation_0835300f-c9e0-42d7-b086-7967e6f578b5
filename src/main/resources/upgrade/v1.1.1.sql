CREATE TABLE  IF NOT EXISTS `aiob_conversation_session_agg_^&` (
    `oneId` VARCHAR ( 255 ) COMMENT 'oneIdID',
    `call_date` DATETIME COMMENT '通话日期（比如 2025-04-07）',
    `time_bucket` VARCHAR ( 10 ) COMMENT '时段（比如12-15)',
    `total_calls` BIGINT SUM COMMENT '拨打总次数',
    `total_connected_calls` BIGINT SUM COMMENT '接通总次数',--
    `total_first_round_hangup` BIGINT SUM COMMENT '首轮挂断次数（轮次为1）',--
    `total_rounds` BIGINT SUM COMMENT '对话总轮数',
    `total_duration_time` BIGINT SUM COMMENT '通话总时长（单位秒）'
) AGGREGATE KEY ( `oneId`, `call_date`, `time_bucket`) DISTRIBUTED BY HASH (`oneId`) BUCKETS 10 PROPERTIES (
	"replication_allocation" = "tag.location.default: 3",
	"is_being_synced" = "false",
	"storage_medium" = "hdd",
	"storage_format" = "V2",
	"light_schema_change" = "true",
	"disable_auto_compaction" = "false",
	"enable_single_replica_compaction" = "false",
"enable_mow_light_delete" = "false" 
);
CREATE TABLE IF NOT EXISTS `user_metric_^&` (
    `oneId` VARCHAR ( 255 ) NOT NULL COMMENT '用户ID，平台维度唯一标识，不能为空',
    `connect_rate` DOUBLE NOT NULL COMMENT '接通率，取值范围0-1，不能为空',
    `time_bucket_statistics` JSON NOT NULL COMMENT '各时间段接通情况，JSON格式，示例：{"0-3":{"count":12,"percent":0.3512}}，不能为空',
    `first_round_hangup_rate` DOUBLE NOT NULL COMMENT '首轮挂断率，取值范围0-1，不能为空',
    `avg_rounds` DOUBLE NOT NULL COMMENT '平均对话轮次，不能为空',
    `avg_duration` DOUBLE NOT NULL COMMENT '平均通话时长，单位：秒，不能为空'
    ) UNIQUE KEY ( `oneId` ) DISTRIBUTED BY HASH ( `oneId` ) BUCKETS 10 PROPERTIES (
   "replication_allocation" = "tag.location.default: 3",
   "is_being_synced" = "false",
   "storage_medium" = "hdd",
   "storage_format" = "V2",
   "enable_unique_key_merge_on_write" = "true",
   "light_schema_change" = "true",
   "disable_auto_compaction" = "false",
   "enable_single_replica_compaction" = "false",
   "enable_mow_light_delete" = "false"
);