package com.baidu.keyue.deepsight.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 健康检查接口
 * <AUTHOR>
 * @date 2024/12/23 11:51
 */

@RestController
@RequestMapping("/deepsight/v1")
public class HealthController {


    /**
     * 健康检查接口
     *
     * @return
     */
    @GetMapping("/actuator/health")
    public String health() {
        return "200";
    }


}
