package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.customer.request.CreateCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.CreateCustomerImportRequest;
import com.baidu.keyue.deepsight.models.customer.request.CustomerGroupDetaiListlRequest;
import com.baidu.keyue.deepsight.models.customer.request.DeleteCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.DeleteCustomerRequest;
import com.baidu.keyue.deepsight.models.customer.request.GetCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.GetCustomerListRequest;
import com.baidu.keyue.deepsight.models.customer.request.ListCustomerGroupAnalysisRequest;
import com.baidu.keyue.deepsight.models.customer.request.UpdateCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.response.CreateCustomerImportResponse;
import com.baidu.keyue.deepsight.models.customer.response.CustomerFieldDetailResponse;
import com.baidu.keyue.deepsight.models.customer.response.CustomerGroupAnalysisItemResponse;
import com.baidu.keyue.deepsight.models.customer.response.CustomerGroupDetailResponse;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupCalculateService;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 客群分析接口
 * @ClassName CustomerGroupController
 * @Description 客群接口
 * <AUTHOR> Chen (<EMAIL>)
 * @Date 2024/12/30 20:32
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/customerGroup")
public class CustomerGroupController {
    private final CustomerGroupService customerGroupService;

    private final CustomerGroupCalculateService customerGroupCalculateService;
    
    /** 导入人群模板文件url */
    @Value("${template.customerGroup}")
    private String templateBosUrl;

    /**
     * 创建客群
     *
     * @param request 创建客群请求体
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public BaseResponse<Void> create(@Valid @RequestBody CreateCustomerGroupRequest request) {
        customerGroupService.createCustomerGroup(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 客群列表查询
     *
     * @param request 列表查询条件
     * @return BasePageResponse<CustomerGroupAnalysisItemResponse> 返回客群分页列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public BasePageResponse<CustomerGroupAnalysisItemResponse> list(@RequestBody ListCustomerGroupAnalysisRequest request) {
        return BasePageResponse.of(customerGroupService.customerGroupAnalysisItems(request));
    }

    /**
     * 删除客群
     *
     * @param request 删除客群的 ID
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public BaseResponse<Void> delete(@Valid @RequestBody DeleteCustomerGroupRequest request) {
        customerGroupService.deleteCustomerGroup(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 查询客群详情
     *
     * @param request 客群 ID
     * @return BaseResponse<CustomerGroupDetailResponse> 客群详情
     */
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public BaseResponse<CustomerGroupDetailResponse> detail(@RequestBody GetCustomerGroupRequest request) {
        return BaseResponse.of(customerGroupService.getCustomerGroupDetail(request));
    }

    /**
     * 查询客群详情列表
     *
     * @param request 客群 ID
     * @return BaseResponse<CustomerGroupDetailResponse> 客群详情
     */
    @RequestMapping(value = "/detailList", method = RequestMethod.POST)
    public BaseResponse<List<CustomerGroupDetailResponse>> detailList(
            @RequestBody CustomerGroupDetaiListlRequest request) {
        return BaseResponse.of(customerGroupService.listCustomerGroupDetail(request));
    }

    /**
     * 客群更新
     *
     * @param request 更新内容
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public BaseResponse<Void> update(@Valid @RequestBody UpdateCustomerGroupRequest request) {
        customerGroupService.updateCustomerGroup(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 客群用户字段列表（MVP版暂不实现，复用数据管理的字段列表）
     *
     * @param request 查询条件
     * @return BasePageResponse<CustomerFieldDetailResponse> 返回客群用户字段列表
     */
    @RequestMapping(value = "/field/list", method = RequestMethod.POST)
    public BasePageResponse<CustomerFieldDetailResponse> fieldList(@RequestBody GetCustomerGroupRequest request) {
        return BasePageResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 客群详情用户列表
     *
     * @param request 客群 ID
     * @return BasePageResponse<Map<String, String>> 客群详情用户列表
     */
    @RequestMapping(value = "/customer/list", method = RequestMethod.POST)
    public BasePageResponse<Map<String, String>> customerList(@RequestBody GetCustomerListRequest request) {
        return BasePageResponse.of(customerGroupService.getCustomerList(request));
    }


    /**
     * 客群详情用户删除
     *
     * @param request 用户ID
     * @return BasePageResponse<Void> 客群详情用户删除
     */
    @RequestMapping(value = "/customer/delete", method = RequestMethod.POST)
    public BaseResponse<Void> customerDelete(@Valid @RequestBody DeleteCustomerRequest request) {
        customerGroupService.deleteCustomer(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 手动执行客群计算
     *
     * @param request 客群 ID
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/manual", method = RequestMethod.POST)
    public BaseResponse<Void> manual(@RequestBody GetCustomerGroupRequest request) {
        customerGroupCalculateService.execByManual(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 导入客群
     *
     * @param request 导入客群请求体
     * @return BaseResponse 通用返回
     */
    @PostMapping(value = "/importData")
    public BaseResponse<CreateCustomerImportResponse> importData(@Valid @RequestBody CreateCustomerImportRequest request) {
        return BaseResponse.of(customerGroupService.importData(request));
    }

    /**
     * 文件导入人群模板
     *
     * @return 文件模板BOS 下载URL
     */
    @GetMapping("/import/template")
    public BaseResponse<String> downloadTemplate() {
        return BaseResponse.of(templateBosUrl);
    }
}
