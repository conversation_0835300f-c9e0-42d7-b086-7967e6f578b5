package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.service.catalog.LabelCatalogService;
import com.baidu.keyue.deepsight.service.label.LabelService;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.catalog.DeleteCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.EditCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogResponse;
import com.baidu.keyue.deepsight.models.catalog.MoveCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.NewCatalogRequest;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.label.LabelDetail;
import com.baidu.keyue.deepsight.models.label.ListLabelBriefRequest;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 标签目录API
 * @className: LabelCatalogController
 * @description: 标签目录API
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/labelCatalog")
public class LabelCatalogController {

    private final LabelCatalogService labelCatalogService;
    private final LabelService labelService;

    /**
     * 新建标签目录
     *
     * @param request 新建标签目录请求体
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public BaseResponse<Void> newLabelCatalog(@RequestBody @Valid NewCatalogRequest request) {
        labelCatalogService.createLabelCatalog(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 删除标签目录
     *
     * @param request 删除标签目录的 ID
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/delete", method = RequestMethod.POST)
    public BaseResponse<Void> deleteLabelCatalog(@RequestBody @Valid DeleteCatalogRequest request) {
        // 检查标签目录是否是当前租户创建的
        String tenantId = WebContextHolder.getTenantId();
        labelCatalogService.getCatalogDetail(request.getCatalogId(), tenantId);

        // check sub labels
        ListLabelBriefRequest labelRequest = new ListLabelBriefRequest();
        labelRequest.setCatalogId(request.getCatalogId());
        BasePageResponse.Page<LabelDetail> listLabelResponse = labelService.listLabel(labelRequest);
        if (listLabelResponse.getTotal() > 0) {
            throw new DeepSightException.CatalogOperatorFailedException(ErrorCode.BAD_REQUEST, "标签目录下存在标签，无法删除");
        }

        labelCatalogService.deleteLabelCatalog(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 更新标签目录名称
     *
     * @param request 更新内容
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public BaseResponse<Void> updateLabelCatalog(@RequestBody @Valid EditCatalogRequest request) {
        labelCatalogService.updateLabelCatalog(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 查询当前用户全部标签目录列表及层级结构
     *
     * @param request 列表查询条件
     * @return BaseResponse<ListCatalogResponse> 返回标签目录列表
     */
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public BaseResponse<ListCatalogResponse> listLabelCatalog(@RequestBody ListCatalogRequest request) {
        return BaseResponse.of(labelService.listLabelTree(request, true));
    }

    /**
     * 移动标签目录顺序
     *
     * @param request 移动目标标签目录及移动后位置
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/move", method = RequestMethod.POST)
    public BaseResponse<Void> moveLabelCatalog(@RequestBody @Valid MoveCatalogRequest request) {
        labelCatalogService.move(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }
}
