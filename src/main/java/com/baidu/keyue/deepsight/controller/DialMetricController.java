package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.enums.AiobFailTypeEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.dial.AlertSettingQueryRequest;
import com.baidu.keyue.deepsight.models.dial.AlertSettingRequest;
import com.baidu.keyue.deepsight.models.dial.BusyLocationDistributionResponse;
import com.baidu.keyue.deepsight.models.dial.CallCoreMetricsRequest;
import com.baidu.keyue.deepsight.models.dial.CallCoreMetricsResponse;
import com.baidu.keyue.deepsight.models.dial.CallResultCompositionResponse;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendDetailRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendDetailResponse;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendResponse;
import com.baidu.keyue.deepsight.models.dial.CoreMetricsResponse;
import com.baidu.keyue.deepsight.models.dial.LineDetailResponse;
import com.baidu.keyue.deepsight.models.dial.LineInfoResponse;
import com.baidu.keyue.deepsight.models.dial.LineRejectTrendResponse;
import com.baidu.keyue.deepsight.models.dial.RejectReasonResponse;
import com.baidu.keyue.deepsight.models.dial.RobotInfoResponse;
import com.baidu.keyue.deepsight.models.dial.TaskInfoResponse;
import com.baidu.keyue.deepsight.models.dial.ThirtyDayRankingRequest;
import com.baidu.keyue.deepsight.models.dial.ThirtyDayRankingResponse;
import com.baidu.keyue.deepsight.models.dial.UnconnectedHeatmapResponse;
import com.baidu.keyue.deepsight.service.dail.DialMetricService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


import java.util.List;

/**
 * 呼通分析相关接口
 * @Description 呼通分析相关接口
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/dial")
public class DialMetricController {
    
    @Resource
    private DialMetricService dialMetricService; 

    /**
     * 查看大盘关键指标
     * @return BaseResponse<SopFlexibleWholeResponse>
     */
    @PostMapping("/r/callAnalysis/coreMetrics")
    public BaseResponse<CoreMetricsResponse> getCoreMetrics() {
        return BaseResponse.of(dialMetricService.getCoreMetrics(WebContextHolder.getTenantId()));
    }

    /**
     * 呼通分析-接通率趋势
     * 查看24小时/30天内，每天/每小时的拨打次数和接通率，返回数据用于折线图的绘制
     * @param request ConnectionRateTrendRequest
     * @return BaseResponse<List < ConnectionRateTrendResponse>>
     */
    @PostMapping("/r/callAnalysis/connectionRateTrend")
    public BaseResponse<List<ConnectionRateTrendResponse>> getConnectionRateTrend(@RequestBody ConnectionRateTrendRequest request) {
        return BaseResponse.of(dialMetricService.getConnectionRateTrend(request, WebContextHolder.getTenantId()));
    }

    /**
     * 呼通分析-接通率趋势-归因分析
     * 使用大模型对接通率的趋势做归因分析
     * @param request ConnectionRateAttributionRequest
     * @return BaseResponse<String>
     */
    @PostMapping("/r/callAnalysis/connectionRateAttribution")
    public BaseResponse<String> analyzeConnectionRateAttribution(@RequestBody ConnectionRateTrendRequest request) {
        return BaseResponse.of("");
    }

    /**
     * 告警设置
     * 针对号线id、任务id、机器人id配置告警信息
     * @param request AlertSettingRequest
     * @return BaseResponse<Void>
     */
    @PostMapping("/w/alertSetting/config")
    public BaseResponse<Integer> configAlertSetting(@RequestBody @Valid AlertSettingRequest request) {
        Integer id = dialMetricService.configAlertSetting(request, WebContextHolder.getTenantId());
        return BaseResponse.of(id);
    }

    /**
     * 告警设置内容获取
     * 针对号线id、任务id、机器人id，查询告警配置
     * @param request AlertSettingQueryRequest
     * @return BaseResponse<AlertSettingResponse>
     */
    @PostMapping("/r/alertSetting/query")
    public BaseResponse<AlertSettingRequest> queryAlertSetting(@RequestBody @Valid AlertSettingQueryRequest request) {
        return BaseResponse.of(dialMetricService.queryAlertSetting(request, WebContextHolder.getTenantId()));
    }

    /**
     * 30天排行榜
     * 针对号线、任务、机器人，查询30天内的聚合统计排行榜
     * @param request ThirtyDayRankingRequest
     * @return BasePageResponse<ThirtyDayRankingResponse>
     */
    @PostMapping("/r/ranking/thirtyDay")
    public BasePageResponse<ThirtyDayRankingResponse> getThirtyDayRanking(@RequestBody @Valid ThirtyDayRankingRequest request) {
        return BasePageResponse.of(dialMetricService.getThirtyDayRanking(request, WebContextHolder.getTenantId()));
    }

    /**
     * 呼通详情-核心指标
     * 针对号线、任务、机器人，查询其对应的核心指标
     * @param request CallCoreMetricsRequest
     * @return BaseResponse<CallCoreMetricsResponse>
     */
    @PostMapping("/r/callDetail/coreMetrics")
    public BaseResponse<CallCoreMetricsResponse> getCallDetailCoreMetrics(@RequestBody CallCoreMetricsRequest request) {
        return BaseResponse.of(dialMetricService.getCallDetailCoreMetrics(request));
    }

    /**
     * 呼通详情-机器人列表
     * 针对号线、任务，查询其包含的所有机器人列表, 过滤未发布状态的机器人
     * @param request RobotListRequest
     * @return BaseResponse<List < RobotInfoResponse>>
     */
    @PostMapping("/r/callDetail/robotList")
    public BaseResponse<List<RobotInfoResponse>> getRobotList(@RequestBody CallCoreMetricsRequest request) {
        return BaseResponse.of(dialMetricService.getRobotList(request));
    }


    /**
     * 呼通详情-任务列表
     * 针对号线、机器人，查询其包含的所有任务列表, 过滤待启动状态的任务
     * @param request TaskListRequest
     * @return BaseResponse<List < TaskInfoResponse>>
     */
    @PostMapping("/r/callDetail/taskList")
    public BaseResponse<List<TaskInfoResponse>> getTaskList(@RequestBody CallCoreMetricsRequest request) {
        return BaseResponse.of(dialMetricService.getTaskList(request));
    }

    /**
     * 呼通详情-号线列表
     * 针对机器人，查询其包含的所有号线列表
     * @param request LineListRequest
     * @return BaseResponse<List < LineInfoResponse>>
     */
    @PostMapping("/r/callDetail/lineList")
    public BaseResponse<List<LineInfoResponse>> getLineList(@RequestBody CallCoreMetricsRequest request) {
        return BaseResponse.of(dialMetricService.getLineList(request));
    }

    /**
     * 呼通详情-接通率趋势
     * 针对号线、任务、机器人，查询其24小时/30天内，每天/每小时的拨打次数和接通率
     * @param request ConnectionRateTrendDetailRequest
     * @return BaseResponse<List < ConnectionRateTrendDetailResponse>>
     */
    @PostMapping("/r/callDetail/connectionRateTrend")
    public BaseResponse<List<ConnectionRateTrendDetailResponse>> getConnectionRateTrendDetail(@RequestBody ConnectionRateTrendDetailRequest request) {
        return BaseResponse.of(dialMetricService.getConnectionRateTrendDetail(request));
    }

    /**
     * 呼通详情-呼通结果构成
     * 针对号线、任务、机器人，查询其近30天呼通结果构成数据
     * @param request CallResultCompositionRequest
     * @return BaseResponse<List < CallResultCompositionResponse>>
     */
    @PostMapping("/r/callDetail/callResultComposition")
    public BaseResponse<List<CallResultCompositionResponse>> getCallResultComposition(@RequestBody CallCoreMetricsRequest request) {
        return BaseResponse.of(dialMetricService.getCallResultComposition(request));
    }

    /**
     * 呼通详情-平台规则导致未接通原因占比
     * 针对号线、任务、机器人，查询平台规则导致未接通原因占比
     * @param request PlatformRuleRejectReasonRequest
     * @return BaseResponse<List < RejectReasonResponse>>
     */
    @PostMapping("/r/callDetail/platformRuleRejectReasons")
    public BaseResponse<List<RejectReasonResponse>> getPlatformRuleRejectReasons(@RequestBody CallCoreMetricsRequest request) {
        return BaseResponse.of(dialMetricService.getRejectReasons(request, AiobFailTypeEnum.PLATFORM_RULE));
    }

    /**
     * 呼通详情-被叫原因导致未接通原因占比
     * 针对号线、任务、机器人，查询被叫原因导致未接通原因占比
     * @param request CalledPartyRejectReasonRequest
     * @return BaseResponse<List < RejectReasonResponse>>
     */
    @PostMapping("/r/callDetail/calledPartyRejectReasons")
    public BaseResponse<List<RejectReasonResponse>> getCalledPartyRejectReasons(@RequestBody CallCoreMetricsRequest request) {
        return BaseResponse.of(dialMetricService.getRejectReasons(request, AiobFailTypeEnum.CALLED_UP));
    }

    /**
     * 呼通详情-号线原因导致未接通
     * 针对号线、任务、机器人，查询号线原因导致未接通占比
     * @param request LineRejectReasonRequest
     * @return BaseResponse<List < RejectReasonResponse>>
     */
    @PostMapping("/r/callDetail/lineRejectReasons")
    public BaseResponse<List<RejectReasonResponse>> getLineRejectReasons(@RequestBody CallCoreMetricsRequest request) {
        return BaseResponse.of(dialMetricService.getRejectReasons(request, AiobFailTypeEnum.LINE));
    }

    /**
     * 呼通详情-用户忙的通话归属地分布
     * 针对号线、任务、机器人，查询用户忙的通话归属地分布
     * @param request BusyLocationDistributionRequest
     * @return BaseResponse<BusyLocationDistributionResponse>
     */
    @PostMapping("/r/callDetail/busyLocationDistribution")
    public BaseResponse<BusyLocationDistributionResponse> getBusyLocationDistribution(@RequestBody CallCoreMetricsRequest request) {
        return BaseResponse.of(dialMetricService.getBusyLocationDistribution(request));
    }

    /**
     * 呼通详情-未接通时间热力图
     * 针对号线、任务、机器人，查询未接通时间热力图数据
     * @param request UnconnectedHeatmapRequest
     * @return BaseResponse<UnconnectedHeatmapResponse>
     */
    @PostMapping("/r/callDetail/unconnectedHeatmap")
    public BaseResponse<UnconnectedHeatmapResponse> getUnconnectedHeatmap(@RequestBody CallCoreMetricsRequest request) {
        return BaseResponse.of(dialMetricService.getUnconnectedHeatmap(request));
    }


    /**
     * 呼通详情-号线原因未接通趋势图
     * 针对号线、任务、机器人，查询号线原因未接通趋势图数据
     * @param request LineRejectTrendRequest
     * @return BaseResponse<List < LineRejectTrendResponse>>
     */
    @PostMapping("/r/callDetail/lineRejectTrend")
    public BaseResponse<List<LineRejectTrendResponse>> getLineRejectTrend(@RequestBody CallCoreMetricsRequest request) {
        return BaseResponse.of(dialMetricService.getLineRejectTrend(request));
    }

    /**
     * 呼通详情-号线详情
     * 查询号线详情
     * @param request CallCoreMetricsRequest
     * @return BaseResponse<List < LineDetailResponse>>
     */
    @PostMapping("/r/callDetail/lineDetail")
    public BaseResponse<LineDetailResponse> getLineDetail(@RequestBody CallCoreMetricsRequest request) {
        return BaseResponse.of(dialMetricService.getLineDetail(request));
    }
}
