package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.predict.PredictConfig;
import com.baidu.keyue.deepsight.models.predict.PredictDataSource;
import com.baidu.keyue.deepsight.models.predict.PredictSwitchUpdateRequest;
import com.baidu.keyue.deepsight.service.dataprediction.DataPredictionService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据增强预测管理API
 * @className: DataPredictionController
 * @description: 数据增强预测管理API
 * @author: lvtao03
 * @date: 2025/02/11 14:24
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/predict")
public class DataPredictionController {
    @Autowired
    private final DataPredictionService dataPredictionService;

    /**
     * 预测数据源配置查询
     *
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/dataSourceDetail", method = RequestMethod.POST)
    public BaseResponse<PredictDataSource> dataSourceDetail() {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
        PredictDataSource dataSourceDetail = dataPredictionService.getDataSourceDetail(tenantId, userId);
        return BaseResponse.of(dataSourceDetail);
    }

    /**
     * 预测数据源配置更新，如果当前租户没有配置则插入
     *
     * @param request 预测数据源配置请求
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/dataSourceUpdate", method = RequestMethod.POST)
    public BaseResponse<Void> dataSourceUpdate(@RequestBody @Valid PredictDataSource request) {
        String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        dataPredictionService.updateDataSource(request, userId, tenantId);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 预测内容列表查询
     * @param request 分页查询请求
     * @return BasePageResponse 分页结果
     */
    @RequestMapping(value = "/jobList", method = RequestMethod.POST)
    public BasePageResponse<PredictConfig> jobList(@RequestBody BasePageRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
        BasePageResponse.Page<PredictConfig> pageData
                    = dataPredictionService.pageQueryPredictConfig(request.getPageNo(), request.getPageSize(),
                tenantId, userId);
        return BasePageResponse.of(pageData);
    }

    /**
     * 预测内容启停
     * @param request 预测内容启停请求
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/jobSwitchUpdate", method = RequestMethod.POST)
    public BaseResponse<Void> jobSwitchUpdate(@RequestBody @Valid PredictSwitchUpdateRequest request) {
        String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        dataPredictionService.updatePredictSwitch(request, userId, tenantId);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

}
