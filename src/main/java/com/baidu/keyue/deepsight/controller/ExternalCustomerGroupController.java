package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.customer.request.GetCustomerListRequest;
import com.baidu.keyue.deepsight.models.customer.request.ListCustomerGroupAnalysisRequest;
import com.baidu.keyue.deepsight.models.customer.request.SamplingStatisticsCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.response.CustomerDorisResponse;
import com.baidu.keyue.deepsight.models.customer.response.CustomerGroupAnalysisItemResponse;
import com.baidu.keyue.deepsight.models.customer.response.SamplingStatisticsCustomerGroupResponse;
import com.baidu.keyue.deepsight.models.datamanage.request.CreateTableSchemaRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.CreateTableResponse;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionCalculateService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务间调用的客群管理接口
 */
@Slf4j
@RestController
@RequestMapping("/external/deepsight/v1")
public class ExternalCustomerGroupController {

    @Autowired
    private CustomerGroupService customerGroupService;

    @Autowired
    private DataTableManageService dataTableManageService;

    @Autowired
    private GroupDiffusionCalculateService groupDiffusionCalculateService;

    /**
     * 客群列表查询
     *
     * @param request 列表查询条件
     * @return BasePageResponse<CustomerGroupAnalysisItemResponse> 返回客群分页列表
     */
    @RequestMapping(value = "/customerGroup/list", method = RequestMethod.POST)
    public BasePageResponse<CustomerGroupAnalysisItemResponse> list(@RequestBody ListCustomerGroupAnalysisRequest request) {
        log.info("external customerGroup list tenantId:{}, userId:{}",
                WebContextHolder.getUserAuthInfo().getUserId(),
                WebContextHolder.getUserAuthInfo().getTenantId());
        return BasePageResponse.of(customerGroupService.customerGroupAnalysisItems(request));
    }

    /**
     * 数据集创建
     *
     * @param request 建表请求
     * @return BasePageResponse<CustomerGroupAnalysisItemResponse> 返回表主键
     */
    @RequestMapping(value = "/datatable/create", method = RequestMethod.POST)
    public BaseResponse<CreateTableResponse> createTable(@Valid @RequestBody CreateTableSchemaRequest request) {
        log.info("external create dataTable tenantId:{}, userId:{}",
                WebContextHolder.getUserAuthInfo().getUserId(),
                WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(dataTableManageService.createTable(request));

    }


    /**
     * 客群详情用户列表的 dorisSql
     *
     * @param request 客群 ID
     * @return BasePageResponse<Map < String, String>>
     */
    @RequestMapping(value = "/customer/dorisSql", method = RequestMethod.POST)
    public BaseResponse<CustomerDorisResponse> customerDorisList(@RequestBody GetCustomerListRequest request) {
        log.info("external customerGroup customerDorisList tenantId:{}, userId:{}",
                WebContextHolder.getUserAuthInfo().getUserId(),
                WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(customerGroupService.getCustomerListDoriSql(request));
    }

    /**
     * 预测客群数量
     * @param request 预测客群请求参数
     * @return BasePageResponse<SamplingStatisticsCustomerGroupResponse> 返回客群圈选人数
     */
    @RequestMapping(value = "/customerGroup/samplingStatistics", method = RequestMethod.POST)
    public BaseResponse<SamplingStatisticsCustomerGroupResponse> samplingStatisticsCustomerGroupCount(
            @Valid @RequestBody SamplingStatisticsCustomerGroupRequest request) {
        log.info("external customerGroup samplingStatistics tenantId:{}, userId:{}",
                WebContextHolder.getUserAuthInfo().getUserId(),
                WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(customerGroupService.samplingStatisticsConsumerGroup(request));
    }


    @RequestMapping(value = "/diffusion/execRightNow/{id}", method = RequestMethod.POST)
    public BaseResponse<Void> diffusionExec(@PathVariable("id") Long id) {
        groupDiffusionCalculateService.execGroupDiffusion(id, Constants.SYSTEM_DEFAULT_USER_ID);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    @RequestMapping(value = "/diffusion/check/{execId}", method = RequestMethod.POST)
    public BaseResponse<Void> diffusionCheck(@PathVariable("execId") Long execId) {
        groupDiffusionCalculateService.fetchGroupDiffusionResult(execId);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }
}
