package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.service.agg.AiobSessionMetricAggService;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.dataprediction.DataPredictionCalService;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionCalculateService;
import com.baidu.keyue.deepsight.service.user.BaiduUserDataService;
import com.baidu.keyue.deepsight.service.user.UserProfileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * 服务间调用的客群管理接口
 */
@Slf4j
@RestController
@RequestMapping("/external/deepsight/v1")
public class ExternalDataProcessController {

    @Autowired
    private CustomerGroupService customerGroupService;

    @Autowired
    private DataTableManageService dataTableManageService;

    @Autowired
    private BaiduUserDataService baiduUserDataService;

    @Autowired
    private UserProfileService userProfileService;

    @Autowired
    private DataPredictionCalService dataPredictionCalService;

    @Autowired
    private GroupDiffusionCalculateService groupDiffusionCalculateService;

    @Autowired
    private AiobSessionMetricAggService aiobSessionMetricAggService;

    @RequestMapping(value = "/execMegMerge/{table}", method = RequestMethod.POST)
    public BaseResponse<Void> execMegMerge(@PathVariable("table") String table) {
        baiduUserDataService.refreshSecretKey();
        baiduUserDataService.handelBaiduDataProcess(table);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    @RequestMapping(value = "/execProfileMerge/{tenantId}/{oneId}", method = RequestMethod.POST)
    public BaseResponse<Void> execProfileMerge(@PathVariable("tenantId") String tenantId, @PathVariable("oneId") String oneId) {
        userProfileService.mergeUserProfileByOneId(tenantId, oneId);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    @RequestMapping(value = "/mackUpPredict/{tenantId}", method = RequestMethod.POST)
    public BaseResponse<Void> mackUpPredict(@PathVariable("tenantId") String tenantId) {
        dataPredictionCalService.makeUpPredictByTenant(tenantId);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    @RequestMapping(value = "/aiobAgg/{aggTable}", method = RequestMethod.POST)
    public BaseResponse<Void> aiobAgg(@PathVariable("aggTable") String aggTable) {
        aiobSessionMetricAggService.aiobSessionMetricAggExec(aggTable);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }
}
