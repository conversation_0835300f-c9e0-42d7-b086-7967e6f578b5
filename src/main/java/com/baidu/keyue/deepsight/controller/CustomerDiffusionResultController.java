package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.diffusion.result.request.CharacteristicAnalyseRequest;
import com.baidu.keyue.deepsight.models.diffusion.result.request.CustomerDiffusionContentListRequest;
import com.baidu.keyue.deepsight.models.diffusion.result.request.DeleteDiffusionResultRequest;
import com.baidu.keyue.deepsight.models.diffusion.result.request.SamplingContractRequest;
import com.baidu.keyue.deepsight.models.diffusion.result.response.CharacteristicAnalyseResponse;
import com.baidu.keyue.deepsight.models.diffusion.result.response.SamplingContractResponse;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.service.diffusion.DiffusionResultService;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionCalculateService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 人群扩散结果API
 *
 * @className: CustomerDiffusionResultController
 * @description: 人群扩散结果API
 * @author: wangzhongcheng
 * @date: 2025/3/25 17:12
 */
@Slf4j
@RestController
@RequestMapping(value = "/deepsight/v1/customerDiffusion")
public class CustomerDiffusionResultController {

    @Autowired
    private DiffusionResultService diffusionResultService;

    @Autowired
    private GroupDiffusionCalculateService groupDiffusionCalculateService;

    /**
     * 获取人群扩散结果可见字段
     *
     * @param customerDiffusionTaskId 人群扩散任务ID
     * @return 人群扩散结果可见字段
     */
    @GetMapping("/r/result/visibleFields")
    public BaseResponse<List<VisibleFieldResponse>> getVisibleFields(@RequestParam Long customerDiffusionTaskId) {
        log.info("获取人群扩散结果可见字段，customerDiffusionTaskId:{}", customerDiffusionTaskId);
        return BaseResponse.of(diffusionResultService.getVisibleFields(customerDiffusionTaskId));
    }

    /**
     * 获取人群扩散结果过滤字段
     *
     * @param customerDiffusionTaskId 人群扩散任务ID
     * @return 人群扩散结果过滤字段
     */
    @GetMapping("/r/result/filterFields")
    public BaseResponse<List<DatasetPropertiesResult>> getFilterFields(@RequestParam Long customerDiffusionTaskId) {
        log.info("获取人群扩散结果过滤字段，customerDiffusionTaskId:{}", customerDiffusionTaskId);
        return BaseResponse.of(diffusionResultService.getFilterFields(customerDiffusionTaskId));
    }

    /**
     * 获取人群扩散结果明细列表
     *
     * @param request 请求参数
     * @return 人群扩散结果明细列表
     */
    @PostMapping("/r/result/contentList")
    public BasePageResponse<Map<String, String>> contentList(@RequestBody @Valid CustomerDiffusionContentListRequest request) {
        if (Objects.isNull(request.getFilters())) {
            request.setFilters(new ArrayList<>());
        }
        return BasePageResponse.of(diffusionResultService.contentList(request));
    }

    /**
     * 人群扩散结果特征分析
     *
     * @param request 请求参数
     * @return 人群扩散结果特征分析结果
     */
    @PostMapping("/r/result/characteristicAnalyse")
    public BaseResponse<CharacteristicAnalyseResponse> characteristicAnalyse(@RequestBody @Valid CharacteristicAnalyseRequest request) {
        return BaseResponse.of(diffusionResultService.characteristicAnalyse(request));
    }

    /**
     * 抽样对比预测结果
     *
     * @return 抽样对比预测结果
     */
    @PostMapping("/r/result/samplingContrast")
    public BaseResponse<SamplingContractResponse> samplingContrast(@RequestBody @Valid SamplingContractRequest request) {
        return BaseResponse.of(diffusionResultService.samplingCharacteristicContract(request));
    }

    /**
     * 打包到客群
     * @param request
     * @return
     */
    @PostMapping("/w/result/packageAsGroup")
    public BaseResponse<Integer> packageAsCustomerGroup(@RequestBody @Valid SamplingContractRequest request) {
        groupDiffusionCalculateService.packageAsCustomerGroup(request.getCustomerDiffusionTaskId());
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 删除扩散结果
     * @param request 请求参数
     * @return
     */
    @PostMapping("/r/result/delete")
    public BaseResponse<Void> deleteDiffusionResult(@RequestBody @Valid DeleteDiffusionResultRequest request) {
        diffusionResultService.deleteDiffusionResult(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

}
