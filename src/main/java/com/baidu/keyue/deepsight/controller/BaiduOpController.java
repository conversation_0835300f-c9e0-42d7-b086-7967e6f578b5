package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableContentListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableFieldRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.TableFieldDetailResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.operation.response.OperationModeResponse;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.models.rules.response.UserPropertiesResponse;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.operation.OperationService;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 百度代运营接口
 * 以下接口因为是待运营情况下需要返回百度的数据
 */
@RestController
@RequestMapping("/deepsight/v1/bd")
public class BaiduOpController {
    @Autowired
    private OperationService operationService;

    @Autowired
    private RuleManagerService ruleManagerService;

    @Autowired
    private DataTableManageService dataTableManageService;

    /**
     * 获取当前租户运营模式
     * @return
     */
    @GetMapping("/operation/mode")
    public BaseResponse<OperationModeResponse> operationMode() {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        OperationModeResponse operationModeResponse = operationService.getTenantOperationMode(tenantId);
        return BaseResponse.of(operationModeResponse);
    }

    /**
     * 用户属性筛选参数
     * 对于代运营模式，需要返回百度的数据
     * @return
     */
    @GetMapping("/rules/users/properties")
    public BaseResponse<UserPropertiesResponse> userProperties() {
        UserPropertiesResponse userPropertiesResp = ruleManagerService.getUserPropertiesResp(true);
        return BaseResponse.of(userPropertiesResp);
    }

    /**
     * 数据表数据字段
     * 对于代运营模式，需要返回百度的数据
     * @param request 数据表
     * @return BaseResponse 通用返回
     */
    @PostMapping(value = "/datatable/field/list")
    public BasePageResponse<TableFieldDetailResponse> getFieldList(@Valid @RequestBody GetTableFieldRequest request) {
        return BasePageResponse.of(dataTableManageService.getTableFieldList(request, true));
    }

    /**
     * 获取过滤字段列表
     * 对于代运营模式，需要返回百度的数据
     * @param dataTableId 数据表id
     * @return
     */
    @GetMapping(value = "/datatable/field/filterList")
    public BaseResponse<List<DatasetPropertiesResult>> getFilterFields(@RequestParam Long dataTableId) {
        return BaseResponse.of(dataTableManageService.getTableContentFilterProperties(dataTableId, true));
    }

    /**
     * 获取可见字段列表
     * 对于代运营模式，需要返回百度的数据
     * @param dataTableId 数据表id
     * @return
     */
    @GetMapping(value = "/datatable/field/visibleList")
    public BaseResponse<List<VisibleFieldResponse>> getVisibleFields(@RequestParam Long dataTableId) {
        return BaseResponse.of(dataTableManageService.getVisibleFields(dataTableId, true));
    }

    /**
     * 数据表数据记录
     * 对于代运营模式，需要返回百度的数据
     * @param request 数据表
     * @return BaseResponse 通用返回
     */
    @PostMapping(value = "/datatable/content/list")
    public BasePageResponse<Map<String, String>> tableFieldContent(@RequestBody GetTableContentListRequest request) {
        return BasePageResponse.of(dataTableManageService.getTableContent(request, true));
    }
}
