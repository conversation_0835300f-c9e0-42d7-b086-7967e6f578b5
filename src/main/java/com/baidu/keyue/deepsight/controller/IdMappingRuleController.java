package com.baidu.keyue.deepsight.controller;


import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.idmapping.request.rule.CreateIdMappingRuleRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.rule.DeleteIdMappingRuleRequest;
import com.baidu.keyue.deepsight.models.idmapping.response.rule.IdMappingRuleFieldsResponse;
import com.baidu.keyue.deepsight.models.idmapping.response.rule.IdMappingRuleItemResponse;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingManagerService;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingRuleService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * id mapping规则配置API
 * @className: IdMappingRuleController
 * @description: id mapping规则配置API
 * @author: chenwenyu03
 * @date: 2025/3/10 15:51
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/idMappingRule")
public class IdMappingRuleController {
    @Autowired
    private IdMappingRuleService idMappingRuleService;

    @Autowired
    private IdMappingManagerService idMappingManagerService;

    /**
     * 创建 id mapping 规则
     *
     */
    @PostMapping("/create")
    public BaseResponse<Void> createIdMappingRule(@Valid @RequestBody CreateIdMappingRuleRequest request) {
        Boolean switchStatus = idMappingManagerService.getSwitchStatus();
        if (Objects.nonNull(switchStatus) && switchStatus) {
            log.error("创建抽取字段失败，ID-Mapping启动中");
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "ID-Mapping启动中，抽取字段不能创建");
        }
        idMappingRuleService.createIdMappingRule(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 更新 id mapping 规则
     *
     */
    @PostMapping("/update")
    public BaseResponse<Void> updateIdMappingRule(@Valid @RequestBody CreateIdMappingRuleRequest request) {
        Boolean switchStatus = idMappingManagerService.getSwitchStatus();
        if (Objects.nonNull(switchStatus) && switchStatus) {
            log.error("更新抽取字段失败，ID-Mapping启动中");
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "ID-Mapping启动中，抽取字段不能更新");
        }
        idMappingRuleService.updateIdMappingRule(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 删除 id mapping 规则
     *
     */
    @PostMapping("/delete")
    public BaseResponse<Void> deleteIdMappingRule(@Valid @RequestBody DeleteIdMappingRuleRequest request) {
        Boolean switchStatus = idMappingManagerService.getSwitchStatus();
        if (Objects.nonNull(switchStatus) && switchStatus) {
            log.error("删除抽取字段失败，ID-Mapping启动中");
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "ID-Mapping启动中，抽取字段不能删除");
        }
        idMappingRuleService.deleteIdMappingRule(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * id mapping 规则分页查询
     * @return id mapping 规则查询结果
     */
    @PostMapping("/list")
    public BasePageResponse<IdMappingRuleItemResponse> listIdMappingRule(@Valid @RequestBody BasePageRequest request) {
        return BasePageResponse.of(idMappingRuleService.listIdMappingRule(request));
    }

    /**
     * id mapping 规则可选字段查询
     * @return id mapping 规则可选字段查询
     */
    @PostMapping("/field/list")
    public BaseResponse<IdMappingRuleFieldsResponse> listField() {
        return BaseResponse.of(idMappingManagerService.listIdMappingRuleField());
    }
}
