package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.support.StandardServletMultipartResolver;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 外呼模块api转发
 *
 * @ClassName AiobController
 * @Description 外呼模块api转发
 * <AUTHOR> yuanji (<EMAIL>)
 * @Date 2025/2/17 10:58
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/aiob")
public class AiobForwardController {
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private StandardServletMultipartResolver multipartResolver;

    @Value("${aiob.url}")
    private String aiobUrl;
    @Value("${aiob.requestPath}")
    private String requestPath;
    @Value("${aiob.targetPath}")
    private String targetPath;


    @GetMapping("/**")
    public ResponseEntity<?> getForwardRequest(HttpServletRequest request) {
        // url = /echopath/v1/aiob/xx
        String uri = request.getRequestURI().replaceFirst(requestPath
                , targetPath);
        // apiPath =  /aiob-server/xx
        String targetUrl = aiobUrl + uri;

        // 获取原请求方法
        HttpMethod method = HttpMethod.valueOf(request.getMethod());

        // 获取请求头
        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headEnums = request.getHeaderNames();
        while (headEnums.hasMoreElements()) {
            String key = headEnums.nextElement();
            String value = ((HttpServletRequest) request).getHeader(key);
            if (key.equalsIgnoreCase("content-length")) {
                continue;
            }
            headers.set(key, value);
        }
        // cookie
        Cookie[] cookies = request.getCookies();
        StringBuilder cookieValue = new StringBuilder();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                cookieValue.append(cookie.getName()).append("=").append(cookie.getValue()).append(";");
            }
            headers.set("Cookie", cookieValue.toString());
            headers.set("Authorization", cookieValue.toString());
        }

        String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        headers.set("User-pk", userId);
        headers.set("Agentid", tenantId);
        String username = WebContextHolder.getUserAuthInfo().getUserName();
        headers.set("Username", username);

        Map<String, String[]> params = request.getParameterMap();
        String contentType = request.getContentType();
        if (!StringUtils.containsIgnoreCase(contentType
                , MediaType.MULTIPART_FORM_DATA_VALUE)) {
            // 不是 multipart/form-data 拼接到url里
            List<String> urlParams = new ArrayList<>();
            for (String key : params.keySet()) {
                String[] values = params.get(key);
                for (String value : values) {
                    urlParams.add(key + "=" + value);
                    // urlParams.add(URLEncoder.encode(key, StandardCharsets.UTF_8) + "="
                    //         + URLEncoder.encode(value, StandardCharsets.UTF_8));
                }
            }
            targetUrl += "?" + String.join("&", urlParams);
        }
        // 获取请求体
        HttpEntity<Map<String, String[]>> entity = new HttpEntity<>(
                request.getParameterMap(), headers);
        // 发起转发请求
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(targetUrl, method, entity, String.class);
            // 判断返回的状态码并修改为自定义的状态码
            if (responseEntity.getStatusCodeValue() == 200) {
                String body = responseEntity.getBody();
                Map<String, Object> bodyMap = JsonUtils.toMap(body);
                String aiobServerCode = JsonUtils.transferToJson(
                        bodyMap.getOrDefault("code", "200"));
                if ("200".equals(aiobServerCode)) {
                    bodyMap.put("code", "deepsight_ok");
                    return ResponseEntity.ok().body(bodyMap);
                }

            }
            log.error("外呼模块转发请求异常: {}", responseEntity.getBody());
            // 外呼模块返回异常
            throw new DeepSightException.BusyRequestException(ErrorCode.INTERNAL_ERROR);
        } catch (Exception e) {
            log.error("外呼模块转发请求异常", e);
            throw new DeepSightException.BusyRequestException(ErrorCode.INTERNAL_ERROR);
        }

    }

    @GetMapping({"aiob-server/core/file/download", "core/file/download"})
    public void getDownload(HttpServletRequest request, HttpServletResponse response) {
        // url = /echopath/v1/aiob/xx
        String uri = request.getRequestURI().replaceFirst(requestPath
                , targetPath);
        // apiPath =  /aiob-server/xx
        String targetUrl = aiobUrl + uri;

        // 获取原请求方法
        HttpMethod method = HttpMethod.valueOf(request.getMethod());

        // 获取请求头
        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headEnums = request.getHeaderNames();
        while (headEnums.hasMoreElements()) {
            String key = headEnums.nextElement();
            String value = ((HttpServletRequest) request).getHeader(key);
            if (key.equalsIgnoreCase("content-length")) {
                continue;
            }
            headers.set(key, value);
        }
        // cookie
        Cookie[] cookies = request.getCookies();
        StringBuilder cookieValue = new StringBuilder();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                cookieValue.append(cookie.getName()).append("=").append(cookie.getValue()).append(";");
            }
            headers.set("Cookie", cookieValue.toString());
            headers.set("Authorization", cookieValue.toString());
        }
        String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        headers.set("User-pk", userId);
        headers.set("Agentid", tenantId);
        String username = WebContextHolder.getUserAuthInfo().getUserName();
        headers.set("Username", username);

        Map<String, String[]> params = request.getParameterMap();
        String contentType = request.getContentType();
        if (!StringUtils.containsIgnoreCase(contentType
                , MediaType.MULTIPART_FORM_DATA_VALUE)) {
            // 不是 multipart/form-data 拼接到url里
            List<String> urlParams = new ArrayList<>();
            for (String key : params.keySet()) {
                String[] values = params.get(key);
                for (String value : values) {
                    urlParams.add(key + "=" + value);
                }
            }
            targetUrl += "?" + String.join("&", urlParams);
        }
        // 获取请求体
        HttpEntity<Map<String, String[]>> entity = new HttpEntity<>(
                request.getParameterMap(), headers);
        // 发起转发请求
        try {
            ResponseEntity<Resource> responseEntity = restTemplate.exchange(targetUrl, method, entity, Resource.class);
            // 判断返回的状态码并修改为自定义的状态码
            if (responseEntity.getStatusCodeValue() == 200) {
                try (InputStream inputStream = responseEntity.getBody().getInputStream()) {
                    OutputStream outputStream = response.getOutputStream();
                    // 处理文件流，例如保存到本地文件
                    byte[] buffer = new byte[4096]; // 4KB buffer
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    // 设置响应头
                    HttpHeaders respHeaders = responseEntity.getHeaders();
                    for (String headerName : respHeaders.keySet()) {
                        response.setHeader(headerName, respHeaders.getFirst(headerName));
                    }
                } catch (IOException e1) {
                    log.error("外呼模块转发请求异常", e1);
                    // 外呼模块返回异常
                    throw new DeepSightException.BusyRequestException(ErrorCode.INTERNAL_ERROR);
                } catch (Exception e) {
                    log.error("外呼模块转发请求异常", e);
                    // 外呼模块返回异常
                    throw new DeepSightException.BusyRequestException(ErrorCode.INTERNAL_ERROR);
                }
            }
        } catch (Exception e) {
            log.error("外呼模块转发请求异常", e);
            throw new DeepSightException.BusyRequestException(ErrorCode.INTERNAL_ERROR);
        }

    }

    @PostMapping("/**")
    public ResponseEntity<?> postForwardRequest(HttpServletRequest request
            , @RequestBody Object reqBody) {
        // url = /echopath/v1/aiob/xx
        String uri = request.getRequestURI().replaceFirst(requestPath
                , targetPath);
        // apiPath =  /aiob-server/xx
        String targetUrl = aiobUrl + uri;

        // 获取原请求方法
        HttpMethod method = HttpMethod.valueOf(request.getMethod());

        // 获取请求头
        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headEnums = request.getHeaderNames();
        while (headEnums.hasMoreElements()) {
            String key = headEnums.nextElement();
            String value = ((HttpServletRequest) request).getHeader(key);
            if (key.equalsIgnoreCase("content-length")) {
                continue;
            }
            headers.set(key, value);
        }
        // cookie
        Cookie[] cookies = request.getCookies();
        StringBuilder cookieValue = new StringBuilder();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                cookieValue.append(cookie.getName()).append("=").append(cookie.getValue()).append(";");
            }
            headers.set("Cookie", cookieValue.toString());
            headers.set("Authorization", cookieValue.toString());
        }
        String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        headers.set("User-pk", userId);
        headers.set("Agentid", tenantId);
        String username = WebContextHolder.getUserAuthInfo().getUserName();
        headers.set("Username", username);
        // 发起转发请求
        try {
            ResponseEntity<String> responseEntity = null;
            if (Objects.nonNull(reqBody)) {
                Map<String, Object> params = JsonUtils.toMap(JsonUtils.toJson(reqBody));
                // 获取请求体
                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(params, headers);
                responseEntity = restTemplate.exchange(targetUrl, method, entity, String.class);
            } else {
                Map<String, String[]> params = request.getParameterMap();
                // 获取请求体
                HttpEntity<Map<String, String[]>> entity = new HttpEntity<>(params, headers);
                responseEntity = restTemplate.exchange(targetUrl, method, entity, String.class);
            }
            // 判断返回的状态码并修改为自定义的状态码
            if (responseEntity.getStatusCodeValue() == 200) {
                String body = responseEntity.getBody();
                Map<String, Object> bodyMap = JsonUtils.toMap(body);
                String aiobServerCode = JsonUtils.transferToJson(
                        bodyMap.getOrDefault("code", "200"));
                if ("200".equals(aiobServerCode)) {
                    bodyMap.put("code", "deepsight_ok");
                    return ResponseEntity.ok().body(bodyMap);
                }

            }
            log.error("外呼模块转发请求异常: {}", responseEntity.getBody());
            // 外呼模块返回异常
            throw new DeepSightException.BusyRequestException(ErrorCode.INTERNAL_ERROR);
        } catch (Exception e) {
            log.error("外呼模块转发请求异常", e);
            throw new DeepSightException.BusyRequestException(ErrorCode.INTERNAL_ERROR);
        }

    }


    @PostMapping({"aiob-server/manage/console/record/export", "manage/console/record/export"})
    public void postDownload(HttpServletRequest request, HttpServletResponse response
            , @RequestBody Object reqBody) {
        // url = /echopath/v1/aiob/xx
        String uri = request.getRequestURI().replaceFirst(requestPath
                , targetPath);
        // apiPath =  /aiob-server/xx
        String targetUrl = aiobUrl + uri;

        // 获取原请求方法
        HttpMethod method = HttpMethod.valueOf(request.getMethod());

        // 获取请求头
        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headEnums = request.getHeaderNames();
        while (headEnums.hasMoreElements()) {
            String key = headEnums.nextElement();
            String value = ((HttpServletRequest) request).getHeader(key);
            if (key.equalsIgnoreCase("content-length")) {
                continue;
            }
            headers.set(key, value);
        }
        // cookie
        Cookie[] cookies = request.getCookies();
        StringBuilder cookieValue = new StringBuilder();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                cookieValue.append(cookie.getName()).append("=").append(cookie.getValue()).append(";");
            }
            headers.set("Cookie", cookieValue.toString());
            headers.set("Authorization", cookieValue.toString());
        }
        String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        headers.set("User-pk", userId);
        headers.set("Agentid", tenantId);
        String username = WebContextHolder.getUserAuthInfo().getUserName();
        headers.set("Username", username);
        // 发起转发请求
        try {
            ResponseEntity<Resource> responseEntity = null;
            if (Objects.nonNull(reqBody)) {
                Map<String, Object> params = JsonUtils.toMap(JsonUtils.toJson(reqBody));
                // 获取请求体
                HttpEntity<Map<String, Object>> entity = new HttpEntity<>(params, headers);
                responseEntity = restTemplate.exchange(targetUrl, method, entity, Resource.class);
            } else {
                Map<String, String[]> params = request.getParameterMap();
                // 获取请求体
                HttpEntity<Map<String, String[]>> entity = new HttpEntity<>(params, headers);
                responseEntity = restTemplate.exchange(targetUrl, method, entity, Resource.class);
            }
            // 判断返回的状态码并修改为自定义的状态码
            if (responseEntity.getStatusCodeValue() == 200) {
                try (InputStream inputStream = responseEntity.getBody().getInputStream()) {
                    OutputStream outputStream = response.getOutputStream();
                    // 处理文件流，例如保存到本地文件
                    byte[] buffer = new byte[4096]; // 4KB buffer
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                    // 设置响应头
                    HttpHeaders respHeaders = responseEntity.getHeaders();
                    for (String headerName : respHeaders.keySet()) {
                        response.setHeader(headerName, respHeaders.getFirst(headerName));
                    }
                } catch (IOException e1) {
                    log.error("外呼模块转发请求异常", e1);
                    // 外呼模块返回异常
                    throw new DeepSightException.BusyRequestException(ErrorCode.INTERNAL_ERROR);
                } catch (Exception e) {
                    log.error("外呼模块转发请求异常", e);
                    // 外呼模块返回异常
                    throw new DeepSightException.BusyRequestException(ErrorCode.INTERNAL_ERROR);
                }
            }
        } catch (Exception e) {
            log.error("外呼模块转发请求异常", e);
            throw new DeepSightException.BusyRequestException(ErrorCode.INTERNAL_ERROR);
        }

    }

    @PostMapping({"/core/file/upload", "/aiob-server/core/file/upload"})
    public ResponseEntity<?> fileUpload(HttpServletRequest request) {
        // url = /echopath/v1/aiob/xx
        String uri = request.getRequestURI().replaceFirst(requestPath
                , targetPath);
        // apiPath =  /aiob-server/xx
        // "http://aicc-dev-k8s.bcetest.baidu.com/aiob-server/core/file/upload"
        String targetUrl = aiobUrl + uri;
        // 获取请求头
        HttpHeaders headers = new HttpHeaders();
        Enumeration<String> headEnums = request.getHeaderNames();
        while (headEnums.hasMoreElements()) {
            String key = headEnums.nextElement();
            String value = ((HttpServletRequest) request).getHeader(key);
            if (key.equalsIgnoreCase("content-length")) {
                continue;
            }
            headers.set(key, value);
        }
        // cookie
        Cookie[] cookies = request.getCookies();
        StringBuilder cookieValue = new StringBuilder();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                cookieValue.append(cookie.getName()).append("=").append(cookie.getValue()).append(";");
            }
            headers.set("Cookie", cookieValue.toString());
            headers.set("Authorization", cookieValue.toString());
        }
        // 获取原请求方法
        HttpMethod method = HttpMethod.valueOf(request.getMethod());
        // Spring的MultiValueMap用于构建表单数据。
        MultiValueMap<String, Object> reqBody = new LinkedMultiValueMap<>();
        Map<String, String[]> reqParamMap = request.getParameterMap();
        for (Map.Entry<String, String[]> entry : reqParamMap.entrySet()) {
            reqBody.add(entry.getKey(), entry.getValue()[0]);
        }
        // 发起转发请求
        try {
            MultipartResolver resolver = multipartResolver;
            MultipartHttpServletRequest multipartRequest = resolver.resolveMultipart(request);
            Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
            for (Map.Entry<String, MultipartFile> entry : fileMap.entrySet()) {
                MultipartFile multipartFile = entry.getValue();
                byte[] fileBytes = multipartFile.getBytes();
                reqBody.add(entry.getKey(), new ByteArrayResource(fileBytes) {
                    @Override
                    public String getFilename() {
                        return multipartFile.getOriginalFilename();
                    }
                });
            }
            // 获取请求体
            HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(
                    reqBody, headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(targetUrl, entity, String.class);
            // 判断返回的状态码并修改为自定义的状态码
            if (responseEntity.getStatusCodeValue() == 200) {
                String body = responseEntity.getBody();
                Map<String, Object> bodyMap = JsonUtils.toMap(body);
                String aiobServerCode = JsonUtils.transferToJson(
                        bodyMap.getOrDefault("code", "200"));
                if ("200".equals(aiobServerCode)) {
                    bodyMap.put("code", "deepsight_ok");
                    return ResponseEntity.ok().body(bodyMap);
                }
            }
            // 外呼模块返回异常
            throw new DeepSightException.BusyRequestException(ErrorCode.INTERNAL_ERROR);
        } catch (Exception e) {
            log.error("外呼模块转发请求异常", e);
            throw new DeepSightException.BusyRequestException(ErrorCode.INTERNAL_ERROR);
        }

    }

}
