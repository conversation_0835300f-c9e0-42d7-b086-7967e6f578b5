package com.baidu.keyue.deepsight.controller;

import java.util.HashMap;
import java.util.List;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.sop.SOPAnalyseProgressRequest;
import com.baidu.keyue.deepsight.models.sop.SOPAnalyseProgressResponse;
import com.baidu.keyue.deepsight.models.sop.SOPNodeConfirmRequest;
import com.baidu.keyue.deepsight.models.sop.SOPNodePredictRequest;
import com.baidu.keyue.deepsight.models.sop.SOPNodePredictResponse;
import com.baidu.keyue.deepsight.models.sop.SopBaseRequest;
import com.baidu.keyue.deepsight.models.sop.SopFlexibleWholeRequest;
import com.baidu.keyue.deepsight.models.sop.SopFlexibleWholeResponse;
import com.baidu.keyue.deepsight.models.sop.SopNodeDetailRequest;
import com.baidu.keyue.deepsight.models.sop.SopNodeDetailResponse;
import com.baidu.keyue.deepsight.models.sop.SopSankeyMetaResponse;
import com.baidu.keyue.deepsight.models.sop.SopSankeyWholeRequest;
import com.baidu.keyue.deepsight.models.sop.SopSankeyWholeResponse;
import com.baidu.keyue.deepsight.models.sop.SopUserConfigUpdateRequest;
import com.baidu.keyue.deepsight.models.sop.SopUserDetailRequest;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionRequest;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionResponse;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.keyue.deepsight.service.sop.impl.AiobSopStatisticService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 外呼SOP相关接口
 * @Description 外呼 SOP 统计
 * <AUTHOR>
 * @Date 2025/5/08 16:51
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/sop")
public class AiobSOPController {

    private final AiobSopStatisticService aiobSopStatisticService;
    private final AiobSOPService aiobSOPService;

    /**
     * 【灵活画布】整体统计数据
     * @param request SopFlexibleWholeRequest
     * @return BaseResponse<SopFlexibleWholeResponse>
     */
    @PostMapping("/r/flexible/whole")
    public BaseResponse<SopFlexibleWholeResponse> flexibleWholeData(@RequestBody @Valid SopFlexibleWholeRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSopStatisticService.flexibleWholeData(tenantId, request));
    }

    /**
     * 【快捷场景】桑基图整体统计数据
     * @param request SopSankeyWholeRequest
     * @return BaseResponse<SopSankeyWholeResponse>
     */
    @PostMapping("/r/sankey/whole")
    public BaseResponse<SopSankeyWholeResponse> sankeyWholeData(@RequestBody @Valid SopSankeyWholeRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSopStatisticService.sankeyWholeData(tenantId, request));
    }

    /**
     * 【快捷场景】桑基图步骤和节点信息
     * @param request SopSankeyWholeRequest
     * @return BaseResponse<SopSankeyMetaResponse>
     */
    @PostMapping("/r/sankey/sopMeta")
    public BaseResponse<SopSankeyMetaResponse> sankeySopMeta(@RequestBody @Valid SopSankeyWholeRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSOPService.listSOPMeta(tenantId, request.getTaskId(), request.getRobotVer()));
    }

    /**
     * 【快捷场景、灵活画布】节点详情统计 (包含上下游节点流量分布、每日流量分布)
     * @param request SopNodeDetailRequest
     * @return BaseResponse<SopNodeDetailResponse>
     */
    @PostMapping("/r/nodeDetail")
    public BaseResponse<SopNodeDetailResponse> nodeDetailStatistics(@RequestBody @Valid SopNodeDetailRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSopStatisticService.nodeDetailStatistics(tenantId, request));
    }

    /**
     * 【快捷场景、灵活画布】分析进度
     * @param request SopNodeDetailRequest
     * @return BaseResponse<SopNodeDetailResponse>
     */
    @PostMapping("/r/analyseProgress")
    public BaseResponse<SOPAnalyseProgressResponse> analyseProgress(@RequestBody @Valid SOPAnalyseProgressRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSopStatisticService.analyseProgress(tenantId, request));
    }

    /**
     * 获取机器人版本列表
     */
    @PostMapping("/r/robotVersionList")
    public BaseResponse<List<SopWholeRobotVersionResponse>> wholeRobotVersion(@RequestBody @Valid SopWholeRobotVersionRequest request) {
        return BaseResponse.of(aiobSOPService.listRobotVersion(request));
    }

    /**
     * 节点预测
     */
    @PostMapping("/r/nodePredict")
    public BaseResponse<SOPNodePredictResponse> predictNode(@RequestBody @Valid SOPNodePredictRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSOPService.predictNode(tenantId, request));
    }

    /**
     * 确认节点
     */
    @PostMapping("/r/nodeConfirm")
    public BaseResponse<Void> editNode(@RequestBody @Valid SOPNodeConfirmRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        aiobSOPService.confirmNode(tenantId, request.getTaskId(), request.getRule(), request.getMarkdown(), request.getRobotVer());
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 【快捷场景、灵活画布】更新分析设置
     * @param request SopUserConfigUpdateRequest
     * @return BaseResponse<Void>
     */
    @PostMapping("/w/userConfig/update")
    public BaseResponse<Void> updateUserConfig(@RequestBody @Valid SopUserConfigUpdateRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        aiobSOPService.updateUserConfig(tenantId, request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 【快捷场景、灵活画布】获取分析设置
     * @param request SopBaseRequest
     * @return BaseResponse<SopUserConfigUpdateRequest>
     */
    @PostMapping("/r/userConfig/get")
    public BaseResponse<SopUserConfigUpdateRequest> getUserConfig(@RequestBody @Valid SopBaseRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        return BaseResponse.of(aiobSOPService.getUserConfig(tenantId, request));
    }

    /**
     * 用户明细
     * @param request SopNodeDetailRequest
     * @return BasePageResponse<Map < String, String>>
     */
    @PostMapping("/r/userDetail")
    public BasePageResponse<HashMap<String, Object>> nodeUserDetail(@RequestBody @Valid SopUserDetailRequest request) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        if (request.getIntent()) {
            request.setCurrNodeId(aiobSopStatisticService.intentTagDecrypt(request.getCurrNodeId()));
        }
        List<String> idTransferRes = aiobSopStatisticService.taskIdTransfer(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer());
        request.setRobotId(idTransferRes.get(0));
        request.setRobotVer(idTransferRes.get(1));
        return BasePageResponse.of(aiobSOPService.getUserDetailV2(tenantId, request));
    }
}
