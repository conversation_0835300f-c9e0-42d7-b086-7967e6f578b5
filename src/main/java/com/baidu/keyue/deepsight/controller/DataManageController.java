package com.baidu.keyue.deepsight.controller;


import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTypeEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableFieldInfoDTO;
import com.baidu.keyue.deepsight.models.datamanage.request.CreateTableSchemaRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.DeleteTableRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigQueryRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigSaveRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetLLMGenFieldEnumRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetLLMGenTableInfoRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableContentListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableDetailRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableFieldRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.TableRecordRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.UpdateTableFieldConfigRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.UpdateTableSchemaRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.CreateTableResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldShowConfigResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.LLMGenFieldEnumResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.LLMGenFieldResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableDetailResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableFieldDetailResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * 数据管理接口
 *
 * @ClassName DataManageController
 * @Description 数据管理接口
 * <AUTHOR> Chen (<EMAIL>)
 * @Date 2024/12/24 11:37
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/datatable")
public class DataManageController {

    @Autowired
    private DataTableManageService dataTableManageService;

    /**
     * 数据集创建
     *
     * @return
     */
    @PostMapping("/create")
    public BaseResponse<CreateTableResponse> createDataTable(@Valid @RequestBody CreateTableSchemaRequest request) {
        // 预置 oneId 字段，并且该字段不能由前端创建
        if (DbTypeEnum.DORIS_TYPE.getDbType().equals(request.getDbType())) {
            checkOneIdFieldAndAdd(request.getTableFieldInfos());
        }
        return BaseResponse.of(dataTableManageService.createTable(request));
    }

    /**
     * 数据集编辑
     *
     * @return
     */
    @PostMapping("/update")
    public BaseResponse<Void> updateDataTable(@Valid @RequestBody UpdateTableSchemaRequest request) {
        dataTableManageService.updateTable(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 数据集保存
     *
     * @return
     */
    @PostMapping("/save")
    public BaseResponse<CreateTableResponse> saveDataTable(@Valid @RequestBody CreateTableSchemaRequest request) {
        return BaseResponse.of(dataTableManageService.saveTable(request));
    }

    /**
     * 数据集删除
     *
     * @return
     */
    @PostMapping("/delete")
    public BaseResponse<Void> deleteTable(@Valid @RequestBody DeleteTableRequest request) {
        dataTableManageService.deleteTable(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

//    /**
//     * ai生成字段
//     *
//     * @return
//     */
//    @PostMapping("/llm/gen/field")
//    public Flux<ServerSentEvent<String>> llmGenFieldInfo(@Valid @RequestBody GetLLMGenTableInfoRequest request) {
//        return dataTableManageService.llmGenFieldInfo(request);
//    }


    /**
     * ai生成字段
     *
     * @return
     */
    @PostMapping("/llm/gen/field")
    public BaseResponse<LLMGenFieldResponse> llmGenFieldInfo(@Valid @RequestBody GetLLMGenTableInfoRequest request) {
        return BaseResponse.of(dataTableManageService.llmGenFieldInfoV1(request));
    }

    /**
     * ai生成枚举
     *
     * @return
     */
    @PostMapping("/llm/gen/enum")
    public BaseResponse<LLMGenFieldEnumResponse> llmGenFieldEnum(@Valid @RequestBody GetLLMGenFieldEnumRequest request) {
        return BaseResponse.of(dataTableManageService.llmGenFieldEnum(request));
    }


    /**
     * 数据集列表
     *
     * @return
     */
    @PostMapping("/list")
    public BasePageResponse<TableDetailResponse> list(@Valid @RequestBody GetTableListRequest request) {
        return BasePageResponse.of(dataTableManageService.getDataTableList(request));
    }

    /**
     * 数据表数据字段
     *
     * @param request 数据表
     * @return BaseResponse 通用返回
     */
    @PostMapping(value = "/field/list")
    public BasePageResponse<TableFieldDetailResponse> getFieldList(@Valid @RequestBody GetTableFieldRequest request) {
        return BasePageResponse.of(dataTableManageService.getTableFieldList(request, false));
    }

    /**
     * 获取过滤字段列表
     *
     * @param dataTableId 数据表id
     * @return
     */
    @GetMapping(value = "/field/filterList")
    public BaseResponse<List<DatasetPropertiesResult>> getFilterFields(@RequestParam Long dataTableId) {
        return BaseResponse.of(dataTableManageService.getTableContentFilterProperties(dataTableId, false));
    }

    /**
     * 获取可见字段列表
     *
     * @param dataTableId 数据表id
     * @return
     */
    @GetMapping(value = "/field/visibleList")
    public BaseResponse<List<VisibleFieldResponse>> getVisibleFields(@RequestParam Long dataTableId) {
        return BaseResponse.of(dataTableManageService.getVisibleFields(dataTableId, false));
    }

    /**
     * 数据表数据记录
     *
     * @param request 数据表
     * @return BaseResponse 通用返回
     */
    @PostMapping(value = "/content/list")
    public BasePageResponse<Map<String, String>> tableFieldContent(@RequestBody GetTableContentListRequest request) {
        return BasePageResponse.of(dataTableManageService.getTableContent(request, false));
    }

    /**
     * 数据字段高级配置
     *
     * @param request 数据表
     * @return BaseResponse 通用返回
     */
    @PostMapping(value = "/field/config/update")
    public BaseResponse<Void> updateTableFieldConfig(@Valid @RequestBody UpdateTableFieldConfigRequest request) {
        dataTableManageService.updateTableFieldConfig(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 数据表记录操作，现仅支持删除
     *
     * @param request 数据表
     * @return BaseResponse 通用返回
     */
    @PostMapping(value = "/content/op")
    public BaseResponse<Void> opTableRecord(@Valid @RequestBody TableRecordRequest request) {
        dataTableManageService.opTableData(request);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 数据表详情
     *
     * @param request 数据表
     * @return BaseResponse 通用返回
     */
    @PostMapping(value = "/detail")
    public BaseResponse<TableDetailResponse> tableDetail(@Valid @RequestBody GetTableDetailRequest request) {
        return BaseResponse.of(dataTableManageService.getDataTableDetail(request));
    }

    /**
     * 检查 oneId 字段，并预置 oneId
     *
     * @param tableFieldInfos
     */
    private void checkOneIdFieldAndAdd(List<TableFieldInfoDTO> tableFieldInfos) {
        // 前端不能创建oneId字段
        for (TableFieldInfoDTO tableFieldInfo : tableFieldInfos) {
            if (Constants.TABLE_USER_ONE_ID.equals(tableFieldInfo.getEnName())) {
                throw new DeepSightException.ParamsErrorException(
                        ErrorCode.BAD_REQUEST, "oneId字段为预置字段，不允许手动创建");
            }
        }

        // 预置 oneId 字段
        TableFieldInfoDTO oneIdField = new TableFieldInfoDTO();
        oneIdField.setCnName(Constants.TABLE_USER_ONE_ID_CNAME);
        oneIdField.setEnName(Constants.TABLE_USER_ONE_ID);
        oneIdField.setDataType(Constants.CONSTANT_VARCHAR);
        oneIdField.setDescription(Constants.TABLE_USER_ONE_ID_CNAME);
        oneIdField.setIsRequired(false);
        oneIdField.setFieldTag(TableFieldTagEnum.NULL.getCode());
        oneIdField.setIsFilterCriteria(true);
        oneIdField.setIsShowValue(true);
        oneIdField.setValueType("text");
        oneIdField.setFieldType(TableFieldTypeEnum.STRING.getValue());
        tableFieldInfos.add(oneIdField);
    }

    /**
     * 数据集列表显示列字段列表获取
     * @param request 配置列表查询请求
     * @return BaseResponse<TableDetailResponse>
     */
    @PostMapping(value = "/r/field/showConfig/query")
    public BaseResponse<FieldShowConfigResponse> getShowConfigList(@RequestBody @Valid FieldShowConfigQueryRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        return BaseResponse.of(dataTableManageService.getFieldShowConfig(tenantId, request));
    }

    /**
     * 数据集列表显示列字段列表保存或更新
     * @param request 配置列表保存或更新请求
     * @return BaseResponse<TableDetailResponse>
     */
    @PostMapping(value = "/w/field/showConfig/saveOrUpdate")
    public BaseResponse<FieldShowConfigResponse> saveOrUpdateFieldShowConfig(@RequestBody @Valid FieldShowConfigSaveRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        String userName = WebContextHolder.getUserName();
        return BaseResponse.of(dataTableManageService.saveOrUpdateFieldShowConfig(tenantId, request, userName));
    }
}
