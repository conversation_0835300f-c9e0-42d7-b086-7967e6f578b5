package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.customer.request.CustomerDiffusionTaskCreateRequest;
import com.baidu.keyue.deepsight.models.customer.request.CustomerDiffusionTaskDeleteRequest;
import com.baidu.keyue.deepsight.models.customer.request.CustomerDiffusionTaskListRequest;
import com.baidu.keyue.deepsight.models.customer.request.CustomerDiffusionTaskRetryRequest;
import com.baidu.keyue.deepsight.models.customer.response.CustomerDiffusionTaskCreateRes;
import com.baidu.keyue.deepsight.models.customer.response.CustomerDiffusionTaskRes;
import com.baidu.keyue.deepsight.models.customer.response.GroupDetailDto;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionCalculateService;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 人群扩散接口
 * @ClassName CustomerDiffusionController
 * @Description 人群扩散
 * <AUTHOR>
 * @Date 2025/3/24 4:51 PM
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/customerDiffusion")
public class CustomerDiffusionController {

    @Resource
    private GroupDiffusionService customerDiffusionService;
    
    @Resource
    private CustomerGroupService customerGroupService;
    
    @Resource
    private GroupDiffusionCalculateService groupDiffusionCalculateService;


    @Value("${customer-group.seed-proportion:0.5}")
    private Double seedProportion;

    /**
     * 人群扩散任务创建
     * @param request 创建参数
     * @return BaseResponse<CustomerDiffusionTaskRes>
     */
    @PostMapping("/w/createTask")
    public BaseResponse<CustomerDiffusionTaskCreateRes> createTask(@RequestBody @Valid CustomerDiffusionTaskCreateRequest request) {
        return BaseResponse.of(customerDiffusionService.createTask(request));
    }

    /**
     * 人群扩散任重新预测
     * @return BaseResponse<String>
     */
    @PostMapping("/w/retryTask")
    public BaseResponse<String> retryTask(@RequestBody CustomerDiffusionTaskRetryRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();
        String userName = WebContextHolder.getUserName();
        customerDiffusionService.retryTask(request, userId, tenantId, userName);
        // 手动执行任务
        groupDiffusionCalculateService.execGroupDiffusion(request.getId(), userId);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 手动执行人群扩散计算
     *
     * @param request 任务 ID
     * @return BaseResponse 通用返回
     */
    @RequestMapping(value = "/w/manual", method = RequestMethod.POST)
    public BaseResponse<Void> manual(@RequestBody CustomerDiffusionTaskRetryRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();
        String userName = WebContextHolder.getUserName();
        customerDiffusionService.execByManual(request, userId, tenantId, userName);
        // 手动执行任务
        groupDiffusionCalculateService.execGroupDiffusion(request.getId(), userId);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 人群扩散任务详情
     * @param id id
     * @return BaseResponse<CustomerDiffusionTaskRes>
     */
    @GetMapping("/r/taskDetail")
    public BaseResponse<CustomerDiffusionTaskRes> taskDetail(@RequestParam("id") Long id) {
        String tenantId = WebContextHolder.getTenantId();
        return BaseResponse.of(customerDiffusionService.getTaskDetail(id, tenantId, true));
    }

    /**
     * 人群扩散任务列表
     * @param request
     * @return BasePageResponse<CustomerDiffusionTaskRes>
     */
    @PostMapping("/r/taskList")
    public BasePageResponse<CustomerDiffusionTaskRes> taskList(@RequestBody @Valid CustomerDiffusionTaskListRequest request) {
        return BasePageResponse.of(customerDiffusionService.getTaskPageList(request));
    }

    /**
     * 人群扩散任务删除
     * @param request
     * @return
     */
    @PostMapping("/w/deleteTask")
    public BaseResponse<Integer> deleteTask(@RequestBody @Valid CustomerDiffusionTaskDeleteRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        String userId = WebContextHolder.getUserId();
        String userName = WebContextHolder.getUserName();
        customerDiffusionService.deleteTask(request.getId(), tenantId, userId, userName);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 人群扩散客群列表
     * @return BaseResponse<List<CustomerGroupAnalysisItemResponse>>
     */
    @GetMapping("/r/groupList")
    public BaseResponse<List<GroupDetailDto>> groupList() {
        return BaseResponse.of(customerGroupService.getAllGroupList(WebContextHolder.getTenantId()));
    }


    /**
     * 人群扩散种子人群最大占比
     * @return BaseResponse<List<CustomerGroupAnalysisItemResponse>>
     */
    @GetMapping("/r/seedProportion")
    public BaseResponse<Double> seedProportion() {
        return BaseResponse.of(seedProportion);
    }

}
