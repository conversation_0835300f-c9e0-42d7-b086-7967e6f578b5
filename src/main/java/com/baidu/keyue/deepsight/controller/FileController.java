package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.datamanage.request.BosStsRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.StsSessionVo;
import com.baidu.keyue.deepsight.service.file.FileService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 文件相关接口
 * @ClassName FileController
 * @Description 文件相关接口
 * <AUTHOR>
 * @Date 2025/3/28 11:15 AM
 */
@RestController
@RequestMapping("/deepsight/v1/file")
@Slf4j
public class FileController {
    
    @Resource
    private FileService fileService;

    /**
     * BOS临时授权-STS
     * @param stsRequest
     * @return
     */
    @PostMapping(value = "/bos/sts")
    public BaseResponse<StsSessionVo> getStsSession(@RequestBody @Valid BosStsRequest stsRequest) {
        UserAuthInfo userAuthInfo = WebContextHolder.getUserAuthInfo();
        return BaseResponse.of(fileService.getStsSession(stsRequest, userAuthInfo));
    }
}
