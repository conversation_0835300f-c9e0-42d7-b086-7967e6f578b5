package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResponse;
import com.baidu.keyue.deepsight.models.rules.response.LabelPropertiesResponse;
import com.baidu.keyue.deepsight.models.rules.response.UserPropertiesResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数据筛选接口
 * @className: RuleController
 * @description: 数据筛选接口
 * @author: wangzhongcheng
 * @date: 2024/12/24 20:43
 */
@RestController
@RequestMapping("/deepsight/v1/rules")
public class RuleController {

    @Autowired
    private RuleManagerService ruleManagerService;

    /**
     * 标签筛选参数
     *
     * @return
     */
    @GetMapping("/labels/properties")
    public BaseResponse<LabelPropertiesResponse> labelProperties() {
        LabelPropertiesResponse labelPropertiesResp = ruleManagerService.getLabelPropertiesResp();

        return BaseResponse.of(labelPropertiesResp);
    }

    /**
     * 数据明细筛选参数
     *
     * @return
     */
    @GetMapping("/datasets/properties")
    public BaseResponse<DatasetPropertiesResponse> datasetProperties() {
        DatasetPropertiesResponse datasetPropertiesResp = ruleManagerService.getDatasetPropertiesResp();

        return BaseResponse.of(datasetPropertiesResp);
    }

    /**
     * 用户属性筛选参数
     *
     * @return
     */
    @GetMapping("/users/properties")
    public BaseResponse<UserPropertiesResponse> userProperties() {
        UserPropertiesResponse userPropertiesResp = ruleManagerService.getUserPropertiesResp(false);

        return BaseResponse.of(userPropertiesResp);
    }

}
