package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.sop.SOPQuickNodeListRequest;
import com.baidu.keyue.deepsight.models.sop.SOPQuickNodeListResponse;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordViewReq;
import com.baidu.keyue.deepsight.models.sop.aiob.AiobDiagramVersionRecordViewResp;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * *@Author: dongjiacheng01
 * *@Description: 开放外呼sop Controller
 * *@Date: 10:13 2025/5/20
 */
@Slf4j
@RestController
@RequestMapping("/external/deepsight/v1/sop")
public class ExternalAiobSOPController {

    private final AiobSOPService sopService;

    public ExternalAiobSOPController(AiobSOPService sopService) {
        this.sopService = sopService;
    }

    /**
     * 获取快捷场景rule和nodes
     * @param request SOPNodeListRequest
     * @return BaseResponse<SOPNodeListResponse>
     */
    @PostMapping("/quick/nodes/list")
    public BaseResponse<SOPQuickNodeListResponse> listQuickSopNodes(@RequestBody @Valid SOPQuickNodeListRequest request) {
        return BaseResponse.of(sopService.listQuickSOPNodes(request.getTenantId(), request.getTaskId(), request.getRobotVer()));
    }

    /**
     * 获取灵活画布流程
     */
    @PostMapping("/diagram/records")
    public BaseResponse<AiobDiagramVersionRecordViewResp> getDiagramRecords(@RequestBody @Valid
                                                                            AiobDiagramVersionRecordViewReq request) {
        return BaseResponse.of(sopService.getDiagramRecords(request.getAgentId(), request.getVersionId()));
    }

}
