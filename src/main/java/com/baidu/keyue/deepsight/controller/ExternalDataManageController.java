package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableDetailRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableFieldRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableUserInfoRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.TableDetailResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableFieldDetailResponse;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * *@Author: dongjiacheng01
 * *@Description: 开放数据集管理接口
 * *@Date: 16:50 2025/5/7
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/external/deepsight/v1/datatable")
public class ExternalDataManageController {

    @Autowired
    private DataTableManageService dataTableManageService;

    /**
     * 数据集列表
     */
    @PostMapping("/list")
    public BasePageResponse<TableDetailResponse> list(@Valid @RequestBody GetTableListRequest request) {
        return BasePageResponse.of(dataTableManageService.getDataTableList(request));
    }

    /**
     * 数据表数据字段
     */
    @PostMapping(value = "/field/list")
    public BasePageResponse<TableFieldDetailResponse> getFieldList(@Valid @RequestBody GetTableFieldRequest request) {
        return BasePageResponse.of(dataTableManageService.getTableFieldList(request, false));
    }

    /**
     * 数据表详情
     */
    @PostMapping(value = "/detail")
    public BaseResponse<TableDetailResponse> tableDetail(@Valid @RequestBody GetTableDetailRequest request) {
        return BaseResponse.of(dataTableManageService.getDataTableDetail(request));
    }

    @GetMapping(value = "/field/filterList")
    public BaseResponse<List<DatasetPropertiesResult>> getFilterFields() {
        return BaseResponse.of(dataTableManageService.getTableProperties());
    }

    @PostMapping(value = "/userInfo")
    public BasePageResponse.Page<Map<String, String>> getUserInfo(@Valid @RequestBody GetTableUserInfoRequest request) {
        return dataTableManageService.getTableUserInfo(true, request);
    }
}
