package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableDataCurlRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.TableSyncDetailResponse;
import com.baidu.keyue.deepsight.service.datamanage.TableContentService;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * *@Author: dongjiacheng01
 * *@Description: 开放数据表content接口
 * *@Date: 13:51 2025/5/8
 */
@RestController
@RequestMapping("/external/deepsight/v1/table/content")
@Slf4j
public class ExternalTableRecordController {

    @Autowired
    private TableContentService contentService;

    /**
     * 数据接入信息
     */
    @PostMapping("/info/sync")
    public BaseResponse<TableSyncDetailResponse> dataCurlInfo(@Valid @RequestBody GetTableDataCurlRequest request) {
        return BaseResponse.of(contentService.getSyncInfo(request));
    }

}
