package com.baidu.keyue.deepsight.controller;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.base.response.BaseResponse;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.SwitchIdMappingRelRequest;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingManagerService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @className: IdMappingManagerController
 * @description: ID mapping 管理API
 * @author: wangz<PERSON>cheng
 * @date: 2025/3/12 10:46
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "/deepsight/v1/idMappingManager")
public class IdMappingManagerController {

    @Autowired
    private IdMappingManagerService idMappingManagerService;

    /**
     * 控制开关状态设置
     * @param request
     * @return
     */
    @PostMapping("/switch/status")
    public BaseResponse<Void> setSwitchStatus(@Valid @RequestBody SwitchIdMappingRelRequest request) {
        idMappingManagerService.setSwitchStatus(request.getStatus());
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    /**
     * 获取ID抽取的开关状态
     * @return
     */
    @GetMapping("/switch/status")
    public BaseResponse<Boolean> getSwitchStatus() {
        return BaseResponse.of(idMappingManagerService.getSwitchStatus());
    }

    @PostMapping("/switch/rerun")
    public BaseResponse<Void> rerunIdMapping() {
        idMappingManagerService.rerunIdMapping();
        return BaseResponse.of(ErrorCode.SUCCESS);
    }

    @PostMapping("/init")
    public BaseResponse<Void> initDefaultIdMapping() {
        String tenantId = WebContextHolder.getTenantId();
        idMappingManagerService.initDefaultIdMapping(tenantId);
        return BaseResponse.of(ErrorCode.SUCCESS);
    }
}
