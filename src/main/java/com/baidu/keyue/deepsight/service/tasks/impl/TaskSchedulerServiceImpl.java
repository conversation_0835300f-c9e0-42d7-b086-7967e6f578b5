package com.baidu.keyue.deepsight.service.tasks.impl;

import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.models.bsc.basic.BaseCalculateContext;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskScheduler;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendTaskSchedulerMapper;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.XID;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TaskSchedulerServiceImpl implements TaskSchedulerService {
    @Autowired
    private ExtendTaskSchedulerMapper schedulerMapper;

    @Override
    public List<TaskSchedulerWithBLOBs> queryLatestByTaskIds(List<Long> taskIds) {
        return schedulerMapper.queryLatestByTaskIds(taskIds);
    }

    @Override
    public List<TaskSchedulerWithBLOBs> queryWithIds(List<Long> ids) {
        TaskSchedulerCriteria taskSchedulerCriteria = new TaskSchedulerCriteria();
        TaskSchedulerCriteria.Criteria criteria = taskSchedulerCriteria.createCriteria();
        criteria.andIdIn(ids);
        return schedulerMapper.selectByExampleWithBLOBs(taskSchedulerCriteria);
    }

    @Override
    public TaskSchedulerWithBLOBs queryWithId(Long id) {
        return schedulerMapper.selectByPrimaryKey(id);
    }

    @Override
    public Map<Long, TaskSchedulerWithBLOBs> queryTaskScheduler(List<Long> taskIds) {
        List<Long> validIds = taskIds.stream()
                .filter(Objects::nonNull)
                .filter(id -> id != 0L)
                .toList();
        if (CollectionUtils.isEmpty(validIds)) {
            return new HashMap<>();
        }
        List<TaskSchedulerWithBLOBs> taskSchedulerRecordList = queryLatestByTaskIds(validIds);
        List<Long> recordIds = taskSchedulerRecordList.stream().map(TaskSchedulerWithBLOBs::getId).toList();

        if (CollectionUtils.isEmpty(recordIds)) {
            return new HashMap<>();
        }

        List<TaskSchedulerWithBLOBs> distinctRecords = queryWithIds(recordIds);

        return distinctRecords.stream().collect(
                Collectors.toMap(TaskSchedulerWithBLOBs::getTaskId, Function.identity(), (k1, k2) -> k2));
    }

    @Override
    public long newTaskScheduler(long taskId, String userId) {
        Date now = new Date();
        String externalId = XID.generateRequestID();
        TaskSchedulerWithBLOBs taskScheduler = new TaskSchedulerWithBLOBs();
        taskScheduler.setTaskId(taskId);
        taskScheduler.setExternalId(externalId);
        taskScheduler.setStatus(TaskExecStatusEnum.RUNNING.getCode());
        taskScheduler.setDel(DelEnum.NOT_DELETED.getBoolean());
        taskScheduler.setCreator(userId);
        taskScheduler.setModifier(userId);
        taskScheduler.setCreateTime(now);
        taskScheduler.setUpdateTime(now);
        schedulerMapper.insert(taskScheduler);

        return taskScheduler.getId();
    }

    @Override
    public void updateSchedulerStatus(Long id, TaskExecStatusEnum status) {
        schedulerMapper.updateSchedulerStatus(id, status.getCode(), new Date());
    }

    @Override
    public void updateScheduler(BaseCalculateContext calculateContext, TaskExecStatusEnum statusEnum) {
        long execId = calculateContext.getExecId();
        TaskSchedulerWithBLOBs execScheduler = queryWithId(execId);

        execScheduler.setExternalId(calculateContext.getInstanceId());
        execScheduler.setStatus(statusEnum.getCode());
        execScheduler.setMessage(Objects.isNull(calculateContext.getErr()) ? null : calculateContext.getErr().getMessage());
        execScheduler.setBody(JsonUtils.toJsonWithOutException(calculateContext));
        execScheduler.setUpdateTime(new Date());

        schedulerMapper.updateByPrimaryKeyWithBLOBs(execScheduler);
    }

    @Override
    public List<TaskSchedulerWithBLOBs> queryRunningScheduler(List<Long> taskIds) {
        // 查询每个 taskId 对应最新的执行记录
        TaskSchedulerCriteria taskSchedulerCriteria = new TaskSchedulerCriteria();
        taskSchedulerCriteria.createCriteria()
                .andTaskIdIn(taskIds)
                .andStatusEqualTo(TaskExecStatusEnum.RUNNING.getCode());
        taskSchedulerCriteria.setOrderByClause("id desc");

        List<TaskSchedulerWithBLOBs> records = schedulerMapper.selectByExampleWithBLOBs(taskSchedulerCriteria);
        Map<Long, TaskSchedulerWithBLOBs> m = records.stream()
                .collect(Collectors.toMap(TaskScheduler::getTaskId, Function.identity(), (k1, k2) -> k1));

        return Lists.newArrayList(m.values());
    }

    @Override
    public TaskScheduler queryLatestFinishedRecord(Long taskId) {
        TaskSchedulerCriteria taskSchedulerCriteria = new TaskSchedulerCriteria();
        taskSchedulerCriteria.createCriteria().andTaskIdEqualTo(taskId).andStatusEqualTo(TaskExecStatusEnum.SUCCESS.getCode());
        taskSchedulerCriteria.setOrderByClause("id desc limit 1");

        List<TaskScheduler> records = schedulerMapper.selectByExample(taskSchedulerCriteria);
        if (CollectionUtils.isNotEmpty(records)) {
            return records.get(0);
        }
        return null;
    }

}
