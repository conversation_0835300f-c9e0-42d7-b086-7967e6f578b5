package com.baidu.keyue.deepsight.service.consumer;

import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.service.datamanage.TableContentService;
import com.baidu.keyue.deepsight.service.tenant.impl.TenantInfoServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className AbstractDataSyncWorker
 * @description 数据接入基类
 * @date 2025/2/20 15:00
 */
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public abstract class AbstractDataSyncWorker {
    private final TableContentService contentService;
    private final TenantInfoServiceImpl tenantInfoService;

    protected abstract void processData(String data);
    
    /**
     * 校验租户是否开通过洞察
     *
     * @param tenantId    租户id
     * @param sourceType 数据源类型 客服/外呼
     */
    protected Boolean validateTenantRole(Long tenantId, String sourceType) {
        TenantInfo info = tenantInfoService.queryTenantInfo(String.valueOf(tenantId));
        // 注：只有开通使用过洞察权限的用户才有权限同步数据
        // todo 判断租户过期时间
        if (null != info) {
            return true;
        }
        return false;
    }

    /**
     * 调用数据接入
     *
     * @param itemMaps    记录集合
     * @param dataTableId 数据表id
     */
    protected void syncData(List<Map<String, Object>> itemMaps, String dataTableId) {
        contentService.handleBatchSync(itemMaps, dataTableId);
    }
}
