package com.baidu.keyue.deepsight.service.operation;

import com.baidu.keyue.deepsight.enums.OperationModeEnum;
import com.baidu.keyue.deepsight.models.operation.response.OperationModeResponse;

public interface OperationService {
    /**
     * 获取租户的运营模式
     * @param tenantId
     * @return
     */
    OperationModeResponse getTenantOperationMode(String tenantId);

    /**
     * 获取租户运营模式
     * @param tenantId
     * @return
     */
    OperationModeEnum detectTenantOperationMode(String tenantId);
}
