package com.baidu.keyue.deepsight.service.diffusion.impl;

import com.baidu.keyue.deepsight.config.BosConfig;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.DiffusionCalculateConfiguration;
import com.baidu.keyue.deepsight.config.DorisConfiguration;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.database.service.DorisUtils;
import com.baidu.keyue.deepsight.enums.DataTableStatusEnum;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.DeleteStatusEnum;
import com.baidu.keyue.deepsight.enums.DiffusionFilterEnum;
import com.baidu.keyue.deepsight.enums.JudgeCriteriaEnum;
import com.baidu.keyue.deepsight.enums.OperationModeEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionCalculateContext;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionGroupContext;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionModelRequest;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionRequestItem;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionResponseItem;
import com.baidu.keyue.deepsight.models.diffusion.dto.DiffusionCharacteristicDTO;
import com.baidu.keyue.deepsight.models.doris.TableDescribeDto;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.meg.Attribute;
import com.baidu.keyue.deepsight.models.meg.Description;
import com.baidu.keyue.deepsight.models.meg.Item;
import com.baidu.keyue.deepsight.models.meg.MEGRequestPrepare;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTaskCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.CustomerDiffusionTaskMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.diffusion.DiffusionResultService;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionCalculateService;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionService;
import com.baidu.keyue.deepsight.service.operation.OperationService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import com.baidu.keyue.deepsight.service.user.BaiduUserDataService;
import com.baidu.keyue.deepsight.service.tool.BosUtils;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TableNameUtil;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.commons.utils.HttpUtil;
import com.baidubce.services.bos.model.BosObject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GroupDiffusionCalculateServiceImpl implements GroupDiffusionCalculateService {

    private static final String SEED_GROUP_DATA_FILE_NAME = "seed";
    private static final String PREDICT_GROUP_DATA_FILE_NAME = "predict";
    private static final String PREDICT_OUTPUT_DONE_FILE_NAME = "donefile";
    private static final String PREDICT_OUTPUT_RESULT_FILE_NAME_PREFIX = "result";
    private static final String PREDICT_OUTPUT_FEAT_FILE_NAME_PREFIX = "featResult";
    private static final String PREDICT_OUTPUT_FAIL_FILE_NAME_PREFIX = "failed";

    @Autowired
    private DiffusionCalculateConfiguration diffusionCalculateConfiguration;

    @Autowired
    private CustomerDiffusionTaskMapper diffusionTaskMapper;

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private TaskSchedulerService taskSchedulerService;

    @Autowired
    private GroupDiffusionService groupDiffusionService;

    @Autowired
    private DorisConfiguration dorisConfiguration;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private CustomerGroupService customerGroupService;

    @Autowired
    private BosUtils bosUtils;

    @Autowired
    private BosConfig bosConfig;

    @Autowired
    private BaiduUserDataService baiduUserDataService;

    @Autowired
    private DiffusionResultService diffusionResultService;

    @Autowired
    private OperationService operationService;

    @Override
    public void execGroupDiffusion(long diffusionId, String userId) {
        log.info("execGroupDiffusion diffusionId: {}", diffusionId);

        // 加载预测任务
        List<CustomerDiffusionTask> diffusionTasks = groupDiffusionService.getCustomerDiffusionTaskWithId(diffusionId);
        if (CollectionUtils.isEmpty(diffusionTasks)) {
            log.warn("execGroupDiffusion diffusionId: {} not found", diffusionId);
            return;
        }
        CustomerDiffusionTask diffusionTask = diffusionTasks.get(0);

        // 初始化执行上下文 dto
        DiffusionCalculateContext context = initDiffusionCalculateContext(diffusionTask, userId);
        log.info("execGroupDiffusion start, execID: {}", context.getExecId());

        // 任务检查（客群检查）
        taskCheck(context);
        if (Objects.nonNull(context.getErr())) {
            onFailure(context);
            return;
        }

        // 获取mock_user 表 schema
        String mockUserTableName = TenantUtils.generateMockUserTableName(context.getTenantId());
        List<TableDescribeDto> tableSchemas = dorisService.describeTableSchema(mockUserTableName);

        // 初始化人群扩散输出文件 ObjectKey
        generateOutputFileConfig(context);

        // 代运营
        if (OperationModeEnum.OPERATION_BY_BAIDU_OP.equals(context.getOperationMode())) {
            // 捞-种子人群数据、入库、上传OSS
            fetchGroupData(context, mockUserTableName, tableSchemas, context.getSeedGroup(), SEED_GROUP_DATA_FILE_NAME);
            if (Objects.nonNull(context.getErr())) {
                onFailure(context);
                return;
            }

            // 捞-预测客群数据、入库、上传OSS
            fetchGroupData(context, mockUserTableName, tableSchemas, context.getPredictGroup(), PREDICT_GROUP_DATA_FILE_NAME);
            if (Objects.nonNull(context.getErr())) {
                onFailure(context);
                return;
            }
        } else {
            // 自运营
            // 捞-种子人群数据、入库、上传OSS
            fetchGroupDataWhenSelfOperation(
                    context, mockUserTableName, tableSchemas, context.getSeedGroup(), SEED_GROUP_DATA_FILE_NAME);
            if (Objects.nonNull(context.getErr())) {
                onFailure(context);
                return;
            }

            // 捞-预测客群数据、入库、上传OSS
            fetchGroupDataWhenSelfOperation(
                    context, mockUserTableName, tableSchemas, context.getPredictGroup(), PREDICT_GROUP_DATA_FILE_NAME);
            if (Objects.nonNull(context.getErr())) {
                onFailure(context);
                return;
            }
        }

        // pre-commit check
        taskPreCommitCheck(context);
        if (Objects.nonNull(context.getErr())) {
            onFailure(context);
            return;
        }

        submitModelExec(context);
        if (Objects.nonNull(context.getErr())) {
            onFailure(context);
            return;
        }

        onProcessing(context);
    }

    @Override
    public List<Pair<CustomerDiffusionTask, TaskInfo>> pullTaskByStatus(TaskExecStatusEnum statusEnum) {
        List<TaskInfo> taskInfoList = taskInfoService.pullWaitExecTask(TaskTypeEnum.CUSTOMER_DIFFUSION, TriggerModeEnum.CRON);
        Map<Long, TaskInfo> taskMap = taskInfoList.stream()
                .collect(Collectors.toMap(TaskInfo::getId, Function.identity(), (k1, k2) -> k2));
        if (taskMap.isEmpty()) {
            return null;
        }
        CustomerDiffusionTaskCriteria criteria = new CustomerDiffusionTaskCriteria();
        criteria.createCriteria()
                .andTaskIdIn(Lists.newArrayList(taskMap.keySet()))
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andCalStatusNotEqualTo(statusEnum.getCode());
        List<CustomerDiffusionTask> taskInfos = diffusionTaskMapper.selectByExample(criteria);


        return taskInfos.stream()
                .map(item -> {
                            Long taskId = item.getTaskId();
                            if (Objects.isNull(taskId)) {
                                return null;
                            }
                            TaskInfo task = taskMap.get(taskId);
                            if (Objects.isNull(task)) {
                                return null;
                            }
                            return new ImmutablePair<>(item, task);
                        }
                ).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void updateCalStatus(DiffusionCalculateContext context, TaskExecStatusEnum calStatus) {
        CustomerDiffusionTask task = context.getDiffusionTask();
        task.setCalStatus(calStatus.getCode());
        diffusionTaskMapper.updateByPrimaryKey(task);
    }

    private DiffusionCalculateContext initDiffusionCalculateContext(CustomerDiffusionTask diffusionTask, String userId) {
        DiffusionCalculateContext context = new DiffusionCalculateContext();
        context.setDiffusionTask(diffusionTask);
        context.setTenantId(diffusionTask.getTenantId());
        context.setThreshold(diffusionTask.getThreshold());

        // 生成临时表配置
        context.setTable(TableNameUtil.generateDiffusionTemporaryTableName(diffusionTask.getId()));

        // init task scheduler record
        context.setExecId(newSchedulerRecord(diffusionTask.getTaskId(), userId));

        // 判断租户运营模式，记录运营模式到 context 中，后续会影响拉数据
        context.setOperationMode(operationService.detectTenantOperationMode(context.getTenantId()));

        return context;
    }

    public void taskCheck(DiffusionCalculateContext context) {
        Long seedGroupId = context.getDiffusionTask().getSeedGroup();
        List<Long> predictGroupIds = Arrays.stream(StringUtils.split(context.getDiffusionTask().getPredictGroup(), ','))
                .map(Long::valueOf).distinct().collect(Collectors.toList());
        if (Objects.isNull(seedGroupId) || CollectionUtils.isEmpty(predictGroupIds)) {
            context.setErr(new DeepSightException.GroupDiffusionGroupCheckFailedException("人群ID为空"));
            return;
        }

        // 客群元数据查询
        List<CustomerGroup> seedGroups = customerGroupService.retrieveCustomerGroupWithIds(Lists.newArrayList(seedGroupId), context.getTenantId());
        List<CustomerGroup> predictGroups = customerGroupService.retrieveCustomerGroupWithIds(predictGroupIds, context.getTenantId());

        if (CollectionUtils.isEmpty(seedGroups) || CollectionUtils.isEmpty(predictGroups)) {
            context.setErr(new DeepSightException.GroupDiffusionGroupCheckFailedException());
            return;
        }

        List<Long> seedGroupIds = Lists.newArrayList(seedGroups.get(0).getId());
        predictGroupIds = predictGroups.stream().map(CustomerGroup::getId).collect(Collectors.toList());

        context.setSeedGroup(new DiffusionGroupContext(seedGroupIds));
        context.setPredictGroup(new DiffusionGroupContext(predictGroupIds));
    }

    private void taskPreCommitCheck(DiffusionCalculateContext context) {
        if (context.getSeedGroup().getCount() == 0L) {
            context.setErr(new DeepSightException.GroupDiffusionGroupCheckFailedException("种子人群为空"));
            return;
        }

        if (context.getPredictGroup().getCount() == 0L) {
            context.setErr(new DeepSightException.GroupDiffusionGroupCheckFailedException("预测人群为空"));
            return;
        }
    }

    private long newSchedulerRecord(long taskId, String userId) {
        return taskSchedulerService.newTaskScheduler(taskId, userId);
    }

    private void onFailure(DiffusionCalculateContext context) {
        // 更新调度记录
        taskSchedulerService.updateScheduler(context, TaskExecStatusEnum.FAILED);
        // 更新计算状态
        updateCalStatus(context, TaskExecStatusEnum.FAILED);
    }

    private void onSuccess(DiffusionCalculateContext context) {
        // 更新调度记录
        taskSchedulerService.updateScheduler(context, TaskExecStatusEnum.SUCCESS);
        // 更新计算状态
        updateCalStatus(context, TaskExecStatusEnum.SUCCESS);

        try {
            TaskInfo taskInfo = taskInfoService.getTaskDetailWithId(context.getDiffusionTask().getTaskId());
            if (Objects.nonNull(taskInfo)) {
                taskInfoService.updateNextCalDate(taskInfo);
            }
        } catch (Exception e) {
            log.error("GroupDiffusionCalculateService.onSuccess update task next run time failed, error : ", e);
        }
    }

    private void onProcessing(DiffusionCalculateContext context) {
        // 更新调度记录
        taskSchedulerService.updateScheduler(context, TaskExecStatusEnum.RUNNING);
        // 更新数据预测配置表里面计算状态
        updateCalStatus(context, TaskExecStatusEnum.RUNNING);
    }

    private void initDorisTable(DiffusionCalculateContext context) {
        try {
            dorisService.deleteDiffusionCalculateTemporaryTable(dorisConfiguration.getDb(), context.getTable());
            dorisService.createDiffusionCalculateTemporaryTable(dorisConfiguration.getDb(), context.getTable());
        } catch (Exception e) {
            log.error("GroupDiffusionCalculateService.initDorisTable error : ", e);
            context.setErr(new DeepSightException.GroupDiffusionGroupDorisTempResourceFailedException());
        }
    }

    /**
     * 初始化临时表信息-用于之后的结果查询
     * @param context
     */
    private void initDataTableInfo(DiffusionCalculateContext context) {
        try {
            DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
            dataTableInfoCriteria.createCriteria().andTableNameEqualTo(context.getTable());
            List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
            // 存在则刷新更新时间
            if (CollectionUtils.isNotEmpty(dataTableInfos)) {
                DataTableInfo dataTableInfo = dataTableInfos.get(0);
                dataTableInfo.setIsDel(DelEnum.NOT_DELETED.getCode());
                dataTableInfo.setUpdateTime(new Date());
                dataTableInfoMapper.updateByPrimaryKey(dataTableInfo);
                return;
            }

            // 初始化临时预测结果表的信息 - 预测结果筛选使用
            DataTableInfo predictResultTmpTable = new DataTableInfo();
            predictResultTmpTable.setCnName(context.getTable());
            predictResultTmpTable.setEnName(context.getTable());
            predictResultTmpTable.setDataType(0);
            predictResultTmpTable.setDescription(StringUtils.EMPTY);
            predictResultTmpTable.setDbType(DbTypeEnum.DORIS_TYPE.getDbType());
            predictResultTmpTable.setTenantid(context.getTenantId());
            predictResultTmpTable.setTableName(context.getTable());
            predictResultTmpTable.setIsDel(DeleteStatusEnum.NORMAL.getStatus().byteValue());
            predictResultTmpTable.setIsVisable(false);
            predictResultTmpTable.setIsPreset((byte) 0);
            predictResultTmpTable.setCreateTime(new Date());
            predictResultTmpTable.setUpdateTime(new Date());
            predictResultTmpTable.setStatus(DataTableStatusEnum.CREATED.getStatus().byteValue());
            dataTableInfoMapper.insert(predictResultTmpTable);

            // 初始化字段
            Date date = new Date();
            List<TableFieldMetaInfo> predictResultTmpFields = JsonUtils.toListUnchecked(
                    Constants.DIFFUSION_PROCESS_TEMPORARY_FIELD_TEMPLATE_JSON, List.class, TableFieldMetaInfo.class);
            for (TableFieldMetaInfo predictResultTmpField : predictResultTmpFields) {
                predictResultTmpField.setDataTableId(predictResultTmpTable.getId());
                predictResultTmpField.setTableEnName(context.getTable());
                predictResultTmpField.setCreateTime(date);
                predictResultTmpField.setUpdateTime(date);
                tableFieldMetaInfoMapper.insert(predictResultTmpField);
            }
        } catch (Exception ex) {
            log.error("GroupDiffusionCalculateService.initDataTable error : ", ex);
            context.setErr(new DeepSightException.GroupDiffusionGroupDorisTempResourceFailedException());
        }
    }

    private long countAllData(DiffusionCalculateContext context, DiffusionGroupContext dataGroup) {
        try {
            long count = dorisService.getCount(dataGroup.getCountSql());
            dataGroup.setCount(count);
            return count;
        } catch (Exception e) {
            log.error("GroupDiffusionCalculateService.countAllData error : ", e);
            context.setErr(new DeepSightException.GroupDiffusionCountFailedException());
            return 0L;
        }
    }

    private void fetchGroupData(
            DiffusionCalculateContext context,
            String mockUserTableName, List<TableDescribeDto> tableSchemas,
            DiffusionGroupContext group, String filename) {
        // 生成 sql
        Pair<String, String> sqlPair = ORMUtils.generateFetchPredictGroupDataSql(
                mockUserTableName, tableSchemas, group.getGroupIds(), context.getThreshold(), OperationModeEnum.OPERATION_BY_BAIDU_OP);
        group.setCountSql(sqlPair.getLeft());
        group.setQuerySql(sqlPair.getRight());

        // 生成本地临时文件名 + ObjectKey
        generateFileConfig(context, group, filename);

        // count
        long count = countAllData(context, group);
        group.setCount(count);
        if (count == 0L) {
            log.info("fetchGroupData count is 0, no need to fetch data, return");
            return;
        }

        dorisService.queryDorisStreaming(group.getQuerySql())
                .buffer(Constants.DIFFUSION_BATCH_SIZE)                // 数据分批
                .flatMap(batch -> this.processData(batch, context.getTenantId(), group))
                .onErrorResume(e -> {
                    log.error("fetchGroupData error", e);
                    return Mono.empty();
                })
                .doOnTerminate(() -> {
                    uploadTempFileToOSS(context, group);   // 上传OSS
                })
                .subscribe();
    }


    public Mono<Void> processData(List<Map<String, Object>> batch, String tenantId, DiffusionGroupContext group) {
        return Flux.fromIterable(batch)
                .flatMap(this::parserOneId)
                .collectList()
                .flatMap(oneIds -> prepareMegRequestData(oneIds, tenantId))
                .flatMapMany(Flux::fromIterable)
                .flatMap(this::callBaiduMegIn, Constants.DIFFUSION_CONCURRENCY)
                .collectList()
                .flatMap(items -> dataBatchAppend(items, group))
                .then();
    }

    public Mono<String> parserOneId(Map<String, Object> item) {
        return Mono.fromCallable(() -> {
            return item.get(Constants.TABLE_USER_ONE_ID).toString();
        }).subscribeOn(Schedulers.boundedElastic());
    }

    private Mono<List<MEGRequestPrepare>> prepareMegRequestData(List<String> oneIds, String tenantId) {
        return Mono.fromCallable(() -> {
            List<MEGRequestPrepare> items = baiduUserDataService.batchGetMEGRequestPrepare(oneIds, tenantId);
            log.info("prepareMegRequestData got items: {}", items.size());
            return items;
        }).subscribeOn(Schedulers.boundedElastic());
    }

    public Mono<DiffusionRequestItem> callBaiduMegIn(MEGRequestPrepare data) {
        return Mono.fromCallable(() -> {
                    DiffusionRequestItem item = baiduUserDataService.getUserAttribute(data);
                    if (Objects.isNull(item)) {
                        log.warn("callBaiduMegInBatch got null: {}", JsonUtils.toJsonWithOutException(data));
                    }
                    return item;
                })
                .retry(Constants.DIFFUSION_RETRY)
                .onErrorResume(e -> Mono.empty())
                .subscribeOn(Schedulers.boundedElastic());
    }

    public Mono<Void> dataBatchAppend(List<DiffusionRequestItem> batch, DiffusionGroupContext group) {
        return Mono.fromCallable(() -> {
                    // 写入临时文件
                    Path filePath = Paths.get(group.getTempFilename());

                    List<DiffusionRequestItem> validData = batch.stream().filter(Objects::nonNull).toList();
                    if (CollectionUtils.isEmpty(validData)) {
                        log.warn("dataBatchAppend validData is empty, batch size: {}", batch.size());
                        return null;
                    }

                    String contentString = validData.stream()
                            .map(JsonUtils::toJsonWithOutException).collect(Collectors.joining("\n"));
                    try {
                        Files.writeString(filePath, contentString + "\n", StandardOpenOption.CREATE, StandardOpenOption.APPEND);
                    } catch (IOException e) {
                        log.error("dataBatchAppend write file error", e);
                    }
                    log.info("dataBatchAppend write file success, filename: {}, batch size: {}", group.getTempFilename(), batch.size());
                    return null;
                })
                .subscribeOn(Schedulers.boundedElastic())
                .then();
    }

    // 6. 上传OSS（伪代码）
    public void uploadTempFileToOSS(DiffusionCalculateContext context, DiffusionGroupContext group) {
        if (!Files.exists(Path.of(group.getTempFilename()))) {
            context.setErr(new DeepSightException.GroupDiffusionDataNullFailedException());
            return;
        }
        try {
            bosUtils.putObject(bosConfig.getBucket().getDiffusion(), group.getObjectKey(), new FileInputStream(group.getTempFilename()));
        } catch (FileNotFoundException e) {
            log.error("uploadTempFileToOSS, file not found error", e);
            context.setErr(new DeepSightException.GroupDiffusionUploadFailedException());
        }
        log.info("uploadTempFileToOSS success, filename: {}, objectKey: {}", group.getTempFilename(), group.getObjectKey());

        try {
            Files.deleteIfExists(Path.of(group.getTempFilename()));
        } catch (IOException e) {
            log.error("uploadTempFileToOSS, delete temp file error", e);
        }
    }

    private void generateFileConfig(DiffusionCalculateContext context, DiffusionGroupContext group, String baseFilename) {
        group.setTempFilename(baseFilename + "-" + context.getExecId());
        group.setObjectKey(java.lang.String.format("customer_diffusion/%s-%d/input/%s", bosConfig.getEnv(), context.getExecId(), baseFilename));
    }

    private void generateOutputFileConfig(DiffusionCalculateContext context) {
        // 生成输出目录文件夹前缀
        String ossDir = String.format("customer_diffusion/%s-%d/output/", bosConfig.getEnv(), context.getExecId());
        context.setOutputDirPrefix(ossDir);
    }

    @Override
    public void fetchGroupDiffusionResult(long execId) {
        log.info("fetchGroupDiffusionResult execId: {}", execId);
        TaskSchedulerWithBLOBs execScheduler = taskSchedulerService.queryWithId(execId);
        DiffusionCalculateContext context = JsonUtils.toObjectWithoutException(execScheduler.getBody(), DiffusionCalculateContext.class);
        if (Objects.isNull(context)) {
            log.error("fetchGroupDiffusionResult execId: {}, context is null", execId);
            return;
        }

        // 获取输出文件夹下所有 ObjectKey
        List<String> objectKeys = bosUtils.listObjectKeys(bosConfig.getBucket().getDiffusion(), context.getOutputDirPrefix());
        log.info("fetchGroupDiffusionResult execId: {}, objectKeys: {}", execId, objectKeys);

        // 检查失败
        if (failFileExits(objectKeys)) {
            log.error("fetchGroupDiffusionResult execId: {}, failFile exist", context.getExecId());
            context.setErr(new DeepSightException.GroupDiffusionOutputFeatureFileHandleException("人群扩散任务执行失败"));
            onFailure(context);
            return;
        }

        // 检查 doneFile
        if (!downFileExits(objectKeys)) {
            log.info("fetchGroupDiffusionResult execId: {}, doneFile not exist", execId);
            return;
        }

        // 解析特征重要度结果
        parseDiffusionFeatResponseItem(context, objectKeys);
        if (Objects.nonNull(context.getErr())) {
            onFailure(context);
            return;
        }

        // 解析预测结果文件
        List<DiffusionResponseItem> predictResults = parseDiffusionPredictResponseItem(context, objectKeys);
        if (Objects.nonNull(context.getErr())) {
            onFailure(context);
            return;
        }

        // 更新 doris
        syncDorisResult(context, predictResults);

        onSuccess(context);
    }

    /**
     * 检查 DoneFile 是否存在
     * @param objectKeys oss 文件 ObjectKey
     * @return bool
     */
    public boolean downFileExits(List<String> objectKeys) {
        if (CollectionUtils.isEmpty(objectKeys)) {
            return false;
        }
        return objectKeys.stream()
                .anyMatch(key ->
                        PREDICT_OUTPUT_DONE_FILE_NAME.equals(StringUtils.substringAfterLast(key, "/"))
                );
    }

    /**
     * 检查任务是否失败
     * @param objectKeys oss 文件 ObjectKey
     * @return bool
     */
    public boolean failFileExits(List<String> objectKeys) {
        if (CollectionUtils.isEmpty(objectKeys)) {
            return false;
        }
        return objectKeys.stream()
                .anyMatch(key ->
                        PREDICT_OUTPUT_FAIL_FILE_NAME_PREFIX.equals(StringUtils.substringAfterLast(key, "/"))
                );
    }

    private void parseDiffusionFeatResponseItem(DiffusionCalculateContext context, List<String> objectKeys) {
        List<String> featFileObjects = objectKeys.stream()
                .filter(key ->
                        StringUtils.startsWith(StringUtils.substringAfterLast(key, "/"), PREDICT_OUTPUT_FEAT_FILE_NAME_PREFIX)
                ).toList();
        if (CollectionUtils.isEmpty(featFileObjects)) {
            log.info("parseDiffusionFeatResponseItem not found featFileObjects, execId: {}", context.getExecId());
            context.setErr(new DeepSightException.GroupDiffusionOutputFeatureFileHandleException("特征重要度结果获取失败"));
            return;
        }

        List<String> lines = ossFileReaderLines(context, featFileObjects.get(0));
        context.setFeatures(
                lines.stream()
                        .map(line -> JsonUtils.toObjectWithoutException(line, DiffusionCharacteristicDTO.class))
                        .filter(Objects::nonNull).collect(Collectors.toList())
        );
    }

    /**
     * 读取 object 所有行
     * @param context
     * @param objectKey
     * @return
     */
    public List<String> ossFileReaderLines(DiffusionCalculateContext context, String objectKey) {
        BosObject bosObject = bosUtils.getObject(bosConfig.getBucket().getDiffusion(), objectKey);
        if (bosObject == null) {
            log.error("ossFileReaderLines execId: {}, task output file: {} not exists", context.getExecId(), objectKey);
            context.setErr(new DeepSightException.GroupDiffusionOutputFileMissingException());
            return Collections.emptyList();
        }

        List<String> items = Lists.newArrayList();
        try (InputStream inputStream = bosObject.getObjectContent();
             InputStreamReader reader = new InputStreamReader(inputStream);
             BufferedReader bufferedReader = new BufferedReader(reader);) {
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                if (StringUtils.isNotBlank(line)) {
                    items.add(line);
                }
            }
        } catch (IOException e) {
            log.error("fetchGroupDiffusionResult execId: {}, read output file: {} error", context.getExecId(), objectKey, e);
            context.setErr(new DeepSightException.GroupDiffusionOutputFileInvalidException());
        }
        return items;
    }

    public List<DiffusionResponseItem> parseDiffusionPredictResponseItem(DiffusionCalculateContext context, List<String> objectKeys) {
        List<String> featFileObjects = objectKeys.stream()
                .filter(key ->
                        StringUtils.startsWith(StringUtils.substringAfterLast(key, "/"), PREDICT_OUTPUT_RESULT_FILE_NAME_PREFIX)
                ).toList();
        if (CollectionUtils.isEmpty(featFileObjects)) {
            log.info("parseDiffusionPredictResponseItem not found predict result FileObjects, execId: {}", context.getExecId());
            context.setErr(new DeepSightException.GroupDiffusionOutputFeatureFileHandleException("预测结果获取失败"));
            return Collections.emptyList();
        }

        List<String> allLines = Lists.newArrayList();
        featFileObjects.forEach(key -> {
            allLines.addAll(ossFileReaderLines(context, key));
        });

        Map<String, DiffusionResponseItem> resultMap = allLines.stream()
                .map(line -> JsonUtils.toObjectWithoutException(line, DiffusionResponseItem.class))
                .filter(item -> Objects.nonNull(item) && Objects.nonNull(item.getScore()))
                .collect(Collectors.toMap(
                        DiffusionResponseItem::getOneId,
                        item -> item,
                        (k1, k2) -> k1
                ));

        if (resultMap.isEmpty()) {
            return Collections.emptyList();
        }

        List<DiffusionResponseItem> results = resultMap.values().stream()
                .sorted(Comparator.comparing(DiffusionResponseItem::getScore).reversed()).collect(Collectors.toList());

        JudgeCriteriaEnum judgeCriteria = JudgeCriteriaEnum.getByCode(context.getDiffusionTask().getJudgeCriteria());
        if (JudgeCriteriaEnum.SIMILARITY.equals(judgeCriteria)) {
            return results.stream()
                    .filter(item ->
                            item.getScore() * 100 >= context.getDiffusionTask().getSimilarity()
                    )
                    .collect(Collectors.toList());
        } else if (JudgeCriteriaEnum.RANKING.equals(judgeCriteria)) {
            return results.subList(0, Math.min(context.getDiffusionTask().getRanking(), results.size()));
        } else {
            return results;
        }
    }

    private void syncDorisResult(DiffusionCalculateContext context, List<DiffusionResponseItem> responseItems) {
        // doris 临时表初始化
        initDorisTable(context);
        if (Objects.nonNull(context.getErr())) {
            onFailure(context);
            return;
        }
        // 初始化临时表 dataset-meta 信息
        initDataTableInfo(context);
        if (Objects.nonNull(context.getErr())) {
            onFailure(context);
            return;
        }
        if (CollectionUtils.isEmpty(responseItems)) {
            return;
        }
        Lists.partition(responseItems, Constants.DIFFUSION_BATCH_SIZE).forEach(batch -> {
            String now = DatetimeUtils.formatDate(LocalDateTime.now());
            String sql = ORMUtils.generateBatchInsertDiffusion(context.getTable(), filterSeedGroup(context, batch), now);
            try {
                dorisService.execSql(sql);
            } catch (Exception e) {
                log.error("batchUpdateDorisResult write doris failed, error:", e);
            }
        });
    }

    /**
     * 当客群配置了过滤种子人群时，需要在结果中做 oneID 过滤
     * @param context 上下文信息
     * @param batch 预测结果
     * @return 过滤后结果
     */
    public List<DiffusionResponseItem> filterSeedGroup(DiffusionCalculateContext context, List<DiffusionResponseItem> batch) {
        // 判断过滤规则
        DiffusionFilterEnum filterRule = DiffusionFilterEnum.getByCode(context.getDiffusionTask().getFilterRule());
        if (DiffusionFilterEnum.NOT_FILTER.equals(filterRule)) {
            return batch;
        }

        if (Objects.isNull(context.getSeedGroup()) || CollectionUtils.isEmpty(context.getSeedGroup().getGroupIds())) {
            log.warn("filterSeedGroup got empty SeedGroup, execId: {}", context.getExecId());
            return batch;
        }
        // 客群字段
        String seedGroupFieldName = DorisUtils.generateCustomerGroupFieldName(context.getSeedGroup().getGroupIds().get(0));
        List<String> oneIdList = batch.stream().map(DiffusionResponseItem::getOneId).filter(StringUtils::isNotBlank).distinct().toList();
        String userTableName = TenantUtils.generateMockUserTableName(context.getTenantId());
        if (CollectionUtils.isEmpty(oneIdList)) {
            log.warn("filterSeedGroup got empty oneIds, execId: {}", context.getExecId());
            return batch;
        }

        String filterSql = ORMUtils.generateDiffusionFilterSeedGroupOneIds(userTableName, seedGroupFieldName, oneIdList);
        List<Map<String, Object>> result = Lists.newArrayList();
        try {
            result = dorisService.selectList(filterSql);
        } catch (Exception e) {
            log.error("filterSeedGroup sql: {}, error: ", filterSql, e);
            return batch;
        }

        Set<String> validOneIds = result.stream()
                .map(map -> (String) map.get("oneId"))
                .collect(Collectors.toSet());
        List<DiffusionResponseItem> afterFilterBatch = batch.stream()
                .filter(item -> validOneIds.contains(item.getOneId()))
                .collect(Collectors.toList());

        log.info("filterSeedGroup beforeBatchSize: {}, afterBatchSize: {}", batch.size(), afterFilterBatch.size());
        return afterFilterBatch;
    }

    private void submitModelExec(DiffusionCalculateContext context) {
        DiffusionModelRequest request = new DiffusionModelRequest();
        request.setTaskId(String.valueOf(context.getExecId()));
        request.getSeedUrls().add(context.getSeedGroup().getObjectKey());
        request.getCandidateUrls().add(context.getPredictGroup().getObjectKey());
        request.setResultUrl(context.getOutputDirPrefix());
        String requestBody = JsonUtils.toJsonWithOutException(request);
        try {
            String resp = HttpUtil.postJson(diffusionCalculateConfiguration.getModelUrl(), requestBody);
            log.info("submitModelExec execId: {}, submit model exec success, resp: {}", context.getExecId(), resp);
            if (StringUtils.isBlank(resp)) {
                log.warn("submitModelExec execId: {}, submit model exec failed, resp is empty, requestBody: {}", context.getExecId(), requestBody);
                context.setErr(new DeepSightException.GroupDiffusionModelSubmitFailedException());
                return;
            }
        } catch (Exception e) {
            log.error("submitModelExec execId: {}, submit model exec failed, requestBody: {}, err: ", context.getExecId(), requestBody, e);
            context.setErr(new DeepSightException.GroupDiffusionModelSubmitFailedException());
        }
        log.info("submitModelExec execId: {}, submit model exec success", context.getExecId());
    }

    @Override
    public void packageAsCustomerGroup(long customerDiffusionTaskId) {
        // 查询人群扩散配置，不存在则抛出异常
        CustomerDiffusionTask diffusionTask = diffusionResultService.checkCustomerDiffusionTask(customerDiffusionTaskId);
        // 查询人群扩散任务最新成功的记录，不存在则抛出异常
        TaskSchedulerWithBLOBs taskScheduler = diffusionResultService.getLatestSuccessTaskSchedulerRecord(diffusionTask.getId(), diffusionTask.getTaskId());
        if (Objects.isNull(taskScheduler)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "操作失败，人群扩散任务未成功执行");
        }

        DiffusionCalculateContext context = JsonUtils.toObjectWithoutException(taskScheduler.getBody(), DiffusionCalculateContext.class);
        if (Objects.isNull(context)) {
            log.error("packageAsCustomerGroup invalid finished task, got null body, diffusionId: {}, taskId: {}", diffusionTask.getId(), diffusionTask.getTaskId());
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "操作失败，人群扩散任务解析特征失败");
        }

        // 创建客群记录、字段
        String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
        CustomerGroup customerGroup = customerGroupService.newDiffusionCustomerGroup(diffusionTask.getTenantId(), userId, diffusionTask.getTaskName());
        if (Objects.isNull(customerGroup)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "操作失败，客群创建异常");
        }
        // 清理老的客群数据
        clearCustomerGroupData(context, diffusionTask.getTenantId(), customerGroup.getId());

        // 写入新的客群数据
        migrateDiffusionDataToCustomerGroup(context, diffusionTask.getTenantId(), customerGroup.getId());

        // 更新客群信息到人群扩散表
        diffusionResultService.updateDiffusionTaskCustomerGroupInfo(diffusionTask.getId(), customerGroup.getId());
    }

    @Override
    public List<TaskSchedulerWithBLOBs> pullRunningTask() {
        // 查询所有在计算中的任务
        CustomerDiffusionTaskCriteria criteria = new CustomerDiffusionTaskCriteria();
        criteria.createCriteria()
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean())
                .andCalStatusEqualTo(TaskExecStatusEnum.RUNNING.getCode());
        List<CustomerDiffusionTask> taskInfos = diffusionTaskMapper.selectByExample(criteria);
        List<Long> taskIds = taskInfos.stream().map(CustomerDiffusionTask::getTaskId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.emptyList();
        }
        // 查询
        return taskSchedulerService.queryRunningScheduler(taskIds);
    }

    public void clearCustomerGroupData(DiffusionCalculateContext context, String tenantId, Long customerGroupId) {
        String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);
        String groupFieldName = DorisUtils.generateCustomerGroupFieldName(customerGroupId);
        String sql = ORMUtils.generateClearCustomerGroupDataSql(mockUserTableName, groupFieldName);
        try {
            dorisService.execSql(sql);
        } catch (Exception e) {
            log.error("clearCustomerGroupData clear doris failed, execId: {}, sql: {}, error:", context.getExecId(), sql, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "操作失败，客群初始化失败");
        }
    }

    public void migrateDiffusionDataToCustomerGroup(DiffusionCalculateContext context, String tenantId, Long customerGroupId) {
        String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);
        String groupFieldName = DorisUtils.generateCustomerGroupFieldName(customerGroupId);
        String fromTable = context.getTable();
        if (StringUtils.isBlank(fromTable)) {
            log.error("migrateDiffusionDataToCustomerGroup diffusionTable not exists, execId: {}", context.getExecId());
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "操作失败，客群打包失败");
        }

        String sql = ORMUtils.generateMigrateDiffusionDataToCustomerGroup(mockUserTableName, fromTable, groupFieldName);
        try {
            dorisService.execSql(sql);
        } catch (Exception e) {
            log.error("migrateDiffusionDataToCustomerGroup migrate doris data failed, execId: {}, sql: {}, error:", context.getExecId(), sql, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.NOT_FOUND, "操作失败，客群打包失败");
        }
    }

    /**
     * 自运营模式下获取客群数据
     * @param context 上下文信息
     * @param mockUserTableName mockUserTableName
     * @param tableSchemas tableSchemas
     * @param group 客群信息
     * @param filename 文件名
     */
    public void fetchGroupDataWhenSelfOperation(
            DiffusionCalculateContext context,
            String mockUserTableName, List<TableDescribeDto> tableSchemas,
            DiffusionGroupContext group, String filename) {
        // 生成 sql
        Pair<String, String> sqlPair = ORMUtils.generateFetchPredictGroupDataSql(
                mockUserTableName, tableSchemas, group.getGroupIds(), context.getThreshold(), OperationModeEnum.OPERATION_BY_SELF);
        group.setCountSql(sqlPair.getLeft());
        group.setQuerySql(sqlPair.getRight());

        // 生成本地临时文件名 + ObjectKey
        generateFileConfig(context, group, filename);

        // count
        long count = countAllData(context, group);
        group.setCount(count);
        if (count == 0L) {
            log.info("fetchGroupDataWhenSelfOperation count is 0, no need to fetch data, return");
            return;
        }

        dorisService.queryDorisStreaming(group.getQuerySql())
                .buffer(Constants.DIFFUSION_BATCH_SIZE)                // 数据分批
                .flatMap(batch -> this.processDataWhenSelfOperation(batch, group, mockUserTableName))
                .onErrorResume(e -> {
                    log.error("fetchGroupDataWhenSelfOperation error", e);
                    return Mono.empty();
                })
                .doOnTerminate(() -> {
                    uploadTempFileToOSS(context, group);   // 上传OSS
                })
                .subscribe();
    }

    public Mono<Void> processDataWhenSelfOperation(
            List<Map<String, Object>> batch, DiffusionGroupContext group, String mockUserTableName) {
        return Flux.fromIterable(batch)
                .flatMap(this::parserOneId)
                .collectList()
                .flatMap(oneIds -> queryBasicInfoWithOneIds(mockUserTableName, oneIds))
                .flatMap(items -> dataBatchAppend(items, group))
                .then();
    }

    public Mono<List<DiffusionRequestItem>> queryBasicInfoWithOneIds(String mockUserTableName, List<String> oneIds) {
        return Mono.fromCallable(() -> {
            // 从 mock user 表查询基础信息
            String sqlStr = ORMUtils.generateQueryBasicInfoWithOneIds(mockUserTableName, oneIds);

            List<Map<String, Object>> recordList = Lists.newArrayList();
            try {
                recordList = dorisService.selectList(sqlStr);
            } catch (Exception e) {
                log.error("queryBasicInfoWithOneIds selectList error, sql: {}, err: ", sqlStr, e);
            }

            // 组装成画像 attributes
            List<DiffusionRequestItem> items = Lists.newArrayList();
            for (Map<String, Object> row : recordList) {
                // oneId
                String oneId = row.get(Constants.TABLE_USER_ONE_ID).toString();

                DiffusionRequestItem item = new DiffusionRequestItem();
                item.setOneId(oneId);
                item.setAttribute(Lists.newArrayList());

                // 基础属性
                for (Map.Entry<String, String> entry : Constants.DIFFUSION_BASIC_INFO_WITH_SELF_OPERATION.entrySet()) {
                    if (Objects.isNull(row.get(entry.getKey()))) {
                        continue;
                    }
                    String fieldValue = row.get(entry.getKey()).toString();
                    if (StringUtils.isBlank(fieldValue)) {
                        continue;
                    }

                    Attribute a = new Attribute();
                    a.setDescription(new Description(entry.getValue()));
                    a.setItem(Lists.newArrayList(new Item(fieldValue, 50)));
                    item.getAttribute().add(a);
                }

                items.add(item);
            }
            log.info("queryBasicInfoWithOneIds got items: {}", items.size());
            return items;
        }).subscribeOn(Schedulers.boundedElastic());
    }
}
