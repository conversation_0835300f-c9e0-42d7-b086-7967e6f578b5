package com.baidu.keyue.deepsight.service.datamanage;

import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionResponse;

import java.util.List;

/**
 * @ClassName AiobRobotVersionService
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/5/27 17:00
 */
public interface AiobRobotVersionService {

    /**
     * 获取机器人版本信息
     * @param tenantId
     * @param robotId
     * @param botVersionId
     * @return
     */
    boolean getAiobRobotVersion(String tenantId, String robotId, String botVersionId);

    /**
     * 获取机器人版本列表
     */
    List<SopWholeRobotVersionResponse> getAiobRobotVersionList(String tenantId, String robotId);

    /**
     * 保存机器人版本信息
     * @param tenantId
     * @param robotId
     * @param botVersionId
     */
    void saveAiobRobotVersion(String tenantId, String robotId, String botVersionId, String botVersionName, Long publishTime);

    /**
     * 保存机器人版本信息
     * @param tenantId
     * @param robotId
     * @param botVersionId
     * @param botVersionName
     * @param publishTime
     */
    void saveDiagramRobotVersion(String tenantId, String robotId, String botVersionId, String botVersionName, String publishTime);

}
