package com.baidu.keyue.deepsight.service.customer;

import com.baidu.keyue.deepsight.models.customer.request.GetCustomerGroupRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * @className: CustomerGroupCalculateService
 * @description: 客群计算服务接口
 * @author: wangzhongcheng
 * @date: 2025/1/16 16:57
 */
public interface CustomerGroupCalculateService {

    /**
     * 手动执行，由前端用户触发
     */
    void execByManual(GetCustomerGroupRequest request);
    

    /**
     * 拉取待执行的任务
     */
    List<Pair<CustomerGroup, TaskInfo>> pullWaitExecCustomerTask();

    /**
     * 由调度器触发执行
     */
    void execByScheduler(CustomerGroup customerGroup, TaskInfo taskInfo);

    /**
     * 无效客群Doris字段清理
     */
    void invalidCustomerDorisFieldClear(CustomerGroup customerGroup);

}
