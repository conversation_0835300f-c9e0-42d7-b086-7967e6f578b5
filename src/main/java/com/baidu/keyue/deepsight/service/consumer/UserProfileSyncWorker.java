package com.baidu.keyue.deepsight.service.consumer;

import com.baidu.keyue.deepsight.models.profile.UserProfileKafkaMessage;
import com.baidu.keyue.deepsight.service.user.UserProfileService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class UserProfileSyncWorker {

    private final UserProfileService userProfileService;

    public UserProfileSyncWorker(UserProfileService userProfileService) {
        this.userProfileService = userProfileService;
    }

    /**
     * 监听 oneID 消息，将 mock-user 表数据迁移到user-profile 表
     * @param record kafka-record
     */
    @KafkaListener(topics = "${kafka.topics.userProfile:deep_sight_user_profile_dev}")
    public void syncUserProfile(ConsumerRecord<String, String> record) {
        String message = record.value();
        log.info("syncUserProfile receive message: {}", message);

        UserProfileKafkaMessage data = JsonUtils.toObjectWithoutException(message, UserProfileKafkaMessage.class);
        if (Objects.isNull(data) || StringUtils.isBlank(data.getOneId()) || StringUtils.isBlank(data.getTenantId())) {
            log.warn("syncUserProfile invalid message");
        } else {
            userProfileService.mergeUserProfileByOneId(data.getTenantId(), data.getOneId());
        }
        log.info("syncUserProfile end");
    }
}
