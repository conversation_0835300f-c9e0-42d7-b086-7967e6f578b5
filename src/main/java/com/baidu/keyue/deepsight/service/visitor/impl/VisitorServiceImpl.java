package com.baidu.keyue.deepsight.service.visitor.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.visitor.request.VisitorDeleteRequest;
import com.baidu.keyue.deepsight.models.visitor.request.VisitorQueryRequest;
import com.baidu.keyue.deepsight.models.visitor.request.VisitorSaveRequest;
import com.baidu.keyue.deepsight.models.visitor.response.VisitorQueryResponse;
import com.baidu.keyue.deepsight.models.visitor.response.VisitorSaveResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.visitor.VisitorService;
import com.baidu.keyue.deepsight.utils.AESUtils;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName VisitorServiceImpl
 * @Description 访客接口实现类
 * <AUTHOR>
 * @Date 2025/2/20 3:59 PM
 */
@Slf4j
@Service
public class VisitorServiceImpl implements VisitorService {

    @Resource
    private DorisService dorisService;

    @Resource
    private TableRecordCommonService tableRecordCommonService;
    @Resource
    private DataTableManageService dataTableManageService;

    private final Map<String, String> fieldMapping = new HashMap<>();

    @PostConstruct
    public void init() {
        // 初始化历史访客表与mock表字段映射
        fieldMapping.put("id", "user_id");
        fieldMapping.put("customer_name", "user_name");
        fieldMapping.put("mobile", "mobile_list");
        fieldMapping.put("source", "source");
        fieldMapping.put("mail", "email_list");
        fieldMapping.put("age", "age");
    }


    @Override
    public VisitorQueryResponse queryList(VisitorQueryRequest queryRequest) {
        String tenantId = WebContextHolder.getTenantId();
        Assert.notBlank(tenantId, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "租户ID不能为空"));
        String id = queryRequest.getId();
        Long mobile = queryRequest.getMobile();
        if (StrUtil.isBlank(id) && mobile == null) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "手机号和ID不能同时为空");
        }
        String tableName = TenantUtils.generateMockUserTableName(tenantId);
        DataTableInfo tableInfo = tableRecordCommonService.getTableByTableName(tableName);
        Map<String, String> encryptFields = tableRecordCommonService.getEncryptFields(tableInfo.getId());
        String secretKey = encryptFields.get("mobile");
        String sql = ORMUtils.generateQueryUserByIdOrMobileList(tableName, id, mobile, secretKey);
        List<Map<String, Object>> maps = dorisService.selectList(sql);
        List<Map<String, Object>> resMap = new ArrayList<>();
        // 回显字段映射
        for (Map<String, Object> map : maps) {
            resMap.add(covertToVisitorData(map, tableName));
        }
        return new VisitorQueryResponse(resMap);
    }

    @Override
    public VisitorSaveResponse insert(VisitorSaveRequest saveRequest) {
        String tenantId = saveRequest.getTenantId();
        Assert.notBlank(tenantId, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "租户ID不能为空"));
        checkVisitorData(saveRequest.getData(), true);
        String tableName = TenantUtils.generateMockUserTableName(tenantId);
        // 自动生成ID
        String id = IdUtil.getSnowflakeNextIdStr();
        Map<String, Object> dataMap = saveRequest.getData();
        Object idObj = dataMap.get("id");
        // 如果是客服工作台同步数据，需要生成ID
        if (Objects.equals(Boolean.FALSE, saveRequest.getIsDorisData()) && idObj == null) {
            dataMap.put("id", id);
        } else if (idObj == null || StrUtil.isBlank(idObj.toString())) {
            // doris老数据同步，无ID跳过
            return new VisitorSaveResponse(null);
        }
        DataTableInfo tableInfo = tableRecordCommonService.getTableByTableName(tableName);
        Long dataTableId = tableInfo.getId();
        Map<String, Object> saveMap = covertToUserData(saveRequest.getData(), dataTableId, false);
        // 获取字段名部分
        Set<String> columns = saveMap.keySet();
        // 获取值部分，处理数据类型（特别是 String 和 ARRAY 类型）
        String values = columns.stream()
                .map(column -> handleSingle(saveMap.get(column)))
                .collect(Collectors.joining(", "));
        // 构建 SQL 语句
        String columnsStr = String.join(", ", columns);
        String sql = String.format("INSERT INTO %s (%s) VALUES (%s);", tableName, columnsStr, values);
        log.info("exec visitor insert :{}", sql);
        dorisService.execSql(sql);
        return new VisitorSaveResponse(id);
    }

    /**
     * 工作台数据转换为user Doris数据
     * 手机号会进行加密
     *
     * @param dataMap     工作台数据
     * @param dataTableId 用户基础信息表ID
     * @param fullUpdate  是否全量更新
     * @return mock——user表数据
     */
    public Map<String, Object> covertToUserData(Map<String, Object> dataMap, Long dataTableId, Boolean fullUpdate) {
        Map<String, Object> saveMap = new HashMap<>();
        Map<String, String> encryptFields = tableRecordCommonService.getEncryptFields(dataTableId);
        String mobileKey = encryptFields.get("mobile");
        for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
            String visitorField = entry.getKey();
            String userField = entry.getValue();
            Object dataObj = dataMap.get(visitorField);
            // 全量更新是，允许将字段改为null
            if (Objects.equals(fullUpdate, true) || dataObj != null) {
                // 手机号字段，加密
                if (Objects.equals(visitorField, "mobile") && dataObj != null) {
                    List<String> mobileList = new ArrayList<>();
                    for (Object obj : (Collection<?>) dataObj) {
                        String mobile = obj.toString();
                        if (StrUtil.isNotBlank(mobile)) {
                            mobileList.add(AESUtils.encrypt(obj.toString(), mobileKey));
                        }
                    }
                    saveMap.put(userField, mobileList);
                } else if (Objects.equals(visitorField, "mail") && dataObj != null) {
                    List<String> mailList = new ArrayList<>();
                    for (Object obj : (Collection<?>) dataObj) {
                        String mail = obj.toString();
                        if (StrUtil.isNotBlank(mail)) {
                            mailList.add(mail);
                        }
                    }
                    saveMap.put(userField, mailList);
                } else {
                    saveMap.put(userField, dataObj);
                }
            }
        }
        return saveMap;
    }

    /**
     * doris数据转为工作台数据，解密
     *
     * @param map       doris 数据
     * @param tableName 表名
     * @return
     */
    public Map<String, Object> covertToVisitorData(Map<String, Object> map, String tableName) {
        Map<String, Object> data = new HashMap<>();
        DataTableInfo tableInfo = tableRecordCommonService.getTableByTableName(tableName);
        Map<String, String> encryptFields = tableRecordCommonService.getEncryptFields(tableInfo.getId());
        String mobileKey = encryptFields.get("mobile");
        for (Map.Entry<String, String> entry : fieldMapping.entrySet()) {
            String visitorField = entry.getKey();
            String userField = entry.getValue();
            Object dataObj = map.get(userField);
            // 手机号字段，解密
            if (dataObj != null && Objects.equals(visitorField, "mobile")) {
                List<Long> mobileList = new ArrayList<>();
                List<String> list = JSONUtil.toList(dataObj.toString(), String.class);
                for (String str : list) {
                    String decrypt = AESUtils.decrypt(str, mobileKey);
                    mobileList.add(Long.parseLong(decrypt));
                }
                data.put(visitorField, JSONUtil.toJsonStr(mobileList));
            } else {
                data.put(visitorField, dataObj);
            }
        }
        return data;
    }

    /**
     * 访客管理数据检查
     *
     * @param data     访客数据
     * @param isInsert 是否是插入数据
     */
    public void checkVisitorData(Map<String, Object> data, boolean isInsert) {
        // 访客数据不能为空
        Assert.notNull(data, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "访客数据不能为空"));
        Object customerName = data.get("customer_name");
        Object source = data.get("source");
        Object mobile = data.get("mobile");
        Object age = data.get("age");
        if (isInsert && customerName == null) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "客户名称不能为空");
        }
        if (isInsert && source == null) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "客户名称不能为空");
        }
        if (isInsert && mobile == null) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "手机号不能为空");
        }
        // 手机号必须是数字数组
        if (mobile != null) {
            Assert.isTrue(mobile instanceof Collection<?>, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "手机号字段必须是数组"));
            Collection<?> arrayList = (Collection<?>) mobile;
            Assert.notEmpty(arrayList, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "手机号不能为空"));
            for (Object mo : arrayList) {
                Assert.isTrue(mo instanceof Number, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "手机号字段必须是数字"));
            }
        }
        // 邮箱必须是字符串数组
        Object mail = data.get("mail");
        if (mail != null) {
            Assert.isTrue(mail instanceof Collection<?>, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "邮箱必须是数组"));
            Collection<?> arrayList = (Collection<?>) mail;
            for (Object ma : arrayList) {
                Assert.isTrue(ma instanceof String, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "邮箱必须是字符串"));
            }
        }
        if (age != null && !(age instanceof Integer)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "年龄必须是int类型");
        }
    }

    @Override
    public void delete(VisitorDeleteRequest deleteRequest) {
        String id = deleteRequest.getId();
        Long mobile = deleteRequest.getMobile();
        String tenantId = WebContextHolder.getTenantId();
        Assert.notBlank(tenantId, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "租户ID不能为空"));
        String tableName = TenantUtils.generateMockUserTableName(tenantId);
        if (StrUtil.isBlank(id) && mobile == null) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "ID和手机号不能同时为空");
        }
        String sql = String.format("DELETE FROM %s WHERE ", tableName);
        // ID为单值匹配，手机号为多值匹配
        if (StrUtil.isNotBlank(id)) {
            sql = sql + "user_id = '" + StringEscapeUtils.escapeSql(id) + "';";
        } else if (mobile != null) {
            DataTableInfo tableInfo = tableRecordCommonService.getTableByTableName(tableName);
            Map<String, String> encryptFields = tableRecordCommonService.getEncryptFields(tableInfo.getId());
            String encrypted = AESUtils.encrypt(mobile.toString(), encryptFields.get("mobile"));
            sql = sql + "array_contains(mobile_list,'" + encrypted + "');";
        }
        dorisService.execSql(sql);
    }

    @Override
    public VisitorSaveResponse update(VisitorSaveRequest saveRequest) {
        // 构建更新语句
        String tenantId = WebContextHolder.getTenantId();
        Assert.notBlank(tenantId, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "租户ID不能为空"));
        Map<String, Object> data = saveRequest.getData();
        checkVisitorData(data, false);
        // 主键ID校验
        Object id = data.get("id");
        if (null == id || StrUtil.isBlank(id.toString())) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "id不能为空");
        }
        // 查询历史数据，避免非全量更新字段导致部分字段更新为null
        String tableName = TenantUtils.generateMockUserTableName(tenantId);
        String querySql = String.format("SELECT * FROM %s WHERE user_id = '%s' ;", tableName, StringEscapeUtils.escapeSql(id.toString()));
        List<Map<String, Object>> maps = dorisService.selectList(querySql);
        Assert.notEmpty(maps, () -> new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "更新数据不存在"));
        DataTableInfo tableInfo = tableRecordCommonService.getTableByTableName(tableName);
        Long dataTableId = tableInfo.getId();
        Map<String, Object> updateMap = covertToUserData(data, dataTableId, saveRequest.getFullUpdate());
        // source不可以编辑
        updateMap.remove("source");
        Map<String, Object> oldData = maps.get(0);
        for (Map.Entry<String, Object> entry : updateMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            // 全量更新时需支持将字段值改为Null，比如年龄
            if (Objects.equals(saveRequest.getFullUpdate(), true) || value != null) {
                oldData.put(key, value);
            }
        }
        oldData.put("deepsight_update_datetime", new Date());
        List<VisibleFieldResponse> visibleFields = dataTableManageService.getVisibleFields(dataTableId, false);
        Set<String> visibleFieldSet = visibleFields.stream().map(VisibleFieldResponse::getEnName).collect(Collectors.toSet());
        visibleFieldSet.remove("user_id");
        visibleFieldSet.add("email_list");
        visibleFieldSet.add("mobile_list");
        String idSql = handleSingle(id);
        StringBuilder setBuilder = new StringBuilder();
        for (Map.Entry<String, Object> entry : oldData.entrySet()) {
            if (visibleFieldSet.contains(entry.getKey())) {
                setBuilder.append(", ").append(entry.getKey()).append(" = ").append(handleSingle(entry.getValue()));
            }
        }
        String setSql = setBuilder.toString().replaceFirst(",", "");
        String updateSql = String.format("UPDATE %s SET %s WHERE user_id = %s;", tableName, setSql, idSql);
        log.info("exec visitor update :{}", updateSql);
        dorisService.execSql(updateSql);
        return new VisitorSaveResponse(id.toString());
    }

    /**
     * 处理单值转换为SQL语句值
     * 处理字符串、数字等类型为SQL可识别的字符串
     *
     * @param value 字段值
     * @return
     */
    public String handleSingle(Object value) {
        if (value == null) {
            return "NULL";
        } else if (value instanceof String) {
            // 如果是 String 类型，处理引号
            String replace = value.toString().replace("'", "''");
            return "'" + StringEscapeUtils.escapeSql(replace) + "'";
        } else if (value instanceof Collection<?> arrayList) {
            // 如果是数组类型（例如 ARRAY<STRING>），转换为 Doris 格式
            StringBuilder builder = new StringBuilder("[");
            for (Object data : arrayList) {
                builder.append(",").append(handleSingle(data));
            }
            return builder.append("]").toString().replaceFirst(",", "");
        } else if (value instanceof Integer || value instanceof Long || value instanceof Float || value instanceof Double) {
            // 数字类型无需加引号
            return value.toString();
        } else if (value instanceof Date date) {
            // 日期类型，转为合适的格式（如 'YYYY-MM-DD HH:MM:SS'）
            return "'" + DatetimeUtils.formatDate(date) + "'";
        } else if (value instanceof LocalDateTime localDateTime) {
            return "'" + localDateTime.format(DatetimeUtils.DATE_TIME_FORMATTER) + "'";
        } else {
            return StringEscapeUtils.escapeSql(value.toString());
        }
    }
}
