package com.baidu.keyue.deepsight.service.idmapping.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.PresetEnum;
import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.idmapping.request.datatable.CreateIdMappingDataTableRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.datatable.DeleteIdMappingDataTableRequest;
import com.baidu.keyue.deepsight.models.idmapping.response.datatable.ListIdMappingDataTableResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTable;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTableCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelation;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelationCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRuleCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingDataTableMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRelationMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRuleMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingDateTableService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @className: IdMappingDateTableServiceImpl
 * @description: IdMappingDateTableService 实现类
 * @author: chenwenyu03
 * @date: 2025/3/11 10:50
 */
@Slf4j
@Service
public class IdMappingDateTableServiceImpl implements IdMappingDateTableService  {

    @Autowired
    private IdMappingDataTableMapper idMappingDataTableMapper;

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    private IdMappingRuleMapper idMappingRuleMapper;

    @Autowired
    private IdMappingRelationMapper idMappingRelMapper;


    @Override
    @Transactional(rollbackOn = Exception.class)
    public void createIdMappingDataTable(CreateIdMappingDataTableRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();
        Long dataTableId = request.getDataTableId();

        if (getByDataTableId(dataTableId) != null) {
            log.error("创建待刷新数据集失败，待创建数据集已存在 dataTableId: {}", dataTableId);
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "待刷新数据集已存在");
        }

        List<DataTableInfo> dataTableInfos = getDataTableInfos(tenantId);

        if (dataTableInfos.isEmpty()) {
            log.error("创建待刷新数据集失败，待创建数据集不合法 dataTableId: {}", dataTableId);
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "待刷新数据集不合法");
        }

        Date date = new Date();
        createDataTable(dataTableId, userId, tenantId, date,
                PresetEnum.NOT_PRESET.getBoolean());
    }

    private void createDataTable(Long dataTableId, String userId, String tenantId, Date date, boolean preset) {
        IdMappingDataTable idMappingDataTable = new IdMappingDataTable();
        idMappingDataTable.setDataTableId(dataTableId);
        idMappingDataTable.setPreset(preset);
        idMappingDataTable.setCreator(userId);
        idMappingDataTable.setModifier(userId);
        idMappingDataTable.setTenantId(tenantId);
        idMappingDataTable.setCreateTime(date);
        idMappingDataTable.setUpdateTime(date);
        idMappingDataTable.setDel(DelEnum.NOT_DELETED.getBoolean());
        idMappingDataTableMapper.insert(idMappingDataTable);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void deleteIdMappingDataTable(DeleteIdMappingDataTableRequest request) {
        String userId = WebContextHolder.getUserId();
        Long dataTableId = request.getDataTableId();

        IdMappingDataTable idMappingDataTable = getByDataTableId(dataTableId);
        if (idMappingDataTable == null) {
            log.error("删除待刷新数据集失败，待删除数据集不存在 dataTableId: {}", dataTableId);
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "待删除数据集不存在");
        } else if (idMappingDataTable.getPreset()) {
            log.error("删除待刷新数据集失败，预设数据集无法删除 dataTableId: {}", dataTableId);
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "预设数据集无法删除");
        }

        Date date = new Date();
        idMappingDataTable.setModifier(userId);
        idMappingDataTable.setUpdateTime(date);
        idMappingDataTable.setDel(DelEnum.DELETED.getBoolean());
        idMappingDataTableMapper.updateByPrimaryKeySelective(idMappingDataTable);
    }

    @Override
    public BasePageResponse.Page<ListIdMappingDataTableResponse> listIdMappingDataTable(BasePageRequest request) {
        String tenantId = WebContextHolder.getTenantId();

        IdMappingDataTableCriteria idMappingDataTableCriteria = new IdMappingDataTableCriteria();
        idMappingDataTableCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());

        long count = idMappingDataTableMapper.countByExample(idMappingDataTableCriteria);

        if (count == 0)  {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }

        // 翻页组件
        PageHelper.startPage(request.getPageNo(), request.getPageSize());

        // 构造返回结果
        idMappingDataTableCriteria.setOrderByClause(Constants.ORDER_BY_CREATE_TIME_DESC);
        List<IdMappingDataTable> idMappingDataTables = idMappingDataTableMapper.selectByExample(idMappingDataTableCriteria);
        List<Long> dataTableIds = idMappingDataTables.stream().map(IdMappingDataTable::getDataTableId).toList();
        Map<Long, String> tableNameMap = getDataTableName(dataTableIds);
        Map<Long, ListIdMappingDataTableResponse> resultMap = assembleListResponse(tenantId, dataTableIds, tableNameMap);

        for (IdMappingDataTable table : idMappingDataTables)  {
            resultMap.get(table.getDataTableId()).setCreateTime(table.getCreateTime());
        }
        List<ListIdMappingDataTableResponse> resultList = dataTableIds.stream()
            .map(resultMap::get)
            .collect(Collectors.toList());
        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, resultList);

    }

    @Override
    public List<ListIdMappingDataTableResponse> listDataTable() {
        String tenantId = WebContextHolder.getTenantId();

        IdMappingDataTableCriteria idMappingDataTableCriteria = new IdMappingDataTableCriteria();
        idMappingDataTableCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<IdMappingDataTable> idMappingDataTables = idMappingDataTableMapper.selectByExample(idMappingDataTableCriteria);
        Set<Long> existedDataTableIds = idMappingDataTables.stream().map(IdMappingDataTable::getDataTableId).collect(Collectors.toSet());

        List<DataTableInfo> dataTableInfos = getDataTableInfos(tenantId);

        List<Long> dataTableIds = dataTableInfos.stream()
                .map(DataTableInfo::getId)
                .filter(dataTableId -> !existedDataTableIds.contains(dataTableId))
                .toList();
        Map<Long, String> tableNameMap = dataTableInfos.stream()
                .collect(Collectors.toMap(DataTableInfo::getId, DataTableInfo::getCnName, (v1, v2) -> v1));

        Map<Long, ListIdMappingDataTableResponse> resultMap = assembleListResponse(tenantId, dataTableIds, tableNameMap);
        return new ArrayList<>(resultMap.values());
    }

    private Map<Long, ListIdMappingDataTableResponse> assembleListResponse(String tenantId,
            List<Long> dataTableIds, Map<Long, String> tableNameMap) {
        // 获取配置的 id mapping 映射字段
        IdMappingRuleCriteria idMappingRuleCriteria= new IdMappingRuleCriteria();
        idMappingRuleCriteria.createCriteria().andTenantIdEqualTo(tenantId);
        List<IdMappingRule> idMappingRules = idMappingRuleMapper.selectByExample(idMappingRuleCriteria);
        List<String> enFieldNames = idMappingRules.stream().map(IdMappingRule::getEnField).toList();

        // 获取数据表字段信息
        TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        tableFieldMetaInfoCriteria.createCriteria().andDataTableIdIn(dataTableIds).
                andEnFieldIn(enFieldNames);
        tableFieldMetaInfoCriteria.setOrderByClause(Constants.ORDER_BY_EN_FIELD_DESC);
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper.selectByExample(tableFieldMetaInfoCriteria);

        Map<Long, ListIdMappingDataTableResponse> responseMap = dataTableIds.stream()
                .collect(Collectors.toMap(
                        id -> id,
                        id -> {
                            ListIdMappingDataTableResponse response = new ListIdMappingDataTableResponse();
                            response.setTableName(tableNameMap.get(id));
                            response.setDataTableId(id);
                            response.setTableCnFields(Lists.newArrayList());
                            response.setTableEnFields(Lists.newArrayList());
                            return response;
                        },
                        (v1, v2) -> v2
                ));

        for (TableFieldMetaInfo tableFieldMetaInfo : tableFieldMetaInfos) {
            Long dataTableId = tableFieldMetaInfo.getDataTableId();
            ListIdMappingDataTableResponse response = responseMap.get(dataTableId);
            response.getTableEnFields().add(tableFieldMetaInfo.getEnField());
            response.getTableCnFields().add(tableFieldMetaInfo.getCnField());
        }

        return responseMap;
    }


    private Map<Long, String> getDataTableName(List<Long> dataTableIds) {
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        dataTableInfoCriteria.createCriteria().andIdIn(dataTableIds);
        List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);

        return dataTableInfos.stream()
                .collect(Collectors.toMap(DataTableInfo::getId, DataTableInfo::getCnName, (v1, v2) -> v1));
    }

    /**
     * 根据dataTableId获取数据表ID对应的IdMappingDataTable对象, 满足条件的IdMappingDataTable应该至多只有一个
     *
     * @param dataTableId 数据表ID
     * @return 如果找到对应的数据表ID，则返回IdMappingDataTable对象；否则返回null
     */
    private IdMappingDataTable getByDataTableId(Long dataTableId) {
        String tenantId = WebContextHolder.getTenantId();
        IdMappingDataTableCriteria idMappingDataTableCriteria = new IdMappingDataTableCriteria();
        idMappingDataTableCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDataTableIdEqualTo(dataTableId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<IdMappingDataTable> idMappingDataTables = idMappingDataTableMapper.selectByExample(idMappingDataTableCriteria);
        return idMappingDataTables.isEmpty() ? null : idMappingDataTables.get(0);
    }

    /**
     * 初始化默认抽取字段规则
     */
    @Override
    @Transactional(rollbackOn = Exception.class)
    public void initDefaultIdMappingDataTable(String tenantId) {
        Date date = new Date();
        IdMappingRelationCriteria idMappingRelationCriteria = new IdMappingRelationCriteria();
        idMappingRelationCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<IdMappingRelation> idMappingRelations = idMappingRelMapper.selectByExample(idMappingRelationCriteria);
        List<Long> usedDataTableIds = idMappingRelations.stream().map(IdMappingRelation::getDataTableId).toList();
        for (Long dataTableId: usedDataTableIds) {
            createDataTable(dataTableId, Constants.SYSTEM_DEFAULT_USER_ID, tenantId, date,
                    PresetEnum.PRESET.getBoolean());
        }
    }

    @Override
    public void validateIdMappingDateTable(String tenantId) {
        // 校验数据表是否配置了id mapping
        List<DataTableInfo> dataTableInfos = getDataTableInfos(tenantId);
        Map<Long, String> tableNameMap = dataTableInfos.stream()
                .collect(Collectors.toMap(DataTableInfo::getId, DataTableInfo::getCnName, (v1, v2) -> v1));

        IdMappingDataTableCriteria idMappingDataTableCriteria = new IdMappingDataTableCriteria();
        idMappingDataTableCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<IdMappingDataTable> idMappingDataTables = idMappingDataTableMapper.selectByExample(idMappingDataTableCriteria);
        List<Long> dataTableIds = idMappingDataTables.stream().map(IdMappingDataTable::getDataTableId).toList();

        Map<Long, ListIdMappingDataTableResponse> resultMap = assembleListResponse(tenantId, dataTableIds, tableNameMap);
        for (ListIdMappingDataTableResponse response : resultMap.values()) {
            Long dataTableId = response.getDataTableId();
            String tableName = response.getTableName();
            if (response.getTableEnFields().isEmpty()) {
                log.error("数据表无可关联ID dataTableId: {}", dataTableId);
                throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST,
                        String.format("数据表 %s 无可关联ID, 请删除", tableName));
            }
        }
    }

    private List<DataTableInfo> getDataTableInfos(String tenantId) {
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        dataTableInfoCriteria.createCriteria()
                .andTenantidEqualTo(tenantId)
                .andIsVisableEqualTo(true)
                .andIsDelEqualTo(DelEnum.NOT_DELETED.getCode())
                .andDbTypeEqualTo(DbTypeEnum.DORIS_TYPE.getDbType());
        return dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
    }
}
