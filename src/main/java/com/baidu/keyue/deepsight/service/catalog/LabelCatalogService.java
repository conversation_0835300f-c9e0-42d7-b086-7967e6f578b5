package com.baidu.keyue.deepsight.service.catalog;

import com.baidu.keyue.deepsight.models.catalog.CatalogDetail;
import com.baidu.keyue.deepsight.models.catalog.DeleteCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.EditCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogResponse;
import com.baidu.keyue.deepsight.models.catalog.MoveCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.NewCatalogRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog;

import java.util.List;
import java.util.Map;

public interface LabelCatalogService {

    /**
     * 新建标签目录
     *
     * @param request
     */
    void createLabelCatalog(NewCatalogRequest request);

    /**
     * 删除标签目录
     *
     * @param request
     */
    void deleteLabelCatalog(DeleteCatalogRequest request);

    /**
     * 更新标签目录名称
     *
     * @param request
     */
    void updateLabelCatalog(EditCatalogRequest request);

    /**
     * 移动标签目录顺序
     *
     * @param request
     */
    void move(MoveCatalogRequest request);

    /**
     * 获取标签列表
     * 详情中包含子级标签目录
     *
     * @param request
     * @return
     */
    ListCatalogResponse list(ListCatalogRequest request);

    /**
     * 构建标签目录层级树
     * 根据 parentID(0) 自顶向下遍历
     *
     * @param catalogMap 标签目录map
     * @param parentId   父ID
     * @return
     */
    List<CatalogDetail> buildCatalogList(Map<Long, List<LabelCatalog>> catalogMap, long parentId);

    /**
     * 取回标签目录下的所有标签ID
     * 包含自身ID
     *
     * @param labelCatalogId 标签目录ID
     * @return
     */
    List<Long> retrieveCatalogIds(Long labelCatalogId);

    /**
     * 校验目录是否存在
     *
     * @param catalogId 标签目录id
     * @return
     */
    LabelCatalog getCatalogDetail(Long catalogId, String tenantId);

    /**
     * 检查标签目录
     *
     * @param tenantId    租户ID
     * @param catalogName 目录名称
     * @return 标签目录信息
     */
    LabelCatalog checkLabelCatalog(String tenantId, String catalogName);

    /**
     * 初始化标签目录
     *
     * @param tenantId
     */
    void initTenantCatalog(String tenantId);
}
