package com.baidu.keyue.deepsight.service.idmapping;

import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.CreateIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.DeleteIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.ListIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.UpdateIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.response.relation.IdMappingDatasetFieldListResponse;
import com.baidu.keyue.deepsight.models.idmapping.response.relation.IdMappingDatasetListResponse;
import com.baidu.keyue.deepsight.models.idmapping.response.relation.IdMappingRelItemResponse;

import java.util.List;
import java.util.Map;

/**
 * @className: IdMappingRelService
 * @description:
 * @author: wangzhongcheng
 * @date: 2025/3/6 14:47
 */
public interface IdMappingRelService {

    /**
     * 创建id映射关系 数据集中那几个字段组成id对
     * @param request 创建id映射关系请求
     */
    void createIdMappingRel(CreateIdMappingRelRequest request);

    /**
     * 更新id映射关系
     * @param request 更新id映射关系请求
     */
    void updateIdMappingRel(UpdateIdMappingRelRequest request);

    /**
     * 删除id映射关系
     * @param request 删除id映射关系请求
     */
    void deleteIdMappingRel(DeleteIdMappingRelRequest request);

    /**
     * 校验是否存在公共ID
     * @return true-存在，false-不存在
     */
    boolean isExistPublicId(Long dataTableId, List<String> enFields);

    /**
     * 获取id对抽取字段list
     * @param request list 请求
     * @return
     */
    BasePageResponse.Page<IdMappingRelItemResponse> listIdMappingRel(ListIdMappingRelRequest request);

    /**
     * 获取ID对抽取可选择数据集
     *
     * @return
     */
    IdMappingDatasetListResponse listIdMappingDataset();

    /**
     * 获取ID对抽取可选择的数据集字段
     *
     * @param dataTableId
     * @return
     */
    IdMappingDatasetFieldListResponse listIdMappingDatasetField(Long dataTableId);

    /**
     * 初始化默认的id-mapping表以及预置的ID对
     *
     * @param tenantId 租户信息
     * @param presetDataTableNameMap 预置数据集名和id的映射
     */
    void initDefaultIdMappingRel(String tenantId, Map<String, Long> presetDataTableNameMap);

    /**
     * 获取id对英文字段出现的个数
     * 此处查了改租户下的所有id对，统计每个字段出现的次数，
     * 一个租户下id对的个数是少量（最多为数据集个数）的加载到内存计算
     * @param tenantId 租户id
     * @return k-英文字段名，v-字段出现次数
     */
    Map<String, Long> getEnFieldCountToIdMappingMap(String tenantId);

    /**
     * 获取id对的英文字段名和中文字段名的对应
     * @param tenantId 租户id
     * @return k-英文字段名，v-英文字段名
     */
    Map<String, String> getEnFieldtoCnFieldMap(String tenantId);

    /**
     * 校验id对抽取
     * @param tenantId 租户id
     */
    void validateIdMappingDateTable(String tenantId);

}
