package com.baidu.keyue.deepsight.service.dail;

import com.baidu.keyue.deepsight.enums.AiobFailTypeEnum;
import com.baidu.keyue.deepsight.models.dial.BusyLocationDistributionResponse;
import com.baidu.keyue.deepsight.models.dial.CallCoreMetricsRequest;
import com.baidu.keyue.deepsight.models.dial.CallCoreMetricsResponse;
import com.baidu.keyue.deepsight.models.dial.CallResultCompositionResponse;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendDetailRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendDetailResponse;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.dial.AlertSettingQueryRequest;
import com.baidu.keyue.deepsight.models.dial.AlertSettingRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendRequest;
import com.baidu.keyue.deepsight.models.dial.ConnectionRateTrendResponse;
import com.baidu.keyue.deepsight.models.dial.CoreMetricsResponse;
import com.baidu.keyue.deepsight.models.dial.LineDetailResponse;
import com.baidu.keyue.deepsight.models.dial.LineInfoResponse;
import com.baidu.keyue.deepsight.models.dial.LineRejectTrendResponse;
import com.baidu.keyue.deepsight.models.dial.RejectReasonResponse;
import com.baidu.keyue.deepsight.models.dial.RobotInfoResponse;
import com.baidu.keyue.deepsight.models.dial.TaskInfoResponse;
import com.baidu.keyue.deepsight.models.dial.ThirtyDayRankingRequest;
import com.baidu.keyue.deepsight.models.dial.ThirtyDayRankingResponse;
import com.baidu.keyue.deepsight.models.dial.UnconnectedHeatmapResponse;

import java.util.List;

/**
 * @ClassName DialMetricService
 * @Description 呼通分析接口
 * <AUTHOR>
 * @Date 2025/6/30 10:39 AM
 */
public interface DialMetricService {
    /**
     * 大盘关键指标
     * 昨日接通率、拨打次数、接通次数、未接通次数
     * 近30天告警次数
     *
     * @return
     */
    CoreMetricsResponse getCoreMetrics(String tenantId);

    /**
     * 呼通分析-接通率趋势
     *
     * @param request  请求体
     * @param tenantId 租户ID
     * @return
     */
    List<ConnectionRateTrendResponse> getConnectionRateTrend(ConnectionRateTrendRequest request, String tenantId);

    /**
     * 告警设置
     * 针对号线id、任务id、机器人id配置告警信息
     *
     * @param request  请求题
     * @param tenantId 租户ID
     * @return 配置ID
     */
    Integer configAlertSetting(AlertSettingRequest request, String tenantId);

    /**
     * 查询呼通率告警记录
     *
     * @param request  请求体
     * @param tenantId 租户ID
     * @return 告警记录
     */
    AlertSettingRequest queryAlertSetting(AlertSettingQueryRequest request, String tenantId);

    /**
     * 分页查询呼通排行榜
     * 针对号线、任务、机器人，查询30天内的聚合统计排行榜
     * 近30天排行榜数据不包含当前天
     * 近12小时接通率趋势不包含当前小时
     *
     * @param request
     * @param tenantId
     * @return
     */
    BasePageResponse.Page<ThirtyDayRankingResponse> getThirtyDayRanking(ThirtyDayRankingRequest request, String tenantId);

    /**
     * 呼通详情-核心指标
     *
     * @param request 请求体
     * @return
     */
    CallCoreMetricsResponse getCallDetailCoreMetrics(CallCoreMetricsRequest request);

    /**
     * 呼通详情-机器人列表
     *
     * @param request 请求体
     * @return
     */
    List<RobotInfoResponse> getRobotList(CallCoreMetricsRequest request);

    /**
     * 呼通详情-任务列表
     *
     * @param request 请求体
     * @return
     */
    List<TaskInfoResponse> getTaskList(CallCoreMetricsRequest request);

    /**
     * 呼通详情-号线列表
     *
     * @param request 请求体
     * @return
     */
    List<LineInfoResponse> getLineList(CallCoreMetricsRequest request);

    /**
     * 呼通详情-接通率趋势
     * 针对号线、任务、机器人，查询其24小时/30天内，每天/每小时的拨打次数和接通率
     *
     * @param request 请求体
     * @return
     */
    List<ConnectionRateTrendDetailResponse> getConnectionRateTrendDetail(ConnectionRateTrendDetailRequest request);

    /**
     * 呼通详情-呼通结果构成
     * 针对号线、任务、机器人，查询其呼通结果构成数据
     *
     * @param request 请求体
     * @return
     */
    List<CallResultCompositionResponse> getCallResultComposition(CallCoreMetricsRequest request);

    /**
     * 呼通详情- 查询各未接通原因占比
     * 针对号线、任务、机器人，查询导致未接通原因占比
     *
     * @param request 请求体, failType 未接通原因
     * @return
     */
    List<RejectReasonResponse> getRejectReasons(CallCoreMetricsRequest request, AiobFailTypeEnum failType);


    /**
     * 呼通详情-用户忙的通话归属地分布
     * 针对号线、任务、机器人，查询用户忙的通话归属地分布
     *
     * @param request 请求体
     * @return
     */
    BusyLocationDistributionResponse getBusyLocationDistribution(CallCoreMetricsRequest request);

    /**
     * 呼通详情-未接通时间热力图
     * 针对号线、任务、机器人，查询未接通时间热力图数据
     *
     * @param request 请求体
     * @return
     */
     UnconnectedHeatmapResponse getUnconnectedHeatmap(CallCoreMetricsRequest request);

    /**
     * 呼通详情-号线原因未接通趋势图
     * 针对号线、任务、机器人，查询号线原因未接通趋势图数据
     *
     * @param request 请求体
     * @return
     */
     List<LineRejectTrendResponse> getLineRejectTrend(CallCoreMetricsRequest request);

    /**
     * 呼通详情-号线详情
     * 查询号线详情
     * @param request 请求体
     * @return
     */
    LineDetailResponse getLineDetail(CallCoreMetricsRequest request);
}
