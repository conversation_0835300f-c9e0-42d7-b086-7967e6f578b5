package com.baidu.keyue.deepsight.service.tenant.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.utils.FileUtil;
import com.baidu.keyue.deepsight.utils.ThreadPoolUtils;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * @ClassName ApplicationRunnerImpl
 * @Description 应用初始化--执行租户升级、Doris模板表修改或创建、MySQL的一次性操作
 * <AUTHOR>
 * @Date 2025/3/14 7:53 PM
 */
@Slf4j
@Service
public class TenantUpgradeRunnerImpl implements ApplicationRunner {

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private DorisService dorisService;

    @Resource
    private TenantInfoService tenantInfoService;

    @Value("${app-version}")
    private Integer version;
    
    @Value("${switch.tenantUpgradeRunner}")
    private Boolean tenantUpgradeRunner;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 系统启动后自行升级租户：先升级公共部分，再查询所有待升级租户自行升级
        if (!Objects.equals(tenantUpgradeRunner, true)) {
            log.info("租户自动升级未开启，跳过");
            return;
        }
        // 多实例单执行，加锁
        RLock lock = redissonClient.getLock("TENANT_AUTO_UPGRADE");
        if (lock.tryLock()) {
            try {
                log.info("租户自动升级开始");
                // 新版本无全局表升级
                upgradeGlobal();
                log.info("tenant global upgrade after system start over");
                ThreadPoolUtils.getSingleThreadPool().execute(() -> {
                    List<TenantInfo> tenantInfos = tenantInfoService.getUpgradeTenant(version);
                    if (CollUtil.isNotEmpty(tenantInfos)) {
                        for (TenantInfo info : tenantInfos) {
                            upgradeTenant(info);
                        }
                    }
                    log.info("tenant upgrade after system start over, count:{}", tenantInfos.size());
                });
            } finally {
                if (lock.isLocked()) {
                    lock.unlock();
                }
            }
        } else {
            log.info("租户自动升级跳过");
        }
    }

    /**
     * 升级租户
     * @param info
     */
    public void upgradeTenant(TenantInfo info) {
        try {
            log.info("{} 自动升级开始", info.getTenantid());
            TenantDTO tenantDTO = new TenantDTO();
            tenantDTO.setTenantInfo(info);
            tenantDTO.setTenantId(info.getTenantid());
            tenantDTO.setType(info.getTenantSource());
            UserAuthInfo userAuthInfo = new UserAuthInfo();
            String userId = info.getUserId();
            userAuthInfo.setUserId(userId == null ? null : Long.parseLong(userId));
            userAuthInfo.setAccountId(info.getAccountid());
            userAuthInfo.setUserName(info.getUsername());
            tenantDTO.setAuthInfo(userAuthInfo);
            tenantInfoService.initOrUpgradeTenant(tenantDTO);
            log.info("{} 自动升级成功", info.getTenantid());
        } catch (Exception exception) {
            log.error("{} 自动升级失败, ", info.getTenantid(), exception);
        }
    }

    /**
     * 全局升级操作
     * Doris模板表创建、修改
     * 字段信息表字段名修改等只需一次性操作的升级
     */
    public void upgradeGlobal() throws IOException {
        // Doris全局表操作：主要是模板表
        String fileAsString = FileUtil.readFileAsString("upgrade/v1.1.3_global.sql");
        if (StrUtil.isBlank(fileAsString)) {
            return;
        }
        String[] split = fileAsString.split(";");
        for (String sql : split) {
            dorisService.operationSchema(sql + ";");
        }
    }
}
