package com.baidu.keyue.deepsight.service.user;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class UserService {
    @Autowired
    private DorisService dorisService;

    public Map<String, Object> queryUserByOneId(String id, String tenantId) {
        String userTableName = TenantUtils.generateMockUserTableName(tenantId);
        String sql = String.format("SELECT * FROM %s WHERE %s = '%s' limit 1",
                userTableName, Constants.TABLE_USER_ONE_ID, StringEscapeUtils.escapeSql(id));
        List<Map<String, Object>> recordList = Lists.newArrayList();
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("UserService.queryUserByOneId selectList error", e);
        }
        if (recordList.isEmpty()) {
            return new HashMap<>();
        }
        return recordList.get(0);
    }

    public String getUserMobileByOneId(String tenantId, String oneId) {
        Map<String, Object> userData = queryUserByOneId(oneId, tenantId);
        Object mobile = userData.get("mobile");
        if (Objects.isNull(mobile)) {
            return null;
        }
        return String.valueOf(mobile);
    }

    public Map<String, Object> queryUserById(String id, String tenantId) {
        String userTableName = TenantUtils.generateMockUserTableName(tenantId);
        String sql = String.format("SELECT * FROM %s WHERE user_id = '%s' limit 1", userTableName, StringEscapeUtils.escapeSql(id));
        List<Map<String, Object>> recordList = Lists.newArrayList();
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("UserService.queryUserById selectList error", e);
        }
        if (recordList.isEmpty()) {
            return new HashMap<>();
        }
        return recordList.get(0);
    }

    public String getUserMobileByUserId(String tenantId, String id) {
        Map<String, Object> userData = queryUserById(id, tenantId);
        Object mobile = userData.get("mobile");
        if (Objects.isNull(mobile)) {
            return null;
        }
        return String.valueOf(mobile);
    }

    public String getUserOneIdByUserId(String tenantId, String id) {
        Map<String, Object> userData = queryUserById(id, tenantId);
        Object oneId = userData.get(Constants.TABLE_USER_ONE_ID);
        if (Objects.isNull(oneId)) {
            return null;
        }
        return String.valueOf(oneId);
    }

    public List<Map<String, Object>> queryMockUserRows(String oneId, String tenantId) {
        String userTableName = TenantUtils.generateMockUserTableName(tenantId);
        String sql = String.format("SELECT * FROM %s WHERE oneId = '%s' order by deepsight_datetime desc", userTableName, oneId);
        try {
            return dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("UserService.queryMockUserRows selectList error", e);
        }

        return Lists.newArrayList();
    }

    public Map<String, Object> queryUserProfileRow(String oneId, String tenantId) {
        String userTableName = TenantUtils.generateUserProfileTableName(tenantId);
        String sql = String.format("SELECT * FROM %s WHERE oneId = '%s' limit 1", userTableName, oneId);
        List<Map<String, Object>> recordList = Lists.newArrayList();
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("UserService.queryUserProfileRow selectList error", e);
        }
        if (recordList.isEmpty()) {
            return new HashMap<>();
        }
        return recordList.get(0);
    }
}
