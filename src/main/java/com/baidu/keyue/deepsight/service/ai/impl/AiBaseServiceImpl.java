package com.baidu.keyue.deepsight.service.ai.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.datamanage.dto.FieldEnumMappingDTO;
import com.baidu.keyue.deepsight.models.datamanage.dto.FieldMappingDto;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableFieldInfoDTO;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldAiMappingRequest;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.service.ai.AiBaseService;
import com.baidu.keyue.deepsight.utils.FileUtil;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName AiBaseServiceImpl
 * @Description Ai服务实现类
 * <AUTHOR>
 * @Date 2025/2/25 5:19 PM
 */
@Slf4j
@Service
public class AiBaseServiceImpl implements AiBaseService {

    @Value("${ai.qianfan.url}")
    private String qianFanUrl;

    @Value("${ai.qianfan.authorization}")
    private String qianFanToken;

    @Value("${ai.template.fieldMapping}")
    private String fieldMappingTemplate;

    @Value("${ai.template.genField}")
    private String genFieldTemplate;

    @Value("${ai.template.genEnum}")
    private String genEnumTemplate;

    private final Map<String, String> templateMap = new HashMap<>();

    @PostConstruct
    public void init() {
        try {
            // 初始化模板信息
            String templateFieldMapping = FileUtil.readFileAsString(fieldMappingTemplate);
            String templateGenEnum = FileUtil.readFileAsString(genEnumTemplate);
            String templateGenFiled = FileUtil.readFileAsString(genFieldTemplate);
            if (StrUtil.isNotBlank(templateFieldMapping)) {
                templateMap.put(fieldMappingTemplate, templateFieldMapping);
            }
            if (StrUtil.isNotBlank(templateGenEnum)) {
                templateMap.put(genEnumTemplate, templateGenEnum);
            }
            if (StrUtil.isNotBlank(templateGenFiled)) {
                templateMap.put(genFieldTemplate, templateGenFiled);
            }
        } catch (IOException e) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "AI解析异常");
        }
    }


    @Override
    public String sendPost(String reqStr, String templatePath, String level) {
        String aiReqTemplate = templateMap.get(templatePath);
        Assert.notBlank(aiReqTemplate, () -> new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "AI解析异常"));
        reqStr = String.format(aiReqTemplate, reqStr);
        FieldAiMappingRequest request = new FieldAiMappingRequest(new FieldAiMappingRequest.Messages(reqStr));
        HttpRequest post = HttpUtil.createPost(qianFanUrl);
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Authorization", qianFanToken);
        post.addHeaders(headers);
        try {
            post.body(JsonUtils.toJson(request));
        } catch (IOException e) {
            log.error("请求AI异常JOSN序列化异常");
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "请求AI异常");
        }
        // 请求AI大模型
        try (HttpResponse response = post.execute()) {
            if (response == null || response.getStatus() != 200) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "请求AI异常");
            }
            String body = response.body();
            JsonNode jsonNode = JsonUtils.toJsonNode(body);
            String textValue = jsonNode.get("choices").get(0).get("message").get("content").textValue();
            String mappingJson = textValue.replaceFirst("json", "").replaceAll("```", "");
            // 特殊层级获取
            if (StrUtil.isNotBlank(level)) {
                JsonNode node = JsonUtils.toJsonNode(mappingJson);
                for (String field : level.split("\\.")) {
                    node = node.get(field);
                }
                mappingJson = node.toString();
            }
            return mappingJson;
        } catch (Exception e) {
            log.error("AI请求异常", e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "请求AI异常");
        }
    }

    @Override
    public List<FieldMappingDto> fieldMapping(String reqStr) {
        String sendPost = sendPost(reqStr, fieldMappingTemplate, null);
        return JsonUtils.readType(sendPost, new TypeReference<>() {
        });
    }

    @Override
    public List<FieldEnumMappingDTO> genFieldEnum(String reqStr, String level) {
        String sendPost = sendPost(reqStr, genEnumTemplate, level);
        return JsonUtils.readType(sendPost, new TypeReference<>() {
        });
    }

    @Override
    public List<TableFieldInfoDTO> genFieldInfo(String promptContent, String level) {
        String sendPost = sendPost(promptContent, genFieldTemplate, level);
        return JsonUtils.readType(sendPost, new TypeReference<>() {
        });
    }
}
