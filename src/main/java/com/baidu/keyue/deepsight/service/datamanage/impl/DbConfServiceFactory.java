package com.baidu.keyue.deepsight.service.datamanage.impl;

import com.baidu.keyue.deepsight.service.datamanage.DbConfService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @className DbConfFactory
 * @description Db工厂类
 * @date 2025/3/6 19:54
 */
@Component
public class DbConfServiceFactory {


    private final Map<String, DbConfService> dbConfServiceMap;

    @Autowired
    public DbConfServiceFactory(Map<String, DbConfService> dbConfServices) {
        this.dbConfServiceMap = dbConfServices;
    }


    public DbConfService getDbService(String type) {
        return dbConfServiceMap.get(type);
    }
}
