package com.baidu.keyue.deepsight.service.datamanage;

import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.request.CreateTableSchemaRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.DeleteTableRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigQueryRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigSaveRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetLLMGenFieldEnumRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetLLMGenTableInfoRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableContentListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableDetailRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableFieldRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigQueryRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableUserInfoRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.TableRecordRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.UpdateTableFieldConfigRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.UpdateTableSchemaRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.CreateTableResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldShowConfigResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.LLMGenFieldEnumResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.LLMGenFieldResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableDetailResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableFieldDetailResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import org.springframework.http.codec.ServerSentEvent;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @className DataTableManageService
 * @description 数据表管理接口
 * @date 2024/12/27 15:06
 */
public interface DataTableManageService {

    /**
     * 创建数据表
     */
    CreateTableResponse createTable(CreateTableSchemaRequest request);

    /**
     * 删除数据表
     */
    void deleteTable(DeleteTableRequest request);

    /**
     * 保存数据表
     */
    CreateTableResponse saveTable(CreateTableSchemaRequest request);

    /**
     * 更新数据表
     */
    void updateTable(UpdateTableSchemaRequest request);

    /**
     * 获取数据表列表
     */
    BasePageResponse.Page<TableDetailResponse> getDataTableList(GetTableListRequest request);

    /**
     * 获取数据表详情
     */
    TableDetailResponse getDataTableDetail(GetTableDetailRequest request);

    /**
     * 获取数据表字段详情
     */
    BasePageResponse.Page<TableFieldDetailResponse> getTableFieldList(GetTableFieldRequest request, Boolean includeBaidu);

    /**
     * 获取数据表内容筛选属性
     */
    List<DatasetPropertiesResult> getTableContentFilterProperties(Long dataTableId, Boolean includeBaidu);
    List<DatasetPropertiesResult> getTableProperties();


    BasePageResponse.Page<Map<String, String>> getTableUserInfo( Boolean includeBaidu,
                                                                 GetTableUserInfoRequest getTableFieldRequest);

    /**
     * 获取数据表可见字段
     */
    List<VisibleFieldResponse> getVisibleFields(Long dataTableId, Boolean includeBaidu);

    /**
     * 获取数据集可见字段(注意越权)
     *
     * @param tableEnName  数据集英文名
     * @param tenantId     租户ID
     * @param includeBaidu 是否包含百度
     * @return 数据集字段信息
     */
    List<VisibleFieldResponse> getVisibleFields(String tableEnName, String tenantId, Boolean includeBaidu);

    /**
     * 获取数据表可见字段
     */
    List<VisibleFieldResponse> getVisibleFields(String tableEnName, Boolean includeBaidu);

    /**
     * 获取数据表数据记录
     */
    BasePageResponse.Page<Map<String, String>> getTableContent(GetTableContentListRequest request, Boolean includeBaidu);

    /**
     * 更新数据字段高级配置
     */
    void updateTableFieldConfig(UpdateTableFieldConfigRequest request);


    /**
     * 数据记录操作
     */
    void opTableData(TableRecordRequest request);

    /**
     * 大模型生成字段枚举
     */
    LLMGenFieldEnumResponse llmGenFieldEnum(GetLLMGenFieldEnumRequest request);

    /**
     * 大模型生成字段信息
     */
    Flux<ServerSentEvent<String>> llmGenFieldInfo(GetLLMGenTableInfoRequest request);

    LLMGenFieldResponse llmGenFieldInfoV1(GetLLMGenTableInfoRequest request);


    /**
     * 获取数据表字段元数据信息
     */
    List<TableFieldMetaInfo> queryTableFieldsMetaInfo(Long dataTableId);

    /**
     * 校验数据集是否存在
     *
     * @param dataTableId
     * @return
     */
    DataTableInfo validDataTableByTenantId(Long dataTableId);

    /**
     * 校验数据集是否存在
     *
     * @param tableEnName 表名：Doris or ES 表名
     * @return 表信息
     */
    DataTableInfo validDataTableByTableName(String tableEnName);

    /**
     * 根据表名校验数据集是否存在
     * 注意越权
     *
     * @param tableEnName 表名
     * @param tenantId    租户ID
     * @return 数据集信息
     */
    DataTableInfo validDataTableByTableName(String tableEnName, String tenantId);

    /**
     * 根据表名查询表详情
     *
     * @param tableName 表名
     * @return
     */
    DataTableInfo getTableDetailWithTableName(String tableName);

    /**
     * 根据表ID和字段英文名查询字段信息
     *
     * @param dataTableId 表ID
     * @param enName      字段英文名
     * @return
     */
    TableFieldMetaInfo queryTableFieldMetaInfo(Long dataTableId, String enName);

    /**
     * 获取表敏感字段
     *
     * @param dataTableId 表ID
     * @return
     */
    List<String> getTableEncryFields(Long dataTableId);

    /**
     * 根据字段标记查询字段
     *
     * @param dataTableId 表ID
     * @param tag         标记
     * @return 字段信息
     */
    TableFieldMetaInfo queryTableFieldByTag(Long dataTableId, TableFieldTagEnum tag);

    /**
     * 数据集显示列字段配置获取
     * 如果没有人为更新过配置，则返回可见字段排序后前X个字段名以及数据集ID
     *
     * @param tenantId 租户ID
     * @param request  配置获取请求
     * @return 数据集显示列配置
     */
    FieldShowConfigResponse getFieldShowConfig(String tenantId, FieldShowConfigQueryRequest request);

    /**
     * 根据主键ID添加或更新数据集显示列配置信息
     *
     * @param tenantId 租户ID
     * @param request  添加或更新请求体
     * @param userName 操作者昵称
     * @return 数据集显示列配置
     */
    FieldShowConfigResponse saveOrUpdateFieldShowConfig(String tenantId, FieldShowConfigSaveRequest request, String userName);
}
