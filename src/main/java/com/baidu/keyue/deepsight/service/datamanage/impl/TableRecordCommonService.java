package com.baidu.keyue.deepsight.service.datamanage.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.ESToJavaTypeMapping;
import com.baidu.keyue.deepsight.enums.MysqlToJavaTypeMapping;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.aiob.entity.DesSecretKey;
import com.baidu.keyue.deepsight.mysqldb.aiob.entity.DesSecretKeyCriteria;
import com.baidu.keyue.deepsight.mysqldb.aiob.entity.DesSystemSecretKey;
import com.baidu.keyue.deepsight.mysqldb.aiob.entity.DesSystemSecretKeyCriteria;
import com.baidu.keyue.deepsight.mysqldb.aiob.mapper.DesSecretKeyMapper;
import com.baidu.keyue.deepsight.mysqldb.aiob.mapper.DesSystemSecretKeyMapper;
import com.baidu.keyue.deepsight.mysqldb.entity.AccessToken;
import com.baidu.keyue.deepsight.mysqldb.entity.AccessTokenCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldEncryConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldEncryConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.AccessTokenMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.FieldEncryConfigMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.gson.JsonElement;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.baidu.keyue.deepsight.utils.AESUtils.encrypt;

/**
 * <AUTHOR>
 * @className TableItemCommonService
 * @description 表数据通用处理类
 * @date 2025/1/9 15:02
 */
@Slf4j
@Service
public class TableRecordCommonService {


    @Autowired
    private DataTableManageService dataTableManageService;

    @Autowired
    private FieldEncryConfigMapper fieldEncryConfigMapper;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    @Autowired
    private DesSecretKeyMapper desSecretKeyMapper;

    @Autowired
    private DesSystemSecretKeyMapper desSystemSecretKeyMapper;

    @Autowired
    private AccessTokenMapper tokenMapper;

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();

    /**
     * 值不允许为空字符串的 Type类型
     */
    private final Set<String> noEmptyTypeSet = new HashSet<>(Arrays.asList(Constants.CONSTANT_NUMBER, Constants.CONSTANT_JSON));

    /**
     * 值不允许为空字符串的 dataType类型
     */
    private final Set<String> dateTypeSet = new HashSet<>(Arrays.asList("date", "time", "datetime"));

    /**
     * 获取表字段必填项
     *
     * @param dataTableId
     */
    @Cacheable(value = "requiredFields", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public List<String> getRequiredFields(Long dataTableId) {
        // 获取表元数据信息
        List<TableFieldMetaInfo> fieldMetaInfos = dataTableManageService.queryTableFieldsMetaInfo(dataTableId);
        if (CollectionUtils.isEmpty(fieldMetaInfos)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "请检查dataTableId");
        }
        List<String> fieldMetaInfoList = fieldMetaInfos.stream()
                .filter(metaInfo -> metaInfo.getIsRequired())
                .map(metaInfo -> metaInfo.getEnField()).toList();
        return fieldMetaInfoList;
    }

    /**
     * 获取表加密字段&密钥
     *
     * @param dataTableId
     */
    @Cacheable(value = "encryptFields", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public Map<String, String> getEncryptFields(Long dataTableId) {
        // 查询敏感字段加密信息
        FieldEncryConfigCriteria fieldEncryConfigCriteria = new FieldEncryConfigCriteria();
        FieldEncryConfigCriteria.Criteria criteria = fieldEncryConfigCriteria.createCriteria();
        criteria.andDataTableIdEqualTo(Long.toString(dataTableId));
        List<FieldEncryConfig> fieldEncryConfigs = fieldEncryConfigMapper.selectByExample(fieldEncryConfigCriteria);
        Map<String, String> fieldEncryptInfo = fieldEncryConfigs.stream()
                .collect(Collectors.toMap(FieldEncryConfig::getEnField, FieldEncryConfig::getSecretKey, (k1, k2) -> k1));
        return fieldEncryptInfo;
    }

    /**
     * 查询表对应类型的字段
     */
    @Cacheable(value = "fieldByFieldTag", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public TableFieldMetaInfo queryTableFieldByTag(Long dataTableId, TableFieldTagEnum tag) {
        TableFieldMetaInfoCriteria criteria = new TableFieldMetaInfoCriteria();
        criteria.createCriteria().andDataTableIdEqualTo(dataTableId).andFieldTagEqualTo(tag.getCode());
        List<TableFieldMetaInfo> info = tableFieldMetaInfoMapper.selectByExample(criteria);
        return CollectionUtils.isEmpty(info) ? null : info.get(0);
    }

    /**
     * 获取表字段必填项
     *
     * @param tableName
     */
    @Cacheable(value = "tableByEnName", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator",
            unless = "#result == null")
    public DataTableInfo getTableByTableName(String tableName) {
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        DataTableInfoCriteria.Criteria criteria = dataTableInfoCriteria.createCriteria();
        criteria.andTableNameEqualTo(tableName).andIsDelEqualTo(DelEnum.NOT_DELETED.getCode());
        List<DataTableInfo> tableInfos = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
        if (CollectionUtils.isEmpty(tableInfos)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "没有对应的表");
        }
        return tableInfos.get(0);
    }

    /**
     * 获取表字段必填项
     *
     * @param tableEnName doris表名
     * @param enField     字段名
     */
    @Cacheable(value = "fieldDetailByName", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator",
            unless = "#result == null")
    public TableFieldMetaInfo getFieldDetailByName(String tableEnName, String enField) {
        TableFieldMetaInfoCriteria metaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = metaInfoCriteria.createCriteria();
        criteria.andTableEnNameEqualTo(tableEnName);
        criteria.andEnFieldEqualTo(enField);
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper.selectByExample(metaInfoCriteria);
        return CollUtil.isNotEmpty(tableFieldMetaInfos) ? tableFieldMetaInfos.get(0) : null;
    }

    /**
     * 获取外呼加密密钥
     *
     * @param secretType
     */
    @Cacheable(value = "aiobEncryptKey", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public String getAiobSecretKey(Integer secretType, Long tenantId, String secretId) {
        String secretKey = null;
        if (null == secretType || null == tenantId) {
            log.warn("aiob query secretKey error.secretType is {},tenantId is {}", secretKey, tenantId);
            return secretKey;
        }
        // secretType为1时，查des_secret_key表。secretType为2或4时，查des_system_secret_key表
        if (1 == secretType) {
            if (null == secretId) {
                log.warn("aiob query secretKey error.secretId is null");
                return secretKey;
            }
            DesSecretKeyCriteria desSecretKeyCriteria = new DesSecretKeyCriteria();
            DesSecretKeyCriteria.Criteria criteria = desSecretKeyCriteria.createCriteria();
            criteria.andIdEqualTo((Long.valueOf(secretId)));
            List<DesSecretKey> desSecretKeys = desSecretKeyMapper.selectByExample(desSecretKeyCriteria);
            return CollectionUtils.isEmpty(desSecretKeys) ? null : desSecretKeys.get(0).getSecretKey();
        }
        if (2 == secretType || 4 == secretType) {
            DesSystemSecretKeyCriteria desSystemSecretKeyCriteria = new DesSystemSecretKeyCriteria();
            DesSystemSecretKeyCriteria.Criteria criteria = desSystemSecretKeyCriteria.createCriteria();
            criteria.andTenantIdEqualTo(tenantId);
            List<DesSystemSecretKey> desSecretKeys = desSystemSecretKeyMapper.selectByExample(desSystemSecretKeyCriteria);
            return CollectionUtils.isEmpty(desSecretKeys) ? null : desSecretKeys.get(0).getSystemSecretKey();
        }
        return secretKey;
    }

    /**
     * 获取表加密字段&密钥
     *
     * @param dataTableId
     */
    @Cacheable(value = "dataTableInfo", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator",
            unless = "#result == null")
    public DataTableInfo getDataTableDetail(Long dataTableId) {
        DataTableInfo info = dataTableInfoMapper.selectByPrimaryKey(dataTableId);
        return info;
    }

    /**
     * 获取租户对应的表
     *
     * @param tenantId
     */
    @Cacheable(value = "tenantTableInfo", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public DataTableInfo getDataTableDetail(String tenantId) {
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        DataTableInfoCriteria.Criteria criteria = dataTableInfoCriteria.createCriteria();
        criteria.andTenantidEqualTo(tenantId);
        criteria.andTableNameEqualTo(TenantUtils.generateAiobSessionTableName(tenantId));
        List<DataTableInfo> infos = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
        return CollectionUtils.isNotEmpty(infos) ? infos.get(0) : null;
    }

    /**
     * 根据表名获取表信息详情
     *
     * @param tableName 表名
     * @param tenantId  租户ID
     */
    @Cacheable(value = "tableDetailInfo", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public DataTableInfo getDataTableDetail(String tableName, String tenantId) {
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        DataTableInfoCriteria.Criteria criteria = dataTableInfoCriteria.createCriteria();
        criteria.andTenantidEqualTo(tenantId);
        criteria.andTableNameEqualTo(tableName);
        List<DataTableInfo> infos = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
        return CollectionUtils.isNotEmpty(infos) ? infos.get(0) : null;
    }

    /**
     * 加密数据
     */
    public Map<String, Object> encryptItem(Map<String, Object> itemMap,
                                           Map<String, String> fieldEncryptInfo) {
        if (MapUtils.isEmpty(fieldEncryptInfo)) {
            return itemMap;
        }
        String field;
        String password;
        String value;
        for (Map.Entry<String, String> entry : fieldEncryptInfo.entrySet()) {
            field = entry.getKey();
            password = entry.getValue();
            if (null == itemMap.get(field)) {
                continue;
            }
            value = itemMap.get(field).toString();
            String encryptStr = encrypt(value, password);
            itemMap.put(field, encryptStr);
        }
        return itemMap;
    }

    /**
     * 获取表字段类型
     *
     * @param dataTableId
     */
    @Cacheable(value = "fieldsType", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public Map<String, String> getFieldsType(Long dataTableId) {
        // 获取表元数据信息
        List<TableFieldMetaInfo> fieldMetaInfos = dataTableManageService.queryTableFieldsMetaInfo(dataTableId);
        if (CollectionUtils.isEmpty(fieldMetaInfos)) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "请检查dataTableId");
        }
        // todo具体类型判断
        Map<String, String> fieldMetaInfoList = fieldMetaInfos.stream()
                .collect(Collectors.toMap(TableFieldMetaInfo::getEnField, TableFieldMetaInfo::getDataType,
                        (k1, k2) -> k1));
        return fieldMetaInfoList;
    }

    /**
     * 数据校验
     */
    public List<String> recordCheck(Map<String, Object> itemMap,
                                    List<String> requiredFields,
                                    Map<String, String> fieldsType,
                                    String dbType) {
        // 字段检查，去除冗余表结构冗余字段
        Set<String> fieldKeys = fieldsType.keySet();
        List<String> itemKeys = new ArrayList<>();
        for (String key : itemMap.keySet()) {
            if (!fieldKeys.contains(key)) {
                itemKeys.add(key);
            }
        }
        if (CollectionUtils.isNotEmpty(itemKeys)) {
            itemKeys.forEach(itemMap::remove);
        }
        Set<String> mapKeys = new HashSet<>(itemMap.keySet());
        List<String> errorFields = Lists.newArrayList();
        // 字段必填检查
        String field;
        Object value;
        for (int i = 0; i < requiredFields.size(); i++) {
            field = requiredFields.get(i);
            // 判读字段是否存在
            if (!mapKeys.contains(field)) {
                errorFields.add(field);
                continue;
            }
            // 判断字段为空
            value = itemMap.get(field);
            if (null == value) {
                errorFields.add(field);
                continue;
            }
            // 如果是Doris数据，特殊类型必填字段判断
            if (Objects.equals(DbTypeEnum.DORIS_TYPE.getDbType(), dbType)) {
                String dataType = fieldsType.get(field);
                MysqlToJavaTypeMapping mapping = MysqlToJavaTypeMapping.getJavaType(dataType);
                if (null == mapping) {
                    errorFields.add(field);
                } else {
                    // 数字类型、JSON、时间类型使用空字符串会报错
                    if ((noEmptyTypeSet.contains(mapping.getType()) || dateTypeSet.contains(dataType))
                            && StrUtil.isBlank(value.toString())) {
                        errorFields.add(field);
                    }
                }
            }
        }
        // 字段类型校验
        if (MapUtils.isNotEmpty(fieldsType)) {
            for (Map.Entry<String, String> entry : fieldsType.entrySet()) {
                field = entry.getKey();
                if (!mapKeys.contains(field)) {
                    continue;
                }
                // 判断字段是否为空值，空值字段不判断类型
                value = itemMap.get(field);
                if (null == value) {
                    itemMap.remove(field);
                    continue;
                }
                // json类型判断
                if (entry.getValue().equals("json") && StringUtils.isEmpty(itemMap.get(field).toString())) {
                    itemMap.put(field, new JSONObject());
                    value = itemMap.get(field);
                }
                // 数字类型、JSON、时间类型值为空字符串的直接移除
                String dataType = fieldsType.get(field);
                if (DbTypeEnum.DORIS_TYPE.getDbType().equals(dbType)) {
                    MysqlToJavaTypeMapping mapping = MysqlToJavaTypeMapping.getJavaType(dataType);
                    if ((noEmptyTypeSet.contains(mapping.getType()) || dateTypeSet.contains(dataType))
                            && StrUtil.isBlank(value.toString())) {
                        itemMap.remove(field);
                        continue;
                    }
                    switchField(field, value, entry.getValue(), errorFields);
                } else {
                    switchEsField(field, value, entry.getValue(), errorFields);
                }
            }
        }
        if (errorFields.size() > 0) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR,
                    " data field check fail:" + errorFields);
        }
        return errorFields;


    }

    /**
     * es字段类型判断
     */
    private static void switchEsField(String field, Object value, String dataType, List<String> errorFields) {
        ESToJavaTypeMapping mapping = ESToJavaTypeMapping.getJavaType(dataType);
        if (null == mapping) {
            errorFields.add(field);
            return;
        }
        // 时间类型
        if (ESToJavaTypeMapping.DATE.getDataType().equals(dataType)) {
            // 时间类型，尝试转换，避免再BSC失败无感知
            try {
                DateTime parse = DateUtil.parse(value.toString());
                if (parse == null) {
                    errorFields.add(field);
                }
                return;
            } catch (Exception e) {
                log.error("时间类型解析异常，阻断导入数据 value:{}", value.getClass());
                errorFields.add(field);
            }
        }
        String type = mapping.getType();
        switch (type) {
            // 数字类型
            case Constants.CONSTANT_NUMBER:
                // 如果是日期：支持常见日期格式&&13位时间戳
                if (Objects.equals("date", dataType)) {
                    try {
                        DateUtil.parse(value.toString());
                    } catch (Exception e) {
                        errorFields.add(field);
                    }
                } else if (!(value instanceof Integer || value instanceof Long || value instanceof Float
                        || value instanceof Double)) {
                    errorFields.add(field);
                }
                break;
            case Constants.CONSTANT_STRING:
                if (!(value instanceof String)) {
                    errorFields.add(field);
                }
                break;
            case Constants.CONSTANT_BOOLEAN:
                if (!(value instanceof Boolean)) {
                    errorFields.add(field);
                }
                break;
            case Constants.CONSTANT_LIST_STRING:
                if (value instanceof List) {
                    List<Object> a = (List<Object>) value;
                    if (CollectionUtils.isEmpty(a)) {
                        break;
                    }
                    Object m = a.get(0);
                    if (!(m instanceof String)) {
                        errorFields.add(field);
                    }
                } else {
                    errorFields.add(field);
                }
                break;
            default:
                errorFields.add(field + " type invalid");
        }

    }

    /**
     * 字段类型判断
     */
    public void switchField(String field, Object value, String dataType, List<String> errorFields) {
        MysqlToJavaTypeMapping mapping = MysqlToJavaTypeMapping.getJavaType(dataType);
        if (null == mapping) {
            errorFields.add(field);
            return;
        }
        String type = mapping.getType();
        switch (type) {
            // 数字类型
            case Constants.CONSTANT_NUMBER:
                if (!(value instanceof Integer || value instanceof Long || value instanceof Float
                        || value instanceof Double)) {
                    errorFields.add(field);
                }
                break;
            case Constants.CONSTANT_STRING:
                if (!(value instanceof String)) {
                    errorFields.add(field);
                }
                // 时间类型，尝试转换，避免再BSC失败无感知
                if (dateTypeSet.contains(dataType)) {
                    try {
                        DateTime parse = DateUtil.parse(value.toString());
                        if (parse == null) {
                            errorFields.add(field);
                        }
                    } catch (Exception e) {
                        log.error("时间类型解析异常，阻断导入数据 value:{}", value.getClass());
                        errorFields.add(field);
                    }
                }
                // varchar && char长度校验
                if ((Objects.equals("varchar", dataType) || Objects.equals("char", dataType))
                        && value.toString().length() > 255) {
                    errorFields.add(field);
                    break;
                }
                break;
            case Constants.CONSTANT_BOOLEAN:
                if (!(value instanceof Boolean)) {
                    errorFields.add(field);
                }
                break;
            case Constants.CONSTANT_JSON:
                if (!isJsonOrArrayJson(value)) {
                    errorFields.add(field);
                }
                break;
            case Constants.CONSTANT_ARRAY:
                // 常规数组[] 以及List类型
                if (!(value.getClass().isArray() || value instanceof List<?>)) {
                    errorFields.add(field);
                }
                break;
            default:
                errorFields.add(field + " type invalid");
        }

    }


    public static boolean isJsonOrArrayJson(Object input) {
        try {
            String str = OBJECT_MAPPER.writeValueAsString(input);
            if (str.trim().startsWith("\"") && str.trim().endsWith("\"")) {
                str = OBJECT_MAPPER.readValue(str, String.class);
            }
            // 尝试解析为JsonElement
            JsonElement element = JsonParser.parseString(str);
            // 如果是Json对象
            if (element.isJsonObject()) {
                return true;
            }
            // 如果是Json数组，检查数组是否包含有效的JSON对象
            if (element.isJsonArray()) {
                for (JsonElement e : element.getAsJsonArray()) {
                    if (!e.isJsonObject()) {
                        return false; // 如果数组元素不是JSON对象，则返回false
                    }
                }
                return true; // 如果所有数组元素都是JSON对象，返回true
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }

    /**
     * 获取鉴权token
     */
    @Cacheable(value = "tenantTableInfo", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator")
    public AccessToken getTableToken(String tableName) {
        AccessTokenCriteria criteria = new AccessTokenCriteria();
        criteria.createCriteria().andTableNameEqualTo(tableName);
        AccessToken val = tokenMapper.selectByExample(criteria).get(0);
        return val;
    }

    /**
     * 数据处理/转换
     *
     * @param itemMap
     */
    public void recordAesEncrypt(Map<String, Object> itemMap) {

    }


    /**
     * 数据处理/转换
     *
     * @param itemMap
     */
    public void recordConvert(Map<String, Object> itemMap) {

    }


    /**
     * 查询表字段排序最大值
     * 表存在字段则返回最大值
     * 否则返回1
     *
     * @param tableEnName
     * @return
     */
    public Integer queryFieldNumberMax(String tableEnName) {
        TableFieldMetaInfoCriteria metaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = metaInfoCriteria.createCriteria();
        criteria.andTableEnNameEqualTo(tableEnName);
        metaInfoCriteria.setOrderByClause("number desc");
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper.selectByExample(metaInfoCriteria);
        if (CollUtil.isEmpty(tableFieldMetaInfos)) {
            return 1;
        }
        List<TableFieldMetaInfo> collect = tableFieldMetaInfos.stream().filter(s -> s.getNumber() != null).collect(Collectors.toList());
        return CollUtil.isNotEmpty(collect) ? collect.get(0).getNumber() : 1;
    }

    /**
     * 保存租户升级添加的字段信息
     *
     * @param tableName     表名
     * @param addFieldNames 添加的字段名
     * @param fieldsInfoMap 表字段信息Map（读取表字段信息文件）
     */
    public void saveUpgradeFieldMetaInfo(String tableName, List<String> addFieldNames, Map<String, TableFieldMetaInfo> fieldsInfoMap) {
        DataTableInfo tableInfo = getTableByTableName(tableName);
        Date now = new Date();
        addFieldNames.forEach(field -> {
            TableFieldMetaInfo tableFieldMetaInfo = fieldsInfoMap.get(field);
            tableFieldMetaInfo.setFromBaidu(false);
            tableFieldMetaInfo.setTableEnName(tableInfo.getTableName());
            tableFieldMetaInfo.setDataTableId(tableInfo.getId());
            tableFieldMetaInfo.setCreateTime(now);
            tableFieldMetaInfo.setUpdateTime(now);
            tableFieldMetaInfoMapper.insert(tableFieldMetaInfo);
        });
    }
}
