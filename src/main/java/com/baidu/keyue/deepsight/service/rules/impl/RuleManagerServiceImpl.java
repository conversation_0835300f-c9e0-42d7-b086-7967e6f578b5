package com.baidu.keyue.deepsight.service.rules.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.service.DatasetPropertiesService;
import com.baidu.keyue.deepsight.service.label.LabelPropertiesService;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.database.service.UserPropertiesService;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.models.rules.response.DatasetInfo;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResponse;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.models.rules.response.LabelCatalog;
import com.baidu.keyue.deepsight.models.rules.response.LabelPropertiesResponse;
import com.baidu.keyue.deepsight.models.rules.response.UserPropertiesResponse;
import com.baidu.keyue.deepsight.models.rules.response.UserPropertiesResult;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @className: RuleManagerServiceImpl
 * @description: 数据筛选 - 规则管理服务实现类
 * @author: wangzhongcheng
 * @date: 2024/12/25 19:50
 */
@Slf4j
@Service
public class RuleManagerServiceImpl implements RuleManagerService {

    @Autowired
    private RuleParseService ruleParseService;

    @Autowired
    private UserPropertiesService userPropertiesService;

    @Autowired
    private LabelPropertiesService labelPropertiesService;

    @Autowired
    private DatasetPropertiesService datasetPropertiesService;

    @Override
    public UserPropertiesResponse getUserPropertiesResp(Boolean includeBaidu) {
        String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
        List<UserPropertiesResult> userProperties = userPropertiesService.getUserProperties(tenantId, includeBaidu);
        return UserPropertiesResponse.builder().list(userProperties).build();
    }

    @Override
    public LabelPropertiesResponse getLabelPropertiesResp() {
        List<LabelCatalog> labelCatalogs = labelPropertiesService.getLabelCatalogs();

        return LabelPropertiesResponse.builder().list(labelCatalogs).build();
    }

    @Override
    public DatasetPropertiesResponse getDatasetPropertiesResp() {
        List<DatasetInfo> datasetInfos = datasetPropertiesService.getDatasetProperties();

        return DatasetPropertiesResponse.builder().list(datasetInfos).build();
    }

    @Override
    public List<DatasetPropertiesResult> getDatasetPropertiesResult(Long dataTableId, Boolean includeBaidu) {
        return datasetPropertiesService.getDatasetProperties(dataTableId, includeBaidu);
    }

    @Override
    public DqlParseResult parseRuleGroup(RuleGroup ruleGroup, AtomicInteger alias) {
        return ruleParseService.parseRuleGroup(ruleGroup, alias);
    }

    @Override
    public DqlParseResult parseRuleNode(RuleNode ruleNode) {
        ruleParseService.checkRuleNode(ruleNode);
        return ruleParseService.parseRuleNode(ruleNode);
    }

    @Override
    public DqlParseResult innerJoin(RuleGroup ruleGroup) {
        DqlParseResult result = null;
        for (RuleNode ruleNode : ruleGroup.getRuleNodes()) {
            DqlParseResult curDqlParseResult = parseRuleNode(ruleNode);
            if (result == null) {
                result = curDqlParseResult;

                if (result.getWhere().size() > 1) {
                    String where = String.join(result.getRelationSplit(), result.getWhere());
                    result.getWhere().clear();
                    result.getWhere().add(String.format("(%s)", where));
                }
            } else {
                result.getInnerJoin().add(curDqlParseResult.getFrom());
                result.getJoinOn().add(String.format(" %s.`%s` = %s.`%s` ",
                        result.getFrom(), Constants.TABLE_USER_ONE_ID,
                        curDqlParseResult.getFrom(), Constants.TABLE_USER_ONE_ID));
                if (curDqlParseResult.getWhere().size() > 0) {
                    String where = String.join(curDqlParseResult.getRelationSplit(), curDqlParseResult.getWhere());
                    result.getWhere().add(String.format("(%s)", where));
                }
            }
        }
        return result;
    }
}
