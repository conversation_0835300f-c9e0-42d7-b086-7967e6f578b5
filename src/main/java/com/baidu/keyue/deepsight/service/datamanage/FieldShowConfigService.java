package com.baidu.keyue.deepsight.service.datamanage;

import com.baidu.keyue.deepsight.models.datamanage.request.FieldShowConfigSaveRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldShowConfigResponse;

/**
 * @ClassName FieldShowConfigService
 * @Description 字段显示列接口
 * <AUTHOR>
 * @Date 2025/5/9 4:03 PM
 */
public interface FieldShowConfigService {

    /**
     * 根据表ID、页面ID查询显示列字段配置
     *
     * @param tableId  表ID
     * @param tenantId 租户ID
     * @return 显示列字段配置
     */
    FieldShowConfigResponse getByTableIdAndTenantIdAndPageId(Long tableId, String tenantId);

    /**
     * 根据主键ID添加或更新显示列字段配置
     * 主键ID为空则添加，否则更新
     *
     * @param request  保存显示列字段配置请求
     * @param tenantId 租户ID
     * @param userName 操作人
     * @return 显示列字段配置
     */
    FieldShowConfigResponse saveOrUpdateById(FieldShowConfigSaveRequest request, String tenantId, String userName);

}
