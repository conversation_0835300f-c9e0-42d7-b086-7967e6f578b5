package com.baidu.keyue.deepsight.service.dataprediction;

import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.predict.PredictConfig;
import com.baidu.keyue.deepsight.models.predict.PredictDataSource;
import com.baidu.keyue.deepsight.models.predict.PredictSwitchUpdateRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs;

import java.util.List;
import java.util.Set;

/**
 * 数据预测服务接口（数据增强）
 */
public interface DataPredictionService {
    /**
     * 初始化给定租户的预测数据配置，即往表中插入一条该租户的数据源配置增强记录
     * @param tenantId
     * @param userId
     */
    void initTenantDateSourceAndConfig(String tenantId, String userId);

    /**
     * 获取预测数据源配置详情
     * @return
     */
    PredictDataSource getDataSourceDetail(String tenantId, String userId);

    /**
     * 更新预测数据源配置
     * @param request
     * @param userId
     * @param tenantId
     */
    void updateDataSource(PredictDataSource request, String userId, String tenantId);

    /**
     * 分页查询给定租户的预测配置列表
     * @param pageNo
     * @param pageSize
     * @param tenantId
     * @return
     */
    BasePageResponse.Page<PredictConfig> pageQueryPredictConfig(Integer pageNo, Integer pageSize,
                                                                String tenantId, String userId);

    /**
     * 更新预测开关状态
     * @param request
     * @param userId
     * @param tenantId
     */
    void updatePredictSwitch(PredictSwitchUpdateRequest request, String userId, String tenantId);

    /**
     * 获取待执行的任务
     * @param taskIds
     * @return
     */
    List<DataPredictionSourceWithBLOBs> getWaitExecPrediction(Set<Long> taskIds);

    /**
     * 更新预测任务的计算状态
     * @param id
     * @param code
     */
    void updateCalStatus(Long id, Byte code);

    /**
     * 获取租户开启预测的配置列表
     * @param tenantId
     * @return
     */
    List<DataPredictionConfig> getOnPredictConfig(String tenantId);
}
