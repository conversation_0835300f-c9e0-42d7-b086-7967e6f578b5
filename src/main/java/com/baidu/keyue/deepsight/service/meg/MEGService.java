package com.baidu.keyue.deepsight.service.meg;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.baidu.keyue.deepsight.enums.PredictTypeEnum;
import com.baidu.keyue.deepsight.models.meg.Attribute;
import com.baidu.keyue.deepsight.models.meg.BaiDuIdResult;
import com.baidu.keyue.deepsight.models.meg.MEGBaiduData;
import com.baidu.keyue.deepsight.models.meg.MEGIdEnum;
import com.baidu.keyue.deepsight.models.meg.MEGIdMappingRequest;
import com.baidu.keyue.deepsight.models.meg.MEGIdMappingResponse;
import com.baidu.keyue.deepsight.models.meg.MEGIds;
import com.baidu.keyue.deepsight.models.meg.MEGUserAttributeResponse;
import com.baidu.keyue.deepsight.models.meg.UserAttribute;
import com.baidu.keyue.deepsight.models.meg.UserAttributeRequest;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.kybase.commons.utils.HttpUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * MEG 请求封装
 * <a href="https://idm.baidu.com/docs/api">idm 接口文档</a>
 */
@Slf4j
@Service
public class MEGService {

    @Value("${baidu.data.idMappingUrl}")
    private String idMappingUrl;

    @Value("${baidu.data.userInfo.queryUrl}")
    private String megUrl;


    private MEGIdMappingRequest buildMEGIdMappingRequest(String mobile) {
        return MEGIdMappingRequest.buildWithMobile(mobile);
    }

    private MEGIdMappingRequest buildBatchMEGIdMappingRequest(List<String> mobiles) {
        return MEGIdMappingRequest.buildWithMobile(mobiles);
    }

    /**
     * 使用IdMapping获取百度id
     * @param mobile 明文手机号码
     * @return 百度 id
     */
    public MEGIds getBaiduId(String mobile) {
        String resp = null;
        try {
            resp = HttpUtil.postJsonWithTry(idMappingUrl, JsonUtils.toJson(buildMEGIdMappingRequest(mobile)));
            MEGIdMappingResponse idMappingResp = JsonUtils.toObject(resp, MEGIdMappingResponse.class);
            if (Objects.isNull(idMappingResp)) {
                return new MEGIds();
            }
            return idMappingResp.parseMEGIds();
        } catch (Exception e) {
            log.warn("MEGService.getBaiduId get id mapping fail, mobile={}, resp={}", mobile, resp);
        }
        return new MEGIds();
    }

    public List<MEGIds> batchGetBaiduId(List<String> mobiles) {
        String resp = null;
        try {
            resp = HttpUtil.postJsonWithTry(idMappingUrl, JsonUtils.toJson(buildBatchMEGIdMappingRequest(mobiles)));
            MEGIdMappingResponse idMappingResp = JsonUtils.toObject(resp, MEGIdMappingResponse.class);
            if (Objects.isNull(idMappingResp)) {
                return Lists.newArrayList();
            }
            return idMappingResp.parseMultiMEGIds();
        } catch (Exception e) {
            log.warn("MEGService.batchGetBaiduId get id mapping fail, mobile-size={}, resp={}", mobiles.size(), resp);
        }
        return Lists.newArrayList();
    }

    /**
     * 使用IdMapping获取百度id
     * @param otherId 明文手机号码
     * @return 百度 id
     */
    public MEGIds getBaiduIdWithOtherId(String otherId, MEGIdEnum type) {
        String resp = null;
        MEGIdMappingRequest req = MEGIdMappingRequest.buildWithOtherId(otherId, type);
        try {
            resp = HttpUtil.postJsonWithTry(idMappingUrl, JsonUtils.toJson(req));
            MEGIdMappingResponse idMappingResp = JsonUtils.toObject(resp, MEGIdMappingResponse.class);
            if (Objects.isNull(idMappingResp)) {
                return new MEGIds();
            }
            return idMappingResp.parseMEGIds();
        } catch (Exception e) {
            log.warn("MEGService.getBaiduIdWithOtherId get id mapping fail, otherId={}, resp={}", otherId, resp);
        }
        return new MEGIds();
    }

    /**
     * 使用 cuid，通过 meg 接口获取画像信息
     * @param cuid
     * @return
     */
    public List<Attribute> getMegAttribute(String cuid, MEGIdEnum t) {
        BaiDuIdResult baiDuIdResult = new BaiDuIdResult(cuid, t.getName());
        UserAttributeRequest req = new UserAttributeRequest();
        List<UserAttribute> userAttributes = new ArrayList<>();
        UserAttribute userAttribute = new UserAttribute();
        userAttribute.setUserId(baiDuIdResult);
        userAttribute.setAttribute(listAttribute());
        userAttributes.add(userAttribute);
        req.setUserAttribute(userAttributes);
        String resp = null;
        try {
            resp = HttpUtil.postJsonWithTry(megUrl, JsonUtils.toJson(req));
            MEGUserAttributeResponse userAttributeResp = JsonUtils.toObject(resp, MEGUserAttributeResponse.class);
            if (Objects.nonNull(userAttributeResp) && userAttributeResp.success() && CollectionUtils.isNotEmpty(userAttributeResp.getUserAttribute())) {
                return userAttributeResp.getUserAttribute().get(0).getAttribute();
            } else {
                log.warn("MEGService.getMegAttribute fail, id={}, resp={}", JsonUtils.toJsonWithOutException(baiDuIdResult), resp);
            }
        } catch (Exception e) {
            log.error("MEGService.getMegAttribute fail, id={}, resp={}", JsonUtils.toJsonWithOutException(baiDuIdResult), resp);
        }
        return Lists.newArrayList();
    }

    public Map<String, List<Attribute>> batchGetMegAttribute(List<String> cuids) {
        List<UserAttribute> userAttributes = new ArrayList<>();
        for (String cuid : cuids) {
            BaiDuIdResult baiDuIdResult = new BaiDuIdResult(cuid, MEGIdEnum.cuid.getName());
            UserAttribute userAttribute = new UserAttribute();
            userAttribute.setUserId(baiDuIdResult);
            userAttribute.setAttribute(listAttribute());
            userAttributes.add(userAttribute);
        }
        UserAttributeRequest req = new UserAttributeRequest();
        req.setUserAttribute(userAttributes);

        Map<String, List<Attribute>> resultMap = new HashMap<>();
        String resp = null;
        try {
            resp = HttpUtil.postJsonWithTry(megUrl, JsonUtils.toJson(req));
            MEGUserAttributeResponse userAttributeResp = JsonUtils.toObject(resp, MEGUserAttributeResponse.class);
            if (Objects.nonNull(userAttributeResp) && userAttributeResp.success() && CollectionUtils.isNotEmpty(userAttributeResp.getUserAttribute())) {
                for (UserAttribute userAttribute : userAttributeResp.getUserAttribute()) {
                    if (Objects.nonNull(userAttribute.getUserId()) && StringUtils.isNotBlank(userAttribute.getUserId().getId())) {
                        resultMap.put(userAttribute.getUserId().getId(), userAttribute.getAttribute());
                    }
                }
            } else {
                log.info("MEGService.batchGetMegAttribute fail, cuids-size={}, resp={}", cuids.size(), resp);
            }
        } catch (Exception e) {
            log.error("MEGService.batchGetMegAttribute fail, cuids-size={}, resp={}", cuids.size(), resp);
        }
        return resultMap;
    }

    /**
     * 根据用户手机号码，查询 idmapping 数据，再根据 cuid 查询 meg 用户画像
     * @param mobile 手机号码
     * @return MEGBaiduData
     */
    public MEGBaiduData getUserAttribute(String mobile) {
        MEGBaiduData bdData = new MEGBaiduData();
        // 获取 id mapping 数据
        MEGIds megIds = getBaiduId(mobile);

        List<Attribute> attributes = Lists.newArrayList();
        List<String> cuids = megIds.get(MEGIdEnum.cuid);
        if (CollectionUtils.isNotEmpty(cuids)) {
            // 获取 meg 画像数据
            attributes = getMegAttribute(cuids.get(0), MEGIdEnum.cuid);
        }

        bdData.setMegIds(megIds);
        bdData.setAttributes(attributes);
        return bdData;
    }

    /**
     * 根据用户百度 id，查询 idmapping 数据、查询 meg 用户画像
     * @param id 百度 id，如 cuid
     * @param type id 类型
     * @return MEGBaiduData
     */
    public MEGBaiduData getUserAttribute(String id, String type) {
        MEGIdEnum e = MEGIdEnum.getIdTypeByName(type);
        if (Objects.isNull(e)) {
            return new MEGBaiduData();
        }

        MEGBaiduData bdData = new MEGBaiduData();
        // 获取 id mapping 数据
        MEGIds megIds = getBaiduIdWithOtherId(id, e);

        // 获取 meg 画像数据
        List<Attribute> attributes = getMegAttribute(id, e);

        bdData.setMegIds(megIds);
        bdData.setAttributes(attributes);
        return bdData;
    }

    /**
     * 需要预测的范围
     * @return 预测对象
     */
    private List<Attribute> listAttribute() {
        List<Attribute> attributes = new ArrayList<>();
        for (PredictTypeEnum predictType : PredictTypeEnum.values()) {
            Attribute attribute = new Attribute();
            if (PredictTypeEnum.AGE_GROUP == predictType) {
                attribute.setName("年龄");
            } else if (PredictTypeEnum.INTERESTS == predictType) {
                attribute.setName("兴趣关注");
            } else if (PredictTypeEnum.LOCATION == predictType) {
                continue;
            } else {
                attribute.setName(predictType.getDesc());
            }
            attributes.add(attribute);
        }
        return attributes;
    }
}
