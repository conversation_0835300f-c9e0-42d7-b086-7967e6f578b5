package com.baidu.keyue.deepsight.service.sop.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.sop.SopEdge;
import com.baidu.keyue.deepsight.models.sop.SopIntent;
import com.baidu.keyue.deepsight.models.sop.SopNode;
import com.baidu.keyue.deepsight.models.sop.SopNodeDailyDistribute;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AiobSopMetricService {

    private final DorisService dorisService;

    public AiobSopMetricService(DorisService dorisService) {
        this.dorisService = dorisService;
    }

    /**
     * 统计任务下的全部节点信息
     * @param tenantId 租户 id
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @param topicId 画布主题id
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return 节点统计信息
     */
    public List<SopNode> wholeNodeMetric(String tenantId, String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        String nodeMetricTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generateWholeNodeMetric(nodeMetricTableName, taskId, robotId, robotVer, topicId, startTime, endTime);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.wholeNodeMetric selectList error, sql: {}, err: ", sql, e);
            return Collections.emptyList();
        }
        // 结果解析
        return recordList.stream().map(record -> {
                    SopNode node = new SopNode();
                    String nodeId = (String) record.get("node_id");
                    if (StringUtils.isBlank(nodeId)) {
                        return null;
                    }
                    node.setNodeId(nodeId);
                    node.setUv((Long) record.get("node_uv"));
                    node.setPv((Long) record.get("node_pv"));
                    return node;
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(SopNode::getPv).reversed())
                .collect(Collectors.toList());
    }

    public List<String> flexibleIdTransfer(String tenantId, String agentId, String versionId) {
        String debugTableName = TenantUtils.generateAiobDebugTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generateFlexibleIdTransferSql(debugTableName, agentId, versionId);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.wholeNodeMetric selectList error, sql: {}, err: ", sql, e);
            return Collections.emptyList();
        }
        if (CollectionUtils.isEmpty(recordList)) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(
                (String) recordList.get(0).get("robot_id"),
                (String) recordList.get(0).get("robot_ver")
        );
    }

    @Cacheable(value = "checkTaskSceneType", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator", unless = "#result != null")
    public Integer checkTaskSceneType(String tenantId, String taskId) {
        String debugTableName = TenantUtils.generateAiobSessionTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generateQueryTaskScene(debugTableName, taskId);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.wholeNodeMetric selectList error, sql: {}, err: ", sql, e);
            return null;
        }
        if (CollectionUtils.isEmpty(recordList)) {
            return null;
        }
        return (Integer) recordList.get(0).get("robotScene");
    }

    /**
     * 统计任务下的全部节点的挂断信息
     * @param tenantId 租户 id
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @param topicId 画布主题id
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return 节点统计信息
     */
    @Cacheable(value = "wholeNodeHangupMetric", cacheManager = "caffeineCacheManager", keyGenerator = "customKeyGenerator", unless = "#result.isEmpty()")
    public Map<String, SopNode> wholeNodeHangupMetric(String tenantId, String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        String nodeMetricTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generateWholeNodeHangupMetric(nodeMetricTableName, taskId, robotId, robotVer, topicId, startTime, endTime);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.wholeNodeHangupMetric selectList error, sql: {}, err: ", sql, e);
            return new HashMap<>();
        }
        // 结果解析
        return recordList.stream().map(record -> {
                    SopNode node = new SopNode();
                    node.setNodeId((String) record.get("node_id"));
                    node.setUv((Long) record.get("node_uv"));
                    node.setPv((Long) record.get("node_pv"));
                    return node;
                })
                .collect(Collectors.toMap(SopNode::getNodeId, Function.identity()));
    }

    /**
     * 统计任务下的全部边信息
     * @param tenantId 租户 id
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @param topicId 画布主题id
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return 边统计信息
     */
    public List<SopEdge> wholeEdgeMetric(String tenantId, String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        String edgeMetricTableName = TenantUtils.generateAiobSOPEdgeTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generateWholeEdgeMetric(edgeMetricTableName, taskId, robotId, robotVer, topicId, startTime, endTime);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.wholeEdgeMetric selectList error, sql: {}, err: ", sql, e);
            return Collections.emptyList();
        }
        // 结果解析
        return recordList.stream().map(record -> {
                    SopEdge node = new SopEdge();
                    node.setFromNodeId((String) record.get("from_node"));
                    node.setEndNodeId((String) record.get("end_node"));
                    node.setWeight((Long) record.get("edge_count"));
                    node.setWeightUv((Long) record.get("edge_count_uv"));
                    return node;
                })
                .sorted(Comparator.comparing(SopEdge::getWeight).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 统计末端节点的意向分布
     * @param tenantId 租户 id
     * @param endNodeIds 末端节点 id 列表
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @param topicId 画布主题id
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return 意向统计信息
     */
    public List<SopIntent> wholeIntentMetric(String tenantId, List<String> endNodeIds, String taskId, String robotId,
                                             String robotVer, String topicId, Date startTime, Date endTime) {
        String nodeMetricTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generateWholeIntentMetric(nodeMetricTableName, endNodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.wholeIntentMetric selectList error, sql: {}, err: ", sql, e);
            return Collections.emptyList();
        }
        // 结果解析
        return recordList.stream().map(record -> {
                    SopIntent node = new SopIntent();
                    node.setNodeId((String) record.get("node_id"));
                    node.setIntent((String) record.get("tag"));
                    node.setUv((Long) record.get("node_uv"));
                    node.setPv((Long) record.get("node_pv"));
                    return node;
                })
                .filter(item -> StringUtils.isNotBlank(item.getIntent()))
                .sorted(Comparator.comparing(SopIntent::getPv).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 获取当前节点的上游节点
     * @param tenantId 租户 id
     * @param currNodeId 当前节点 id
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @param topicId 画布主题id
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return 上游节点 id 列表
     */
    @Cacheable(value = "getAllPreviewNodes", cacheManager = "aiobSopCacheManager", keyGenerator = "customKeyGenerator", unless = "#result.isEmpty()")
    public List<String> getAllPreviewNodes(String tenantId, String currNodeId, String taskId, String robotId,
                                           String robotVer, String topicId, Date startTime, Date endTime) {
        String edgeMetricTableName = TenantUtils.generateAiobSOPEdgeTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generateGetAllFromNodesSQL(edgeMetricTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.getAllPreviewNodes selectList error, sql: {}, err: ", sql, e);
            return Collections.emptyList();
        }

        return recordList.stream()
                .map(record -> (String) record.get("from_node"))
                .collect(Collectors.toList());
    }

    /**
     * 统计任务下的全部节点信息
     * @param tenantId 租户 id
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @param topicId 画布主题id
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return 节点统计信息
     */
    @Cacheable(value = "wholeNodeMetricWithIds", cacheManager = "aiobSopCacheManager", keyGenerator = "customKeyGenerator", unless = "#result.isEmpty()")
    public List<SopNode> wholeNodeMetricWithIds(String tenantId, List<String> nodeIds,
                                                String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        String nodeMetricTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generateWholeNodeMetricWithIdsSQL(nodeMetricTableName, nodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.wholeNodeMetricWithIds selectList error, sql: {}, err: ", sql, e);
            return Collections.emptyList();
        }
        // 结果解析
        return recordList.stream().map(record -> {
                    SopNode node = new SopNode();
                    node.setNodeId((String) record.get("node_id"));
                    node.setUv((Long) record.get("node_uv"));
                    node.setPv((Long) record.get("node_pv"));
                    return node;
                })
                .sorted(Comparator.comparing(SopNode::getPv).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 指定节点 id 的合并 uv pv
     * @param tenantId 租户 id
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @param topicId 画布主题id
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return 节点统计信息
     */
    @Cacheable(value = "nodeMetricWithIds", cacheManager = "aiobSopCacheManager", keyGenerator = "customKeyGenerator", unless = "#result.length>0")
    public Long[] nodeMetricWithIds(String tenantId, List<String> nodeIds,
                                    String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        Long[] defaultResult = new Long[]{0L, 0L};
        if (nodeIds.isEmpty()) {
            return defaultResult;
        }
        String nodeMetricTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generateNodeMetricWithIdsSQL(nodeMetricTableName, nodeIds, taskId, robotId, robotVer, topicId, startTime, endTime);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.wholeNodeMetricWithIds selectList error, sql: {}, err: ", sql, e);
            return defaultResult;
        }
        // 结果解析
        if (recordList.isEmpty()) {
            return defaultResult;
        }
        return new Long[]{(Long) recordList.get(0).get("node_uv"), (Long) recordList.get(0).get("node_pv")};
    }

    /**
     * 统计 当前节点-下游节点 边的 uv、pv
     * @param tenantId 租户 id
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @param topicId 画布主题id
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return 边统计信息
     */
    @Cacheable(value = "currNodeToForwardNodeEdgeMetric", cacheManager = "aiobSopCacheManager", keyGenerator = "customKeyGenerator", unless = "#result.isEmpty()")
    public List<SopNode> currNodeToForwardNodeEdgeMetric(String tenantId, String currNodeId,
                                                         String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        String edgeMetricTableName = TenantUtils.generateAiobSOPEdgeTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generateCurrNodeToForwardNodeEdgeMetricSQL(edgeMetricTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.currNodeToForwardNodeEdgeMetric selectList error, sql: {}, err: ", sql, e);
            return Collections.emptyList();
        }
        // 结果解析
        return recordList.stream().map(record -> {
                    SopNode node = new SopNode();
                    node.setNodeId((String) record.get("end_node"));
                    node.setUv((Long) record.get("node_uv"));
                    node.setPv((Long) record.get("node_pv"));
                    return node;
                })
                .sorted(Comparator.comparing(SopNode::getPv).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 统计 上游节点 -> 当前节点 边
     * @param tenantId 租户 id
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @param topicId 画布主题id
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return 边统计信息
     */
    @Cacheable(value = "previewNodeToCurrNodeEdgeMetric", cacheManager = "aiobSopCacheManager", keyGenerator = "customKeyGenerator", unless = "#result.isEmpty()")
    public List<SopEdge> previewNodeToCurrNodeEdgeMetric(String tenantId, List<String> previewNodeIds, String currNodeId,
                                                         String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        String edgeMetricTableName = TenantUtils.generateAiobSOPEdgeTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generatePreviewNodeToCurrNodeEdgeMetricSQL(edgeMetricTableName,
                previewNodeIds, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.previewNodeToCurrNodeEdgeMetric selectList error, sql: {}, err: ", sql, e);
            return Collections.emptyList();
        }
        // 结果解析
        return recordList.stream().map(record -> {
                    SopEdge node = new SopEdge();
                    node.setFromNodeId((String) record.get("from_node"));
                    node.setEndNodeId(currNodeId);
                    node.setWeight((Long) record.get("edge_count"));
                    node.setWeightUv((Long) record.get("edge_count_uv"));
                    return node;
                })
                .sorted(Comparator.comparing(SopEdge::getWeight).reversed())
                .collect(Collectors.toList());
    }

    /**
     * 统计 当前节点-下游节点 边的 uv、pv
     * @param tenantId 租户 id
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @param topicId 画布主题id
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return 边统计信息
     */
    @Cacheable(value = "dailyCurrNodeToForwardNodeEdgeMetric", cacheManager = "aiobSopCacheManager", keyGenerator = "customKeyGenerator", unless = "#result.isEmpty()")
    public List<SopNodeDailyDistribute> dailyCurrNodeToForwardNodeEdgeMetric(String tenantId, String currNodeId,
                                                                             String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        String edgeMetricTableName = TenantUtils.generateAiobSOPEdgeTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generateDailyCurrNodeToForwardNodeEdgeMetricSQL(edgeMetricTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.dailyCurrNodeToForwardNodeEdgeMetric selectList error, sql: {}, err: ", sql, e);
            return Collections.emptyList();
        }
        // 结果解析
        Map<String, List<SopNode>> dailyNodeDistribute = new HashMap<>();
        recordList.forEach(record -> {
            String date = String.valueOf(record.get("day"));

            SopNode node = new SopNode();
            node.setNodeId((String) record.get("end_node"));
            node.setUv((Long) record.get("node_uv"));
            node.setPv((Long) record.get("node_pv"));
            if (!dailyNodeDistribute.containsKey(date)) {
                dailyNodeDistribute.put(date, Lists.newArrayList());
            }
            dailyNodeDistribute.get(date).add(node);
        });
        return dailyNodeDistribute.entrySet().stream()
                .map(entry -> new SopNodeDailyDistribute(
                        entry.getKey(), entry.getValue().stream().sorted(Comparator.comparing(SopNode::getNodeId)).toList()
                ))
                .sorted(Comparator.comparing(SopNodeDailyDistribute::getDate))
                .toList();
    }

    /**
     * 统计任务已接通电话人数
     * @param tenantId 租户 id
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @return 边统计信息
     */
    public Long countSessionConnectedCall(String tenantId, String taskId, String robotId, String robotVer) {
        String sessionTableName = TenantUtils.generateAiobSessionTableName(tenantId);
        String sql = ORMUtils.generateCountSessionConnectedCallSQL(sessionTableName, taskId, robotId, robotVer);
        try {
            return dorisService.getCount(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.countSessionConnectedCall selectList error, sql: {}, err: ", sql, e);
            return 0L;
        }
    }

    public Long countFlexibleSessionConnectedCall(String tenantId, String taskId, String robotId, String robotVer) {
        String sessionTableName = TenantUtils.generateAiobSessionTableName(tenantId);
        String debugTableName = TenantUtils.generateAiobDebugTableName(tenantId);
        String sql = ORMUtils.generateCountFlexibleSessionConnectedCallSQL(sessionTableName, debugTableName, taskId, robotId, robotVer);
        try {
            return dorisService.getCount(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.countFlexibleSessionConnectedCall selectList error, sql: {}, err: ", sql, e);
            return 0L;
        }
    }

    public Long countFlexibleProcessedCall(String tenantId, String taskId, String robotId, String robotVer) {
        String sessionTableName = TenantUtils.generateAiobSessionTableName(tenantId);
        String nodeMetricTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        String sql = ORMUtils.generateCountFlexibleProcessedCallSQL(sessionTableName, nodeMetricTableName, taskId, robotId, robotVer);
        try {
            return dorisService.getCount(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.countFlexibleProcessedCall selectList error, sql: {}, err: ", sql, e);
            return 0L;
        }
    }

    /**
     * 统计已分析人数
     * @param tenantId 租户 id
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @return 边统计信息
     */
    public Long countAnalysedMetricSQL(String tenantId, String taskId, String robotId, String robotVer) {
        String sessionTableName = TenantUtils.generateAiobSessionTableName(tenantId);
        String nodeMetricTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        String sql = ORMUtils.generateCountAnalysedMetricSQL(sessionTableName, nodeMetricTableName, taskId, robotId, robotVer);
        try {
            return dorisService.getCount(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.countAnalysedMetricSQL selectList error, sql: {}, err: ", sql, e);
            return 0L;
        }
    }

    public List<Map<String, Object>> getNodeUvByTaskIdSQL(String tenantId, String taskId, Date startTime, Date endTime) {
        String nodeMetricTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        String sql = ORMUtils.generateNodeUVSQL(nodeMetricTableName, taskId, startTime, endTime);
        try {
            return dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.getNodeUvByTaskIdSQL selectList error, sql: {}, err: ", sql, e);
            return new ArrayList<>();
        }
    }

    public List<Map<String, Object>> getNodeHangUpByTaskIdSQL(String tenantId, String taskId, Date startTime, Date endTime) {
        String nodeMetricTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        String sql = ORMUtils.generateNodeHangUpSQL(nodeMetricTableName, taskId, startTime, endTime);
        try {
            return dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.getNodeHangUpByTaskIdSQL selectList error, sql: {}, err: ", sql, e);
            return new ArrayList<>();
        }
    }

    public List<Map<String, Object>> getAgentIdSQL(String tenantId, String sessionId, String nodeId) {
        String tableName = TenantUtils.generateAiobDebugTableName(tenantId);
        String sql = ORMUtils.generateQueryAgentIdBySQL(tableName, sessionId, nodeId);
        try {
            return dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.getNodeHangUpByTaskIdSQL selectList error, sql: {}, err: ", sql, e);
            return new ArrayList<>();
        }
    }

    /**
     * 统计指定节点每日流量
     * @param tenantId 租户 id
     * @param currNodeId 当前节点 id
     * @param taskId 任务 id
     * @param robotId 机器人 id
     * @param robotVer 机器人版本
     * @param topicId 画布主题id
     * @param startTime 开始时间
     * @param endTime 截止时间
     * @return 节点统计信息
     */
    @Cacheable(value = "dailyCurrNodeMetric", cacheManager = "aiobSopCacheManager", keyGenerator = "customKeyGenerator", unless = "#result.isEmpty()")
    public Map<String, SopNode> dailyCurrNodeMetric(String tenantId, String currNodeId,
                                                    String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        String nodeMetricTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        // 构造 sql
        String sql = ORMUtils.generateDailyCurrNodeMetric(nodeMetricTableName, currNodeId, taskId, robotId, robotVer, topicId, startTime, endTime);
        // 执行查询
        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("AiobSopMetricService.dailyCurrNodeMetric selectList error, sql: {}, err: ", sql, e);
            return Collections.emptyMap();
        }
        // 结果解析
        Map<String, SopNode> currDailyMap = new HashMap<>();
        recordList.forEach(record -> {
            SopNode node = new SopNode();
            node.setNodeId(currNodeId);
            node.setUv((Long) record.get("node_uv"));
            node.setPv((Long) record.get("node_pv"));
            String date = String.valueOf(record.get("day"));
            currDailyMap.put(date, node);
        });
        return currDailyMap;
    }
}
