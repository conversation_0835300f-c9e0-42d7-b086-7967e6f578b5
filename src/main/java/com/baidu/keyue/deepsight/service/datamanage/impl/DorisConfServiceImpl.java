package com.baidu.keyue.deepsight.service.datamanage.impl;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.MysqlToJavaTypeMapping;
import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableFieldInfoDTO;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableContentListRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.models.rules.response.FilterEnumInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.DbConfService;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @className DorisConfServiceImpl
 * @description doris建表
 * @date 2025/3/5 11:13
 */
@Slf4j
@Service
public class DorisConfServiceImpl implements DbConfService {
    @Autowired
    private DorisService dorisService;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    @Lazy
    private DataTableManageServiceImpl tableManageService;

    @Autowired
    private RuleManagerService ruleManagerService;

    private static final List<String> DORIS_RESERVED_KEYWORDS = Arrays.asList("ADD", "ALL", "ALTER", "ANALYZE",
            "AND", "AS", "BETWEEN", "BY", "CASE", "CREATE", "CROSS", "DESC",
            "DELETE", "DESCRIBE", "DISTINCT", "DROP", "EXISTS", "FALSE", "FOR", "FROM", "GROUP", "HAVING",
            "IN", "INDEX", "INNER", "INSERT", "INTERSECT", "INTO", "IS", "JOIN", "KEY", "LEFT", "LIKE", "LIMIT",
            "NOT", "NULL", "ON", "OR", "ORDER", "OUTER", "PRIMARY", "RIGHT", "SELECT", "SET", "TABLE", "THEN",
            "TO", "TRUE", "UNION", "UNIQUE", "UPDATE", "VALUES", "WHEN", "WHERE", "WITH");


    @Override
    public void dbCollInit(List<TableFieldInfoDTO> fieldInfos, String tableName) {
        String sql = genCreateTableSql(tableName, fieldInfos);
        dorisService.execSql(sql);
        // doris增加deepsight_datetime字段且默认值为当前时间
        String addFieldSql = String.format("ALTER TABLE %s ADD COLUMN deepsight_datetime DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '写入时间';",
                StringEscapeUtils.escapeSql(tableName));
        dorisService.operationSchema(addFieldSql);
    }

    @Override
    public void dbFieldUpdate(TableFieldInfoDTO fieldInfo, String tableName) {
        String sql = genAddTableFieldSql(tableName, fieldInfo.getEnName() , fieldInfo.getDataType());
        dorisService.execSql(sql);
    }

    @Override
    public BasePageResponse.Page<Map<String, String>> search(GetTableContentListRequest request, Boolean includeBaidu) {
        // 获取doris表名，查询获取内容
        // 构造 ruleNode
        RuleNode ruleNode = RuleNode.builder()
                .dataTableId(request.getDataTableId())
                .filters(request.getFilters())
                .type(RuleTypeEnum.DATASET).build();
        // 解析规则节点，生成doris查询语句，设置查询字段
        DqlParseResult dqlParseResult = ruleManagerService.parseRuleNode(ruleNode);
        long count = dorisService.getCount(dqlParseResult.parseCountSql());
        // 获取可见字段
        List<VisibleFieldResponse> visibleFieldResponse = tableManageService.getVisibleFields(request.getDataTableId(), includeBaidu);
        if (CollectionUtils.isEmpty(visibleFieldResponse)) {
            throw new DeepSightException.ParamsErrorException(
                    ErrorCode.NOT_FOUND, "数据表不存在可见字段");
        }
        List<String> visibleFields = visibleFieldResponse.stream()
                .map(field -> String.format("`%s`", field.getEnName())).toList();
        String primaryKey = tableManageService.queryTableFieldByTag(request.getDataTableId(), TableFieldTagEnum.PRIMARY).getEnField();
        dqlParseResult.setSelect(String.join(Constants.SEPARATOR, visibleFields));
        dqlParseResult.setOrderBy(String.format("`%s`", primaryKey));
        dqlParseResult.setOffset((request.getPageNo() - 1) * request.getPageSize());
        dqlParseResult.setSize(request.getPageSize());
        List<Map<String, Object>> tableContentList = dorisService.selectList(dqlParseResult.parseDorisSql());

        // datetime格式转换，加密字段***输出等
        List<Map<String, String>> results = tableContentList.stream().map(dorisData ->
                dorisDataConvertToShowData(request.getDataTableId(), dorisData, visibleFieldResponse)).toList();

        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, results);
    }

    @Override
    public void delete(String tableName, String field, List<String> idList) {
        // 将主键ID数组转换为逗号分隔的字符串
        String ids = idList.stream()
                .map(StringEscapeUtils::escapeSql)
                .map(s -> "\"" + s + "\"")
                .collect(Collectors.joining(","));
        String sql = String.format("DELETE FROM %s WHERE %s IN (%s);",
                StringEscapeUtils.escapeSql(tableName), StringEscapeUtils.escapeSql(field), ids);
        dorisService.execSql(sql);
    }

    @Override
    public void deleteTable(String tableName) {
        String sql = String.format("DROP TABLE IF EXISTS %s;", StringEscapeUtils.escapeSql(tableName));
        dorisService.execSql(sql);
    }

    /**
     * 将doris数据转为展示对象
     */
    public Map<String, String> dorisDataConvertToShowData(Long dataTableId,
                                                          Map<String, Object> dorisData,
                                                          List<VisibleFieldResponse> visibleFieldResponse) {
        // 展示映射
        List<String> sensitiveFields = visibleFieldResponse.stream()
                .filter(field -> TableFieldTagEnum.SENSITIVE.equals(field.getTableFieldTag()))
                .map(VisibleFieldResponse::getEnName).toList();
        Map<String, Map<String, String>> enumFields = visibleFieldResponse.stream()
                .filter(field -> CollectionUtils.isNotEmpty(field.getEnums()))
                .collect(Collectors.toMap(VisibleFieldResponse::getEnName, field ->
                                field.getEnums().stream()
                                        .collect(Collectors.toMap(FilterEnumInfo::getKey, FilterEnumInfo::getValue, (k1, k2) -> k2)),
                        (k1, k2) -> k2)
                );
        Map<String, VisibleFieldResponse> fieldMap = new HashMap<>();
        visibleFieldResponse.forEach(field -> fieldMap.put(field.getEnName(), field));
        List<String> fieldEncrys = tableManageService.getTableEncryFields(dataTableId);

        // 转换展示数据
        Map<String, String> showData = new HashMap<>();
        for (Map.Entry<String, Object> entry : dorisData.entrySet()) {
            String enName = entry.getKey();
            Object value = entry.getValue();
            // 数据集字段配置为敏感字段，则不展示具体值
            if (sensitiveFields.contains(entry.getKey())) {
                showData.put(entry.getKey(), Constants.ENCRY_FIELD_VIEW);
            } else if (CollectionUtils.isNotEmpty(fieldEncrys) && fieldEncrys.contains(entry.getKey())) {
                showData.put(entry.getKey(), Constants.ENCRY_FIELD_VIEW);
            } else if (enumFields.keySet().contains(entry.getKey())) {
                // 枚举是否映射
                Optional.ofNullable(value).ifPresent(va -> showData.put(enName, convertEnum(enumFields, fieldMap, enName, va)));
            } else {
                showData.put(entry.getKey(), getFieldShowValue(entry.getValue()));
            }
        }
        return showData;
    }

    /**
     * 生成palo建表sql
     */
    public String genCreateTableSql(String tableName, List<TableFieldInfoDTO> fieldInfos) {
        StringBuilder sql = new StringBuilder();
        String primaryKey = null;
        // 主键字段排在最前，其他顺序不变
        fieldInfos = fieldInfos.stream()
                .sorted(Comparator.comparingInt(field ->
                        TableFieldTagEnum.PRIMARY.getCode().equals(field.getFieldTag()) ? 0 : 1))
                .collect(Collectors.toList());
        sql.append("CREATE TABLE IF NOT EXISTS ").append(StringEscapeUtils.escapeSql(tableName)).append(" (");
        for (int i = 0;i < fieldInfos.size();i ++) {
            String fieldName = StringEscapeUtils.escapeSql(fieldInfos.get(i).getEnName());
            String dataType = fieldInfos.get(i).getDataType();
            boolean notNull = fieldInfos.get(i).getIsRequired();
            String desc = fieldInfos.get(i).getDescription();
            if (containsIgnoreCase(DORIS_RESERVED_KEYWORDS, fieldName)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "字段名不能包含保留关键字：" + fieldName);
            }
            // 只有varchar类型需要指定长度
            if (MysqlToJavaTypeMapping.VARCHAR.getDataType().equals(dataType)) {
                sql.append("\n    ").append(fieldName).append(" ").append(dataType + "(255)");
            } else if (MysqlToJavaTypeMapping.ARRAY.getDataType().equals(dataType)) {
                sql.append("\n    ").append(fieldName).append(" ").append(dataType + "<String>");
            } else {
                sql.append("\n    ").append(fieldName).append(" ").append(dataType);
            }
            if (notNull) {
                sql.append(" NOT NULL");
            }
            sql.append(" COMMENT '").append(desc).append("'");
            if (i < fieldInfos.size() - 1) {
                sql.append(",");
            }
            // 记录主键字段
            if (TableFieldTagEnum.PRIMARY.getCode().equals(fieldInfos.get(i).getFieldTag())) {
                primaryKey = fieldName;
            }
        }
        if (null == primaryKey) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据集缺少主键");
        }
        sql.append("\n)");
        sql.append(" ENGINE=OLAP\n");
        sql.append("UNIQUE KEY(`").append(primaryKey).append("`)\n");
        sql.append("DISTRIBUTED BY HASH(`").append(primaryKey).append("`) BUCKETS 10\n");
        sql.append("PROPERTIES (")
                .append("\n    \"replication_num\" = \"1\"\n");
        sql.append(");");
        return sql.toString();
    }

    public static boolean containsIgnoreCase(List<String> array, String target) {
        for (String s : array) {
            if (s.equalsIgnoreCase(target)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 新增表字段sql
     */
    public String genAddTableFieldSql(String tableName, String field, String dataType) {
        StringBuilder sql = new StringBuilder(String.format("ALTER TABLE %s ADD COLUMN %s ", tableName, field));
        // 只有varchar类型需要指定长度
        if (MysqlToJavaTypeMapping.VARCHAR.getDataType().equals(dataType)) {
            sql.append(dataType + "(255)");
        } else if (MysqlToJavaTypeMapping.ARRAY.getDataType().equals(dataType)) {
            sql.append(dataType + "<String>");
        } else {
            sql.append(dataType);
        }
        sql.append(";");
        return sql.toString();

    }
    public String getFieldShowValue(Object value) {
        if (Objects.isNull(value)) {
            return StringUtils.EMPTY;
        }

        if (value instanceof LocalDateTime dateTime) {
            return DatetimeUtils.formatDate(dateTime);
        }

        return String.valueOf(value);
    }

    /**
     * 转换枚举
     * 需要展示枚举value是转换枚举值
     *
     * @param enumFields 枚举字段集合
     * @param fieldMap   表可见字段
     * @param enName     字段英文名
     * @param value      数据值
     * @return
     */
    public String convertEnum(Map<String, Map<String, String>> enumFields, Map<String, VisibleFieldResponse> fieldMap, String enName, Object value) {
        VisibleFieldResponse fieldResponse = fieldMap.get(enName);
        Boolean isShowValue = fieldResponse.getIsShowValue();

        Map<String, String> enumMap = enumFields.get(enName);
        // 如果无需展示value，直接返回业务数据
        if (Objects.equals(Boolean.FALSE, isShowValue) || CollUtil.isEmpty(enumMap)) {
            return value.toString();
        }
        try {
            // 如果是多值枚举
            String dataStr = value.toString();
            if (JSONUtil.isTypeJSONArray(dataStr)) {
                List<String> collect = JSONUtil.toList(dataStr, String.class).stream()
                        .map(obj -> enumMap.get(obj.toString()))
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.toList());
                return JSONUtil.toJsonStr(collect);
            } else {
                // 单值枚举
                return enumMap.get(value.toString());
            }
        } catch (Exception e) {
            log.error("content list convertEnum Exception:", e);
        }
        return value.toString();
    }
}
