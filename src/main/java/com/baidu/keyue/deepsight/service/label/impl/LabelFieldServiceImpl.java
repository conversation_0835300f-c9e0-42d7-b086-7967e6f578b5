package com.baidu.keyue.deepsight.service.label.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.DorisConfiguration;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelField;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelFieldMapper;
import com.baidu.keyue.deepsight.service.label.LabelFieldService;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class LabelFieldServiceImpl implements LabelFieldService {

    @Autowired
    private ExtendLabelFieldMapper labelFieldMapper;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private DorisConfiguration dorisConfiguration;

    @Override
    public long createNewLabelField(String tenantId, String labelName) {
        String userId = WebContextHolder.getUserId();
        Date now = new Date();
        LabelField l = new LabelField();
        l.setFieldType("array");
        l.setFieldDesc(labelName);
        l.setTableSpace(dorisConfiguration.getDb());
        l.setLabelTable(TenantUtils.generateMockUserTableName(tenantId));
        l.setDel(DelEnum.NOT_DELETED.getBoolean());
        l.setCreator(userId);
        l.setModifier(userId);
        l.setCreateTime(now);
        l.setUpdateTime(now);
        labelFieldMapper.insert(l);
        long fieldId = l.getId();
        String fieldStringName = genFieldName(fieldId);
        newDorisLabelProcessField(tenantId, fieldStringName);
        return fieldId;
    }

    @Override
    public void deleteFieldById(long id) {
        LabelField labelField = new LabelField();
        labelField.setId(id);
        labelField.setDel(true);
        labelFieldMapper.updateByPrimaryKeySelective(labelField);
    }

    @Override
    public String genFieldName(long fieldId) {
        return String.format(Constants.DORIS_LABEL_FIELD_TEM, fieldId);
    }

    @Override
    public void newDorisLabelProcessField(String tenantId, String fieldName) {
        try {
            dorisService.execSql(String.format(
                    "ALTER TABLE %s.%s ADD COLUMN %s array<varchar(150)> NULL DEFAULT \"[]\"",
                    dorisConfiguration.getDb(), TenantUtils.generateMockUserTableName(tenantId), fieldName));
            dorisService.execSql(String.format(
                    "ALTER TABLE %s.%s ADD COLUMN %s array<varchar(150)> NULL DEFAULT \"[]\"",
                    dorisConfiguration.getDb(), TenantUtils.generateUserProfileTableName(tenantId), fieldName));
        } catch (Exception e) {
            log.error("LabelFieldService.newDorisField error : ", e);
        }
    }

    @Override
    public void deleteDorisLabelProcessField(String tenantId, long fieldId) {
        String fieldStringName = genFieldName(fieldId);
        dorisService.deleteLabelProcessField(
                dorisConfiguration.getDb(), TenantUtils.generateMockUserTableName(tenantId), fieldStringName);
        dorisService.deleteLabelProcessField(
                dorisConfiguration.getDb(), TenantUtils.generateUserProfileTableName(tenantId), fieldStringName);
    }

}
