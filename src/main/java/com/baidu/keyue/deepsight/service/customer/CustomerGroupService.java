package com.baidu.keyue.deepsight.service.customer;

import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.customer.request.CreateCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.CreateCustomerImportRequest;
import com.baidu.keyue.deepsight.models.customer.request.CustomerGroupDetaiListlRequest;
import com.baidu.keyue.deepsight.models.customer.request.DeleteCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.DeleteCustomerRequest;
import com.baidu.keyue.deepsight.models.customer.request.GetCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.GetCustomerListRequest;
import com.baidu.keyue.deepsight.models.customer.request.ListCustomerGroupAnalysisRequest;
import com.baidu.keyue.deepsight.models.customer.request.SamplingStatisticsCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.request.UpdateCustomerGroupRequest;
import com.baidu.keyue.deepsight.models.customer.response.CreateCustomerImportResponse;
import com.baidu.keyue.deepsight.models.customer.response.CustomerDorisResponse;
import com.baidu.keyue.deepsight.models.customer.response.CustomerGroupAnalysisItemResponse;
import com.baidu.keyue.deepsight.models.customer.response.CustomerGroupDetailResponse;
import com.baidu.keyue.deepsight.models.customer.response.GroupDetailDto;
import com.baidu.keyue.deepsight.models.customer.response.SamplingStatisticsCustomerGroupResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName CustomerGroupService
 * @Description 客群分析数据操作定义
 * <AUTHOR> Chen (<EMAIL>)
 * @Date 2025/1/13 11:29
 */
public interface CustomerGroupService {

    /**
     * 创建客群
     *
     * @param request
     * @return
     */
    Long createCustomerGroup(CreateCustomerGroupRequest request);

    /**
     * 获取客群列表
     *
     * @param request
     * @return
     */
    BasePageResponse.Page<CustomerGroupAnalysisItemResponse> customerGroupAnalysisItems(ListCustomerGroupAnalysisRequest request);

    /**
     * 删除客群
     *
     * @param request
     */
    void deleteCustomerGroup(DeleteCustomerGroupRequest request);

    /**
     * 获取客群详情
     *
     * @param request
     * @return
     */
    CustomerGroupDetailResponse getCustomerGroupDetail(GetCustomerGroupRequest request);

    /**
     * 获取客群详情列表
     *
     * @param request
     * @return
     */
    List<CustomerGroupDetailResponse> listCustomerGroupDetail(CustomerGroupDetaiListlRequest request);

    /**
     * 预测客群圈选人群数量
     *
     * @param request 客群圈选请求
     * @return 客群圈选人群数量
     */
    SamplingStatisticsCustomerGroupResponse samplingStatisticsConsumerGroup(
            SamplingStatisticsCustomerGroupRequest request);

    /**
     * 更新客群详情
     *
     * @param request
     */
    void updateCustomerGroup(UpdateCustomerGroupRequest request);

    /**
     * 获取客群用户列表
     *
     * @param request
     * @return
     */
    BasePageResponse.Page<Map<String, String>> getCustomerList(GetCustomerListRequest request);

    /**
     * 生成查客群客户列表的 doris查询语句
     */
    CustomerDorisResponse getCustomerListDoriSql(GetCustomerListRequest request);

    /**
     * 删除客群用户
     *
     * @param request
     */
    void deleteCustomer(DeleteCustomerRequest request);

    /**
     * 根据用户ID和客群ID获取客群信息
     *
     * @param tenantId        租户ID
     * @param customerGroupId 客群ID
     * @return CustomerGroup
     */
    CustomerGroup getCustomerGroupByTenantIdAndCustomerGroupId(String tenantId, Long customerGroupId);

    /**
     * 根据用户ID和客群ID获取客群信息
     * 不判空，无异常
     *
     * @param customerGroupId 客群ID
     * @param tenantId        租户ID
     * @return
     */
    CustomerGroup getByIdAndTenantId(Long customerGroupId, String tenantId);

    /**
     * 更新任务状态
     *
     * @param customerGroup
     * @param statusEnum
     */
    void updateCustomerGroupCalTaskStatus(CustomerGroup customerGroup, TaskExecStatusEnum statusEnum);

    /**
     * 定时任务使用：查询执行中的客群列表
     *
     * @return
     */
    List<CustomerGroup> queryRunningCustomer();

    /**
     * 查询待执行客群任务
     *
     * @param taskIds 任务ID列表
     */
    List<CustomerGroup> getWaitExecCustomer(Set<Long> taskIds);

    /**
     * 查询已删除标签的客群
     */
    List<CustomerGroup> queryDeletedLabel(Integer seconds);

    /**
     * 根据ids查询客群
     *
     * @param ids      主键ids
     * @param tenantId 租户ID
     * @return
     */
    List<CustomerGroup> retrieveCustomerGroupWithIds(List<Long> ids, String tenantId);

    /**
     * 全员客群初始化
     *
     * @param tenantId 租户ID
     * @param userId   用户ID
     */
    void initDefaultConsumerGroup(String tenantId, String userId);

    /**
     * 导入客群
     *
     * @param request 导入客群请求体
     * @return 导入客群返回体
     */
    CreateCustomerImportResponse importData(CreateCustomerImportRequest request);

    /**
     * 统计客群人数
     *
     * @param tenantId 住户ID
     * @param groupId  客群ID
     * @return 客群人数
     */
    Long getUserCount(String tenantId, Long groupId);

    /**
     * 统计客群人数
     *
     * @param tenantId 租户ID
     * @param groupIds 客群IDS
     * @return 客群人数总合
     */
    Long getUserCount(String tenantId, Set<Long> groupIds);

    /**
     * 租户所有客群列表
     * 查询租户下所有客群列表
     *
     * @param tenantId 租户ID
     * @return 租户所有客群列表
     */
    List<GroupDetailDto> getAllGroupList(String tenantId);

    /**
     * 创建人群扩散客群，已存在就直接返回，不存在则创建
     *
     * @param tenantId          租户ID
     * @param userId            用户ID
     * @param customerGroupName 客群名称
     * @return 客群
     */
    CustomerGroup newDiffusionCustomerGroup(String tenantId, String userId, String customerGroupName);
}
