package com.baidu.keyue.deepsight.service.user;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.diffusion.DiffusionRequestItem;
import com.baidu.keyue.deepsight.models.meg.Attribute;
import com.baidu.keyue.deepsight.models.meg.MEGBaiduData;
import com.baidu.keyue.deepsight.models.meg.MEGIdEnum;
import com.baidu.keyue.deepsight.models.meg.MEGIds;
import com.baidu.keyue.deepsight.models.meg.MEGRequestPrepare;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldEncryConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldEncryConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.FieldEncryConfigMapper;
import com.baidu.keyue.deepsight.service.meg.MEGService;
import com.baidu.keyue.deepsight.service.tool.RedisService;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.MobileProcessUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * 百度用户真实数据获取
 */
@Slf4j
@Service
public class BaiduUserDataService {

    @Value("${baidu.whiteList.userTables: mock_user_1234567890}")
    private String mockUserTableWhiteList;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private FieldEncryConfigMapper fieldEncryConfigMapper;

    @Autowired
    private MEGService megService;

    /**
     * 往redis里面读取 file_encry_config 配置信息，放入到redis
     * redis key = table_en_name + "#" + en_filed
     * redis value = scret_key
     * 注：file_encry_config里面存放的是指定表某个字段的加密key，例如mock_user表的mobile字段是加密后的，解密时需要取到该key解密
     */
    public void refreshSecretKey() {
        FieldEncryConfigCriteria criteria = new FieldEncryConfigCriteria();
        List<FieldEncryConfig> list = fieldEncryConfigMapper.selectByExample(criteria);
        for (FieldEncryConfig config : list) {
            String key = config.getTableEnName() + "#" + config.getEnField();
            String value = config.getSecretKey();
            redisService.set(key, value);
        }
    }

    private List<String> filterMockUserTableWhiteList(List<String> allUserTable) {
        if (StringUtils.isBlank(mockUserTableWhiteList)) {
            return Collections.emptyList();
        } else if ("all".equals(mockUserTableWhiteList)) {
            return allUserTable;
        } else {
            List<String> whiteList = Lists.newArrayList(mockUserTableWhiteList.split(Constants.SEPARATOR));
            return allUserTable.stream().filter(whiteList::contains).collect(Collectors.toList());
        }
    }

    /**
     * 拉取百度数据，写入到doris mock_user_xxx表中bd开头的字段
     */
    public void handelBaiduDataProcess(String userTable) {
        // 密钥
        String decryptKey = redisService.get(userTable + "#" + "mobile");

        // 获取当前表解密后的手机号
        List<HashMap<String, String>> mobileInfo = getMobileFromTable(userTable, decryptKey);
        if (mobileInfo.isEmpty()) {
            log.info("handelBaiduDataProcess Got: empty mobile, tableName={}, mobileSize=0", userTable);
            return;
        }
        log.info("handelBaiduDataProcess Got: tableName={}, mobileSize={}", userTable, mobileInfo.size());

        for (HashMap<String, String> entry : mobileInfo) {
            log.info("handelBaiduDataProcess, tableName={}, entry={}", userTable, JsonUtils.toJsonWithOutException(entry));
            String oneId = entry.get(Constants.TABLE_USER_ONE_ID);
            if (StringUtils.isBlank(oneId) || !checkIdsSize(entry)) {
                log.warn("handelBaiduDataProcess checkIdsSize block, tableName={}, oneId=[{}]", userTable, oneId);
                continue;
            }

            if (StringUtils.isNotBlank(entry.get("mobile"))) {
                MEGBaiduData megData = megService.getUserAttribute(entry.get("mobile"));
                if (megData.isEmpty()) {
                    log.warn("handelBaiduDataProcess megData is empty, tableName={}, oneId=[{}]", userTable, oneId);
                    continue;
                }
                updateUserIdmappingAndMegData(userTable, oneId, megData);
            } else {
                for (String key : Constants.MEG_MERGE_MEG_FIELDS) {
                    if (StringUtils.isBlank(entry.get(key))) {
                        continue;
                    }

                    MEGBaiduData megData = megService.getUserAttribute(entry.get(key), key);
                    if (megData.isEmpty()) {
                        log.warn("handelBaiduDataProcess megData is empty, tableName={}, oneId=[{}]", userTable, oneId);
                        continue;
                    }
                    updateUserIdmappingAndMegData(userTable, oneId, megData);
                    break;
                }
            }

        }
    }

    private boolean checkIdsSize(HashMap<String, String> entry) {
        int count = 0;
        for (String key : Constants.MEG_MERGE_SELECT_FIELDS) {
            if (StringUtils.isNotBlank(entry.get(key))) {
                count++;
            }
        }
        return count <= 3;
    }

    /**
     * 更新表中用户信息
     * @param userTable
     * @param oneId
     * @param megData
     */
    private void updateUserIdmappingAndMegData(String userTable, String oneId, MEGBaiduData megData) {
        String sql = ORMUtils.updateUserMegData(userTable, oneId, megData);
        log.info("updateUserIdmappingAndMegData, tableName={}, userId={}, sql={}", userTable, oneId, sql);
        if (StringUtils.isNotBlank(sql)) {
            try {
                dorisService.execSql(sql);
            } catch (Exception e) {
                log.error("updateUserIdmappingAndMegData error, tableName={}, userId={}", userTable, oneId, e);
            }
        }
    }

    /**
     * 获取所有用户表名 mock_user_xxx
     */
    public List<String> getAllUserTable() {
        List<String> tableNames = dorisService.showTablesWithPrefix(Constants.DORIS_DEFAULT_LABEL_TABLE);
        return filterMockUserTableWhiteList(tableNames);
    }

    /**
     * 获取最近一小时内 mock user 表中的增量数据
     * @param tableName mock user 表
     * @param decryptKey 表对应的 aes密钥
     * @return map<mobileAes, mobile>
     */
    public List<HashMap<String, String>> getMobileFromTable(String tableName, String decryptKey) {
        String anHourAgo = DatetimeUtils.backToDate(60);
        String sql = String.format(
                "select oneId,mobile,IMEI,BAIDUID,CUID,USERID,MAC,UNIONID,IDFA,OAID from %s where deepsight_update_datetime > '%s'",
                tableName, anHourAgo
        );
        List<Map<String, Object>> result = Lists.newArrayList();
        try {
            result = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("getMobileFromTable error, tableName={}", tableName, e);
        }
        List<HashMap<String, String>> mobiles = Lists.newArrayList();

        for (Map<String, Object> map : result) {
            HashMap<String, String> ids = new HashMap<>();

            if (Objects.nonNull(map.get("mobile"))) {
                String mobileAes = (String) map.get("mobile");
                String m = MobileProcessUtils.decryptMobile(decryptKey, mobileAes);

                if (StringUtils.isNotBlank(m)) {
                    ids.put("mobile", m);
                    ids.put("mobileAes", mobileAes);
                }
            }

            for (String key : Constants.MEG_MERGE_MEG_FIELDS) {
                if (Objects.nonNull(map.get(key))) {
                    String b = String.valueOf(map.get(key));
                    if (StringUtils.isNotBlank(b)) {
                        ids.put(key, b);
                    }
                }
            }
            ids.put(Constants.TABLE_USER_ONE_ID, String.valueOf(map.get(Constants.TABLE_USER_ONE_ID)));
            mobiles.add(ids);
        }
        return mobiles;
    }

    public List<MEGRequestPrepare> batchGetMEGRequestPrepare(List<String> oneIds, String tenantId) {
        if (CollectionUtils.isEmpty(oneIds)) {
            return Collections.emptyList();
        }
        String mockUserTableName = TenantUtils.generateMockUserTableName(tenantId);
        String decryptKey = redisService.get(mockUserTableName + "#" + "mobile");

        String sql = ORMUtils.generateOneIdRetrieveSql(mockUserTableName, oneIds);
        List<Map<String, Object>> result = Lists.newArrayList();
        try {
            result = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("batchGetUserAttribute sql: {}, error: ", sql, e);
        }

        return result.stream()
                .map(map -> {
                    String oneId = (String) map.get("oneId");
                    String cuid = String.valueOf(map.get(MEGIdEnum.cuid.getName()));
                    String mobile = String.valueOf(map.get("mobile"));

                    if (Objects.nonNull(map.get(MEGIdEnum.cuid.getName())) && StringUtils.isNotBlank(cuid)) {
                        return new MEGRequestPrepare(oneId, cuid, null);
                    } else if (Objects.nonNull(map.get("mobile")) && StringUtils.isNotBlank(mobile)) {
                        return new MEGRequestPrepare(oneId, null, MobileProcessUtils.decryptMobile(decryptKey, mobile));
                    } else {
                        return null;
                    }
                })
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    public DiffusionRequestItem getUserAttribute(MEGRequestPrepare requestData) {
        if (StringUtils.isBlank(requestData.getCuid())) {
            MEGIds megIds = megService.getBaiduId(requestData.getMobile());
            List<String> cuids = megIds.get(MEGIdEnum.cuid);
            if (CollectionUtils.isNotEmpty(cuids)) {
                requestData.setCuid(cuids.get(0));
            } else {
                return null;
            }
        }

        List<Attribute> attribute = megService.getMegAttribute(requestData.getCuid(), MEGIdEnum.cuid);
        return new DiffusionRequestItem(requestData.getOneId(), attribute);
    }
}
