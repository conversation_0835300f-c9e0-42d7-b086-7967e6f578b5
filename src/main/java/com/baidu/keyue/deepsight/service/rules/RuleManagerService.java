package com.baidu.keyue.deepsight.service.rules;

import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResponse;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.models.rules.response.LabelPropertiesResponse;
import com.baidu.keyue.deepsight.models.rules.response.UserPropertiesResponse;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @className: RuleManagerService
 * @description: 规则管理服务接口 - 用于交互的服务
 * @author: wangzhongcheng
 * @date: 2024/12/25 19:46
 */
public interface RuleManagerService {

    /**
     * 获取前端筛选用户属性
     * @param includeBaidu 是否包含百度数据
     */
    UserPropertiesResponse getUserPropertiesResp(Boolean includeBaidu);

    /**
     * 获取前端筛选标签属性
     */
    LabelPropertiesResponse getLabelPropertiesResp();

    /**
     * 获取前端筛选数据集属性
     */
    DatasetPropertiesResponse getDatasetPropertiesResp();

    /**
     * 获取可筛选的数据集属性结果
     */
    List<DatasetPropertiesResult> getDatasetPropertiesResult(Long dataTableId, Boolean includeBaidu);

    /**
     * 解析规则组为DQL语句
     */
    DqlParseResult parseRuleGroup(RuleGroup ruleGroup, AtomicInteger alias);

    /**
     * 解析规则节点为DQL语句
     * @param ruleNode 规则节点，为业务提供单表查询
     * @return
     */
    DqlParseResult parseRuleNode(RuleNode ruleNode);

    /**
     * 针对规则组内连接规则节点
     */
    DqlParseResult innerJoin(RuleGroup ruleGroup);

}
