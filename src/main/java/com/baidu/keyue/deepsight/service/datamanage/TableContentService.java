package com.baidu.keyue.deepsight.service.datamanage;

import com.baidu.keyue.deepsight.models.datamanage.dto.UploadDetailDto;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportFieldMappingRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportSaveRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportTaskDeleteRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.FileImportTaskListRequest;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableDataCurlRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldAiMappingResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileImportTaskDeleteResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileImportTaskQueryResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileImportTaskResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FileSaveResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableSyncDetailResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportWithBLOBs;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className TableContentService
 * @description 表内容同步/修改
 * @date 2025/1/8 15:51
 */
public interface TableContentService {

    /**
     * 数据批量写入
     */
    void handleBatchSync(List<Map<String, Object>> itemMaps, String dataTableId);

    /**
     * 数据表调用信息
     */
    TableSyncDetailResponse getSyncInfo(GetTableDataCurlRequest request);

    /**
     * 文件上传保存
     * 目前只支持csv、xlsx、xls格式用于数据导入使用
     *
     * @param request
     * @param userAuthInfo
     * @return 文件ID
     */
    FileSaveResponse fileSave(FileImportSaveRequest request, UserAuthInfo userAuthInfo);

    /**
     * 文件导入数据
     *
     * @param fileImportRequest
     * @param userAuthInfo
     * @return
     */
    FileImportTaskResponse fileImport(FileImportRequest fileImportRequest, UserAuthInfo userAuthInfo);

    /**
     * 查询数据集文件导入记录
     * 按照任务组分组聚合
     *
     * @param taskListRequest
     * @param userAuthInfo
     * @return
     */
    List<FileImportTaskQueryResponse> fileImportTaskList(FileImportTaskListRequest taskListRequest, UserAuthInfo userAuthInfo);

    /**
     * 批量更新导入数据任务
     *
     * @param taskList
     */
    void updateImportTasksById(List<TaskFileImportWithBLOBs> taskList);

    /**
     * 删除文件导入数据任务
     * 支持根据任务组ID和文件ids删除
     * 优先使用任务组ID
     * 任务组ID为空则使用文件ids
     *
     * @param deleteRequest
     * @param userAuthInfo
     * @return
     */
    FileImportTaskDeleteResponse fileImportTaskDelete(FileImportTaskDeleteRequest deleteRequest, UserAuthInfo userAuthInfo);

    /**
     * 获取字段映射
     *
     * @param mappingRequest 映射请求
     * @return
     */
    FieldAiMappingResponse fieldMapping(FileImportFieldMappingRequest mappingRequest);

    List<Map<String, Object>> importFileData(TaskFileImportWithBLOBs taskFileImport,
                                             FileImportRequest fileImportRequest,
                                             FileImportTaskResponse res,
                                             DataTableInfo tableInfo);

    /**
     * 文件上传
     *
     * @param files    文件
     * @return
     */
    List<UploadDetailDto> fileUpload(MultipartFile[] files, UserAuthInfo data);
}
