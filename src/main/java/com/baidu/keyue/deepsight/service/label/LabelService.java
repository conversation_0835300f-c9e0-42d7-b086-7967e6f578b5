package com.baidu.keyue.deepsight.service.label;

import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.base.response.BaseRecordResponse;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogResponse;
import com.baidu.keyue.deepsight.models.label.GetLabelDetailRequest;
import com.baidu.keyue.deepsight.models.label.GetLabelDistributeRequest;
import com.baidu.keyue.deepsight.models.label.LabelDetail;
import com.baidu.keyue.deepsight.models.label.LabelDistribute;
import com.baidu.keyue.deepsight.models.label.LabelOriginalDetail;
import com.baidu.keyue.deepsight.models.label.LabelUserListRequest;
import com.baidu.keyue.deepsight.models.label.ListLabelBriefRequest;
import com.baidu.keyue.deepsight.models.label.NewLabelRequest;
import com.baidu.keyue.deepsight.models.label.UpdateLabelRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.Label;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;

import java.util.List;
import java.util.Set;

public interface LabelService {

    /**
     * 创建标签
     * @param request 标签详情
     */
    void createLabel(NewLabelRequest request);

    /**
     * 标签列表查询
     * @param request 分页参数
     * @return
     */
    BasePageResponse.Page<LabelDetail> listLabel(ListLabelBriefRequest request);

    /**
     * 查询标签详情
     * @param labelId 标签 id
     * @return
     */
    Label getLabelByTenantAndLabelId(Long labelId);

    /**
     * 查询标签详情
     * @param tenantId
     * @param labelId
     * @return
     */
    LabelWithBLOBs getLabelByTenantAndLabelId(String tenantId, Long labelId);

    /**
     * 标签删除
     * @param label 标签
     */
    void deleteLabel(Label label);

    /**
     * 标签详情页-详情信息
     * @param request 标签 id
     * @return
     */
    LabelOriginalDetail getLabelDetail(GetLabelDetailRequest request);

    /**
     * 标签分布查询
     * @param request
     * @return
     */
    LabelDistribute labelDistribution(GetLabelDistributeRequest request);

    /**
     * 更新标签详情
     * @param request
     */
    void updateLabel(UpdateLabelRequest request);

    /**
     * 标签目录+标签树
     * @param request 标签目录请求
     * @param onlyCount 是否只做标签目录的标签数量统计
     * @return
     */
    ListCatalogResponse listLabelTree(ListCatalogRequest request, Boolean onlyCount);

    /**
     * 定时任务使用：查询待执行标签
     * @param taskIds 任务 id 集合
     */
    List<LabelWithBLOBs> getWaitExecLabel(Set<Long> taskIds);

    /**
     * 定时任务使用：查询执行中的标签列表
     * @return
     */
    List<LabelWithBLOBs> queryRunningLabel();

    /**
     * 查询已删除的标签
     * @param seconds
     * @return
     */
    List<Label> queryDeletedLabel(Integer seconds);

    /**
     * 根据字段 id 反查标签信息
     * @param labelFields 字段 id
     * @return 标签集合
     */
    List<Label> retrieveLabelWithFieldIds(List<Long> labelFields, String tenantId);

    /**
     * 标签用户列表
     *
     * @param request  标签列表请求
     * @param tenantId 租户ID
     * @return 标签用户列表
     */
    BaseRecordResponse labelUserList(LabelUserListRequest request, String tenantId);
}
