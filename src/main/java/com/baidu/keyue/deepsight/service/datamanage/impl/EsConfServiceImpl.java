package com.baidu.keyue.deepsight.service.datamanage.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.enums.ESToJavaTypeMapping;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.dto.ElasticMapping;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableFieldInfoDTO;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableContentListRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.models.rules.response.FilterEnumInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.service.datamanage.DbConfService;
import com.baidu.keyue.deepsight.service.rules.impl.RuleParseServiceImpl;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.client.indices.PutMappingRequest;
import org.elasticsearch.common.unit.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @className EsConfServiceImpl
 * @description es建索引
 * @date 2025/3/5 11:14
 */
@Slf4j
@Service
public class EsConfServiceImpl implements DbConfService {

    @Autowired
    private RestHighLevelClient client;

    @Autowired
    @Lazy
    private TableRecordCommonService commonService;

    @Autowired
    @Lazy
    private DataTableManageServiceImpl tableManageService;

    @Autowired
    private RuleParseServiceImpl ruleParseService;

    @Value("${spring.data.elasticsearch.prefix:dev_}")
    private String indexPrefix;





    @Override
    public void dbCollInit(List<TableFieldInfoDTO> fieldInfos, String indexName) {
        indexName = indexPrefix + indexName;
        GetIndexRequest getIndexRequest = new GetIndexRequest(indexName);
        try {
            Boolean existFlag = client.indices().exists(getIndexRequest,
                    RequestOptions.DEFAULT);
            if (existFlag) {
                log.warn("indexName is exists.");
            }

            ElasticMapping mapping = getElasticMapping(fieldInfos);
            if (mapping == null) {
                mapping = new ElasticMapping();
            }
            if (CollectionUtils.isEmpty(mapping.getKeywords())) {
                mapping.setKeywords(Lists.newArrayList(Constants.CONSTANT_CLUSTER_CODES));
            } else {
                mapping.getKeywords().add(Constants.CONSTANT_CLUSTER_CODES);
            }
            Map<String, Map<String, Map<String, Object>>> mappingMap = new HashMap<>(Constants.MAP_CAPACITY16);
            Map<String, Map<String, Object>> settingMap = new HashMap<>(Constants.MAP_CAPACITY16);
            Map<String, Object> indexMap = new HashMap<>(Constants.MAP_CAPACITY16);
            indexMap.put("max_result_window", 100000000);
            indexMap.put("number_of_shards", 1);
            indexMap.put("number_of_replicas", 3);
            Map<String, Map<String, Object>> typeMap = setTypeMap(mapping);
            mappingMap.put("properties", typeMap);
            settingMap.put("index", indexMap);
            CreateIndexRequest request = new CreateIndexRequest(indexName);
            request.mapping(mappingMap);
            request.settings(settingMap);
            try {
                client.indices().create(request, RequestOptions.DEFAULT);
            } catch (Exception e) {
                log.error("indexName create error " + e.getMessage());
                throw new DeepSightException.EsException(ErrorCode.INTERNAL_ERROR, e.getMessage());
            }
        } catch (Exception e) {
            throw new DeepSightException.EsException(ErrorCode.INTERNAL_ERROR, e.getMessage());
        }




    }

    @Override
    public void dbFieldUpdate(TableFieldInfoDTO fieldInfo, String tableName)  {
        String indexName = indexPrefix + tableName;
        PutMappingRequest request = new PutMappingRequest(indexName);
        ElasticMapping mapping = getElasticMapping(Arrays.asList(fieldInfo));
        Map<String, Map<String, Map<String, Object>>> mappingMap = new HashMap<>(Constants.MAP_CAPACITY16);
        Map<String, Map<String, Object>> typeMap = setTypeMap(mapping);
        mappingMap.put("properties", typeMap);
        request.source(mappingMap);
        try {
            client.indices().putMapping(request, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("es putMapping error " + e.getMessage());
            throw new DeepSightException.EsException(ErrorCode.INTERNAL_ERROR, e.getMessage());
        }


    }

    @Override
    public BasePageResponse.Page<Map<String, String>> search(GetTableContentListRequest request, Boolean includeBaidu) {
        List<RuleFilter> conditions = request.getFilters();
        List<VisibleFieldResponse> visibleFieldResponse = tableManageService.getVisibleFields(request.getDataTableId(), includeBaidu);
        List<Long> fieldIds = null == conditions ? new ArrayList<>() : conditions.stream()
                .map(RuleFilter::getFieldId).toList();
        Map<Long, TableFieldMetaInfo> fieldInfoMap = ruleParseService.getFiledMetaInfoMap(fieldIds);
        List<String> visibleFields = visibleFieldResponse.stream()
                .map(field -> String.format("`%s`", field.getEnName())).toList();
        List<String> sensitiveFields = visibleFieldResponse.stream()
                .filter(field -> TableFieldTagEnum.SENSITIVE.equals(field.getTableFieldTag()))
                .map(VisibleFieldResponse::getEnName).toList();
        Map<String, Map<String, String>> enumFields = visibleFieldResponse.stream()
                .filter(field -> CollectionUtils.isNotEmpty(field.getEnums()))
                .collect(Collectors.toMap(VisibleFieldResponse::getEnName, field ->
                        field.getEnums().stream()
                                .collect(Collectors.toMap(FilterEnumInfo::getKey, FilterEnumInfo::getValue, (k1, k2) -> k2))
                ));
        List<String> fieldEncrys = tableManageService.getTableEncryFields(request.getDataTableId());
        Map<String, VisibleFieldResponse> fieldMap = new HashMap<>();
        visibleFieldResponse.forEach(field -> fieldMap.put(field.getEnName(), field));
        BoolQueryBuilder builder = getBoolQueryBuilder(conditions, fieldInfoMap);
        DataTableInfo info = commonService.getDataTableDetail(request.getDataTableId());
        List<Map<String, Object>> results = new ArrayList<>();
        Long count = 0L;
        try {
            count = searchCount(builder, info.getTableName());
            if (count == 0) {
                return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, new ArrayList<>());
            }
            Pageable pageable = PageRequest.of(request.getPageNo(), request.getPageSize(), Sort.by("updateTime"));
            results = searchList(builder, pageable, info.getTableName());
        } catch (Exception e) {
            log.error("es search error", e);
        }

        // datetime格式转换，加密字段***输出等
        List<Map<String, String>> res = results.stream().map(tableContent -> {
            Map<String, String> lineResult = new HashMap<>();
            for (Map.Entry<String, Object> entry : tableContent.entrySet()) {
                String enName = entry.getKey();
                Object value = entry.getValue();
                // 数据集字段配置为敏感字段，则不展示具体值
                if (sensitiveFields.contains(entry.getKey())) {
                    lineResult.put(entry.getKey(), Constants.ENCRY_FIELD_VIEW);
                } else if (CollectionUtils.isNotEmpty(fieldEncrys) && fieldEncrys.contains(entry.getKey())) {
                    lineResult.put(entry.getKey(), Constants.ENCRY_FIELD_VIEW);
                } else if (enumFields.containsKey(entry.getKey())) {
                    // 枚举是否映射
                    Optional.ofNullable(value).ifPresent(va -> lineResult.put(enName, convertEnum(enumFields, fieldMap, enName, va)));
                } else {
                    lineResult.put(entry.getKey(), getFieldShowValue(entry.getValue()));
                }
            }
            return lineResult;
        }).toList();
        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, res);


    }

    @Override
    public void delete(String indexName, String field, List<String> ids) {
        BulkRequest bulkRequest = new BulkRequest();
        String index = indexPrefix + indexName;
        ids.stream().forEach(id -> bulkRequest.add(new DeleteRequest(index, id)));
        try {
            BulkResponse response = client.bulk(bulkRequest, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("es delete doc error " + e.getMessage());
            throw new DeepSightException.EsException(ErrorCode.INTERNAL_ERROR, e.getMessage());
        }

    }

    @Override
    public void deleteTable(String indexName) {
        indexName = indexPrefix + indexName;
        // 创建删除索引请求
        DeleteIndexRequest request = new DeleteIndexRequest(indexName);
        // 执行删除操作
        try {
            client.indices().delete(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("es delete index error " + e.getMessage());
        }

    }

    public static Map<String, Map<String, Object>> setTypeMap(ElasticMapping mapping) {
        Map<String, Map<String, Object>> typeMap = new HashMap<>(Constants.MAP_CAPACITY16);

        mapping.getKeywords().forEach(field -> typeMap.put(field, keywordMap()));
        if (CollectionUtils.isNotEmpty(mapping.getObjects())) {
            mapping.getObjects().forEach(field -> typeMap.put(field, objectMap()));
        }
        if (CollectionUtils.isNotEmpty(mapping.getNoIndex())) {
            mapping.getNoIndex().forEach(field -> typeMap.put(field, noIndexMap()));
        }
        if (CollectionUtils.isNotEmpty(mapping.getWildcards())) {
            mapping.getWildcards().forEach(field -> typeMap.put(field, wildcardMap()));
        }
        if (CollectionUtils.isNotEmpty(mapping.getIkFields())) {
            mapping.getIkFields().forEach(field -> typeMap.put(field, ikFieldMap()));
        }
        if (CollectionUtils.isNotEmpty(mapping.getIntFields())) {
            mapping.getIntFields().forEach(field -> typeMap.put(field, intMap()));
        }
        if (CollectionUtils.isNotEmpty(mapping.getFloatFields())) {
            mapping.getFloatFields().forEach(field -> typeMap.put(field, floatMap()));
        }
        if (CollectionUtils.isNotEmpty(mapping.getBoolFields())) {
            mapping.getBoolFields().forEach(field -> typeMap.put(field, boolMap()));
        }
        if (CollectionUtils.isNotEmpty(mapping.getDateFields())) {
            mapping.getDateFields().forEach(field -> typeMap.put(field, dateMap()));
        }
        if (CollectionUtils.isNotEmpty(mapping.getMultiFields())) {
            for (String field : mapping.getMultiFields()) {
                typeMap.put(field, textMap());
                Map<String, Object> map = typeMap.get(field);
                map.put("fields", mutiFields());
            }
        }
        typeMap.put(Constants.UPDATE_TIME, longMap());
        return typeMap;
    }

    private static Map<String, Object> longMap() {
        Map<String, Object> keywordMap = new HashMap<>(8);
        keywordMap.put("type", "long");
        return keywordMap;
    }

    private static Map<String, Object> intMap() {
        Map<String, Object> keywordMap = new HashMap<>(8);
        keywordMap.put("type", "integer");
        return keywordMap;
    }

    private static Map<String, Object> boolMap() {
        Map<String, Object> keywordMap = new HashMap<>(8);
        keywordMap.put("type", "boolean");
        return keywordMap;
    }
    private static Map<String, Object> dateMap() {
        Map<String, Object> keywordMap = new HashMap<>(8);
        keywordMap.put("type", "date");
        keywordMap.put("format", "yyyy-MM-dd HH:mm:ss||strict_date_optional_time||epoch_millis");
        return keywordMap;
    }

    private static Map<String, Object> floatMap() {
        Map<String, Object> keywordMap = new HashMap<>(8);
        keywordMap.put("type", "float");
        return keywordMap;
    }

    private static Map<String, Object> textMap() {
        Map<String, Object> keywordMap = new HashMap<>(8);
        keywordMap.put("type", "text");
        return keywordMap;
    }

    private static Map<String, Object> keywordMap() {
        Map<String, Object> keywordMap = new HashMap<>(8);
        keywordMap.put("type", "keyword");
        return keywordMap;
    }

    private static Map<String, Object> objectMap() {
        Map<String, Object> keywordMap = new HashMap<>(8);
        keywordMap.put("type", "object");
        keywordMap.put("enabled", false);
        return keywordMap;
    }

    private static Map<String, Object> wildcardMap() {
        Map<String, Object> keywordMap = new HashMap<>(8);
        keywordMap.put("type", "wildcard");
        return keywordMap;
    }

    private static Map<String, Object> noIndexMap() {
        Map<String, Object> keywordMap = new HashMap<>(8);
        keywordMap.put("enabled", false);
        return keywordMap;
    }

    private static Map<String, Object> ikFieldMap() {
        Map<String, Object> keywordMap = new HashMap<>(8);
        keywordMap.put("type", "text");
        keywordMap.put("analyzer", "ik_max_word");
        keywordMap.put("search_analyzer", "ik_smart");
        return keywordMap;
    }

    private static JSONObject mutiFields() {
        JSONObject mapping = new JSONObject();
        mapping.put("type", "keyword");
        mapping.put("ignore_above", 256);
        JSONObject keywordMap = new JSONObject();
        keywordMap.put("keyword", mapping);

        return keywordMap;
    }

    private ElasticMapping getElasticMapping(List<TableFieldInfoDTO> fieldInfos) {
        ElasticMapping mappingRes = new ElasticMapping();
        List<String> multiFields = new ArrayList<>();
        List<String> keywords = new ArrayList<>();
        List<String> intFields = new ArrayList<>();
        List<String> floatFields = new ArrayList<>();
        List<String> boolFields = new ArrayList<>();
        List<String> dateFields = new ArrayList<>();
        for (TableFieldInfoDTO fieldInfo : fieldInfos) {
            ESToJavaTypeMapping mapping = ESToJavaTypeMapping.getJavaType(fieldInfo.getDataType());
            switch (mapping) {
                case INT:
                    intFields.add(fieldInfo.getEnName());
                    break;
                case FLOAT:
                    floatFields.add(fieldInfo.getEnName());
                    break;
                case STING, STINGS:
                    if (TableFieldTagEnum.SEARCH.getCode().equals(fieldInfo.getFieldTag())) {
                        multiFields.add(fieldInfo.getEnName());
                    } else {
                        keywords.add(fieldInfo.getEnName());
                    }
                    break;
                case DATE:
                    dateFields.add(fieldInfo.getEnName());
                    break;
                case BOOLEAN:
                    boolFields.add(fieldInfo.getEnName());
                    break;
            }
        }
        mappingRes.setMultiFields(multiFields);
        mappingRes.setKeywords(keywords);
        mappingRes.setBoolFields(boolFields);
        mappingRes.setIntFields(intFields);
        mappingRes.setFloatFields(floatFields);
        mappingRes.setDateFields(dateFields);
        return mappingRes;

    }
    /**
     * 获取boolQueryBuilder
     *
     * @param conditions
     * @return
     */
    private static BoolQueryBuilder getBoolQueryBuilder(List<RuleFilter> conditions, Map<Long, TableFieldMetaInfo> map) {
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        RangeQueryBuilder rangeQueryBuilder;
        if (null == conditions) {
            return boolBuilder;
        }
        for (RuleFilter ruleFilter : conditions) {
            String enField = map.get(ruleFilter.getFieldId()).getEnField();
            String param = null;
            List<String> params = new ArrayList<>();
            if (null != ruleFilter.getParams() && ruleFilter.getParams().size() == 1) {
                param =  ruleFilter.getParams().get(0);
            }
            if (CollUtil.isNotEmpty(ruleFilter.getParams())) {
                params.addAll(ruleFilter.getParams());
            }
            switch (ruleFilter.getFunction()) {
                case CONTAIN:
                    if (CollUtil.isNotEmpty(params)) {
                        boolBuilder.must(QueryBuilders.termsQuery(enField, params));
                    }
                    break;
                case NOT_CONTAIN:
                    if (CollUtil.isNotEmpty(params)) {
                        boolBuilder.mustNot(QueryBuilders.termsQuery(enField, params));
                    }
                    break;
                case IS_NULL:
                    boolBuilder.mustNot(QueryBuilders.existsQuery(enField));
                    break;
                case IS_NOT_NULL:
                    boolBuilder.must(QueryBuilders.existsQuery(enField));
                    break;
                case GREATER_EQUALS:
                    rangeQueryBuilder = QueryBuilders.rangeQuery(enField);
                    rangeQueryBuilder.gte(param);
                    boolBuilder.must(rangeQueryBuilder);
                    break;
                case GREATER_THAN:
                    rangeQueryBuilder = QueryBuilders.rangeQuery(enField);
                    rangeQueryBuilder.gt(param);
                    boolBuilder.must(rangeQueryBuilder);
                    break;
                case LESS_EQUALS:
                    rangeQueryBuilder = QueryBuilders.rangeQuery(enField);
                    rangeQueryBuilder.lte(param);
                    boolBuilder.must(rangeQueryBuilder);
                    break;
                case LESS_THAN:
                    rangeQueryBuilder = QueryBuilders.rangeQuery(enField);
                    rangeQueryBuilder.lt(param);
                    boolBuilder.must(rangeQueryBuilder);
                    break;
                case EQUALS:
                    boolBuilder.must(QueryBuilders.termQuery(enField, param));
                    break;
                case NOT_EQUALS:
                    boolBuilder.mustNot(QueryBuilders.termQuery(enField, param));
                    break;
                case LEAST:
                    boolBuilder.must(QueryBuilders.existsQuery(enField));
                    break;
                case BETWEEN:
                    rangeQueryBuilder = QueryBuilders.rangeQuery(enField);
                    rangeQueryBuilder.gt(ruleFilter.getParams().get(0));
                    rangeQueryBuilder.lt(ruleFilter.getParams().get(1));
                    boolBuilder.must(rangeQueryBuilder);
                    break;
            }
        }
        return boolBuilder;
    }

    /**
     * 仅查询总数,不返回数据
     *
     * @param boolBuilder
     * @return
     */
    public long searchCount(BoolQueryBuilder boolBuilder, String indexName) throws IOException {
        indexName = indexPrefix + indexName;
        // 查询总数
        CountRequest countRequest = new CountRequest(indexName);
        countRequest.query(boolBuilder);
        return client.count(countRequest, RequestOptions.DEFAULT).getCount();
    }

    /**
     * 仅查询数据,不返回总数
     *
     * @param boolBuilder
     * @param pageable
     * @return
     */
    public List<Map<String, Object>> searchList(BoolQueryBuilder boolBuilder, Pageable pageable, String indexName)
            throws IOException {
        indexName = indexPrefix + indexName;
        List<Map<String, Object>> dataList = Lists.newArrayList();
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.trackTotalHits(true);
        sourceBuilder.query(boolBuilder);
        sourceBuilder.from((pageable.getPageNumber() - 1)  * pageable.getPageSize());
        sourceBuilder.size(pageable.getPageSize());
        sourceBuilder.timeout(TimeValue.timeValueMinutes(5L));
        SearchRequest searchRequest = new SearchRequest(indexName);
        searchRequest.source(sourceBuilder);
        SearchResponse response = client.search(searchRequest, RequestOptions.DEFAULT);
        // 返回搜索结果
        if (response != null) {
            SearchHits searchHits = response.getHits();
            for (SearchHit hit : searchHits) {
                Map<String, Object> mutableMap = new HashMap<>(hit.getSourceAsMap());
                mutableMap.put("_id", hit.getId());
                dataList.add(mutableMap);
            }
        }
        return dataList;
    }

    public String getFieldShowValue(Object value) {
        if (Objects.isNull(value)) {
            return StringUtils.EMPTY;
        }

        if (value instanceof LocalDateTime dateTime) {
            return DatetimeUtils.formatDate(dateTime);
        }

        return String.valueOf(value);
    }

    /**
     * 转换枚举
     * 需要展示枚举value是转换枚举值
     *
     * @param enumFields 枚举字段集合
     * @param fieldMap   表可见字段
     * @param enName     字段英文名
     * @param value      数据值
     * @return
     */
    public String convertEnum(Map<String, Map<String, String>> enumFields, Map<String, VisibleFieldResponse> fieldMap, String enName, Object value) {
        VisibleFieldResponse fieldResponse = fieldMap.get(enName);
        Boolean isShowValue = fieldResponse.getIsShowValue();

        Map<String, String> enumMap = enumFields.get(enName);
        // 如果无需展示value，直接返回业务数据
        if (Objects.equals(Boolean.FALSE, isShowValue) || CollUtil.isEmpty(enumMap)) {
            return value.toString();
        }
        try {
            // 如果是多值枚举
            String dataStr = value.toString();
            if (JSONUtil.isTypeJSONArray(dataStr)) {
                List<String> collect = JSONUtil.toList(dataStr, String.class).stream()
                        .map(obj -> enumMap.get(obj.toString()))
                        .filter(StrUtil::isNotBlank)
                        .collect(Collectors.toList());
                return JSONUtil.toJsonStr(collect);
            } else {
                // 单值枚举
                return enumMap.get(value.toString());
            }
        } catch (Exception e) {
            log.error("content list convertEnum Exception:", e);
        }
        return value.toString();
    }



}
