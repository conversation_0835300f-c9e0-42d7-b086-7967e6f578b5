package com.baidu.keyue.deepsight.service.diffusion;

import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.baidu.keyue.deepsight.models.diffusion.result.request.CharacteristicAnalyseRequest;
import com.baidu.keyue.deepsight.models.diffusion.result.request.CustomerDiffusionContentListRequest;
import com.baidu.keyue.deepsight.models.diffusion.result.request.DeleteDiffusionResultRequest;
import com.baidu.keyue.deepsight.models.diffusion.result.request.SamplingContractRequest;
import com.baidu.keyue.deepsight.models.diffusion.result.response.CharacteristicAnalyseResponse;
import com.baidu.keyue.deepsight.models.diffusion.result.response.SamplingContractResponse;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;

import java.util.List;
import java.util.Map;

/**
 * @className: DiffusionResultService
 * @description: 扩散结果服务
 * @author: wangzhongcheng
 * @date: 2025/3/25 17:14
 */
public interface DiffusionResultService {

    /**
     * 获取扩散结果可见字段: 基础用户表字段+扩散结果得分字段
     *
     * @param customerDiffusionTaskId 扩散任务ID
     * @return List<VisibleFieldResponse> 可见字段
     */
    List<VisibleFieldResponse> getVisibleFields(Long customerDiffusionTaskId);

    /**
     * 获取扩散结果过滤字段
     *
     * @param customerDiffusionTaskId 扩散任务ID
     * @return List<DatasetPropertiesResult> 过滤字段
     */
    List<DatasetPropertiesResult> getFilterFields(Long customerDiffusionTaskId);

    /**
     * 扩散结果内容列表
     * @param request 扩散结果内容列表请求
     * @return BasePageResponse.Page<Map < String, String>> 扩散结果内容列表
     */
    BasePageResponse.Page<Map<String, String>> contentList(CustomerDiffusionContentListRequest request);

    /**
     * 删除扩散结果
     *
     * @param request 删除扩散结果请求
     */
    void deleteDiffusionResult(DeleteDiffusionResultRequest request);

    /**
     * 人群扩散结果特征分析
     *
     * @param request 特征分析请求
     * @return CharacteristicAnalyseResponse 特征分析结果
     */
    CharacteristicAnalyseResponse characteristicAnalyse(CharacteristicAnalyseRequest request);

    /**
     * 抽样特征对比
     *
     * @param request 抽样特征对比请求
     * @return SamplingContractResponse 抽样特征对比结果
     */
    SamplingContractResponse samplingCharacteristicContract(SamplingContractRequest request);

    /**
     * 检查人群扩散任务是否存在；如果任务不存在，则抛出异常；如果任务存在，则返回任务对象
     * @param customerDiffusionTaskId 人群扩散任务 id
     * @return 人群扩散任务
     */
    CustomerDiffusionTask checkCustomerDiffusionTask(Long customerDiffusionTaskId);

    /**
     * 检查租户权限
     */
    void checkTenantPermission(Long customerDiffusionTaskId);

    /**
     * 查询人群扩散最近成功的调度记录
     * @param customerDiffusionTaskId 人群扩散任务id
     * @param taskId 调度任务 id
     * @return 任务调度记录
     */
    TaskSchedulerWithBLOBs getLatestSuccessTaskSchedulerRecord(long customerDiffusionTaskId, long taskId);

    /**
     * 更新人群扩散任务的客群信息
     * @param customerDiffusionTaskId 人群扩散任务 id
     * @param customerGroupId 客群 id
     */
    void updateDiffusionTaskCustomerGroupInfo(long customerDiffusionTaskId, long customerGroupId);
}
