package com.baidu.keyue.deepsight.service.tenant;

import cn.hutool.core.collection.CollUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.IdMappingRuleFiledTypeEnum;
import com.baidu.keyue.deepsight.enums.IdMappingRuleMergePolicyEnum;
import com.baidu.keyue.deepsight.enums.PresetEnum;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.idmapping.dto.IdMappingRelFieldDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTable;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTableCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelation;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelationCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendIdMappingRuleMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingDataTableMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRelationMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRuleMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.idmapping.impl.IdMappingManagerServiceImpl;
import com.baidu.keyue.deepsight.service.idmapping.impl.IdMappingRelServiceImpl;
import com.baidu.keyue.deepsight.service.idmapping.impl.IdMappingRuleServiceImpl;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName TenantV3UpgradeHandler
 * @Description v1.1.2租户升级--初始化idmapping
 * <AUTHOR>
 * @Date 2025/7/8 5:33 PM
 */
@Slf4j
@Service
@Order(5)
public class TenantV4UpgradeHandler extends TenantUpgradeHandler {

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private TenantInfoMapper tenantInfoMapper;
    
    @Resource
    @Lazy
    private TenantInfoService tenantInfoService;
    
    @Resource
    private IdMappingManagerServiceImpl idMappingManagerService;
    
    @Resource
    private DataTableManageService dataTableManageService;
    
    @Resource
    private IdMappingRuleServiceImpl idMappingRuleService;
    
    @Resource
    private IdMappingRelationMapper idMappingRelMapper;
    
    @Resource
    private IdMappingRelServiceImpl idMappingRelService;
    
    @Resource
    private ExtendIdMappingRuleMapper extendIdMappingRuleMapper;
    
    @Resource
    private IdMappingDataTableMapper idMappingDataTableMapper;
    
    @Resource
    private IdMappingRuleMapper idMappingRuleMapper;
    
    @Resource
    private TenantV5UpgradeHandler v5UpgradeHandler;

    /**
     * ID对字段
     */
    private final List<String> idFields = List.of("taskId", "mobile");


    @PostConstruct
    public void init() throws IOException {
        setVersion(5);
        addNextHandler(v5UpgradeHandler);
    }

    public TenantV4UpgradeHandler(DorisService dorisService) {
        super(dorisService);
    }

    @Override
    protected boolean needUpdateTenant(TenantInfo tenant) {
        // 再次检查版本
        TenantInfo tenantInfo = tenantInfoService.queryTenantInfo(tenant.getTenantid());
        tenant.setUserId(tenantInfo.getUserId());
        Integer version = tenantInfo.getVersion();
        return Objects.equals(version, 4);
    }

    @Override
    protected void executeUpgrade(TenantDTO tenant) {
        transactionTemplate.execute(status -> {
            try {
                TenantInfo info = tenant.getTenantInfo();
                String userId = Constants.SYSTEM_DEFAULT_USER_ID;
                String tenantId = info.getTenantid();
                // 关闭ID mapping 任务
                idMappingManagerService.setSwitchStatus(false, tenantId);
                log.info("v1.1.2.1 {} id mapping task close", tenantId);
                // 添加预置ID对
                addIdRelation(userId, tenantId);
                log.info("v1.1.2.1 {} addIdRelation over", tenantId);
                try {
                    // 开启ID mapping任务，允许开启失败
                    idMappingManagerService.setSwitchStatus(true, tenantId);
                    log.info("v1.1.2.1 {} id mapping task open", tenantId);
                } catch (Exception e) {
                    log.info("v1.1.2.1 {} id mapping task open fail", tenantId, e);
                }
                // 更新责任链租户信息
                info.setVersion(version);
                // 更新数据库租户版本
                TenantInfo tenantInfo = new TenantInfo();
                tenantInfo.setVersion(version);
                tenantInfo.setId(info.getId());
                tenantInfo.setUpdateTime(new Date());
                tenantInfoMapper.updateByPrimaryKeySelective(tenantInfo);
            } catch (Exception e) {
                log.error("tenant v1.1.2.1 addIdRelation Upgrade error!, tenantId:{} ", tenant.getTenantId(), e);
                status.setRollbackOnly();
                throw new DeepSightException.InitTenantFailedException(ErrorCode.INTERNAL_ERROR, "租户v1.1.2版本升级失败");
            }
            return null;
        });
    }

    /**
     * 预置ID对
     * @param userId
     * @param tenantId
     */
    public void addIdRelation(String userId, String tenantId) {
        String tableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DataTableInfo dataTableInfo = dataTableManageService.validDataTableByTableName(tableName, tenantId);
        Long dataTableId = dataTableInfo.getId();
        IdMappingRelationCriteria idMappingRelationCriteria = new IdMappingRelationCriteria();
        idMappingRelationCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDataTableIdEqualTo(dataTableId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<IdMappingRelation> idMappingRelations = idMappingRelMapper.selectByExample(idMappingRelationCriteria);
        IdMappingRelation idMappingRelation;
        if (CollUtil.isEmpty(idMappingRelations)) {
            // 插入数据集ID对记录
            Date date = new Date();
            idMappingRelation = new IdMappingRelation();
            idMappingRelService.assembleIdMappingRel(dataTableId, idFields, idMappingRelation);
            idMappingRelation.setDataTableId(dataTableId);
            idMappingRelation.setCreator(userId);
            idMappingRelation.setModifier(userId);
            idMappingRelation.setTenantId(tenantId);
            idMappingRelation.setCreateTime(date);
            idMappingRelation.setUpdateTime(date);
            idMappingRelation.setDel(DelEnum.NOT_DELETED.getBoolean());
            idMappingRelation.setPreset(PresetEnum.PRESET.getBoolean());
            idMappingRelMapper.insertSelective(idMappingRelation);
        } else {
            IdMappingRelation relation = idMappingRelations.get(0);
            // 更新ID对
            Date date = new Date();
            idMappingRelService.assembleIdMappingRel(dataTableId, idFields, relation);
            relation.setModifier(userId);
            relation.setUpdateTime(date);
            idMappingRelMapper.updateByPrimaryKeySelective(relation);
            idMappingRelation = relation;
        }
        // 添加id映射配置
        autoAddIdMappingRule(idMappingRelation, tenantId);
        // 刷新ID对的数据集到oneId刷新列表
        autoAddIdMappingDataTable(idMappingRelation, userId);
        // 调整mobile权重，同时刷新字段值为单值
        List<IdMappingRule> idMappingRules = idMappingRuleService.getExistedFields(tenantId);
        Map<Integer, IdMappingRule> pMap = idMappingRules.stream()
                .collect(Collectors.toMap(IdMappingRule::getPriority, map -> map, (k1, k2) -> k2));
        for (IdMappingRule rule : idMappingRules) {
            if (Objects.equals("mobile", rule.getEnField())) {
                // 如果原始包含1排序，且不为mobile，给原排序1的降级
                if (pMap.containsKey(1) && !Objects.equals(pMap.get(1).getEnField(), "mobile")) {
                    IdMappingRule oldSort = pMap.get(1);
                    int priority = 2;
                    while (pMap.containsKey(priority)) {
                        priority++;
                    }
                    oldSort.setPriority(priority);
                    oldSort.setUpdateTime(new Date());
                    oldSort.setModifier(userId);
                    idMappingRuleMapper.updateByPrimaryKeySelective(oldSort);
                    log.info("change old sort:{}", oldSort.getId());
                }
                IdMappingRule idMappingRule = new IdMappingRule();
                idMappingRule.setId(rule.getId());
                idMappingRule.setPriority(1);
                idMappingRule.setUpdateTime(new Date());
                idMappingRule.setModifier(userId);
                idMappingRuleMapper.updateByPrimaryKeySelective(idMappingRule);
            }
        }
    }

    public void autoAddIdMappingRule(IdMappingRelation idMappingRelation, String tenantId) {
        int priority = Constants.ID_MAPPING_MAX_PRIORITY;
        List<IdMappingRule> needAddIdMappingRule = new ArrayList<>();
        List<IdMappingRelFieldDTO> idMappingRelFieldDTOS = IdMappingRelFieldDTO.convertFrom(idMappingRelation);
        // 获取该租户下所有已配置字段
        List<IdMappingRule> idMappingRules = idMappingRuleService.getExistedFields(tenantId);
        Set<Integer> priorities = idMappingRules.stream().map(IdMappingRule::getPriority).collect(Collectors.toSet());
        List<String> alreadyExistEnFields = idMappingRules.stream().map(IdMappingRule::getEnField).toList();
        Date date = new Date();
        for (IdMappingRelFieldDTO idMappingRelFieldDTO : idMappingRelFieldDTOS) {
            String enField = idMappingRelFieldDTO.getTableEnField();
            if (alreadyExistEnFields.contains(enField)) {
                continue;
            }
            IdMappingRule idMappingRule = new IdMappingRule();
            while (priorities.contains(priority)) {
                priority--;
            }
            priorities.add(priority);
            // 如果设置为负数 客户又没有设置优先级会在保存的时候统一报错
            idMappingRule.setPriority(Math.max(Constants.ID_MAPPING_INVALID_PRIORITY, priority));
            idMappingRule.setMergePolicy(IdMappingRuleMergePolicyEnum.SAVE_NEWEST.getCode());
            idMappingRule.setFieldType(IdMappingRuleFiledTypeEnum.SINGLE_VALUE.getCode());
            idMappingRule.setPreset(PresetEnum.PRESET.getBoolean());
            idMappingRule.setDescription(StringUtils.EMPTY);
            idMappingRule.setTenantId(tenantId);
            idMappingRule.setEnField(idMappingRelFieldDTO.getTableEnField());
            idMappingRule.setCnField(idMappingRelFieldDTO.getTableCnField());
            idMappingRule.setCreator(Constants.SYSTEM_DEFAULT_USER_ID);
            idMappingRule.setModifier(Constants.SYSTEM_DEFAULT_USER_ID);
            idMappingRule.setCreateTime(date);
            idMappingRule.setUpdateTime(date);
            needAddIdMappingRule.add(idMappingRule);
        }
        if (CollectionUtils.isEmpty(needAddIdMappingRule)) {
            return;
        }
        List<String> needAddEnField = needAddIdMappingRule.stream().map(IdMappingRule::getEnField).toList();
        Map<String, String> descriptionMap = idMappingRuleService.getDefaultDesc(needAddEnField, idMappingRelation.getDataTableId());
        needAddIdMappingRule.forEach(idMappingRule ->
                idMappingRule.setDescription(descriptionMap.getOrDefault(idMappingRule.getEnField(), StringUtils.EMPTY)));
        extendIdMappingRuleMapper.batchInsert(needAddIdMappingRule);
    }

    public void autoAddIdMappingDataTable(IdMappingRelation idMappingRelation, String userId) {
        // 获取该租户下所有已存在的待刷新数据集
        IdMappingDataTableCriteria mappingDataTableCriteria = new IdMappingDataTableCriteria();
        mappingDataTableCriteria.createCriteria()
                .andDataTableIdEqualTo(idMappingRelation.getDataTableId())
                .andTenantIdEqualTo(idMappingRelation.getTenantId());
        List<IdMappingDataTable> idMappingTables = idMappingDataTableMapper.selectByExample(mappingDataTableCriteria);
        if (!idMappingTables.isEmpty()) {
            IdMappingDataTable idMappingDataTable = idMappingTables.get(0);
            idMappingDataTable.setModifier(userId);
            idMappingDataTable.setUpdateTime(idMappingRelation.getUpdateTime());
            idMappingDataTable.setDel(DelEnum.NOT_DELETED.getBoolean());
            idMappingDataTableMapper.updateByPrimaryKeySelective(idMappingDataTable);
            return;
        }
        IdMappingDataTable idMappingDataTable = new IdMappingDataTable();
        idMappingDataTable.setPreset(PresetEnum.PRESET.getBoolean());
        idMappingDataTable.setDel(DelEnum.NOT_DELETED.getBoolean());
        idMappingDataTable.setCreator(userId);
        idMappingDataTable.setModifier(userId);
        idMappingDataTable.setTenantId(idMappingRelation.getTenantId());
        idMappingDataTable.setCreateTime(idMappingRelation.getUpdateTime());
        idMappingDataTable.setUpdateTime(idMappingRelation.getUpdateTime());
        idMappingDataTable.setDataTableId(idMappingRelation.getDataTableId());
        idMappingDataTableMapper.insert(idMappingDataTable);
    }
}
