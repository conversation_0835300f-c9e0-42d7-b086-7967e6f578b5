package com.baidu.keyue.deepsight.service.label;

import com.baidu.keyue.deepsight.models.label.GetLabelDetailRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.Label;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

/**
 * 标签执行（BSC）模式
 * */
public interface LabelCalculateService {

    /**
     * 手动执行，由前端用户触发
     */
    void execByManual(GetLabelDetailRequest request);

    /**
     * 定时任务触发
     */
    void execByScheduler(LabelWithBLOBs label, TaskInfo task);

    /**
     * 查询待执行标签及其任务
     * @return
     */
    List<Pair<LabelWithBLOBs, TaskInfo>> pullWaitExecLabelTask();
    

    /**
     * 查询最近删除的标签
     * @param seconds 时间范围
     * @return
     */
    List<Label> queryDeletedLabel(Integer seconds);

    /**
     * 已删除标签字段清理
     * @param label
     */
    void invalidLabelDorisFieldClear(Label label);

    /**
     * 手动取消标签任务
     * @param label 标签
     */
    void cancelExecByManual(Label label);
}
