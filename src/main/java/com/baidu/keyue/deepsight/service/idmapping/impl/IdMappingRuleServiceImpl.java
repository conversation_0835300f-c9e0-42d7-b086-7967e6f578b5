package com.baidu.keyue.deepsight.service.idmapping.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.IdMappingRuleFiledTypeEnum;
import com.baidu.keyue.deepsight.enums.IdMappingRuleMergePolicyEnum;
import com.baidu.keyue.deepsight.enums.PresetEnum;
import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.idmapping.dto.IdMappingRelFieldDTO;
import com.baidu.keyue.deepsight.models.idmapping.request.rule.CreateIdMappingRuleRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.rule.DeleteIdMappingRuleRequest;
import com.baidu.keyue.deepsight.models.idmapping.response.rule.IdMappingRuleFieldsResponse;
import com.baidu.keyue.deepsight.models.idmapping.response.rule.IdMappingRuleItemResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelation;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelationCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRuleCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendIdMappingRuleMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRelationMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRuleMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingRuleService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @className: IdMappingRuleServiceImpl
 * @description:
 * @author: wangzhongcheng
 * @date: 2025/3/11 16:02
 */
@Slf4j
@Service
public class IdMappingRuleServiceImpl implements IdMappingRuleService {

    @Autowired
    private IdMappingRuleMapper idMappingRuleMapper;

    @Autowired
    private ExtendIdMappingRuleMapper extendIdMappingRuleMapper;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    private IdMappingRelationMapper idMappingRelMapper;


    private static final Map<String, IdMappingRuleItemResponse> PREDSET_FIELDS = Map.ofEntries(
            Map.entry("user_id", new IdMappingRuleItemResponse(
                "user_id", "业务侧实际注册产生的用户ID",IdMappingRuleFiledTypeEnum.SINGLE_VALUE, 0
            )),
            Map.entry("BAIDUID", new IdMappingRuleItemResponse(
                    "BAIDUID", "保存在用户的浏览器cookie的baidu域中，作为cookie的唯一标示，数据格式是32位16进制",
                    IdMappingRuleFiledTypeEnum.SINGLE_VALUE, 3
            )),
            Map.entry("UNIONID", new IdMappingRuleItemResponse(
                    "UNIONID", "用户在微信开放平台上的唯一标识，包括小程序、公众号等",
                    IdMappingRuleFiledTypeEnum.SINGLE_VALUE, 5
            )),
            Map.entry("mobile", new IdMappingRuleItemResponse(
                "mobile", "手机号",IdMappingRuleFiledTypeEnum.MULTI_VALUE, 10
            )),
            Map.entry("CUID", new IdMappingRuleItemResponse(
        "CUID", "百度定义移动设备唯一标示",IdMappingRuleFiledTypeEnum.MULTI_VALUE, 20
            )),
            Map.entry("DEVICEID", new IdMappingRuleItemResponse(
                    "DEVICEID", "客户侧通过设备标识计算出来的设备指纹",
                    IdMappingRuleFiledTypeEnum.MULTI_VALUE, 30
            )),
            Map.entry("IMEI", new IdMappingRuleItemResponse(
                    "IMEI", "国际移动设备身份码",IdMappingRuleFiledTypeEnum.MULTI_VALUE, 40
            )),
            Map.entry("MAC", new IdMappingRuleItemResponse(
                    "MAC", "Mac 地址",IdMappingRuleFiledTypeEnum.MULTI_VALUE, 50
            )),
            Map.entry("IDFA", new IdMappingRuleItemResponse(
                    "IDFA", "广告主标识符，即每台 iOS 设备独有的字母和数字组合",
                    IdMappingRuleFiledTypeEnum.MULTI_VALUE, 60
            )),
            Map.entry("OAID", new IdMappingRuleItemResponse(
                    "OAID", "匿名设备标识符, 是《移动智能终端补充设备标识体系规范》中的一种 Android 平台的设备标识",
                    IdMappingRuleFiledTypeEnum.MULTI_VALUE, 70
            )),
            Map.entry("anonymous_id", new IdMappingRuleItemResponse(
                    "anonymous_id", "SDK 随机生成的匿名访客标识",
                    IdMappingRuleFiledTypeEnum.MULTI_VALUE, 90
            ))
    );

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void createIdMappingRule(CreateIdMappingRuleRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();
        String enField = request.getEnField();

        if (getFieldByEnName(enField) != null) {
            log.error("添加抽取字段失败，待添加字段已存在 enField: {}", enField);
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "待添加字段已存在");
        }

        List<IdMappingRule> idMappingRules = getExistedFields(tenantId);
        Set<Integer> priorities = idMappingRules.stream().map(IdMappingRule::getPriority).collect(Collectors.toSet());

        if (priorities.contains(request.getPriority())) {
            log.error("添加抽取字段失败，待添加字段优先级已存在 enField: {}", enField);
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "待添加字段优先级已存在");
        }

        Date date = new Date();
        IdMappingRule idMappingRule = new IdMappingRule();
        idMappingRule.setTenantId(tenantId);
        idMappingRule.setEnField(request.getEnField());
        idMappingRule.setCnField(request.getCnField());
        idMappingRule.setCreator(userId);
        idMappingRule.setModifier(userId);
        idMappingRule.setCreateTime(date);
        idMappingRule.setUpdateTime(date);
        idMappingRule.setDescription(request.getDescription());
        idMappingRule.setPreset(PREDSET_FIELDS.containsKey(enField));
        idMappingRule.setPriority(request.getPriority());
        idMappingRule.setFieldType(request.getFieldType().byteValue());
        idMappingRule.setMergePolicy(request.getMergePolicy().byteValue());
        idMappingRuleMapper.insert(idMappingRule);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void updateIdMappingRule(CreateIdMappingRuleRequest request) {
        String userId = WebContextHolder.getUserId();
        String tenantId = WebContextHolder.getTenantId();
        String enField = request.getEnField();
        Long id = request.getId();

        if (id == null) {
            log.error("更新抽取字段失败，待修改字段id为空");
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "待修改字段id为空");
        }
        IdMappingRule idMappingRule = idMappingRuleMapper.selectByPrimaryKey(id);

        if (idMappingRule == null || !Objects.equals(tenantId, idMappingRule.getTenantId())) {
            log.error("更新抽取字段失败，待修改字段id不存在 id: {}", id);
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "待修改字段id不存在");
        }

        List<IdMappingRule> idMappingRules = getExistedFields(tenantId);

        Set<Integer> priorities = idMappingRules.stream().filter(
                rule -> !rule.getEnField().equals(enField)
        ).map(IdMappingRule::getPriority).collect(Collectors.toSet());

        if (priorities.contains(request.getPriority())) {
            log.error("更新抽取字段失败，待添加字段优先级已存在 enField: {}", enField);
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "待更新字段优先级已存在");
        }

        Date date = new Date();
        idMappingRule.setTenantId(tenantId);
        idMappingRule.setModifier(userId);
        idMappingRule.setUpdateTime(date);
        idMappingRule.setDescription(request.getDescription());
        idMappingRule.setPriority(request.getPriority());
        idMappingRule.setFieldType(request.getFieldType().byteValue());
        idMappingRule.setMergePolicy(request.getMergePolicy().byteValue());
        idMappingRuleMapper.updateByPrimaryKeySelective(idMappingRule);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void deleteIdMappingRule(DeleteIdMappingRuleRequest request) {
        String tenantId = WebContextHolder.getTenantId();
        String enField = request.getEnField();

        // 预设字段无法通过前端页面删除 这里加一个条件防止通过接口删除 不进行报错
        IdMappingRuleCriteria idMappingRuleCriteria = new IdMappingRuleCriteria();
        idMappingRuleCriteria.createCriteria().
                andEnFieldEqualTo(enField).
                andPresetEqualTo(PresetEnum.NOT_PRESET.getBoolean()).
                andTenantIdEqualTo(tenantId);

        idMappingRuleMapper.deleteByExample(idMappingRuleCriteria);
    }

    @Override
    public BasePageResponse.Page<IdMappingRuleItemResponse> listIdMappingRule(BasePageRequest request) {
        String tenantId = WebContextHolder.getTenantId();

        IdMappingRuleCriteria idMappingRuleCriteria = new IdMappingRuleCriteria();
        idMappingRuleCriteria.createCriteria().andTenantIdEqualTo(tenantId);

        long count = idMappingRuleMapper.countByExample(idMappingRuleCriteria);
        if (count == 0) {
            return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), 0L, Lists.newArrayList());
        }

        // 翻页组件
        PageHelper.startPage(request.getPageNo(), request.getPageSize());

        idMappingRuleCriteria.setOrderByClause(Constants.ORDER_BY_PRIORITY_ASC);
        List<IdMappingRule> idMappingRules = idMappingRuleMapper.selectByExample(idMappingRuleCriteria);

        List<IdMappingRuleItemResponse> resultList = idMappingRules.stream()
                .map(IdMappingRuleItemResponse::new).toList();

        return BasePageResponse.Page.of(request.getPageNo(), request.getPageSize(), count, resultList);

    }

    @Override
    public IdMappingRuleFieldsResponse listField(Map<String, String> enFieldtoCnFieldMap) {
        String tenantId = WebContextHolder.getTenantId();
        List<IdMappingRule> idMappingRules = getExistedFields(tenantId);
        List<IdMappingRuleItemResponse> resultList = new ArrayList<>();

        Set<String> existFields = idMappingRules.stream().map(IdMappingRule::getEnField).collect(Collectors.toSet());
        Set<Integer> priorities = idMappingRules.stream().map(IdMappingRule::getPriority).collect(Collectors.toSet());

        Set<String> unExistFields = enFieldtoCnFieldMap.keySet().stream()
                .filter(enField -> !existFields.contains(enField)).collect(Collectors.toSet());

        IdMappingRelationCriteria idMappingRelCriteria = new IdMappingRelationCriteria();
        idMappingRelCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<IdMappingRelation> idMappingRelations = idMappingRelMapper.selectByExample(idMappingRelCriteria);

        Map<Long, List<String>> dataTableIdenFieldsMap = new HashMap<>();
        for (IdMappingRelation idMappingRel : idMappingRelations) {
            List<String> enFields = JsonUtils.toListUnchecked(idMappingRel.getEnFields(), List.class, String.class);
            // 跳过字段都配置好了的表
            if (enFields.stream().anyMatch(unExistFields::contains)){
                dataTableIdenFieldsMap.put(idMappingRel.getDataTableId(), enFields);
            }
        }
        Map<String, String> enfieldDescMap = new HashMap<>();
        for (Map.Entry<Long, List<String>> entry : dataTableIdenFieldsMap.entrySet()) {
            Long dataTableId = entry.getKey();
            List<String> enFields = entry.getValue();
            Map<String, String> descriptionMap = getDefaultDesc(enFields, dataTableId);
            enfieldDescMap.putAll(descriptionMap);
        }


        for (String enField : unExistFields) {
            IdMappingRuleItemResponse response = new IdMappingRuleItemResponse(
                    null,
                    enField,
                    enFieldtoCnFieldMap.get(enField),
                    enfieldDescMap.getOrDefault(enField, ""),
                    IdMappingRuleMergePolicyEnum.SAVE_NEWEST.getInteger(),
                    IdMappingRuleFiledTypeEnum.SINGLE_VALUE.getInteger(),
                    Constants.ID_MAPPING_INVALID_PRIORITY,
                    PresetEnum.NOT_PRESET.getBoolean(),
                    null
            );
            resultList.add(response);
        }
        return new IdMappingRuleFieldsResponse(resultList, priorities.stream().toList());
    }

    /**
     * 初始化默认抽取字段规则
     */
    @Override
    @Transactional(rollbackOn = Exception.class)
    public void initDefaultIdMappingRule(String tenantId, Map<String, String> enFieldtoCnFieldMap) {
        List<IdMappingRule> needAddIdMappingRule = new ArrayList<>();
        Date date = new Date();
        for (Map.Entry<String, String> entry : enFieldtoCnFieldMap.entrySet()) {
            String enField = entry.getKey();
            String cnField = entry.getValue();
            IdMappingRuleItemResponse presetField = PREDSET_FIELDS.get(enField);
            if (presetField == null) {
                continue;
            }
            IdMappingRule idMappingRule = new IdMappingRule();

            idMappingRule.setEnField(enField);
            idMappingRule.setCnField(cnField);
            idMappingRule.setCreateTime(date);
            idMappingRule.setUpdateTime(date);
            idMappingRule.setTenantId(tenantId);
            idMappingRule.setCreator(Constants.SYSTEM_DEFAULT_USER_ID);
            idMappingRule.setModifier(Constants.SYSTEM_DEFAULT_USER_ID);
            idMappingRule.setPreset(PresetEnum.PRESET.getBoolean());
            idMappingRule.setDescription(presetField.getDescription());
            idMappingRule.setMergePolicy(presetField.getMergePolicy().byteValue());
            idMappingRule.setFieldType(presetField.getFieldType().byteValue());
            idMappingRule.setPriority(presetField.getPriority());

            needAddIdMappingRule.add(idMappingRule);

        }
        if (CollectionUtils.isEmpty(needAddIdMappingRule)) {
            log.error("初始化默认抽取字段规则失败，待添加字段为空");
            return;
        }
        extendIdMappingRuleMapper.batchInsert(needAddIdMappingRule);
    }


    /**
     * 自动添加id对规则，已存在的不添加
     */
    @Transactional(rollbackOn = Exception.class)
    public void autoAddIdMappingRule(IdMappingRelation idMappingRelation) {
        String tenantId = WebContextHolder.getTenantId();
        int priority = Constants.ID_MAPPING_MAX_PRIORITY;
        List<IdMappingRule> needAddIdMappingRule = new ArrayList<>();
        List<IdMappingRelFieldDTO> idMappingRelFieldDTOS = IdMappingRelFieldDTO.convertFrom(idMappingRelation);

        // 获取该租户下所有已配置字段
        List<IdMappingRule> idMappingRules = getExistedFields(tenantId);
        Set<Integer> priorities = idMappingRules.stream().map(IdMappingRule::getPriority).collect(Collectors.toSet());

        List<String> alreadyExistEnFields = idMappingRules.stream().map(IdMappingRule::getEnField).toList();
        Date date = new Date();
        for (IdMappingRelFieldDTO idMappingRelFieldDTO : idMappingRelFieldDTOS) {
            String enField = idMappingRelFieldDTO.getTableEnField();
            if (alreadyExistEnFields.contains(enField)) {
                continue;
            }

            IdMappingRule idMappingRule = new IdMappingRule();
            while (priorities.contains(priority)) {
                priority--;
            }
            priorities.add(priority);

            // 如果设置为负数 客户又没有设置优先级会在保存的时候统一报错
            idMappingRule.setPriority(Math.max(Constants.ID_MAPPING_INVALID_PRIORITY, priority));
            idMappingRule.setPreset(PresetEnum.NOT_PRESET.getBoolean());
            idMappingRule.setMergePolicy(IdMappingRuleMergePolicyEnum.SAVE_NEWEST.getCode());
            idMappingRule.setFieldType(IdMappingRuleFiledTypeEnum.SINGLE_VALUE.getCode());
            idMappingRule.setPreset(PresetEnum.NOT_PRESET.getBoolean());
            idMappingRule.setDescription(StringUtils.EMPTY);
            idMappingRule.setTenantId(WebContextHolder.getTenantId());
            idMappingRule.setEnField(idMappingRelFieldDTO.getTableEnField());
            idMappingRule.setCnField(idMappingRelFieldDTO.getTableCnField());
            idMappingRule.setCreator(Constants.SYSTEM_DEFAULT_USER_ID);
            idMappingRule.setModifier(Constants.SYSTEM_DEFAULT_USER_ID);
            idMappingRule.setCreateTime(date);
            idMappingRule.setUpdateTime(date);
            needAddIdMappingRule.add(idMappingRule);
        }
        if (CollectionUtils.isEmpty(needAddIdMappingRule)) {
            return;
        }

        List<String> needAddEnField = needAddIdMappingRule.stream().map(IdMappingRule::getEnField).toList();
        Map<String, String> descriptionMap = getDefaultDesc(needAddEnField, idMappingRelation.getDataTableId());
        needAddIdMappingRule.forEach(idMappingRule ->
                idMappingRule.setDescription(descriptionMap.getOrDefault(idMappingRule.getEnField(), StringUtils.EMPTY)));
        extendIdMappingRuleMapper.batchInsert(needAddIdMappingRule);
    }

    @Override
    public void validateIdMappingRule(String tenantId, Map<String, String> enFieldtoCnFieldMap) {
        List<IdMappingRule> idMappingRules = getExistedFields(tenantId);
        // 按照错误的优先级来进行校验
        // 1. 校验是否存在不合法的字段
        for (IdMappingRule idMappingRule : idMappingRules) {
            String enField = idMappingRule.getEnField();
            String cnField = idMappingRule.getCnField();
            if (!enFieldtoCnFieldMap.containsKey(enField)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR,
                        String.format("字段 %s(%s) 无法从任何数据集提取, 请删除", cnField, enField));
            }
        }

        // 2. 校验是否存在不合法的优先级
        Set<Integer> properties = new HashSet<>();
        for (IdMappingRule idMappingRule : idMappingRules) {
            Integer priority = idMappingRule.getPriority();
            String enField = idMappingRule.getEnField();
            String cnField = idMappingRule.getCnField();
            if (priority > Constants.ID_MAPPING_MAX_PRIORITY || priority <= Constants.ID_MAPPING_INVALID_PRIORITY) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR,
                        String.format("字段 %s(%s) 优先级不合法, 请修改为 0 - 100 之间", cnField, enField));
            }
            if (properties.contains(priority)) {
                throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR,
                        String.format("字段 %s(%s) 优先级重复, 请修改", cnField, enField));
            }
            properties.add(priority);
        }

        // 3. 校验优先级最高的字段是否为单值类型
        IdMappingRule firstIdMappingRule = idMappingRules.get(0);
        if (!firstIdMappingRule.getFieldType().equals(IdMappingRuleFiledTypeEnum.SINGLE_VALUE.getCode())) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR,
                    String.format("优先级最高的字段 %s(%s)  必须为单值类型, 请修改",
                            firstIdMappingRule.getCnField(), firstIdMappingRule.getEnField()));
        }
    }

    private IdMappingRule getFieldByEnName(String enField) {
        String tenantId = WebContextHolder.getTenantId();
        IdMappingRuleCriteria idMappingRuleCriteria = new IdMappingRuleCriteria();
        idMappingRuleCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andEnFieldEqualTo(enField);
        List<IdMappingRule> idMappingRules = idMappingRuleMapper.selectByExample(idMappingRuleCriteria);
        // 同租户下只能存在一个相同enField字段
        return idMappingRules.isEmpty() ? null : idMappingRules.get(0);
    }

    public List<IdMappingRule> getExistedFields(String tenantId) {
        IdMappingRuleCriteria idMappingRuleCriteria = new IdMappingRuleCriteria();
        idMappingRuleCriteria.createCriteria().andTenantIdEqualTo(tenantId);
        idMappingRuleCriteria.setOrderByClause(Constants.ORDER_BY_PRIORITY_ASC);
        return idMappingRuleMapper.selectByExample(idMappingRuleCriteria);
    }

    // 反显描述信息
    public Map<String, String> getDefaultDesc(List<String> needAddEnField, Long dataTableId) {
        TableFieldMetaInfoCriteria tableFieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        tableFieldMetaInfoCriteria.createCriteria()
                .andEnFieldIn(needAddEnField)
                .andDataTableIdEqualTo(dataTableId);
        tableFieldMetaInfoCriteria.setOrderByClause(Constants.ORDER_BY_ID_ASC);
        List<TableFieldMetaInfo> fieldMetaInfos = tableFieldMetaInfoMapper.selectByExample(tableFieldMetaInfoCriteria);
        return fieldMetaInfos.stream()
                .collect(Collectors.toMap(
                        TableFieldMetaInfo::getEnField,
                        field -> Optional.ofNullable(field.getDescription()).orElse(""),
                        (v1, v2) -> v2)
                );
    }
}
