package com.baidu.keyue.deepsight.service.label;

public interface LabelFieldService {
    /**
     * 创建标签字段
     *
     * @param tenantId  租户ID
     * @param labelName 标签名
     * @return
     */
    long createNewLabelField(String tenantId, String labelName);

    /**
     * 根据ID删除标签字段
     *
     * @param id 标签字段ID
     */
    void deleteFieldById(long id);

    /**
     * 获取标签名
     *
     * @param fieldId 字段ID
     * @return
     */
    String genFieldName(long fieldId);

    /**
     * Doris表新增标签字段
     *
     * @param tenantId  租户ID
     * @param fieldName 字段名
     */
    void newDorisLabelProcessField(String tenantId, String fieldName);

    /**
     * Doris表撒删除标签字段
     *
     * @param tenantId 租户ID
     * @param field    字段ID
     */
    void deleteDorisLabelProcessField(String tenantId, long field);
}
