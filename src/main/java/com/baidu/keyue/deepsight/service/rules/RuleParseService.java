package com.baidu.keyue.deepsight.service.rules;

import com.baidu.keyue.deepsight.models.rules.dto.DqlParseResult;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * @className: RuleParseService
 * @description: 规则解析服务接口
 * @author: wangzhongcheng
 * @date: 2024/12/23 16:40
 */
public interface RuleParseService {

    /**
     * 解析规则组
     * @param ruleGroup 规则组
     * @return
     */
    DqlParseResult parseRuleGroup(RuleGroup ruleGroup, AtomicInteger alias);

    /**
     * 解析规则节点，DqlParseResult 默认 select 为与用户关联的字段，如果没有则 select *
     * @param ruleNode 规则节点
     * @return
     */
    DqlParseResult parseRuleNode(RuleNode ruleNode);

    /**
     * 校验规则节点，填充 filter 字段信息，防止sql注入等
     * @param ruleNode
     */
    void checkRuleNode(RuleNode ruleNode);

    /**
     * 校验规则组租户权限
     * @param ruleGroup
     */
    void checkRuleGroupTenantPermission(RuleGroup ruleGroup, String tenantId);

}
