package com.baidu.keyue.deepsight.service.sop.impl;

import com.baidu.keyue.deepsight.models.sop.SOPAnalyseProgressRequest;
import com.baidu.keyue.deepsight.models.sop.SOPAnalyseProgressResponse;
import com.baidu.keyue.deepsight.models.sop.SOPMetaInternal;
import com.baidu.keyue.deepsight.models.sop.SopEdge;
import com.baidu.keyue.deepsight.models.sop.SopFlexibleWholeRequest;
import com.baidu.keyue.deepsight.models.sop.SopFlexibleWholeResponse;
import com.baidu.keyue.deepsight.models.sop.SopHangup;
import com.baidu.keyue.deepsight.models.sop.SopIntent;
import com.baidu.keyue.deepsight.models.sop.SopNode;
import com.baidu.keyue.deepsight.models.sop.SopNodeDailyDistribute;
import com.baidu.keyue.deepsight.models.sop.SopNodeDetailRequest;
import com.baidu.keyue.deepsight.models.sop.SopNodeDetailResponse;
import com.baidu.keyue.deepsight.models.sop.SopNodeWithHangup;
import com.baidu.keyue.deepsight.models.sop.SopSankeyMetaResponse;
import com.baidu.keyue.deepsight.models.sop.SopSankeyStep;
import com.baidu.keyue.deepsight.models.sop.SopSankeyWholeRequest;
import com.baidu.keyue.deepsight.models.sop.SopSankeyWholeResponse;
import com.baidu.keyue.deepsight.service.sop.AiobSOPService;
import com.baidu.keyue.deepsight.utils.AESUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AiobSopStatisticService {

    private final AiobSopMetricService aiobSopMetricService;
    private final AiobSOPService aiobSOPService;
    private static final String INTENT_KEY = "ea437d38025246b6";

    public AiobSopStatisticService(AiobSopMetricService aiobSopMetricService, AiobSOPService aiobSOPService) {
        this.aiobSopMetricService = aiobSopMetricService;
        this.aiobSOPService = aiobSOPService;
    }

    public int percent(long num, long base) {
        if (base == 0) {
            return 0;
        }
        return (int) Math.round((double) num / base * 100);
    }

    public String intentTagCrypt(String tag) {
        try {
            return AESUtils.encrypt(tag, INTENT_KEY);
        } catch (Exception e) {
            log.error("intentTagCrypt error", e);
            return "";
        }
    }

    public String intentTagDecrypt(String tagCipherText) {
        try {
            return AESUtils.decrypt(tagCipherText, INTENT_KEY);
        } catch (Exception e) {
            log.error("intentTagDecrypt error", e);
            return tagCipherText;
        }
    }

    public void calculateNodeRatios(List<SopNode> nodeMetrics, List<SopEdge> edgeMetrics) {
        if (CollectionUtils.isEmpty(nodeMetrics)) {
            return;
        }
        // 创建节点ID到节点数据的映射
        Map<String, SopNode> nodeMap = nodeMetrics.stream()
                .peek(node -> {
                    node.setUvPercent(0);
                    node.setPvPercent(0);
                })
                .collect(Collectors.toMap(SopNode::getNodeId, node -> node, (k1, k2) -> k1));

        if (CollectionUtils.isEmpty(edgeMetrics)) {
            return;
        }
        // 创建上游节点到下游节点的边映射, group by endNode
        Map<String, List<SopEdge>> toEdgesGroup = edgeMetrics.stream().collect(Collectors.groupingBy(SopEdge::getEndNodeId));

        // 遍历所有边，计算比例
        for (Map.Entry<String, List<SopEdge>> entry : toEdgesGroup.entrySet()) {
            String nodeId = entry.getKey();
            List<SopEdge> fromEdges = entry.getValue();
            if (CollectionUtils.isEmpty(fromEdges)) {
                continue;
            }
            SopNode toNode = nodeMap.get(nodeId);
            if (Objects.isNull(toNode)) {
                continue;
            }

            // 获取上游节点的整体uv、pv
            long uv = 0L;
            long pv = 0L;
            for (SopEdge edge : fromEdges) {
                if (StringUtils.isBlank(edge.getFromNodeId())) {
                    continue;
                }
                String fromNodeId = edge.getFromNodeId();
                SopNode fromNode = nodeMap.get(fromNodeId);
                if (Objects.isNull(fromNode)) {
                    continue;
                }
                uv += fromNode.getUv();
                pv += fromNode.getPv();
            }

            if (uv > 0) {
                toNode.setUvPercent(percent(toNode.getUv(), uv));
            } else {
                toNode.setUvPercent(0);
            }
            if (pv > 0) {
                toNode.setPvPercent(percent(toNode.getPv(), pv));
            } else {
                toNode.setPvPercent(0);
            }
        }
    }

    public void calculateNodeRatiosV2(List<SopNode> nodeMetrics, List<SopEdge> edgeMetrics,
                                      String tenantId, String taskId, String robotId, String robotVer, String topicId, Date startTime, Date endTime) {
        if (CollectionUtils.isEmpty(nodeMetrics)) {
            return;
        }
        // 创建节点ID到节点数据的映射
        Map<String, SopNode> nodeMap = nodeMetrics.stream()
                .peek(node -> {
                    node.setUvPercent(0);
                    node.setPvPercent(0);
                })
                .collect(Collectors.toMap(SopNode::getNodeId, node -> node, (k1, k2) -> k1));

        if (CollectionUtils.isEmpty(edgeMetrics)) {
            return;
        }
        // 创建上游节点到下游节点的边映射, group by endNode
        Map<String, List<SopEdge>> toEdgesGroup = edgeMetrics.stream().collect(Collectors.groupingBy(SopEdge::getEndNodeId));

        // 遍历所有边，计算比例
        for (Map.Entry<String, List<SopEdge>> entry : toEdgesGroup.entrySet()) {
            String nodeId = entry.getKey();
            List<SopEdge> fromEdges = entry.getValue();
            if (CollectionUtils.isEmpty(fromEdges)) {
                continue;
            }
            SopNode toNode = nodeMap.get(nodeId);
            if (Objects.isNull(toNode)) {
                continue;
            }

            // 获取上游节点的整体uv、pv
            long uv = 0L;
            long pv = 0L;
            if (CollectionUtils.isNotEmpty(fromEdges)) {
                if (fromEdges.size() == 1) {
                    SopEdge edge = fromEdges.get(0);
                    if (StringUtils.isNotBlank(edge.getFromNodeId())) {
                        String fromNodeId = edge.getFromNodeId();
                        SopNode fromNode = nodeMap.get(fromNodeId);
                        if (Objects.nonNull(fromNode)) {
                            uv += fromNode.getUv();
                            pv += fromNode.getPv();
                        }
                    }
                } else {
                    List<String> fromNodes = fromEdges.stream().map(SopEdge::getFromNodeId).distinct().collect(Collectors.toList());
                    Long[] countPair = aiobSopMetricService.nodeMetricWithIds(tenantId, fromNodes, taskId, robotId, robotVer, topicId, startTime, endTime);
                    if (countPair.length == 2) {
                        uv = countPair[0];
                        pv = countPair[1];
                    }
                }
            }

            if (uv > 0) {
                toNode.setUvPercent(percent(toNode.getUv(), uv));
            }
            if (pv > 0) {
                toNode.setPvPercent(percent(toNode.getPv(), pv));
            }
        }
    }

    public void completeFirstStepNodePercent(List<SopNode> nodeMetrics, List<SopEdge> edgeMetrics) {
        if (CollectionUtils.isEmpty(nodeMetrics) || CollectionUtils.isEmpty(edgeMetrics)) {
            return;
        }

        Set<String> hasFromNodeIds = edgeMetrics.stream().map(SopEdge::getEndNodeId).collect(Collectors.toSet());
        nodeMetrics.forEach(node -> {
            String nodeId = node.getNodeId();
            // 不存在上游边的节点认为是起始节点
            if (!hasFromNodeIds.contains(nodeId) && node.getUvPercent() == 0 && node.getPvPercent() == 0) {
                node.setUvPercent(100);
                node.setPvPercent(100);
            }
        });
    }

    /**
     * 灵活画布 整体统计数据
     * @param tenantId 租户 id
     * @param request 请求参数
     * @return 统计结果
     */
    public SopFlexibleWholeResponse flexibleWholeData(String tenantId, SopFlexibleWholeRequest request) {
        // id 转换
        List<String> idTransferRes = aiobSopMetricService.flexibleIdTransfer(tenantId, request.getRobotId(), request.getRobotVer());
        if (CollectionUtils.isEmpty(idTransferRes)) {
            return new SopFlexibleWholeResponse(Collections.emptyList(), Collections.emptyList());
        }
        request.setRobotId(idTransferRes.get(0));
        request.setRobotVer(idTransferRes.get(1));

        // 统计节点数据
        List<SopNode> nodeMetrics = aiobSopMetricService.wholeNodeMetric(
                tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());

        // 统计节点挂断数据
        Map<String, SopNode> nodeHangupMetricMap = aiobSopMetricService.wholeNodeHangupMetric(
                tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());

        // 统计边数据
        List<SopEdge> edgeMetrics = aiobSopMetricService.wholeEdgeMetric(
                tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());
        // 计算节点的 uv pv 比例
        calculateNodeRatios(nodeMetrics, edgeMetrics);
        // 计算首节点比人数例
        completeFirstStepNodePercent(nodeMetrics, edgeMetrics);

        // 结果排序
        nodeMetrics = nodeMetrics.stream().sorted(Comparator.comparing(SopNode::getNodeId)).toList();

        // 挂断数据统计
        List<SopNodeWithHangup> sopNodeWithHangups = nodeMetrics.stream().map(node -> {
            SopNodeWithHangup sopNodeWithHangup = new SopNodeWithHangup();
            BeanUtils.copyProperties(node, sopNodeWithHangup);
            SopHangup hangupInfo = new SopHangup();
            sopNodeWithHangup.setHangup(hangupInfo);

            SopNode hangupNode = nodeHangupMetricMap.get(node.getNodeId());
            if (Objects.isNull(hangupNode)) {
                sopNodeWithHangup.setHangup(new SopHangup());
            } else {
                hangupInfo.setUv(hangupNode.getUv());
                hangupInfo.setPv(hangupNode.getPv());
                hangupInfo.setUvPercent(percent(hangupNode.getUv(), node.getUv()));
                hangupInfo.setPvPercent(percent(hangupNode.getPv(), node.getPv()));
            }
            return sopNodeWithHangup;
        }).collect(Collectors.toList());

        return new SopFlexibleWholeResponse(sopNodeWithHangups, edgeMetrics);
    }

    /**
     * 构造默认桑基图结果，在没有数据的情况下返回
     * @param sopMetaList
     * @return
     */
    public SopSankeyWholeResponse buildDefaultSankeyWholeData(List<SOPMetaInternal> sopMetaList) {
        List<SopSankeyStep> steps = sopMetaList.stream().filter(Objects::nonNull).map(step -> {
            SopSankeyStep stepMetric = new SopSankeyStep(step.getStepId(), step.getStepName(), Lists.newArrayList());
            if (CollectionUtils.isNotEmpty(step.getNodes())) {
                stepMetric.setNodes(
                        step.getNodes().stream().map(node ->
                                        new SopNode(node.getNodeId(), -1L, -1L, -1, -1, ""))
                                .toList()
                );
            }
            return stepMetric;
        }).collect(Collectors.toList());
        return new SopSankeyWholeResponse(steps, Collections.emptyList());
    }

    public List<String> getAllNodeIdsWithStep(List<SOPMetaInternal> sopMetaList) {
        return sopMetaList.stream()
                .filter(item -> CollectionUtils.isNotEmpty(item.getNodes()))
                .flatMap(item -> item.getNodes().stream())
                .map(SOPMetaInternal.SopMetaNode::getNodeId)
                .filter(StringUtils::isNotBlank)
                .distinct()
                .toList();
    }

    public List<String> getAllStartNodeIds(List<SOPMetaInternal> sopMetaList) {
        List<String> startNodeIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(sopMetaList.get(0).getNodes())) {
            startNodeIds = sopMetaList.get(0).getNodes()
                    .stream()
                    .map(SOPMetaInternal.SopMetaNode::getNodeId).distinct().toList();
        }

        return startNodeIds;
    }

    /**
     * 快捷场景桑基图 整体统计数据
     * @param tenantId 租户 id
     * @param request 请求参数
     * @return 统计结果
     */
    public SopSankeyWholeResponse sankeyWholeData(String tenantId, SopSankeyWholeRequest request) {
        // sop meta 获取
        SopSankeyMetaResponse sopSankeyMetaResponse = aiobSOPService.listSOPMeta(tenantId, request.getTaskId(), request.getRobotVer());
        List<SOPMetaInternal> sopMetaList = sopSankeyMetaResponse.getSopMeta();
        if (!sopSankeyMetaResponse.getManualChecked() || CollectionUtils.isEmpty(sopMetaList)) {
            return new SopSankeyWholeResponse(Collections.emptyList(), Collections.emptyList());
        }

        // 全部节点
        List<String> allNodeIds = getAllNodeIdsWithStep(sopMetaList);
        if (CollectionUtils.isEmpty(allNodeIds)) {
            return buildDefaultSankeyWholeData(sopMetaList);
        }

        // 起始节点 (用户处理起始节点的流量占比)
        List<String> startNodeIds = getAllStartNodeIds(sopMetaList);

        // 统计节点数据
        List<SopNode> nodeMetrics = aiobSopMetricService.wholeNodeMetric(
                tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());
        if (CollectionUtils.isEmpty(nodeMetrics)) {
            sopSankeyMetaResponse = aiobSOPService.listSOPMeta(tenantId, request.getTaskId(), request.getRobotVer());
            sopMetaList = sopSankeyMetaResponse.getSopMeta();
            return buildDefaultSankeyWholeData(sopMetaList);
        }
        // 统计边数据
        List<SopEdge> edgeMetrics = aiobSopMetricService.wholeEdgeMetric(
                tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());
        calculateNodeRatiosV2(nodeMetrics, edgeMetrics,
                tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());
        Map<String, SopNode> nodeMetricMap = nodeMetrics.stream()
                .collect(Collectors.toMap(SopNode::getNodeId, node -> node));

        // 统计末端节点的意向数据
        List<SopNode> intentNodeMetric = Lists.newArrayList();
        List<SopEdge> intentEdgeMetric = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(allNodeIds)) {
            // group by node_id & tag
            List<SopIntent> originalIntents = aiobSopMetricService.wholeIntentMetric(
                    tenantId, allNodeIds, request.getTaskId(), request.getRobotId(), request.getRobotVer(),
                    request.getTopicId(), request.getStartTime(), request.getEndTime());

            // 计算意向节点的 uv pv precent
            originalIntents.stream()
                    .filter(intent -> StringUtils.isNotBlank(intent.getIntent()))
                    .collect(Collectors.groupingBy(SopIntent::getIntent))
                    .forEach((intent, intents) -> {
                        String intentId = intentTagCrypt(intent);
                        if (StringUtils.isBlank(intentId)) {
                            return;
                        }

                        AtomicLong intentUv = new AtomicLong(0L);
                        AtomicLong intentPv = new AtomicLong(0L);
                        AtomicLong nodeUv = new AtomicLong(0L);
                        AtomicLong nodePv = new AtomicLong(0L);
                        intents.forEach(intentMetric -> {
                            intentUv.addAndGet(intentMetric.getUv());
                            intentPv.addAndGet(intentMetric.getPv());
                            nodeUv.addAndGet(nodeMetricMap.get(intentMetric.getNodeId()).getUv());
                            nodePv.addAndGet(nodeMetricMap.get(intentMetric.getNodeId()).getPv());
                            intentEdgeMetric.add(new SopEdge(intentMetric.getNodeId(), intentId, intentMetric.getPv(), intentMetric.getUv()));
                        });
                        SopNode newIntent = new SopNode();
                        newIntent.setNodeId(intentId);
                        newIntent.setIntent(intent);
                        newIntent.setUv(intentUv.get());
                        newIntent.setPv(intentPv.get());
                        if (nodeUv.get() > 0) {
                            newIntent.setUvPercent(percent(newIntent.getUv(), nodeUv.get()));
                        }
                        if (nodePv.get() > 0) {
                            newIntent.setPvPercent(percent(newIntent.getPv(), nodePv.get()));
                        }
                        intentNodeMetric.add(newIntent);
                    });
        }
        intentEdgeMetric.addAll(edgeMetrics);

        // 起始节点的uv pv percent 为 100
        for (String nodeId : startNodeIds) {
            if (nodeMetricMap.containsKey(nodeId)) {
                nodeMetricMap.get(nodeId).setUvPercent(100);
                nodeMetricMap.get(nodeId).setPvPercent(100);
            }
        }

        // 步骤 + 节点 数据组装
        List<SopSankeyStep> steps = sopMetaList.stream().map(step -> {
            SopSankeyStep stepMetric = new SopSankeyStep(step.getStepId(), step.getStepName(), Lists.newArrayList());
            for (SOPMetaInternal.SopMetaNode node : step.getNodes()) {
                if (nodeMetricMap.containsKey(node.getNodeId())) {
                    stepMetric.getNodes().add(nodeMetricMap.get(node.getNodeId()));
                }
            }
            stepMetric.setNodes(stepMetric.getNodes().stream().sorted(Comparator.comparing(SopNode::getNodeId)).toList());
            return stepMetric;
        }).collect(Collectors.toList());

        // 增加意向标签模拟步骤
        steps.add(new SopSankeyStep("意向分析", "意向分析", intentNodeMetric));

        return new SopSankeyWholeResponse(steps, intentEdgeMetric);
    }

    public List<String> taskIdTransfer(String tenantId, String taskId, String robotId, String robotVer) {
        Integer robotScene = aiobSopMetricService.checkTaskSceneType(tenantId, taskId);
        if (Objects.isNull(robotScene)) {
            return Lists.newArrayList(robotId, robotVer);
        }
        if (robotScene == 6) {
            // id 转换
            List<String> idTransferRes = aiobSopMetricService.flexibleIdTransfer(tenantId, robotId, robotVer);
            if (CollectionUtils.isNotEmpty(idTransferRes)) {
                return idTransferRes;
            }
        }
        return Lists.newArrayList(robotId, robotVer);
    }

    /**
     * 节点详情
     * @param tenantId 租户 id
     * @param request 请求参数
     * @return 节点详情结果
     */
    public SopNodeDetailResponse nodeDetailStatistics(String tenantId, SopNodeDetailRequest request) {
        SopNodeDetailResponse response = new SopNodeDetailResponse(Lists.newArrayList(), Lists.newArrayList(), Lists.newArrayList());
        List<String> idTransferRes = taskIdTransfer(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer());
        request.setRobotId(idTransferRes.get(0));
        request.setRobotVer(idTransferRes.get(1));

        String currNodeId = request.getCurrNodeId();
        // 1、统计上游节点的 uv、pv
        //   1.1 获取上游节点
        List<String> previewNodeIds = aiobSopMetricService.getAllPreviewNodes(tenantId, currNodeId, request.getTaskId(),
                request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());
        //   1.2 统计上游节点+当前节点的 uv、pv
        List<String> mixedNodeIds = Lists.newArrayList(previewNodeIds);
        mixedNodeIds.add(currNodeId);
        mixedNodeIds = mixedNodeIds.stream().sorted().toList();
        List<SopNode> previewNodeMetrics = aiobSopMetricService.wholeNodeMetricWithIds(tenantId, mixedNodeIds,
                request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());

        // 2、统计下游节点的 uv、pv（实际采用：当前节点-下游节点 边的 uv、pv）
        List<SopNode> currNodeToForwardNodeMetrics = aiobSopMetricService.currNodeToForwardNodeEdgeMetric(tenantId, currNodeId,
                request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());

        // 3、边
        //   3.1 上游节点 -> 当前节点
        List<SopEdge> previewNodeToCurrNodeEdgeMetricList = aiobSopMetricService.previewNodeToCurrNodeEdgeMetric(
                tenantId, previewNodeIds, currNodeId, request.getTaskId(), request.getRobotId(),
                request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime()
        );
        //   3.2 当前节点 -> 下游节点 (直接使用下游节点的 uv、pv)
        List<SopEdge> currNodeToForwardNodeEdgeMetricList = currNodeToForwardNodeMetrics.stream().map(node -> {
            SopEdge e = new SopEdge();
            e.setFromNodeId(currNodeId);
            e.setEndNodeId(node.getNodeId());
            e.setWeight(node.getPv());
            e.setWeightUv(node.getUv());
            return e;
        }).toList();

        // 4、每日下游流量分布
        // 4.1、当前节点的每日流量分布
        Map<String, SopNode> dailyCurrNodeMetric = aiobSopMetricService.dailyCurrNodeMetric(tenantId, currNodeId,
                request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());

        // 4.2、下游节点的每日流量分布
        List<SopNodeDailyDistribute> dailyCurrNodeToForwardMetrics = aiobSopMetricService.dailyCurrNodeToForwardNodeEdgeMetric(tenantId, currNodeId,
                request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());

        // 4.3 下游节点每日流量 uv pv percent
        dailyCurrNodeToForwardMetrics.forEach(daily -> {
            String date = daily.getDate();
            SopNode dailyNode = dailyCurrNodeMetric.get(date);
            if (Objects.nonNull(dailyNode) && Objects.nonNull(dailyNode.getUv()) && dailyNode.getUv() > 0) {
                daily.getNodes().forEach(node -> {
                    node.setUvPercent(percent(node.getUv(), dailyNode.getUv()));
                    node.setPvPercent(percent(node.getPv(), dailyNode.getPv()));
                });
            }
        });
        // 收集下游节点，在每日下游分布中填充每个日期无数据的节点分布
        Set<String> dailyNodeIds = new HashSet<>();
        dailyCurrNodeToForwardMetrics.forEach(daily -> daily.getNodes().forEach(node -> dailyNodeIds.add(node.getNodeId())));
        for (SopNodeDailyDistribute daily : dailyCurrNodeToForwardMetrics) {
            Set<String> collect = daily.getNodes().stream().map(SopNode::getNodeId).collect(Collectors.toSet());
            // 填充无数据node
            List<SopNode> emptyDatalist = dailyNodeIds.stream()
                    .filter(nodeId -> !collect.contains(nodeId))
                    .map(nodeId -> new SopNode(nodeId, 0L, 0L, 0, 0, null))
                    .collect(Collectors.toList());
            emptyDatalist.addAll(daily.getNodes());
            daily.setNodes(emptyDatalist);
        }
        List<SopNode> allNodes = Lists.newArrayList();
        allNodes.addAll(previewNodeMetrics);
        allNodes.addAll(currNodeToForwardNodeMetrics);
        response.setNodes(Lists.newArrayList());

        response.setEdges(Lists.newArrayList());
        response.getEdges().addAll(previewNodeToCurrNodeEdgeMetricList);
        response.getEdges().addAll(currNodeToForwardNodeEdgeMetricList);

        response.setDailyDistribute(dailyCurrNodeToForwardMetrics);

        calculateNodeRatiosV2(allNodes, response.getEdges(),
                tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());

        response.setNodes(completeHangup(tenantId, request, allNodes));

        if (CollectionUtils.isEmpty(previewNodeIds)) {
            response.getNodes().stream().filter(item -> currNodeId.equals(item.getNodeId()))
                    .forEach(item -> {
                        item.setUvPercent(100);
                        item.setPvPercent(100);
                    });
        } else {
            completePreviewNodeDetail(response.getNodes(), previewNodeIds);
        }
        return response;
    }

    public void completePreviewNodeDetail(List<SopNodeWithHangup> nodes, List<String> previewNodeIds) {
        AtomicLong uv = new AtomicLong(0L);
        AtomicLong pv = new AtomicLong(0L);
        nodes.stream().filter(node -> previewNodeIds.contains(node.getNodeId()))
                .forEach(node -> {
                    uv.addAndGet(node.getUv());
                    pv.addAndGet(node.getPv());
                });
        nodes.stream().filter(node -> previewNodeIds.contains(node.getNodeId()))
                .forEach(node -> {
                    if (uv.get() > 0) {
                        node.setUvPercent(percent(node.getUv(), uv.get()));
                    }
                    if (pv.get() > 0) {
                        node.setPvPercent(percent(node.getPv(), pv.get()));
                    }
                });
    }

    public List<SopNodeWithHangup> completeHangup(String tenantId, SopNodeDetailRequest request, List<SopNode> allNodes) {
        // 统计节点挂断数据
        Map<String, SopNode> nodeHangupMetricMap = aiobSopMetricService.wholeNodeHangupMetric(
                tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer(), request.getTopicId(), request.getStartTime(), request.getEndTime());

        return allNodes.stream().map(node -> {
                    SopNodeWithHangup sopNodeWithHangup = new SopNodeWithHangup();
                    BeanUtils.copyProperties(node, sopNodeWithHangup);
                    SopHangup hangupInfo = new SopHangup();
                    sopNodeWithHangup.setHangup(hangupInfo);

                    SopNode hangupNode = nodeHangupMetricMap.get(node.getNodeId());
                    if (Objects.isNull(hangupNode)) {
                        sopNodeWithHangup.setHangup(new SopHangup());
                    } else {
                        hangupInfo.setUv(hangupNode.getUv());
                        hangupInfo.setPv(hangupNode.getPv());
                        hangupInfo.setUvPercent(percent(hangupNode.getUv(), node.getUv()));
                        hangupInfo.setPvPercent(percent(hangupNode.getPv(), node.getPv()));
                    }
                    return sopNodeWithHangup;
                })
                .sorted(Comparator.comparing(SopNode::getNodeId)).toList();
    }

    /**
     * 任务进度统计
     * @param tenantId 租户 id
     * @param request 请求参数
     * @return 任务进度统计结果
     */
    public SOPAnalyseProgressResponse analyseProgress(String tenantId, SOPAnalyseProgressRequest request) {
        SOPAnalyseProgressResponse response = new SOPAnalyseProgressResponse(0L, 0L, 0L);
        // 根据taskId 判断任务类型
        String taskId = request.getTaskId();
        Integer robotScene = aiobSopMetricService.checkTaskSceneType(tenantId, taskId);
        if (Objects.isNull(robotScene)) {
            return response;
        }

        // 接通人数(任务维度) -- 根据 taskId 查询 session表 sipCode = '200' 的数量
        response.setTotalConnectedCall(aiobSopMetricService.countSessionConnectedCall(tenantId, request.getTaskId(), null, null));

        // 5, "快捷场景"
        if (robotScene == 5) {
            // 接通人数(区分版本) -- 根据 taskId 查询 session表 sipCode = '200' AND botVersionId = '{version}' 的数量
            response.setConnectedCall(aiobSopMetricService.countSessionConnectedCall(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer()));
            // 洞察已分析(区分版本) -- 查询 node_metric 表 distinct oneID
            response.setProcessed(aiobSopMetricService.countAnalysedMetricSQL(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer()));
        } else if (robotScene == 6) {
            // 6, "灵活画布"

            // 接通人数(区分版本) -- 根据 taskId 查询 session表 sipCode = '200' AND botVersionId = '{version}' 的数量
            response.setConnectedCall(aiobSopMetricService.countFlexibleSessionConnectedCall(tenantId, request.getTaskId(), request.getRobotId(), request.getRobotVer()));

            // id 转换
            List<String> idTransferRes = aiobSopMetricService.flexibleIdTransfer(tenantId, request.getRobotId(), request.getRobotVer());
            if (CollectionUtils.isEmpty(idTransferRes)) {
                return response;
            }
            // 洞察已分析(区分版本)
            response.setProcessed(aiobSopMetricService.countFlexibleProcessedCall(tenantId, request.getTaskId(), idTransferRes.get(0), idTransferRes.get(1)));
        }

        return response;
    }
}
