package com.baidu.keyue.deepsight.service.tenant;

import cn.hutool.core.collection.CollUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.DorisTableTemplateConfig;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.PresetEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTypeEnum;
import com.baidu.keyue.deepsight.enums.TableFieldValueEnum;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroupCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.CustomerGroupMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingManagerService;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @className TenantV1UpgradeHandler
 * @description
 * @date 2025/3/10 10:33
 */
@Slf4j
@Service
@Order(2)
public class TenantV1UpgradeHandler extends TenantUpgradeHandler {

    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private TenantInfoMapper tenantInfoMapper;

    @Resource
    private IdMappingManagerService idMappingManagerService;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    private CustomerGroupMapper customerGroupMapper;

    @Resource
    private TableRecordCommonService tableRecordCommonService;

    @Resource
    private DorisTableTemplateConfig dorisTableTemplateConfig;

    @Resource
    private TenantV2UpgradeHandler tenantV2UpgradeHandler;
    
    @Resource
    @Lazy
    private TenantInfoService tenantInfoService;

    private Map<String, TableFieldMetaInfo> aiobRecordFieldMap = new HashMap<>();
    private Map<String, TableFieldMetaInfo> aiobSessionFieldMap = new HashMap<>();
    private Map<String, TableFieldMetaInfo> keyueRecordFieldMap = new HashMap<>();
    private Map<String, TableFieldMetaInfo> memoryExtractFieldMap = new HashMap<>();
    private Map<String, TableFieldMetaInfo> mockUserFieldMap = new HashMap<>();

    /**
     * 外呼对话内容新增字段
     */
    private final List<String> aiobRecordField = List.of("deepsight_datetime", "deepsight_update_datetime");
    /**
     * 外呼对话记录新增字段
     */
    private final List<String> aiobSessionField = List.of("deepsight_datetime", "deepsight_update_datetime",
            "BAIDUID", "IMEI", "CUID", "MAC");
    /**
     * 客服对话内容新增字段
     */
    private final List<String> keyueRecordField = List.of("deepsight_datetime", "deepsight_update_datetime",
            "BAIDUID", "UNIONID", "IMEI", "CUID", "MAC", "anonymous_id");
    /**
     * 记忆提取新增字段
     */
    private final List<String> memoryExtractField = List.of("deepsight_datetime", "deepsight_update_datetime");
    /**
     * 用户基础信息新增字段
     */
    private final List<String> mockUserField = List.of("mobile_list", "email_list", "source",
            "deepsight_datetime", "deepsight_update_datetime",
            "IMEI", "BAIDUID", "CUID", "USERID", "MAC", "UNIONID", "IDFA", "OAID", "anonymous_id");

    public TenantV1UpgradeHandler(DorisService dorisService) {
        super(dorisService);
    }

    @PostConstruct
    public void init() throws IOException {
        setVersion(2);
        initDorisSql("upgrade/v1.0.0.sql");
        dorisTableTemplateConfig.getUserTableFieldsInfo().forEach(field -> mockUserFieldMap.put(field.getEnField(), field));
        dorisTableTemplateConfig.getAiobRecordFieldsInfo().forEach(field -> aiobRecordFieldMap.put(field.getEnField(), field));
        dorisTableTemplateConfig.getAiobSessionFieldsInfo().forEach(field -> aiobSessionFieldMap.put(field.getEnField(), field));
        dorisTableTemplateConfig.getKeyueSessionFieldsInfo().forEach(field -> keyueRecordFieldMap.put(field.getEnField(), field));
        dorisTableTemplateConfig.getUserMemoryTableFieldsInfo().forEach(field -> memoryExtractFieldMap.put(field.getEnField(), field));
        addNextHandler(tenantV2UpgradeHandler);
    }

    @Override
    protected boolean needUpdateTenant(TenantInfo tenant) {
        Integer version = tenantInfoService.queryTenantInfo(tenant.getTenantid()).getVersion();
        return Objects.equals(version, 1);
    }

    @Override
    protected void executeUpgrade(TenantDTO tenant) {
        transactionTemplate.execute(status -> {
            try {
                log.info("tenant v1 upgrade start.tenantId is {}.", tenant.getTenantId());
                // 初始化palo表&内容
                execDorisTable(tenant.getTenantId());
                log.info("tenant v1 execDorisTableSql finished init.tenantId is {}.", tenant.getTenantId());
                // 旧版本用户执行Doris表升级
                if (!tenant.getIsInit()) {
                    execDorisSqlUpgrade(tenant);
                    log.info("tenant v1 execDorisSqlUpgrade finished init.tenantId is {}.", tenant.getTenantId());
                    initPaloTableContent(tenant.getTenantId());
                    log.info("tenant v1 init palo content finished init.tenantId is {}.", tenant.getTenantId());
                }
                // 初始化表信息&表字段信息
                initTableAndField(tenant.getTenantId(), tenant.getIsInit());
                log.info("tenant v1 initTableAndField finished init.tenantId is {}.", tenant.getTenantId());
                // 初始化 id-mapping 信息
                idMappingManagerService.initDefaultIdMapping(tenant.getTenantId());
                log.info("tenant v1 init id-mapping finished init.tenantId is {}.", tenant.getTenantId());
                // 更新责任链租户信息
                TenantInfo info = tenant.getTenantInfo();
                info.setVersion(version);
                // 更新数据库租户版本
                TenantInfo tenantInfo = new TenantInfo();
                tenantInfo.setVersion(version);
                tenantInfo.setId(info.getId());
                tenantInfoMapper.updateByPrimaryKeySelective(tenantInfo);
            } catch (Exception e) {
                log.error("Tenant V1 Upgrade error!, tenantId:{}, msg is：{}", tenant.getTenantId(), e.getMessage());
                log.error("Tenant V1 Upgrade error:", e);
                status.setRollbackOnly();
                throw new DeepSightException.InitTenantFailedException(ErrorCode.INTERNAL_ERROR, "租户v1版本升级失败");
            }
            return null;
        });
    }


    public void execDorisTable(String tenantId) {
        execDorisTableSql(tenantId);
        // 创建客群宽表字段
        CustomerGroupCriteria customerGroupCriteria = new CustomerGroupCriteria();
        customerGroupCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andCustomerGroupNameEqualTo(Constants.DEFAULT_CUSTOMER_GROUP_FOR_ALL)
                .andCreatorEqualTo(Constants.SYSTEM_DEFAULT_USER_ID);
        List<CustomerGroup> customerGroups = customerGroupMapper.selectByExample(customerGroupCriteria);
        if (CollUtil.isEmpty(customerGroups) || customerGroups.size() != 1) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "全员客群不存在:" + tenantId);
        }
        Long id = customerGroups.get(0).getId();
        String fieldName = String.format("process_customer_%d", id);
        dorisService.operationSchema(String.format(
                "ALTER TABLE %s ADD COLUMN %s varchar(150) NULL DEFAULT '1'",
                TenantUtils.generateMockUserTableName(tenantId), fieldName));
        dorisService.operationSchema(String.format(
                "ALTER TABLE %s ADD COLUMN %s varchar(150) NULL DEFAULT '1'",
                TenantUtils.generateUserProfileTableName(tenantId), fieldName));
    }

    /**
     * 初始化表信息&表字段信息
     *
     * @param tenantId
     * @param isInit
     */
    public void initTableAndField(String tenantId, Boolean isInit) {
        // 新表字段信息添加逻辑
        // 客群预置字段置为预置
        CustomerGroup presetCustomerGroup = new CustomerGroup();
        presetCustomerGroup.setPreset(PresetEnum.PRESET.getBoolean());
        CustomerGroupCriteria presetCustomerGroupCriteria = new CustomerGroupCriteria();
        presetCustomerGroupCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andCreatorEqualTo(Constants.SYSTEM_DEFAULT_USER_ID);
        customerGroupMapper.updateByExampleSelective(presetCustomerGroup, presetCustomerGroupCriteria);
        CustomerGroup notPresetCustomerGroup = new CustomerGroup();
        notPresetCustomerGroup.setPreset(PresetEnum.NOT_PRESET.getBoolean());
        CustomerGroupCriteria notPresetCustomerGroupCriteria = new CustomerGroupCriteria();
        notPresetCustomerGroupCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andCreatorNotEqualTo(Constants.SYSTEM_DEFAULT_USER_ID);
        customerGroupMapper.updateByExampleSelective(notPresetCustomerGroup, notPresetCustomerGroupCriteria);
        // 新租户无需干预老数据，跳过
        if (isInit) {
            log.info("{} 新租旧数据户升级跳过", tenantId);
            return;
        }
        // 为表元信息添加 oneId 字段
        List<DataTableInfo> dataTableInfos = getDataTableInfosForTenant(tenantId);
        for (DataTableInfo dataTableInfo : dataTableInfos) {
            addOneIdFieldToTableMeta(dataTableInfo.getId(), dataTableInfo.getTableName());
        }
        // 用户基础信息表增加字段元信息
        String userTableName = TenantUtils.generateMockUserTableName(tenantId);
        DataTableInfo userTableInfo = tableRecordCommonService.getTableByTableName(userTableName);
        saveMetaInfo(userTableName, mockUserField, mockUserFieldMap);
        updateOrSaveMeta(userTableName, mockUserFieldMap, "device_id", "DEVICEID", userTableInfo);
        // 外呼对话记录表增加字段元信息
        String aiobSessionTableName = TenantUtils.generateAiobSessionTableName(tenantId);
        saveMetaInfo(aiobSessionTableName, aiobSessionField, aiobSessionFieldMap);
        // 外呼对话内容表增加字段元信息
        String aiobRecordTableName = TenantUtils.generateAiobRecordTableName(tenantId);
        saveMetaInfo(aiobRecordTableName, aiobRecordField, aiobRecordFieldMap);
        // 客服对话内容表增加字段元信息
        String keyueRecordTableName = TenantUtils.generateKeyueRecordTableName(tenantId);
        DataTableInfo keyueRecordTableInfo = tableRecordCommonService.getTableByTableName(keyueRecordTableName);
        saveMetaInfo(keyueRecordTableName, keyueRecordField, keyueRecordFieldMap);
        updateOrSaveMeta(keyueRecordTableName, keyueRecordFieldMap, "uid", "user_id", keyueRecordTableInfo);
        // 用户记忆提取表增加字段元信息
        String memoryExtractTableName = TenantUtils.generateMemoryExtractTableName(tenantId);
        saveMetaInfo(memoryExtractTableName, memoryExtractField, mockUserFieldMap);
    }

    /**
     * 更新字段信息英文字段名
     * 如果旧值不存在，则添加新值
     *
     * @param tableName        表名
     * @param mockUserFieldMap 字段元信息map
     * @param oldName          字段旧名
     * @param newName          字段新名
     * @param tableInfo        表信息
     */
    public void updateOrSaveMeta(String tableName, Map<String, TableFieldMetaInfo> mockUserFieldMap, String oldName, String newName, DataTableInfo tableInfo) {
        TableFieldMetaInfo oldField = tableRecordCommonService.getFieldDetailByName(tableName, oldName);
        if (oldField != null) {
            oldField.setEnField(newName);
            tableFieldMetaInfoMapper.updateByPrimaryKey(oldField);
        } else {
            TableFieldMetaInfo tableFieldMetaInfo = mockUserFieldMap.get(newName);
            tableFieldMetaInfo.setFromBaidu(false);
            tableFieldMetaInfo.setTableEnName(tableInfo.getTableName());
            tableFieldMetaInfo.setDataTableId(tableInfo.getId());
            tableFieldMetaInfo.setNumber(tableRecordCommonService.queryFieldNumberMax(tableName) + 1);
            tableFieldMetaInfoMapper.insert(tableFieldMetaInfo);
        }
    }

    /**
     * 保存字段信息
     *
     * @param tableName        表名
     * @param fieldList        字段名列表
     * @param fieldMetaInfoMap 字段信息map
     */
    public void saveMetaInfo(String tableName, List<String> fieldList, Map<String, TableFieldMetaInfo> fieldMetaInfoMap) {
        DataTableInfo tableInfo = tableRecordCommonService.getTableByTableName(tableName);
        fieldList.forEach(field -> {
            TableFieldMetaInfo tableFieldMetaInfo = fieldMetaInfoMap.get(field);
            tableFieldMetaInfo.setFromBaidu(false);
            tableFieldMetaInfo.setTableEnName(tableInfo.getTableName());
            tableFieldMetaInfo.setDataTableId(tableInfo.getId());
            tableFieldMetaInfoMapper.insert(tableFieldMetaInfo);
        });

    }

    /**
     * 获取租户的表信息
     *
     * @param tenantId 租户id
     * @return
     */
    private List<DataTableInfo> getDataTableInfosForTenant(String tenantId) {
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        dataTableInfoCriteria.createCriteria()
                .andTenantidEqualTo(tenantId)
                .andDbTypeEqualTo(DbTypeEnum.DORIS_TYPE.getDbType())
                .andIsDelEqualTo(DelEnum.NOT_DELETED.getCode());
        return dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
    }

    /**
     * 添加oneId字段元信息
     */
    public void addOneIdFieldToTableMeta(Long datatableId, String tableEnName) {
        if (Objects.isNull(datatableId)) {
            log.error("添加oneId的字段元信息失败  datatableId：{}，tableEnName：{}", datatableId, tableEnName);
            return;
        }

        Date date = new Date();
        TableFieldMetaInfo tableFieldMetaInfo = new TableFieldMetaInfo();
        tableFieldMetaInfo.setDataTableId(datatableId);
        tableFieldMetaInfo.setTableEnName(tableEnName);
        tableFieldMetaInfo.setCnField(Constants.TABLE_USER_ONE_ID_CNAME);
        tableFieldMetaInfo.setEnField(Constants.TABLE_USER_ONE_ID);
        tableFieldMetaInfo.setFieldType(TableFieldTypeEnum.STRING.getValue());
        tableFieldMetaInfo.setDescription(Constants.TABLE_USER_ONE_ID_CNAME);
        tableFieldMetaInfo.setIsFilterCriteria(true);
        tableFieldMetaInfo.setIsRequired(false);
        tableFieldMetaInfo.setIsSecrete(false);
        tableFieldMetaInfo.setFromBaidu(false);
        tableFieldMetaInfo.setNumber(1000);
        tableFieldMetaInfo.setIsVisable(true);
        tableFieldMetaInfo.setFieldTag(TableFieldTagEnum.ID.getCode());
        tableFieldMetaInfo.setCreateTime(date);
        tableFieldMetaInfo.setUpdateTime(date);
        tableFieldMetaInfo.setValueType(TableFieldValueEnum.TEXT.getValueType());
        tableFieldMetaInfo.setDataType("varchar");
        tableFieldMetaInfo.setIsShowValue(true);

        tableFieldMetaInfoMapper.insert(tableFieldMetaInfo);
    }

    public void initPaloTableContent(String tenantId) {
        // 给租户所有palo表添加 oneId 字段
        String addOneIdFieldSql = "ALTER TABLE `%s` ADD COLUMN `oneId` varchar(64) COMMENT '唯一id';";
        List<DataTableInfo> dataTableInfosForTenant = getDataTableInfosForTenant(tenantId);
        for (DataTableInfo dataTableInfo : dataTableInfosForTenant) {
            String tableName = dataTableInfo.getTableName();
            String execSql = String.format(addOneIdFieldSql, tableName);
            if (dorisService.existTable(tableName)) {
                dorisService.operationSchema(execSql);
            }
        }
    }

    /**
     * 判断表是否存在 oneId
     *
     * @param dorisTableName doris 表名
     * @return
     */
    private boolean isExistOneIdFieldFromTable(String dorisTableName) {
        Map<String, String> fieldSchema = dorisService.getFieldSchema(dorisTableName);
        return fieldSchema.containsKey(String.format("%s.`%s`", dorisTableName, Constants.TABLE_USER_ONE_ID));
    }
}
