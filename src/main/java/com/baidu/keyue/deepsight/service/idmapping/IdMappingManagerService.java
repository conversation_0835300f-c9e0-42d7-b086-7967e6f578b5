package com.baidu.keyue.deepsight.service.idmapping;

import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.CreateIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.DeleteIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.UpdateIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.response.rule.IdMappingRuleFieldsResponse;

/**
 * @className: IdMappingManagerService
 * @description: idMapping管理服务
 * @author: wangz<PERSON><PERSON>
 * @date: 2025/3/12 10:34
 */
public interface IdMappingManagerService {

    /**
     * 初始化默认的ID映射关系
     */
    void initDefaultIdMapping(String tenantId);

    /**
     * 获取ID-Mapping任务的开关状态
     */
    Boolean getSwitchStatus();

    /**
     * 设置ID-Mapping任务的开关状态
     */
    void setSwitchStatus(boolean open);

    /**
     * 重新执行ID抽取
     */
    void rerunIdMapping();

    /**
     * 创建ID映射关系
     */
    void createIdMappingRel(CreateIdMappingRelRequest request);

    /**
     * 更新ID映射关系
     */
    void updateIdMappingRel(UpdateIdMappingRelRequest request);

    /**
     * 删除ID映射关系
     */
    void deleteIdMappingRel(DeleteIdMappingRelRequest request);

    /**
     * id mapping 规则可选字段查询
     */
    public IdMappingRuleFieldsResponse listIdMappingRuleField();

}
