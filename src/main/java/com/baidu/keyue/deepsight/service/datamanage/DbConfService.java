package com.baidu.keyue.deepsight.service.datamanage;

import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableFieldInfoDTO;
import com.baidu.keyue.deepsight.models.datamanage.request.GetTableContentListRequest;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className DbConfService
 * @description db接口
 * @date 2025/3/5 11:09
 */
public interface DbConfService {

    /**
     * db coll创建方法，用于建表/索引
     *
     * @return
     */
    void dbCollInit(List<TableFieldInfoDTO> fieldInfos, String tableName);

    /**
     * db update方法，用于增加字段
     *
     * @return
     */
    void dbFieldUpdate(TableFieldInfoDTO fieldInfo, String tableName);

    /**
     * 分页查询
     *
     * @return
     */
    BasePageResponse.Page<Map<String, String>> search(GetTableContentListRequest request, Boolean includeBaidu);

    /**
     * 数据记录删除
     *
     * @return
     */
    void delete(String tableName, String field, List<String> ids);

    /**
     * 数据表/索引删除
     *
     * @return
     */
    void deleteTable(String tableName);


}
