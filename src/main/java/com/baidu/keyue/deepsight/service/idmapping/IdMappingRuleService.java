package com.baidu.keyue.deepsight.service.idmapping;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.idmapping.request.rule.CreateIdMappingRuleRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.rule.DeleteIdMappingRuleRequest;
import com.baidu.keyue.deepsight.models.idmapping.response.rule.IdMappingRuleFieldsResponse;
import com.baidu.keyue.deepsight.models.idmapping.response.rule.IdMappingRuleItemResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelation;

import java.util.Map;

/**
 * @className: IdMappingRuleService
 * @description: id mapping 规则配置
 * @author: chenwenyu03
 * @date: 2025/3/10 15:51
 */
public interface IdMappingRuleService {

    /**
     * 创建 id mapping 规则
     *
     * @param request 创建 id mapping 规则 请求
     */
    void createIdMappingRule(CreateIdMappingRuleRequest request);

    /**
     * 更新 id mapping 规则
     *
     * @param request 更新 id mapping 规则 请求
     */
    void updateIdMappingRule(CreateIdMappingRuleRequest request);

    /**
     * 删除 id mapping 规则
     *
     * @param request id mapping 规则分页查询 请求
     */
    void deleteIdMappingRule(DeleteIdMappingRuleRequest request);


    /**
     * id mapping 规则分页查询
     *
     */
    BasePageResponse.Page<IdMappingRuleItemResponse> listIdMappingRule(BasePageRequest request);

    /**
     * id mapping 规则可选字段查询
     *
     */
    IdMappingRuleFieldsResponse listField(Map<String, String> enFieldCountMap );


    /**
     * id mapping 根据配置的id对抽取规则 自动添加规则
     *
     */
    void autoAddIdMappingRule(IdMappingRelation idMappingRelation);

    /**
     * 初始化默认的 id mapping 规则
     *
     * @param tenantId 租户id
     * @param enFieldtoCnFieldMap 预置数据集名和id的映射
     */
    void initDefaultIdMappingRule(String tenantId, Map<String, String> enFieldtoCnFieldMap);

    /**
     * 校验 id mapping 规则
     *
     * @param tenantId 租户id
     * @param enFieldtoCnFieldMap 预置数据集名和id的映射
     */
    void validateIdMappingRule(String tenantId, Map<String, String> enFieldtoCnFieldMap);
}
