package com.baidu.keyue.deepsight.service.dataprediction;

import java.util.List;

import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import org.apache.commons.lang3.tuple.Pair;

/**
 * 数据预测服务执行接口
 */
public interface DataPredictionCalService {

    /**
     * 获取待执行的任务
     * @return
     */
    List<Pair<DataPredictionSourceWithBLOBs, TaskInfo>> pullWaitExecTask();

    /**
     * 数据增强定时任务
     * @param left 数据增强配置
     * @param right 任务详情
     */
    void taskExec(DataPredictionSourceWithBLOBs left, TaskInfo right);

    /**
     * 根据 mock user 表做数据增强预测
     * @param tenantId 租户 id
     */
    void makeUpPredictByTenant(String tenantId);
}
