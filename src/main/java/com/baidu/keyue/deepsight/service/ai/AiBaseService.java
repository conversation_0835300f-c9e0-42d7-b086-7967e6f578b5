package com.baidu.keyue.deepsight.service.ai;

import com.baidu.keyue.deepsight.models.datamanage.dto.FieldEnumMappingDTO;
import com.baidu.keyue.deepsight.models.datamanage.dto.FieldMappingDto;
import com.baidu.keyue.deepsight.models.datamanage.dto.TableFieldInfoDTO;

import java.util.List;

/**
 * @ClassName AiBaseService
 * @Description Ai服务类
 * <AUTHOR>
 * @Date 2025/2/25 5:19 PM
 */
public interface AiBaseService {

    /**
     * 发送千帆AI请求
     *
     * @param reqStr       请求体
     * @param templatePath 模板文件路径
     * @param level        取值层级，英文逗号间隔，如data.configs
     * @param <T>
     * @return
     */
    String sendPost(String reqStr, String templatePath, String level);

    /**
     * AI字段语义映射
     *
     * @param reqStr 请求体
     * @return
     */
    List<FieldMappingDto> fieldMapping(String reqStr);

    /**
     * AI生成枚举
     *
     * @param reqStr 请求体
     * @param level  结果获取层级
     * @return
     */
    List<FieldEnumMappingDTO> genFieldEnum(String reqStr, String level);

    /**
     * AI生成字段
     *
     * @param promptContent prompt
     * @param level         结果获取层级
     * @return
     */
    List<TableFieldInfoDTO> genFieldInfo(String promptContent, String level);
}
