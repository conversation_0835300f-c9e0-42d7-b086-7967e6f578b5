package com.baidu.keyue.deepsight.service.sop.impl;

import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.idmapping.dto.DatasetKafkaMsgDTO;
import com.baidu.keyue.deepsight.service.sop.AiobSOPReCalService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * *@Author: dongjiacheng01
 * *@Description: aiob sop重跑服务实现类
 * *@Date: 11:00 2025/5/21
 */
@Service
@Slf4j
public class AiobSOPReCalServiceImpl implements AiobSOPReCalService {

    @Value("${deepSight.kafka.topic.sop}")
    private String kafkaTopic;

    private final DorisService dorisService;
    private final KafkaTemplate<String, String> kafkaTemplate;

    public AiobSOPReCalServiceImpl(DorisService dorisService,
                                   KafkaTemplate<String, String> kafkaTemplate) {
        this.dorisService = dorisService;
        this.kafkaTemplate = kafkaTemplate;
    }

    @Override
    public Void reRunQuickSOP(String tenantId, String robotVer) {
        log.info("start reRun quick sop session, tenantId: {}, robotVer: {}", tenantId, robotVer);
        // 1. 查询机器人版本下所有类型为「快捷场景」的「接通」会话
        String sessionTableName = TenantUtils.generateAiobSessionTableName(tenantId);
        List<Map<String, Object>> resultList;
        try {
            String sql = ORMUtils.getSOPAiobConnectedSession(sessionTableName, robotVer, 5);
            resultList = dorisService.selectList(sql);
        } catch (Exception e) {
            log.error("quick scenario robotVer[{}] sop rerun failed, select session from doris: {}",
                    robotVer, e.getMessage(), e);
            throw new RuntimeException("sop rerun session select failed");
        }
        if (CollectionUtils.isEmpty(resultList)) {
            log.info("quick scenario robotVer[{}] not found connected session in {}", robotVer, sessionTableName);
            return null;
        }
        // 2. 封装kafka消息、并发送
        for (Map<String, Object> row : resultList) {
            DatasetKafkaMsgDTO kafkaMsgDTO = buildKafkaMsgDtoFromSession(sessionTableName, row);
            kafkaTemplate.send(kafkaTopic, JsonUtils.toJsonUnchecked(kafkaMsgDTO))
                    .whenComplete((result, e) -> {
                        if (Objects.nonNull(e)) {
                            log.error("quick scenario sop rerun kafka msg send got error: {}", e.getMessage(), e);
                        } else {
                            log.debug("quick scenario sop rerun msg send success [Topic: {}, Partition: {}, Offset: {}]",
                                    result.getRecordMetadata().topic(),
                                    result.getRecordMetadata().partition(),
                                    result.getRecordMetadata().offset());
                        }
                    });
        }
        // 3.清除指标数据
        clearMetrics(tenantId, robotVer);
        return null;
    }

    /**
     * 从session记录中构造kafka msg dto
     */
    public DatasetKafkaMsgDTO buildKafkaMsgDtoFromSession(String sessionTableName, Map<String, Object> session) {
        DatasetKafkaMsgDTO kafkaMsgDTO = new DatasetKafkaMsgDTO();
        HashMap<String, Object> dataMap = Maps.newHashMap(session);

        if (session.containsKey("startTime")) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime startTime = (LocalDateTime) session.get("startTime");
            dataMap.put("startTime", startTime.format(formatter));
        }

        kafkaMsgDTO.setCode(sessionTableName);
        kafkaMsgDTO.setData(dataMap);
        return kafkaMsgDTO;
    }

    public void clearMetrics(String tenantId, String robotVer) {
        if (StringUtils.isBlank(robotVer) || StringUtils.isBlank(tenantId)) {
            return;
        }
        // 清除节点统计信息
        String nodeMetricTableName = TenantUtils.generateAiobSOPNodeTableName(tenantId);
        String clearNodeMetricSql = ORMUtils.generateClearSOPMetricDataSql(nodeMetricTableName, robotVer);
        try {
            dorisService.execSql(clearNodeMetricSql);
        } catch (Exception e) {
            log.error("clear node sop metric data failed, robotVer:{}, sql:{}, err: ", robotVer, clearNodeMetricSql, e);
        }

        // 清除边 统计信息
        String edgeMetricTableName = TenantUtils.generateAiobSOPEdgeTableName(tenantId);
        String clearEdgeMetricSql = ORMUtils.generateClearSOPMetricDataSql(edgeMetricTableName, robotVer);
        try {
            dorisService.execSql(clearEdgeMetricSql);
        } catch (Exception e) {
            log.error("clear edge sop metric data failed, robotVer:{}, sql:{}, err: ", robotVer, clearEdgeMetricSql, e);
        }
    }
}
