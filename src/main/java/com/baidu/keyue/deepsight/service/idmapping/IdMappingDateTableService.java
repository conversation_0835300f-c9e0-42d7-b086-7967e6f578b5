package com.baidu.keyue.deepsight.service.idmapping;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.idmapping.request.datatable.CreateIdMappingDataTableRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.datatable.DeleteIdMappingDataTableRequest;
import com.baidu.keyue.deepsight.models.idmapping.response.datatable.ListIdMappingDataTableResponse;

import java.util.List;

/**
 * @className: IdMappingDateTableService
 * @description: id mapping 数据表规则配置
 * @author: chenwenyu03
 * @date: 2025/3/11 10:50
 */
public interface IdMappingDateTableService {

    /**
     * 创建 id mapping 数据表规则
     *
     * @param request 创建 id mapping 数据表规则 请求
     */
    void createIdMappingDataTable(CreateIdMappingDataTableRequest request);

    /**
     * 删除 id mapping 数据表规则
     *
     * @param request 删除 id mapping 数据表规则 请求
     */
    void deleteIdMappingDataTable(DeleteIdMappingDataTableRequest request);

    /**
     * 查询 id mapping 数据表规则
     *
     */
    BasePageResponse.Page<ListIdMappingDataTableResponse> listIdMappingDataTable(BasePageRequest request);

    /**
     * 查询 id mapping 数据表规则
     */
    List<ListIdMappingDataTableResponse> listDataTable();

    /**
     * 初始化 id mapping 数据表规则
     */
    void initDefaultIdMappingDataTable(String tenantId);

    /**
     * 校验 id mapping 数据表规则
     *
     * @param tenantId 租户id
     */
    void validateIdMappingDateTable(String tenantId);
}
