package com.baidu.keyue.deepsight.service.tasks;

import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.models.bsc.basic.BaseCalculateContext;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskScheduler;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;

import java.util.List;
import java.util.Map;

public interface TaskSchedulerService {
    /**
     * 根据ID查询
     * @param taskIds
     * @return
     */
    List<TaskSchedulerWithBLOBs> queryLatestByTaskIds(List<Long> taskIds);

    List<TaskSchedulerWithBLOBs> queryWithIds(List<Long> ids);

    TaskSchedulerWithBLOBs queryWithId(Long id);

    Map<Long, TaskSchedulerWithBLOBs> queryTaskScheduler(List<Long> taskIds);

    long newTaskScheduler(long taskId, String userId);

    void updateScheduler(BaseCalculateContext calculateContext, TaskExecStatusEnum statusEnum);

    void updateSchedulerStatus(Long id, TaskExecStatusEnum status);

    /**
     * 查询每个 taskId 对应最新的一条执行记录
     * @param taskIds
     * @return
     */
    List<TaskSchedulerWithBLOBs> queryRunningScheduler(List<Long> taskIds);

    /**
     * 查询指定任务最近一条执行成功的记录
     * @param taskId
     * @return
     */
    TaskScheduler queryLatestFinishedRecord(Long taskId);
}
