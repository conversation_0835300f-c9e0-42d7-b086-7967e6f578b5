package com.baidu.keyue.deepsight.service.consumer;

import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.service.datamanage.TableContentService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.tenant.impl.TenantInfoServiceImpl;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.kybase.sdk.user.service.OpAdminApi;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @className AIConversationDataWoker
 * @description 客服回话数据消费
 * @date 2025/2/21 18:36
 */
@Slf4j
@Component
public class AIConversationDataWorker extends AbstractDataSyncWorker{


    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private TableRecordCommonService commonService;

    public AIConversationDataWorker(TableContentService contentService, OpAdminApi opAdminApi, TenantInfoServiceImpl tenantInfoService) {
        super(contentService, tenantInfoService);
    }


    @Override
    protected void processData(String data) {
        try {
            Map<String, Object> map = objectMapper.readValue(data, Map.class);
            Long tenantId = Long.valueOf(map.get("tenantId").toString());
            if (!validateTenantRole(tenantId, "智能客服")) {
                return;
            }

            String answer = String.valueOf(map.get("answer"));
            String answerText = buildAnswerText(answer);
            map.put("answerText", answerText);
            // 统一了useid做ID mapping
            map.put("user_id", map.get("uid"));
            DataTableInfo dataTableInfo = commonService.getTableByTableName(TenantUtils.generateKeyueRecordTableName(String.valueOf(tenantId)));
            syncData(Arrays.asList(map), String.valueOf(dataTableInfo.getId()));
        } catch (Exception e) {
            log.error("kefu ai-conversation process error. data is {}", data);
        }


    }

    private String buildAnswerText(String answer) {
        // 用于拼接 reply.text
        StringBuilder textBuilder = new StringBuilder();
        try {
            // 创建 ObjectMapper
            ObjectMapper objectMapper = new ObjectMapper();
            // 解析 JSON 数组
            JsonNode rootNode = objectMapper.readTree(answer);

            // 遍历每个 JSON 对象
            for (JsonNode node : rootNode) {
                // 检查 reply 是否存在且不为空
                JsonNode replyNode = node.get("reply");
                if (replyNode != null && replyNode.has("text")) {
                    // 拼接 reply.text
                    textBuilder.append(replyNode.get("text").asText());
                }
            }
        } catch (Exception e) {
            log.warn("build answer text failed, answer={}", answer, e);
            return answer;
        }
        return textBuilder.toString();
    }

    /**
     * 从 kafka 监听器消费消息，处理历史记录日志数据
     *
     * @param msg 消息
     */
    @KafkaListener(topics = "${deepSight.kafka.topic.keyue:deep_sight_ai_conversation_data_sync}")
    public void conversationHistoryLogConsumer(String msg) {
        log.debug("receive msg is {}", msg);
        processData(msg);
    }
}
