package com.baidu.keyue.deepsight.service.file;

import com.baidu.keyue.deepsight.models.datamanage.request.BosStsRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.StsSessionVo;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;

/**
 * @ClassName FileService
 * @Description 文件服务接口
 * <AUTHOR>
 * @Date 2025/3/28 11:17 AM
 */
public interface FileService {
    /**
     * 获取BOS STS 临时objectKey 写 授权
     *
     * @param stsRequest   sts请求
     * @param userAuthInfo 认证信息
     * @return
     */
    StsSessionVo getStsSession(BosStsRequest stsRequest, UserAuthInfo userAuthInfo);
}
