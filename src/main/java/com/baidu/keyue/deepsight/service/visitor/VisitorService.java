package com.baidu.keyue.deepsight.service.visitor;

import com.baidu.keyue.deepsight.models.visitor.request.VisitorDeleteRequest;
import com.baidu.keyue.deepsight.models.visitor.request.VisitorMigrationRequest;
import com.baidu.keyue.deepsight.models.visitor.request.VisitorQueryRequest;
import com.baidu.keyue.deepsight.models.visitor.request.VisitorSaveRequest;
import com.baidu.keyue.deepsight.models.visitor.response.VisitorQueryResponse;
import com.baidu.keyue.deepsight.models.visitor.response.VisitorSaveResponse;

/**
 * @ClassName VisitorService
 * @Description 访客管理接口
 * <AUTHOR>
 * @Date 2025/2/20 3:58 PM
 */
public interface VisitorService {
    /**
     * 查询列表
     * 优先根据ID查询
     * 其次使用手机号查询
     *
     * @param queryRequest
     * @return
     */
    VisitorQueryResponse queryList(VisitorQueryRequest queryRequest);

    /**
     * 写入数据
     *
     * @param saveRequest
     * @return 系统生成的ID
     */
    VisitorSaveResponse insert(VisitorSaveRequest saveRequest);

    /**
     * 删除数据
     * 根据手机号或者ID删除
     *
     * @param deleteRequest
     */
    void delete(VisitorDeleteRequest deleteRequest);

    /**
     * 更新数据
     * @param saveRequest
     * @return
     */
    VisitorSaveResponse update(VisitorSaveRequest saveRequest);
}
