package com.baidu.keyue.deepsight.service.dataprediction.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.PredictTypeEnum;
import com.baidu.keyue.deepsight.enums.PredictUpdateModEnum;
import com.baidu.keyue.deepsight.enums.PromptTypeEnum;
import com.baidu.keyue.deepsight.enums.SwitchEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.models.predict.PredictConfig;
import com.baidu.keyue.deepsight.models.predict.PredictDataSet;
import com.baidu.keyue.deepsight.models.predict.PredictDataSource;
import com.baidu.keyue.deepsight.models.predict.PredictSwitchUpdateRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataPredictionConfigCustomerMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataPredictionConfigMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataPredictionSourceMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.service.dataprediction.DataPredictionService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.kybase.commons.utils.JsonUtil;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 数据预测服务实现类（数据增强）
 */
@Slf4j
@Service
public class DataPredictionServiceImpl implements DataPredictionService {
    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    private DataPredictionSourceMapper dataPredictionSourceMapper;

    @Autowired
    private DataPredictionConfigMapper dataPredictionConfigMapper;

    @Autowired
    private DataPredictionConfigCustomerMapper dataPredictionConfigCustomerMapper;

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    /**
     * 初始化租户数据源配置
     * TODO：待明确具体配置内容后再完善
     * @param tenantId
     * @param userId
     */
    @Override
    @Transactional
    public void initTenantDateSourceAndConfig(String tenantId, String userId) {
        // 初始化数据增强数据源配置，往data_prediction_source插入一条记录
        initTenantDataSource(tenantId, userId);
        // 初始化数据增强内容配置表，往data_prediction_config插入一条记录
        initTenantDataConfig(tenantId, userId);
    }

    @Override
    @Transactional
    public PredictDataSource getDataSourceDetail(String tenantId, String userId) {
        try {
            DataPredictionSourceCriteria criteria = new DataPredictionSourceCriteria();
            criteria.createCriteria()
                    .andTenantIdEqualTo(tenantId)
                    .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
            List<DataPredictionSourceWithBLOBs> resultList = dataPredictionSourceMapper.selectByExampleWithBLOBs(criteria);
            if (CollectionUtils.isEmpty(resultList)) {
                initTenantDataSource(tenantId, userId);
                resultList = dataPredictionSourceMapper.selectByExampleWithBLOBs(criteria);
            }
            DataPredictionSourceWithBLOBs source = resultList.get(0);
            // 转换逻辑，将数据库表中的记录转换为PredictDataSource的字段
            PredictDataSource predictDataSource = new PredictDataSource();
            predictDataSource.setDataSourceList(filterDataSourceList(source.getDataSourceList(), tenantId));
            predictDataSource.setPromptType(PromptTypeEnum.getByCode(source.getPromptType()));
            predictDataSource.setPrompt(source.getPrompt());
            Byte predictionUpdateType = source.getPredictionUpdateType() ? (byte) 1 : (byte) 0;
            predictDataSource.setPredictionUpdateType(PredictUpdateModEnum.getByCode(predictionUpdateType));
            predictDataSource.setTriggerFrequency(TriggerFrequencyEnum.getByCode(source.getTriggerFrequency()));
            predictDataSource.setTriggerFrequencyValue(
                    JsonUtil.fromJson(source.getTriggerFrequencyValue(), TriggerFrequencyValue.class));
            predictDataSource.setDescription(source.getDescription());
            return predictDataSource;
        } catch (Exception e) {
            log.error("getDateSourceDetail error, tenantId:{}", tenantId, e);
            throw new DeepSightException.DataPredictionGetDataSourceException(ErrorCode.INTERNAL_ERROR,
                    "获取数据增强配置失败");
        }
    }

    public List<PredictDataSet> filterDataSourceList(String dataSourceListJson, String tenantId) {
        // 解析数据集列表 json
        List<PredictDataSet> sourceList = JsonUtil.fromJson(dataSourceListJson, new TypeToken<>() {
        });
        List<Long> datasetIds = sourceList.stream().map(PredictDataSet::getDatasetId).toList();
        if (CollectionUtils.isEmpty(datasetIds)) {
            return Collections.emptyList();
        }
        // 根据数据集 id + 租户 id + delete=false 查询有效数据集
        DataTableInfoCriteria criteria = new DataTableInfoCriteria();
        criteria.createCriteria()
                .andIdIn(datasetIds)
                .andTenantidEqualTo(tenantId)
                .andIsDelEqualTo(DelEnum.NOT_DELETED.getCode());
        try {
            List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExample(criteria);
            // 使用有效数据集 id，对数据增强数据集过滤
            Set<Long> validIds = dataTableInfos.stream().map(DataTableInfo::getId).collect(Collectors.toSet());
            return sourceList.stream().filter(item -> validIds.contains(item.getDatasetId())).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("DataPredict filterDataSourceList query dataTableInfos error", e);
            return Collections.emptyList();
        }
    }

    @Override
    public void updateDataSource(PredictDataSource request, String userId, String tenantId) {
        try {
            // 1.组装数据库表中的记录
            DataPredictionSourceWithBLOBs data = new DataPredictionSourceWithBLOBs();
            data.setTenantId(tenantId);
            data.setDataSourceList(JsonUtil.toJson(request.getDataSourceList()));
            data.setPromptType(request.getPromptType().getCode());
            data.setPrompt(request.getPrompt());
            data.setPredictionUpdateType(Objects.equals(request.getPredictionUpdateType().getCode(),
                    PredictUpdateModEnum.HOLD.getCode()));
            data.setTriggerMod(TriggerModeEnum.CRON.getCode());
            if (Objects.nonNull(request.getTriggerFrequency())) {
                data.setTriggerFrequency(request.getTriggerFrequency().getCode());
            }
            if (Objects.nonNull(request.getTriggerFrequencyValue())) {
                data.setTriggerFrequencyValue(JsonUtils.toJsonWithOutException(request.getTriggerFrequencyValue()));
            }
            data.setDescription(request.getDescription());
            data.setDel(DelEnum.NOT_DELETED.getBoolean());
            data.setModifier(userId);
            Date now = new Date();
            data.setUpdateTime(now);

            // 2.判断数据库表中是否存在该租户的数据增强记录
            DataPredictionSourceCriteria criteria = new DataPredictionSourceCriteria();
            criteria.createCriteria()
                    .andTenantIdEqualTo(tenantId)
                    .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
            List<DataPredictionSourceWithBLOBs> resultList = dataPredictionSourceMapper.selectByExampleWithBLOBs(criteria);
            if (CollectionUtils.isEmpty(resultList)) {
                // 之前没有该租户的记录，说明初始化失败，抛出异常
                log.error("data prediction source init failed, tenantId:{}", tenantId);
                throw new DeepSightException.DataPredictionInitFailedException();
            }
            // 之前有该租户的记录，则更新
            data.setId(resultList.get(0).getId());
            Long originTaskId = resultList.get(0).getTask();
            if (originTaskId == null) {
                // 之前没有这个任务，创建新任务
                Long taskId = taskInfoService.createCalTask(
                        TaskTypeEnum.DATA_PREDICT,
                        TriggerModeEnum.CRON,
                        request.getTriggerFrequency(),
                        request.getTriggerFrequencyValue());
                data.setTask(taskId);
            } else {
                // 之前有该任务，更新任务配置
                taskInfoService.updateCalTaskTrigger(originTaskId,
                        TriggerModeEnum.CRON,
                        request.getTriggerFrequency(),
                        request.getTriggerFrequencyValue());
            }
            if (data.getCalStatus() == null) {
                data.setCalStatus(TaskExecStatusEnum.PENDING.getCode());
            }
            dataPredictionSourceMapper.updateByPrimaryKeySelective(data);
        } catch (DeepSightException.DataPredictionInitFailedException e) {
            log.error("updateDataSource error, tenantId:{}, request={}", tenantId, JsonUtil.toJson(request), e);
            throw e;
        } catch (Exception e) {
            log.error("updateDataSource error, tenantId:{}, request={}", tenantId, JsonUtil.toJson(request), e);
            throw new DeepSightException.DataPredictionUpdateDataSourceException(ErrorCode.INTERNAL_ERROR,
                    "更新数据增强配置失败");
        }

    }

    @Override
    @Transactional
    public BasePageResponse.Page<PredictConfig> pageQueryPredictConfig(Integer pageNo,
                                                                       Integer pageSize,
                                                                       String tenantId, String userId) {
        try {
            DataPredictionConfigCriteria criteria = new DataPredictionConfigCriteria();
            criteria.createCriteria()
                    .andTenantIdEqualTo(tenantId)
                    .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
            // 1. 查询总数
            long count = dataPredictionConfigMapper.countByExample(criteria);
            if (count == 0) {
                initTenantDataConfig(tenantId, userId);
                count = dataPredictionConfigMapper.countByExample(criteria);
            }
            long offset = (long) (pageNo - 1) * pageSize;
            if (count == 0 || offset > count) {
                return BasePageResponse.Page.of(pageNo, pageSize, count, Lists.newArrayList());
            }
            // 2. 查询具体该页的数据（TODO 排序规则待定）
            criteria.setOrderByClause(String.format("prediction_type asc limit %d offset %d", pageSize, offset));
            List<DataPredictionConfig> resultList = dataPredictionConfigMapper.selectByExample(criteria);
            // 3. 查询数据集和所用字段
            List<PredictDataSet> dataSets = queryPredictDataSetAndFields(tenantId, userId);
            String datasetsName = dataSets.stream().map(PredictDataSet::getDatasetName).collect(Collectors.joining("、"));
            String fieldsName = dataSets.stream().map(PredictDataSet::getFieldName).collect(Collectors.joining("、"));
            // 4. 组装结果返回
            List<PredictConfig> predictConfigs = new ArrayList<>();
            for (DataPredictionConfig data : resultList) {
                PredictConfig predictConfig = new PredictConfig();
                predictConfig.setId(data.getId());
                predictConfig.setPredictType(
                        PredictTypeEnum.getByCode(data.getPredictionType()).getDesc());
                predictConfig.setStatus(data.getStatus() ? SwitchEnum.OFF : SwitchEnum.ON);
                predictConfig.setDatasetsName(datasetsName);
                predictConfig.setFieldsName(fieldsName);
                predictConfig.setDescription(data.getDescription());
                predictConfigs.add(predictConfig);
            }
            return BasePageResponse.Page.of(pageNo, pageSize, count, predictConfigs);
        } catch (Exception e) {
            log.error("pageQueryPredictConfig error, tenantId:{}", tenantId, e);
            throw new DeepSightException.DataPredictionDataConfigException(ErrorCode.INTERNAL_ERROR,
                    "获取预测内容配置失败");
        }
    }

    public List<PredictDataSet> queryPredictDataSetAndFields(String tenantId, String userId) {
        DataPredictionSourceCriteria criteria = new DataPredictionSourceCriteria();
        criteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        List<DataPredictionSourceWithBLOBs> resultList = dataPredictionSourceMapper.selectByExampleWithBLOBs(criteria);
        if (CollectionUtils.isEmpty(resultList)) {
            initTenantDataSource(tenantId, userId);
            resultList = dataPredictionSourceMapper.selectByExampleWithBLOBs(criteria);
        }
        DataPredictionSourceWithBLOBs source = resultList.get(0);
        return JsonUtil.fromJson(source.getDataSourceList(), new TypeToken<>() {
        });
    }

    @Override
    public void updatePredictSwitch(PredictSwitchUpdateRequest request, String userId, String tenantId) {
        try {
            Long id = request.getId();
            DataPredictionConfigCriteria criteria = new DataPredictionConfigCriteria();
            criteria.createCriteria()
                    .andIdEqualTo(id)
                    .andTenantIdEqualTo(tenantId)
                    .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
            List<DataPredictionConfig> resultList = dataPredictionConfigMapper.selectByExample(criteria);
            if (CollectionUtils.isEmpty(resultList)) {
                // 找不到该租户的配置，说明最开始初始化有问题
                log.error("data prediction config not found, id:{}, tenantId:{}", id, tenantId);
                throw new DeepSightException.DataPredictionInitFailedException();
            }
            DataPredictionConfig needModifyRecord = resultList.get(0);
            needModifyRecord.setStatus(request.getStatus().getBoolean());
            Date now = new Date();
            needModifyRecord.setModifier(userId);
            needModifyRecord.setUpdateTime(now);
            dataPredictionConfigMapper.updateByPrimaryKeySelective(needModifyRecord);
        } catch (DeepSightException.DataPredictionInitFailedException e) {
            log.error("updatePredictSwitch error, tenantId:{}, request={}", tenantId, JsonUtil.toJson(request), e);
            throw e;
        } catch (Exception e) {
            log.error("updatePredictSwitch error, tenantId:{}, request={}", tenantId, JsonUtil.toJson(request), e);
            throw new DeepSightException.DataPredictionUpdatePredictSwitchException(ErrorCode.INTERNAL_ERROR,
                    "更新数据增强开关失败");
        }
    }

    @Override
    public List<DataPredictionSourceWithBLOBs> getWaitExecPrediction(Set<Long> taskIds) {
        DataPredictionSourceCriteria criteria = new DataPredictionSourceCriteria();
        criteria.createCriteria()
                .andTaskIn(Lists.newArrayList(taskIds))
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        return dataPredictionSourceMapper.selectByExampleWithBLOBs(criteria);
    }

    @Override
    public void updateCalStatus(Long id, Byte calStatus) {
        DataPredictionSourceCriteria criteria = new DataPredictionSourceCriteria();
        criteria.createCriteria().andIdEqualTo(id);
        DataPredictionSourceWithBLOBs record = new DataPredictionSourceWithBLOBs();
        record.setCalStatus(calStatus);
        dataPredictionSourceMapper.updateByExampleSelective(record, criteria);
    }

    @Override
    public List<DataPredictionConfig> getOnPredictConfig(String tenantId) {
        DataPredictionConfigCriteria criteria = new DataPredictionConfigCriteria();
        criteria.createCriteria()
                .andTenantIdEqualTo(tenantId)
                .andStatusEqualTo(SwitchEnum.ON.getBoolean())
                .andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        return dataPredictionConfigMapper.selectByExample(criteria);
    }

    @Transactional
    public void initTenantDataSource(String tenantId, String userId) {
        DataPredictionSourceWithBLOBs data = new DataPredictionSourceWithBLOBs();
        data.setTenantId(tenantId);
        // 默认数据集配置：长期记忆提取数据
        DataTableInfo memExtractTable = getTenantMemExtractTable(tenantId);
        if (memExtractTable == null) {
            log.error("initTenantDataSource error, tenantId:{}, memExtractTable is null", tenantId);
            throw new DeepSightException.DataPredictionInitFailedException();
        }
        List<PredictDataSet> dataSourceList = new ArrayList<>();
        PredictDataSet dataSet = new PredictDataSet();
        dataSet.setDatasetId(memExtractTable.getId());
        dataSet.setDatasetName("用户长期记忆数据集");
        dataSet.setFieldId(1L);
        dataSet.setFieldName("记忆内容");
        dataSet.setIsPreset(true);
        dataSourceList.add(dataSet);
        data.setDataSourceList(JsonUtil.toJson(dataSourceList));
        data.setPromptType(PromptTypeEnum.DEFAULT.getCode());
        data.setPrompt("");
        // predictionUpdateType=true表示PredictUpdateModEnum.HOLD，即预测到结果就不在更新
        // predictionUpdateType=false表示PredictUpdateModEnum.REPLACE，即预测到结果就继续更新
        data.setPredictionUpdateType(false);
        data.setTriggerMod(TriggerModeEnum.CRON.getCode());
        // 设置每天1点执行数据增强任务
        data.setTriggerFrequency(TriggerFrequencyEnum.DAY.getCode());
        TriggerFrequencyValue triggerFrequencyValue = new TriggerFrequencyValue();
        triggerFrequencyValue.setHour(1);
        data.setTriggerFrequencyValue(JsonUtil.toJson(triggerFrequencyValue));
        data.setDel(DelEnum.NOT_DELETED.getBoolean());
        data.setCreator(userId);
        data.setModifier(userId);
        Date now = new Date();
        data.setCreateTime(now);
        data.setUpdateTime(now);

        Long taskId = taskInfoService.createCalTask(
                TaskTypeEnum.DATA_PREDICT,
                TriggerModeEnum.CRON,
                TriggerFrequencyEnum.DAY,
                triggerFrequencyValue);
        data.setTask(taskId);
        dataPredictionSourceMapper.insert(data);
    }

    /**
     * 初始化租户数据增强内容配置
     * @param tenantId
     * @param userId
     */
    private void initTenantDataConfig(String tenantId, String userId) {
        // 每个预测项都需要插入一条记录，因此使用批量插入
        List<DataPredictionConfig> dataList = new ArrayList<>();
        for (PredictTypeEnum predictTypeEnum : PredictTypeEnum.values()) {
            DataPredictionConfig data = new DataPredictionConfig();
            data.setTenantId(tenantId);
            data.setPredictionType(predictTypeEnum.getCode());
            // 默认启用
            data.setStatus(SwitchEnum.ON.getBoolean());
            data.setDel(DelEnum.NOT_DELETED.getBoolean());
            data.setCreator(userId);
            data.setModifier(userId);
            Date now = new Date();
            data.setCreateTime(now);
            data.setUpdateTime(now);
            dataList.add(data);
        }
        if (!CollectionUtils.isEmpty(dataList)) {
            dataPredictionConfigCustomerMapper.batchInsert(dataList);
        }
    }

    private DataTableInfo getTenantMemExtractTable(String tenantId) {
        DataTableInfoCriteria criteria = new DataTableInfoCriteria();
        criteria.createCriteria()
                .andTenantidEqualTo(tenantId)
                .andIsDelEqualTo(DelEnum.NOT_DELETED.getCode())
                .andTableNameLike(Constants.DORIS_MEMORY_EXTRACT_TABLE + "%");
        List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExample(criteria);
        if (CollectionUtils.isEmpty(dataTableInfos)) {
            return null;
        }
        return dataTableInfos.get(0);
    }
}
