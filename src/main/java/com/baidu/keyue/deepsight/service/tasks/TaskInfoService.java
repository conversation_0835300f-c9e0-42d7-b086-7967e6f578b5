package com.baidu.keyue.deepsight.service.tasks;

import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;

import java.util.List;

public interface TaskInfoService {

    /**
     * 新建标签计算任务
     *
     * @param triggerMod 更新类型
     * @param frequency  执行频率
     * @param value      执行频率具体时间配置
     * @return 任务ID
     */
    long createNewLabelCalTask(TriggerModeEnum triggerMod,
                               TriggerFrequencyEnum frequency, TriggerFrequencyValue value);

    /**
     * 根据ID删除任务
     *
     * @param id ID
     */
    void deleteFieldById(long id);

    /**
     * 更新标签计算任务更新类型
     *
     * @param id         主键ID
     * @param triggerMod 更新类型
     * @param frequency  执行频率
     * @param value      执行频率具体时间配置
     */
    void updateLabelCalTaskTrigger(long id, TriggerModeEnum triggerMod,
                                   TriggerFrequencyEnum frequency, TriggerFrequencyValue value);

    /**
     * 获取任务详情
     *
     * @param id 任务ID
     * @return
     */
    TaskInfo getTaskDetailWithId(Long id);

    /**
     * 根据id批量查询任务详情
     *
     * @param ids 任务ids
     * @return
     */
    List<TaskInfo> getTaskDetailWithIds(TaskTypeEnum taskTypeEnum, List<Long> ids);

    /**
     * 获取待执行的任务
     *
     * @param taskTypeEnum 任务类型
     * @param triggerMod   更新类型
     * @return
     */
    List<TaskInfo> pullWaitExecTask(TaskTypeEnum taskTypeEnum, TriggerModeEnum triggerMod);

    /**
     * 更新下次执行时间
     *
     * @param task
     */
    void updateNextCalDate(TaskInfo task);

    /**
     * 客群生产任务创建
     *
     * @param taskTypeEnum 任务类型
     * @param triggerMod   更新类型
     * @param frequency    更新频率
     * @param value        执行频率具体时间配置
     * @return 任务ID
     */
    long createCalTask(TaskTypeEnum taskTypeEnum,
                       TriggerModeEnum triggerMod,
                       TriggerFrequencyEnum frequency,
                       TriggerFrequencyValue value);

    /**
     * 创建记忆提取任务
     *
     * @param triggerMod 更新类型
     * @param frequency  更新频率
     * @param value      执行频率具体时间配置
     * @param userId     用户ID
     * @return
     */
    long createMemoryExtractTask(TriggerModeEnum triggerMod,
                                 TriggerFrequencyEnum frequency, TriggerFrequencyValue value, String userId);

    /**
     * 更新记忆提取执行频率
     *
     * @param id         任务ID
     * @param triggerMod 更新类型
     * @param frequency  更新频率
     * @param value      执行频率具体时间配置
     */
    void updateMemoryExtractTaskTrigger(long id, TriggerModeEnum triggerMod,
                                        TriggerFrequencyEnum frequency, TriggerFrequencyValue value);

    /**
     * 更新任务配置
     *
     * @param taskId                任务ID
     * @param triggerMode           更新类型
     * @param triggerFrequency      更新频率
     * @param triggerFrequencyValue 执行频率具体时间配置
     */
    void updateCalTaskTrigger(Long taskId, TriggerModeEnum triggerMode, TriggerFrequencyEnum triggerFrequency, TriggerFrequencyValue triggerFrequencyValue);

    /**
     * 创建任务
     *
     * @param taskTypeEnum          任务类型
     * @param triggerMod            更新类型
     * @param triggerFrequency      执行频率
     * @param triggerFrequencyValue 频率值
     * @return 任务ID
     */
    Long createTask(TaskTypeEnum taskTypeEnum, 
                    TriggerModeEnum triggerMod, 
                    TriggerFrequencyEnum triggerFrequency, 
                    TriggerFrequencyValue triggerFrequencyValue);
}