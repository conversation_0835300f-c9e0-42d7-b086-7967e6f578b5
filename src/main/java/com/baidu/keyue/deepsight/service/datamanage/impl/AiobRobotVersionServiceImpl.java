package com.baidu.keyue.deepsight.service.datamanage.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.models.sop.SopWholeRobotVersionResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobRobotVersion;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobRobotVersionCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.AiobRobotVersionMapper;
import com.baidu.keyue.deepsight.service.datamanage.AiobRobotVersionService;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName AiobRobotVersionServiceImpl
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/5/27 17:04
 */
@Slf4j
@Service
public class AiobRobotVersionServiceImpl implements AiobRobotVersionService {

    @Autowired
    private AiobRobotVersionMapper aiobRobotVersionMapper;

    /**
     * 获取指定租户下指定机器人的指定版本信息
     *
     * @param tenantId 租户ID
     * @param robotId  机器人ID
     * @param botVersionId 机器人版本号
     * @return 如果获取到机器人版本信息，则返回true；否则返回false
     */
    @Override
    @Cacheable(value = "aiobRobotVersion", cacheManager = "caffeineCacheManager",
            key = "{#tenantId, #robotId, #botVersionId}", unless = "#result == false")
    public boolean getAiobRobotVersion(String tenantId, String robotId, String botVersionId) {
        AiobRobotVersionCriteria criteria = new AiobRobotVersionCriteria();
        criteria.createCriteria().andTenantIdEqualTo(tenantId).andRobotIdEqualTo(robotId).andRobotVersionEqualTo(botVersionId);
        return !aiobRobotVersionMapper.selectByExample(criteria).isEmpty();
    }

    @Override
    public List<SopWholeRobotVersionResponse> getAiobRobotVersionList(String tenantId, String robotId) {
        AiobRobotVersionCriteria criteria = new AiobRobotVersionCriteria();
        criteria.createCriteria().andTenantIdEqualTo(tenantId).andRobotIdEqualTo(robotId);
        criteria.setOrderByClause(Constants.ORDER_BY_CREATE_TIME_DESC);
        List<AiobRobotVersion> aiobRobotVersionList = aiobRobotVersionMapper.selectByExample(criteria);
        List<SopWholeRobotVersionResponse> responseList = new ArrayList<>();
        for (AiobRobotVersion aiobRobotVersion : aiobRobotVersionList) {
            SopWholeRobotVersionResponse response = new SopWholeRobotVersionResponse();
            response.setRobotVersionId(aiobRobotVersion.getRobotVersion());
            response.setRobotVersionName(aiobRobotVersion.getRobotVersionName());
            response.setCreateTime(aiobRobotVersion.getCreateTime());
            responseList.add(response);
        }
        return responseList;
    }

    /**
     * 保存AIoB机器人版本信息
     *
     * @param tenantId    租户ID
     * @param robotId     机器人ID
     * @param botVersionId 机器人版本ID
     */
    @Override
    public void saveAiobRobotVersion(String tenantId, String robotId, String botVersionId, String botVersionName, Long publishTime) {
        AiobRobotVersion aiobRobotVersion = new AiobRobotVersion();
        aiobRobotVersion.setTenantId(tenantId);
        aiobRobotVersion.setRobotId(robotId);
        aiobRobotVersion.setRobotVersion(botVersionId);
        aiobRobotVersion.setRobotVersionName(botVersionName);
        aiobRobotVersion.setCreateTime(DatetimeUtils.fromTimestamp(publishTime));
        aiobRobotVersionMapper.insert(aiobRobotVersion);
    }

    @Override
    public void saveDiagramRobotVersion(String tenantId, String robotId, String botVersionId, String botVersionName, String publishTime) {
        AiobRobotVersion aiobRobotVersion = new AiobRobotVersion();
        aiobRobotVersion.setTenantId(tenantId);
        aiobRobotVersion.setRobotId(robotId);
        aiobRobotVersion.setRobotVersion(botVersionId);
        aiobRobotVersion.setRobotVersionName(botVersionName);
        aiobRobotVersion.setCreateTime(DatetimeUtils.fromDatetimeStr(publishTime, DatetimeUtils.DATE_TIME_FORMATTER));
        aiobRobotVersionMapper.insert(aiobRobotVersion);
    }
}
