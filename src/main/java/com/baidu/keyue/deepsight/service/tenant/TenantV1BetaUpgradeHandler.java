package com.baidu.keyue.deepsight.service.tenant;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.database.service.UserPropertiesService;
import com.baidu.keyue.deepsight.enums.DataTableStatusEnum;
import com.baidu.keyue.deepsight.enums.DbTypeEnum;
import com.baidu.keyue.deepsight.enums.DeleteStatusEnum;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldEncryConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.FieldEncryConfigMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TenantInfoMapper;
import com.baidu.keyue.deepsight.service.catalog.LabelCatalogService;
import com.baidu.keyue.deepsight.service.customer.CustomerGroupService;
import com.baidu.keyue.deepsight.service.datamanage.impl.AccessTokenService;
import com.baidu.keyue.deepsight.service.dataprediction.DataPredictionService;
import com.baidu.keyue.deepsight.service.memory.MemoryService;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @className TenantV1UpgradeHandler
 * @description 租户初始化第一版本
 * @date 2025/3/10 10:44
 */
@Slf4j
@Service
@Order(1)
public class TenantV1BetaUpgradeHandler extends TenantUpgradeHandler {

    @Autowired
    private TenantInfoMapper tenantInfoMapper;

    @Autowired
    private DataTableInfoMapper tableInfoMapper;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    private FieldEncryConfigMapper fieldEncryConfigMapper;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private LabelCatalogService labelCatalogService;

    @Autowired
    private DataPredictionService dataPredictionService;

    @Autowired
    private UserPropertiesService userPropertiesService;

    @Autowired
    private MemoryService memoryService;

    @Autowired
    private CustomerGroupService customerGroupService;

    @Autowired
    private TransactionTemplate transactionTemplate;
    @Autowired
    private AccessTokenService accessTokenService;


    @Value("${aiob_session_field_info:classpath:datamanage/aiob_session_service_field_info.txt}")
    private String sessionFieldsInfo;
    @Value("${usern_field_info:classpath:datamanage/user_field_info.txt}")
    private String userFieldsInfo;
    @Value("${aiob_talk_field_info:classpath:datamanage/aiob_record_field_info.json}")
    private String aiobTalkFieldsInfo;
    @Value("${customer_talk_field_info:classpath:datamanage/keyue_record_field_info.json}")
    private String customerTalkFieldsInfo;
    @Value("${user_memory:classpath:datamanage/memory_extract_field_info.json}")
    private String userMemoryFieldsInfo;
    @Autowired
    private ApplicationContext context;

    @Resource
    private TenantV1UpgradeHandler tenantV1UpgradeHandler;

    private List<TableFieldMetaInfo> userTableFieldsInfo = new ArrayList<>();
    private List<TableFieldMetaInfo> aiobTableFieldsInfo = new ArrayList<>();
    private List<TableFieldMetaInfo> aiobTalkTableFieldsInfo = new ArrayList<>();
    private List<TableFieldMetaInfo> customerTalkTableFieldsInfo = new ArrayList<>();
    private List<TableFieldMetaInfo> userMemoryTableFieldsInfo = new ArrayList<>();

    public TenantV1BetaUpgradeHandler(DorisService dorisService) {
        super(dorisService);
    }


    @PostConstruct
    private void initTableFieldsInfo() {
        try {
            InputStream userFields = context.getResource(userFieldsInfo).getInputStream();
            InputStream aiobFields = context.getResource(sessionFieldsInfo).getInputStream();
            InputStream aiobTalkFields = context.getResource(aiobTalkFieldsInfo).getInputStream();
            InputStream customerTalkFields = context.getResource(customerTalkFieldsInfo).getInputStream();
            InputStream userMemoryFields = context.getResource(userMemoryFieldsInfo).getInputStream();
            List<JSONObject> userFieldJson = JSON.parseObject(userFields, List.class);
            List<JSONObject> sessionFieldJson = JSON.parseObject(aiobFields, List.class);
            List<JSONObject> aiobTalkFieldsJson = JSON.parseObject(aiobTalkFields, List.class);
            List<JSONObject> customerTalkFieldsJson = JSON.parseObject(customerTalkFields, List.class);
            List<JSONObject> userMemoryFieldsJson = JSON.parseObject(userMemoryFields, List.class);
            userFieldJson.stream().forEach(json -> {
                json.remove("id");
                userTableFieldsInfo.add(JSON.parseObject(JSON.toJSONString(json), TableFieldMetaInfo.class));
            });
            sessionFieldJson.stream().forEach(json -> {
                json.remove("id");
                aiobTableFieldsInfo.add(JSON.parseObject(JSON.toJSONString(json), TableFieldMetaInfo.class));
            });
            aiobTalkFieldsJson.forEach(json -> aiobTalkTableFieldsInfo.add(JSON.parseObject(JSON.toJSONString(json), TableFieldMetaInfo.class)));
            customerTalkFieldsJson.forEach(json -> customerTalkTableFieldsInfo.add(JSON.parseObject(JSON.toJSONString(json), TableFieldMetaInfo.class)));
            userMemoryFieldsJson.forEach(json -> userMemoryTableFieldsInfo.add(JSON.parseObject(JSON.toJSONString(json), TableFieldMetaInfo.class)));
        } catch (Exception e) {
            log.error("read init table fields info error");
        }
        addNextHandler(tenantV1UpgradeHandler);
        setVersion(1);
    }

    @Override
    protected boolean needUpdateTenant(TenantInfo tenant) {
        return tenant == null || Objects.equals(0, tenant.getVersion());
    }

    @Override
    protected void executeUpgrade(TenantDTO tenant) {
        String tenantId = tenant.getTenantId();
        UserAuthInfo info = tenant.getAuthInfo();
        String type = tenant.getType();
        transactionTemplate.execute(status -> {
            try {
                // 初始化租户信息
                TenantInfo newInfo = initTenantInfo(tenantId, info, type);
                tenant.setTenantInfo(newInfo);
                log.info("tenantInfo finished init.tenantId is {}.", tenantId);
                // 初始化表信息&表字段信息
                initTableAndField(tenantId, type);
                log.info("tenant table and tableField finished init.tenantId is {}.", tenantId);
                // 初始化palo表&内容
                mockPaloTableContent(tenantId);
                log.info("tenant mock palo content finished init.tenantId is {}.", tenantId);
                // 初始化标签目录
                labelCatalogService.initTenantCatalog(tenantId);
                // 初始化租户数据增强数据源配置
                dataPredictionService.initTenantDateSourceAndConfig(tenantId, String.valueOf(info.getUserId()));
                // 初始化当前租户的记忆提取任务
                memoryService.initTenantMemoryExtractTask(tenantId, String.valueOf(info.getUserId()));
                // 全员客群初始化
                customerGroupService.initDefaultConsumerGroup(tenantId, String.valueOf(info.getUserId()));
                // 更新租户版本
                TenantInfo tenantInfo = tenant.getTenantInfo();
                TenantInfo tenantUpdate = new TenantInfo();
                tenantUpdate.setId(tenantInfo.getId());
                tenantUpdate.setVersion(version);
                tenantInfoMapper.updateByPrimaryKeySelective(tenantUpdate);
                tenantInfo.setVersion(version);
            } catch (Exception e) {
                log.error("Tenant v1 beta Init error!, tenantId:{}, msg is：", tenantId, e);
                status.setRollbackOnly();
                throw new DeepSightException.InitTenantFailedException(ErrorCode.INTERNAL_ERROR, "租户初始化失败");
            }
            return null;
        });

    }

    public void mockPaloTableContent(String tenantId) {
        // 创建mock_user表
        String newUserTable = TenantUtils.generateMockUserTableName(tenantId);
        dorisService.execSql(String.format("DROP TABLE IF EXISTS %s", newUserTable));
        String createUserTableSql = String.format("CREATE TABLE IF NOT EXISTS %s LIKE global_default_user", newUserTable);
        dorisService.execSql(createUserTableSql);

        // 创建mock_session表--外呼记录表
        String newSessionTable = TenantUtils.generateAiobSessionTableName(tenantId);
        dorisService.execSql(String.format("DROP TABLE IF EXISTS %s", newSessionTable));
        String createSessionTableSql = String.format("CREATE TABLE IF NOT EXISTS %s LIKE global_default_session", newSessionTable);
        dorisService.execSql(createSessionTableSql);

        // 插入mock_user数据
        String insertUserSql = String.format("INSERT INTO %s select * from global_default_user", newUserTable);
        dorisService.execSql(insertUserSql);

        // 插入mock_session数据--外呼记录表
        String insertSessionSql = String.format("INSERT INTO %s select * from global_default_session", newSessionTable);
        dorisService.execSql(insertSessionSql);

        // 创建记忆提取表
        String memoryExtractTable = TenantUtils.generateMemoryExtractTableName(tenantId);
        String createMemoryExtractTableSql = String.format("CREATE TABLE IF NOT EXISTS %s LIKE global_default_memory_info", memoryExtractTable);
        dorisService.execSql(createMemoryExtractTableSql);

        // 创建外呼对话内容表
        String aiobTaleTable = TenantUtils.generateAiobRecordTableName(tenantId);
        dorisService.execSql(String.format("DROP TABLE IF EXISTS %s", aiobTaleTable));
        String createAiobTaleTableSql = String.format("CREATE TABLE IF NOT EXISTS %s LIKE global_default_aiob_conversation_record_info", aiobTaleTable);
        dorisService.execSql(createAiobTaleTableSql);
        // 插入外呼对话数据
        String insertAiobTableSql = String.format("INSERT INTO %s select * from global_default_aiob_conversation_record_info", aiobTaleTable);
        dorisService.execSql(insertAiobTableSql);

        // 创建客服对话内容表
        String customerTalkTable = TenantUtils.generateKeyueRecordTableName(tenantId);
        String createCustomerTalkTableSql = String.format("CREATE TABLE IF NOT EXISTS %s LIKE global_default_keyue_conversation_record_info", customerTalkTable);
        dorisService.execSql(createCustomerTalkTableSql);

    }

    public void initTableAndField(String tenantId, String type) {
        // 表基本信息初始化
        // 1、获取user模版表
        String userTableName = TenantUtils.generateMockUserTableName(tenantId);
        DataTableInfo userTableInfo = initUserTableInfo(userTableName, tenantId);
        tableInfoMapper.insert(userTableInfo);
        // 2、获取外呼记录模板表aiob_conversation_session_service模板表
        String sessionTableName = TenantUtils.generateAiobSessionTableName(tenantId);
        DataTableInfo sessionTableInfo = initSessionTableInfo(sessionTableName, tenantId);
        tableInfoMapper.insert(sessionTableInfo);
        // 3、获取外呼对话内容aiob_conversation_record模板表
        String aiobTalkTableName = TenantUtils.generateAiobRecordTableName(tenantId);
        DataTableInfo aiobTalkTableInfo = initTableInfo(aiobTalkTableName, tenantId, "外呼对话内容表",
                "aiob_conversation_record", "外呼对话内容表数据源", 0, DbTypeEnum.DORIS_TYPE);
        tableInfoMapper.insert(aiobTalkTableInfo);
        // 4、获取客服对话内容keyue_conversation_record模板表
        String customerTalkTableName = TenantUtils.generateKeyueRecordTableName(tenantId);
        DataTableInfo customerTalkTableInfo = initTableInfo(customerTalkTableName, tenantId, "客服对话内容表",
                "keyue_conversation_record", "客服对话内容表数据源", 0, DbTypeEnum.DORIS_TYPE);
        tableInfoMapper.insert(customerTalkTableInfo);
        // 5、获取用户记忆提取memory_extract_info模板表
        String userMemoryTableName = TenantUtils.generateMemoryExtractTableName(tenantId);
        DataTableInfo userMemoryTableInfo = initTableInfo(userMemoryTableName, tenantId, "用户长期记忆提取表",
                "memory_extract_info", "用户长期记忆提取表数据源", 0, DbTypeEnum.DORIS_TYPE);
        tableInfoMapper.insert(userMemoryTableInfo);
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        DataTableInfoCriteria.Criteria criteria = dataTableInfoCriteria.createCriteria();
        criteria.andTableNameIn(Arrays.asList(userTableInfo.getTableName(), sessionTableInfo.getTableName()));
        List<DataTableInfo> dataTableInfos = tableInfoMapper.selectByExample(dataTableInfoCriteria);
        // 表字段信息初始化
        Optional<Long> userTableId = dataTableInfos.stream()
                .filter(info -> userTableName.equals(info.getTableName()))
                .map(DataTableInfo::getId).findFirst();
        userTableFieldsInfo.stream().forEach(fieldInfo -> {
            fieldInfo.setTableEnName(TenantUtils.generateMockUserTableName(tenantId));
            fieldInfo.setDataTableId(userTableId.get());
            // 判断该字段是否来自百度，如果来自百度，则 enFiled 字段在 user_field_from_baidu.json 文件中
            fieldInfo.setFromBaidu(userPropertiesService.filedFromBaidu(fieldInfo.getEnField()));
            tableFieldMetaInfoMapper.insert(fieldInfo);
        });
        Optional<Long> sessionTableId = dataTableInfos.stream()
                .filter(info -> sessionTableName.equals(info.getTableName()))
                .map(DataTableInfo::getId).findFirst();
        aiobTableFieldsInfo.stream().forEach(fieldInfo -> {
            fieldInfo.setTableEnName(TenantUtils.generateAiobSessionTableName(tenantId));
            fieldInfo.setDataTableId(sessionTableId.get());
            fieldInfo.setFromBaidu(false);
            tableFieldMetaInfoMapper.insert(fieldInfo);
        });
        // 存储外呼对话内容、客服对话内容、用户长期记忆提取表信息
        initDataTableMetaInfo(aiobTalkTableFieldsInfo, aiobTalkTableInfo);
        initDataTableMetaInfo(customerTalkTableFieldsInfo, customerTalkTableInfo);
        initDataTableMetaInfo(userMemoryTableFieldsInfo, userMemoryTableInfo);

        // 外呼手机号密钥初始化
        FieldEncryConfig sessionTableConfig = initFieldEncryConfig(sessionTableName, sessionTableId.get());
        fieldEncryConfigMapper.insert(sessionTableConfig);

        // 用户表手机号密钥初始化
        FieldEncryConfig userTableConfig = initFieldEncryConfig(userTableName, userTableId.get());
        fieldEncryConfigMapper.insert(userTableConfig);

        // 初始化token
        accessTokenService.createTableToken(userTableName, Long.valueOf(tenantId));
        accessTokenService.createTableToken(sessionTableName, Long.valueOf(tenantId));
        accessTokenService.createTableToken(aiobTalkTableName, Long.valueOf(tenantId));
        accessTokenService.createTableToken(customerTalkTableName, Long.valueOf(tenantId));
        accessTokenService.createTableToken(userMemoryTableName, Long.valueOf(tenantId));


    }

    /**
     * 初始化表字段信息
     *
     * @param talkTableFieldsInfo 表字段信息
     * @param tableInfo           表信息
     */
    public void initDataTableMetaInfo(List<TableFieldMetaInfo> talkTableFieldsInfo, DataTableInfo tableInfo) {
        for (TableFieldMetaInfo metaInfo : talkTableFieldsInfo) {
            metaInfo.setTableEnName(tableInfo.getTableName());
            metaInfo.setDataTableId(tableInfo.getId());
            metaInfo.setFromBaidu(false);
            tableFieldMetaInfoMapper.insert(metaInfo);
        }
    }

    public TenantInfo initTenantInfo(String tenantId, UserAuthInfo info, String type) {
        TenantInfo newTenant = new TenantInfo();
        if (Constants.TENANT_LOGIN_TYPE.equals(type)) {
            // 登录租户信息记录
            newTenant.setTenantid(tenantId);
            newTenant.setAccountid(info.getAccountId());
            newTenant.setUsername(info.getUserName());
            newTenant.setUserId(null == info.getUserId() ? null : String.valueOf(info.getUserId()));
            newTenant.setTenantSource(type);
            newTenant.setCreateTime(new Date());
            newTenant.setUpdateTime(new Date());
        }
        if (Constants.TENANT_AIOB_TYPE.equals(type)) {
            // 外呼租户信息记录
            newTenant.setTenantid(tenantId);
            newTenant.setUserId(Constants.DEFAULT_USER_ID);
            newTenant.setTenantSource(type);
            newTenant.setCreateTime(new Date());
            newTenant.setUpdateTime(new Date());
        }
        if (Constants.TENANT_DEFAULT_TYPE.equals(type)) {
            // 默认租户信息记录
            newTenant.setTenantid(tenantId);
            newTenant.setUserId(Constants.AIOB_USER_ID);
            newTenant.setTenantSource(type);
            newTenant.setCreateTime(new Date());
            newTenant.setUpdateTime(new Date());
        }
        int i = tenantInfoMapper.insertSelective(newTenant);
        return newTenant;
    }

    private FieldEncryConfig initFieldEncryConfig(String tableName, Long id) {
        FieldEncryConfig config = new FieldEncryConfig();
        String password = Constants.MOBILE_DECRYPT_KEY;
        config.setSecretKey(password);
        config.setDataTableId(String.valueOf(id));
        config.setTableEnName(tableName);
        config.setEnField("mobile");
        config.setCreateTime(new Date());
        return config;

    }


    private List<TableFieldMetaInfo> initTableFieldInfo(String tableName) {
        TableFieldMetaInfoCriteria fieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = fieldMetaInfoCriteria.createCriteria();
        criteria.andTableEnNameEqualTo(tableName);
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper
                .selectByExampleWithBLOBs(fieldMetaInfoCriteria);
        return tableFieldMetaInfos;
    }

    private DataTableInfo initUserTableInfo(String tableName, String tenantId) {
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setTableName(tableName);
        dataTableInfo.setCnName("用户基础信息表");
        dataTableInfo.setEnName("user_table");
        dataTableInfo.setDataType(1);
        dataTableInfo.setDescription("用户基础数据表数据源");
        dataTableInfo.setTenantid(tenantId);
        dataTableInfo.setStatus(DataTableStatusEnum.CREATED.getStatus().byteValue());
        dataTableInfo.setIsVisable(true);
        dataTableInfo.setIsPreset((byte) 1);
        dataTableInfo.setIsDel(DeleteStatusEnum.NORMAL.getStatus().byteValue());
        dataTableInfo.setCreateTime(new Date());
        dataTableInfo.setUpdateTime(new Date());
        dataTableInfo.setDbType(DbTypeEnum.DORIS_TYPE.getDbType());
        return dataTableInfo;
    }

    private DataTableInfo initSessionTableInfo(String tableName, String tenantId) {
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setTableName(tableName);
        dataTableInfo.setCnName("外呼记录表");
        dataTableInfo.setEnName("session_service_table");
        dataTableInfo.setDescription("外呼记录表数据源");
        dataTableInfo.setDataType(0);
        dataTableInfo.setTenantid(tenantId);
        dataTableInfo.setStatus(DataTableStatusEnum.CREATED.getStatus().byteValue());
        dataTableInfo.setIsVisable(true);
        dataTableInfo.setIsPreset((byte) 1);
        dataTableInfo.setIsDel(DeleteStatusEnum.NORMAL.getStatus().byteValue());
        dataTableInfo.setCreateTime(new Date());
        dataTableInfo.setUpdateTime(new Date());
        dataTableInfo.setDbType(DbTypeEnum.DORIS_TYPE.getDbType());
        return dataTableInfo;
    }

    /**
     * 初始化预制表信息
     *
     * @param tableName   表名
     * @param tenantId    租户ID
     * @param cnName      中文名
     * @param enName      英文名
     * @param description 描述
     * @param dataType    数据类型 0事实数据 1维度数据
     * @return
     */
    private DataTableInfo initTableInfo(String tableName,
                                        String tenantId,
                                        String cnName,
                                        String enName,
                                        String description,
                                        Integer dataType,
                                        DbTypeEnum dbType
    ) {
        DataTableInfo dataTableInfo = new DataTableInfo();
        dataTableInfo.setTableName(tableName);
        dataTableInfo.setCnName(cnName);
        dataTableInfo.setEnName(enName);
        dataTableInfo.setDescription(description);
        dataTableInfo.setDataType(dataType);
        dataTableInfo.setTenantid(tenantId);
        dataTableInfo.setStatus(DataTableStatusEnum.CREATED.getStatus().byteValue());
        dataTableInfo.setIsVisable(true);
        dataTableInfo.setIsPreset((byte) 1);
        dataTableInfo.setIsDel(DeleteStatusEnum.NORMAL.getStatus().byteValue());
        dataTableInfo.setCreateTime(new Date());
        dataTableInfo.setUpdateTime(new Date());
        dataTableInfo.setDbType(dbType.getDbType());
        return dataTableInfo;
    }
}
