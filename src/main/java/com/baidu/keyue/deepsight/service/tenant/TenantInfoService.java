package com.baidu.keyue.deepsight.service.tenant;

import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;

import java.util.List;

/**
 * <AUTHOR>
 * @className TenantInfoService
 * @description 租户信息
 * @date 2025/1/17 12:44
 */
public interface TenantInfoService {

    /**
     * 查租户详情
     *
     * @param tenantId 租户ID
     * @return
     */
    TenantInfo queryTenantInfo(String tenantId);

    /**
     * 添加租户
     *
     * @param tenantInfo 租户信息
     */
    void saveTenantInfo(TenantInfo tenantInfo);

    /**
     * 初始化租户
     * 1、初始化租户信息
     * 2、初始化表信息&表字段信息
     * 3、初始化palo表&内容
     * 4、初始化标签目录
     * 5、初始化租户数据增强数据源配置
     * 6、初始化当前租户的记忆提取任务
     * 7、全员客群初始化
     * 8、初始化id-mapping表
     *
     * @param tenantDTO 租户升级类
     */
    void initOrUpgradeTenant(TenantDTO tenantDTO);

    /**
     * 获取默认租户
     *
     * @return
     */
    TenantInfo getDefaultTenant();


    /**
     * 查询待升级的租户列表
     * @param version 当前系统版本
     * @return
     */
    List<TenantInfo> getUpgradeTenant(Integer version);

    /**
     * 查询所有租户信息
     * @return
     */
    List<TenantInfo> getAllTenantInfo();
}
