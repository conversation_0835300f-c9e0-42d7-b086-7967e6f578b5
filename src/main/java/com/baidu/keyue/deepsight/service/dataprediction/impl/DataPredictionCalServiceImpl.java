package com.baidu.keyue.deepsight.service.dataprediction.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.DataPredictCalculateConfiguration;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.PredictTypeEnum;
import com.baidu.keyue.deepsight.enums.PredictUpdateModEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.bsc.datapredict.DataPredictMessage;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.models.predict.PredictDataSet;
import com.baidu.keyue.deepsight.models.predict.cal.DataPredictionCalculateContext;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.service.datamanage.DataTableManageService;
import com.baidu.keyue.deepsight.service.datamanage.impl.TableRecordCommonService;
import com.baidu.keyue.deepsight.service.dataprediction.DataPredictionCalService;
import com.baidu.keyue.deepsight.service.dataprediction.DataPredictionService;
import com.baidu.keyue.deepsight.service.rules.RuleManagerService;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import com.baidu.keyue.deepsight.utils.MobileProcessUtils;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.utils.XID;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DataPredictionCalServiceImpl implements DataPredictionCalService {
    @Autowired
    private DataPredictionService dataPredictionService;

    @Autowired
    private TaskSchedulerService taskSchedulerService;

    @Autowired
    private DataPredictCalculateConfiguration dataPredictCalculateConfiguration;

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;
    @Autowired
    private DataTableManageService dataTableManageService;


    @Autowired
    private RuleManagerService ruleManagerService;

    @Autowired
    private TaskInfoService taskInfoService;

    @Autowired
    protected DorisService dorisService;

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    @Autowired
    private TableRecordCommonService tableRecordCommonService;

    @Override
    public List<Pair<DataPredictionSourceWithBLOBs, TaskInfo>> pullWaitExecTask() {
        List<TaskInfo> taskInfoList = taskInfoService.pullWaitExecTask(TaskTypeEnum.DATA_PREDICT, TriggerModeEnum.CRON);
        Map<Long, TaskInfo> taskMap = taskInfoList.stream()
                .collect(Collectors.toMap(TaskInfo::getId, Function.identity(), (k1, k2) -> k2));
        if (taskMap.isEmpty()) {
            log.info("DataPredictionCalService.pullWaitExecTask Got Empty");
            return Collections.emptyList();
        }

        List<DataPredictionSourceWithBLOBs> list = dataPredictionService.getWaitExecPrediction(taskMap.keySet());
        if (CollectionUtils.isEmpty(list)) {
            log.info("DataPredictionCalService.getWaitExecPrediction Got Empty");
            return Collections.emptyList();
        }

        return list.stream()
                .map(item -> {
                            Long taskId = item.getTask();
                            if (Objects.isNull(taskId)) {
                                return null;
                            }
                            TaskInfo task = taskMap.get(taskId);
                            if (Objects.isNull(task)) {
                                return null;
                            }
                            return new ImmutablePair<>(item, task);
                        }
                ).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public void taskExec(DataPredictionSourceWithBLOBs dataPredictionSource, TaskInfo taskInfo) {
        List<DataPredictionConfig> onPredictConfig = dataPredictionService.getOnPredictConfig(dataPredictionSource.getTenantId());
        if (CollectionUtils.isEmpty(onPredictConfig)) {
            // 没有需要预测的属性，直接返回
            log.info("dataPredictionSource.taskId:{}, no need predict attribute", dataPredictionSource.getTask());
            return;
        }

        long execId = newSchedulerRecord(dataPredictionSource.getTask());
        DataPredictionCalculateContext calculateExecInstance = new DataPredictionCalculateContext();
        calculateExecInstance.setExecId(execId);
        calculateExecInstance.setInstanceId("");
        calculateExecInstance.setDataPredictionSource(dataPredictionSource);
        TriggerModeEnum triggerMode = TriggerModeEnum.getByCode(dataPredictionSource.getTriggerMod());
        calculateExecInstance.setTriggerMode(triggerMode);

        HashSet<String> predictAttributes = genPredictAttributes(onPredictConfig);
        List<PredictDataSet> predictDataSets = JsonUtils.toListUnchecked(dataPredictionSource.getDataSourceList(),
                List.class, PredictDataSet.class);
        predictDataSets.forEach(predictDataSet -> {
            DataTableInfo tableInfo = dataTableInfoMapper.selectByPrimaryKey(predictDataSet.getDatasetId());
            if (Objects.isNull(tableInfo)) {
                log.info("dataPredictionSource.taskId:{}, tableInfo is null", dataPredictionSource.getId());
                return;
            }
            if (!tableInfo.getTableName().startsWith(Constants.DORIS_MEMORY_EXTRACT_TABLE)) {
                log.info("dataPredictionSource.taskId:{}, not memory table: {}", dataPredictionSource.getId(), tableInfo.getTableName());
                return;
            }

            String userTable = TenantUtils.generateMockUserTableName(tableInfo.getTenantid());
            DataTableInfo userTableDetail = dataTableManageService.getTableDetailWithTableName(userTable);
            if (Objects.isNull(userTableDetail)) {
                log.info("dataPredictionSource.taskId:{}, user table is null", dataPredictionSource.getId());
                return;
            }
            Map<String, String> fieldEncryptInfo = tableRecordCommonService.getEncryptFields(userTableDetail.getId());

            try {
                List<DataPredictMessage> messages = fetchDataRows(dataPredictionSource, tableInfo, predictAttributes, fieldEncryptInfo);
                if (CollectionUtils.isNotEmpty(messages)) {
                    messages.forEach(this::kafkaMessageSend);
                }
                log.info("dataPredictionSource.taskId:{}, messages-size:{}", dataPredictionSource.getTask(), messages.size());
            } catch (Exception e) {
                log.error("dataPredictionSource.taskId:{}, fetchDataRows error. ", dataPredictionSource.getTask(), e);
            }
        });

        onFinished(calculateExecInstance, taskInfo);
    }

    public TableFieldMetaInfo getFiledMetaInfoMap(long fieldId, long tableId) {
        TableFieldMetaInfoCriteria fieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = fieldMetaInfoCriteria.createCriteria();
        criteria.andIdEqualTo(fieldId).andDataTableIdEqualTo(tableId);
        List<TableFieldMetaInfo> fieldInfos = tableFieldMetaInfoMapper.selectByExample(fieldMetaInfoCriteria);
        if (org.springframework.util.CollectionUtils.isEmpty(fieldInfos)) {
            return null;
        }
        return fieldInfos.get(0);
    }

    private HashSet<String> genPredictAttributes(List<DataPredictionConfig> onPredictConfig) {
        return onPredictConfig.stream().map(dataPredictionConfig -> {
            PredictTypeEnum predictType = PredictTypeEnum.getByCode(dataPredictionConfig.getPredictionType());
            if (predictType != null) {
                String attribute = predictType.getDesc();
                if (PredictTypeEnum.AGE_GROUP.equals(predictType)) {
                    attribute = "年龄";
                } else if (PredictTypeEnum.INTERESTS.equals(predictType)) {
                    attribute = "兴趣关注";
                }
                return attribute;
            }
            return null;
        }).filter(StringUtils::isNotBlank).collect(Collectors.toCollection(HashSet::new));
    }

    private long newSchedulerRecord(long taskId) {
        return taskSchedulerService.newTaskScheduler(taskId, "CronScheduler");
    }

    protected void onFinished(DataPredictionCalculateContext execContext, TaskInfo task) {
        // 更新调度记录
        taskSchedulerService.updateSchedulerStatus(execContext.getExecId(), TaskExecStatusEnum.SUCCESS);
        // 更新数据预测配置表里面计算状态
        dataPredictionService.updateCalStatus(execContext.getDataPredictionSource().getId(),
                TaskExecStatusEnum.SUCCESS.getCode());
        // 更新下一次任务的执行时间
        if (!TriggerModeEnum.REALTIME.equals(execContext.getTriggerMode())) {
            taskInfoService.updateNextCalDate(task);
        }
    }

    private List<DataPredictMessage> fetchDataRows(
            DataPredictionSourceWithBLOBs dataPredictionSource,
            DataTableInfo tableInfo,
            HashSet<String> predictAttributes, Map<String, String> fieldEncryptInfo) {
        String tableName = tableInfo.getTableName();

        List<Map<String, Object>> recordList;
        if (!tableName.startsWith(Constants.DORIS_MEMORY_EXTRACT_TABLE)) {
            return Lists.newArrayList();
        }

        TriggerFrequencyEnum triggerFrequencyEnum = TriggerFrequencyEnum.getByCode(dataPredictionSource.getTriggerFrequency());
        TriggerFrequencyValue triggerFrequencyValue = JsonUtils.toObjectWithoutException(dataPredictionSource.getTriggerFrequencyValue(), TriggerFrequencyValue.class);

        String mockUserTable = TenantUtils.generateMockUserTableName(dataPredictionSource.getTenantId());
        String sqlStr = ORMUtils.getDataPredictSql(mockUserTable, tableName, DatetimeUtils.backToWithFrequency(triggerFrequencyEnum, triggerFrequencyValue));

        try {
            log.info("dataPredictionSource.fetchDataRows taskId:{}, sqlStr:{}", dataPredictionSource.getTask(), sqlStr.replace("\n", " "));
            recordList = dorisService.selectList(sqlStr);
        } catch (Exception e) {
            log.error("DataPredictionCalService.fetchDataRows selectList error", e);
            return Lists.newArrayList();
        }
        return buildDataPredictMessage(dataPredictionSource, predictAttributes, tableInfo, recordList, fieldEncryptInfo);
    }

    private List<DataPredictMessage> buildDataPredictMessage(DataPredictionSourceWithBLOBs dataPredictionSource,
                                                             HashSet<String> predictAttributes,
                                                             DataTableInfo tableInfo,
                                                             List<Map<String, Object>> recordList, Map<String, String> fieldEncryptInfo) {
        // sessionId --> 消息内容，用于合并相同sessionId的消息内容
        HashMap<String, DataPredictMessage> sessionIdMap = new HashMap<>();
        for (int i = 0; i < recordList.size(); i++) {
            Map<String, Object> record = recordList.get(i);
            String oneId = (String) record.get("oneId");
            String text = (String) record.get("content");
            String sessionId = (String) record.get("id");
            String type = (String) record.get("type");
            String mobile = (String) record.get("mobile");
            String originalMobile = MobileProcessUtils.decryptMobile(mobile, fieldEncryptInfo);

            if (StringUtils.isBlank(text)) {
                continue;
            }

            if (StringUtils.isBlank(sessionId) || "null".equals(sessionId)) {
                sessionId = XID.generateRequestID();
            }

            Byte predictionUpdateType = dataPredictionSource.getPredictionUpdateType() ? (byte) 1 : (byte) 0;
            DataPredictMessage.Content content = new DataPredictMessage.Content();
            content.setText(text);
            content.setType(type);
            if (sessionIdMap.containsKey(sessionId)) {
                DataPredictMessage message = sessionIdMap.get(sessionId);
                message.getContents().add(content);
            } else {
                DataPredictMessage message = new DataPredictMessage();
                message.setModelUrl(dataPredictCalculateConfiguration.getModelUrl());
                message.setTenantId(dataPredictionSource.getTenantId());
                message.setPrompt(dataPredictionSource.getPrompt());
                message.setOneId(oneId);
                message.setOriginalMobile(originalMobile);
                message.setMode(Objects.requireNonNull(PredictUpdateModEnum.getByCode(predictionUpdateType)).getCode().intValue());
                message.setPredictAttributes(predictAttributes);
                message.setExternalId(sessionId);
                List<DataPredictMessage.Content> contents = Lists.newArrayList();
                contents.add(content);
                message.setContents(contents);
                sessionIdMap.put(sessionId, message);
            }
        }
        return new ArrayList<>(sessionIdMap.values());
    }

    private void kafkaMessageSend(DataPredictMessage data) {
        try {
            String msg = JsonUtils.toJson(data);
            kafkaTemplate.send(dataPredictCalculateConfiguration.getKafkaConfig().getKafkaTopic(), msg);
        } catch (Exception e) {
            log.error("kafka message send error", e);
        }
    }

    @Override
    public void makeUpPredictByTenant(String tenantId) {
        String userTableName = TenantUtils.generateMockUserTableName(tenantId);
        DataTableInfo userTableDetail = dataTableManageService.getTableDetailWithTableName(userTableName);
        if (Objects.isNull(userTableDetail)) {
            log.info("dataPredictionSource.tenantId:{}, user table is null", tenantId);
            return;
        }
        Map<String, String> fieldEncryptInfo = tableRecordCommonService.getEncryptFields(userTableDetail.getId());

        List<DataPredictionConfig> onPredictConfig = dataPredictionService.getOnPredictConfig(tenantId);
        if (CollectionUtils.isEmpty(onPredictConfig)) {
            log.warn("dataPredictionSource.tenantId:{}, onPredictConfig is empty", tenantId);
            return;
        }
        HashSet<String> predictAttributes = genPredictAttributes(onPredictConfig);

        String sqlStr = String.format("select oneId,mobile from %s where oneId != '' AND mobile != ''", userTableName);

        List<Map<String, Object>> recordList;
        try {
            recordList = dorisService.selectList(sqlStr);
            log.info("DataPredictionCalService.fetchDataRows selectList success, recordList size:{}", recordList.size());
        } catch (Exception e) {
            log.error("DataPredictionCalService.fetchDataRows selectList error", e);
            return;
        }

        recordList.stream().map(record -> {
                    String oneId = (String) record.get("oneId");
                    String mobile = (String) record.get("mobile");
                    String originalMobile = MobileProcessUtils.decryptMobile(mobile, fieldEncryptInfo);

                    DataPredictMessage message = new DataPredictMessage();
                    message.setModelUrl(dataPredictCalculateConfiguration.getModelUrl());
                    message.setTenantId(tenantId);
                    message.setPrompt("");
                    message.setOneId(oneId);
                    message.setOriginalMobile(originalMobile);
                    message.setMode((int) PredictUpdateModEnum.REPLACE.getCode());
                    message.setPredictAttributes(predictAttributes);
                    message.setExternalId("");
                    List<DataPredictMessage.Content> contents = Lists.newArrayList(
                            new DataPredictMessage.Content("", "event")
                    );
                    message.setContents(contents);
                    return message;
                })
                .toList()
                .forEach(this::kafkaMessageSend);
    }
}
