package com.baidu.keyue.deepsight.service.datamanage.impl;

import com.baidu.keyue.deepsight.mysqldb.entity.AccessToken;
import com.baidu.keyue.deepsight.mysqldb.entity.AccessTokenCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.AccessTokenMapper;
import com.baidu.keyue.deepsight.utils.AESUtils;
import com.baidu.keyue.deepsight.utils.UUIDUtils;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Date;

/**
 * <AUTHOR>
 * @className AccessTokenServiceImpl
 * @description
 * @date 2025/2/17 12:02
 */
@Service
@Slf4j
public class AccessTokenService {

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private TableRecordCommonService commonService;


    @Autowired
    private AccessTokenMapper tokenMapper;


    /**
     * aes加密 私钥
     */
    private static final String PRIVATE_KEY = "helloworld";

    /**
     * 解析tableName
     */
    private static final String TABLE_REGEX = "dp-api-auth-v1/.*?/";

    /***
     * 默认缓存30天
     */
    @Value("${access.token.cache.expire.seconds:2592000}")
    private int expireRedisSeconds;

    /**
     * token版本号
     */
    public static final String TOKEN_VERSION_V1 = "dp-api-auth-v1/";

    /***
     * 默认缓存30天
     */
    @Value("${access.token.max:1000}")
    private int maxAccessToken;


    private String getAccessToken(final String accessToken) {
        String keyPrefix = "access_token_v2:";
        return keyPrefix + accessToken;
    }

    public void createTableToken(String tableName, Long tenantId) {
        AccessToken token = new AccessToken();
        token.setAccessKey(UUIDUtils.genUUID());
        token.setTenantId(tenantId);
        token.setSecretKey(AESUtils.encrypt(UUIDUtils.genUUID(), PRIVATE_KEY));
        token.setCreateTime(new Date());
        token.setAccessKeyDesc("数据表token");
        token.setTableName(tableName);
        tokenMapper.insert(token);
    }

    public String getTableToken(String tableName, Long tenantId) {
        AccessTokenCriteria criteria = new AccessTokenCriteria();
        criteria.createCriteria().andTableNameEqualTo(tableName).andTenantIdEqualTo(tenantId);
        AccessToken val = tokenMapper.selectByExample(criteria).get(0);
        return createRefreshToken(val.getAccessKey(), val.getSecretKey(), tableName);
    }


    private String genRefreshToken(final String ak, final String sk, String tableName) {
        return TOKEN_VERSION_V1  + tableName + "/" + AESUtils.encrypt(ak, sk);

    }

    private String getAkSkKey(final String ak, final String sk) {
        return "ak_sk:" + ak + ":" + sk;
    }


    public AccessToken fetchAccessBeanFromCache(final String ak, final String sk) {
        final String key = getAkSkKey(ak, sk);

        try {
            RBucket<AccessToken> bucket = redisson.getBucket(key);
            AccessToken val = bucket.get();
            if (val != null) {
                return val;
            }
            AccessTokenCriteria criteria = new AccessTokenCriteria();
            criteria.createCriteria().andAccessKeyEqualTo(ak).andSecretKeyEqualTo(sk);
            val = tokenMapper.selectByExample(criteria).get(0);
            if (val != null) {
                val.setSecretKey(sk);
                RBucket<AccessToken> beanRBucket = redisson.getBucket(key);
                beanRBucket.set(val, Duration.ofSeconds(5));
                return val;
            }
        } catch (Exception e) {
            log.error("redis get error", e);
        }
        return null;
    }

    private String getAccessListKey(final String tenantId, final String accessToken) {
        String keyPrefix = "access_token_v2_list:";
        return keyPrefix + tenantId + ":" + accessToken;
    }

    private String createRefreshToken(final String ak, final String sk, String tableName) {
        String token = genRefreshToken(ak, sk, tableName);

        AccessToken val = fetchAccessBeanFromCache(ak, sk);
        if (val == null) {
            return null;
        }
        try {
            RBucket<AccessToken> bucket = redisson.getBucket(getAccessToken(token));
            bucket.set(val);
            RList<String> list =
                    redisson.getList(getAccessListKey(String.valueOf(val.getTenantId()), ak));
            // 给当前值加入到第一个个位置，不然达到阀值后，会删除最新的
            list.add(0, token);
            if (list.size() > maxAccessToken) {
                String expireToken = list.remove(maxAccessToken);
                if (StringUtils.isNotBlank(expireToken)) {
                    removeAccessToken(expireToken);
                }
            }
            return token;
        } catch (Exception e) {
            log.error("redis set error", e);
        }

        return null;
    }


    private void removeAccessToken(final String accessToken) {
        RBucket<AccessToken> bucket = redisson.getBucket(getAccessToken(accessToken));
        bucket.delete();
    }
    public Boolean validateAuthorization(HttpServletRequest request,
                                         final String accessToken) {
        RBucket<AccessToken> bucket = redisson.getBucket(getAccessToken(accessToken));
        if (null != bucket.get()) {
            String tableName = bucket.get().getTableName();
            DataTableInfo info = commonService.getTableByTableName(tableName);
            request.setAttribute("dataTableId", info.getId());
            request.setAttribute("dbType", info.getDbType());
            return true;
        }
        return false;
    }




}
