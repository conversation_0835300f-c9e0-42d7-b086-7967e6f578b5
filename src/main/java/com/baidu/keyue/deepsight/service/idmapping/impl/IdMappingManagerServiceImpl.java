package com.baidu.keyue.deepsight.service.idmapping.impl;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.PresetEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.idmapping.dto.IdMappingRuleRedisDTO;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.CreateIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.DeleteIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.request.relation.UpdateIdMappingRelRequest;
import com.baidu.keyue.deepsight.models.idmapping.response.rule.IdMappingRuleFieldsResponse;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTableCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTableWithRule;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGenerator;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGeneratorCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelationCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRuleCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.DataTableInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendIdMappingDataTableMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendTaskInfoMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingDataTableMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingGeneratorMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRelationMapper;
import com.baidu.keyue.deepsight.mysqldb.mapper.IdMappingRuleMapper;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingDateTableService;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingManagerService;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingRelService;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingResetCalculateService;
import com.baidu.keyue.deepsight.service.idmapping.IdMappingRuleService;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @className: IdMappingManagerServiceImpl
 * @description: id-mapping 管理服务实现类
 * @author: wangzhongcheng
 * @date: 2025/3/12 10:35
 */
@Slf4j
@Service
public class IdMappingManagerServiceImpl implements IdMappingManagerService {

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private IdMappingRelService idMappingRelService;

    @Autowired
    private IdMappingRuleService idMappingRuleService;

    @Autowired
    private IdMappingDateTableService idMappingDateTableService;

    @Autowired
    private DorisService dorisService;

    @Autowired
    private DataTableInfoMapper dataTableInfoMapper;

    @Autowired
    private ExtendIdMappingDataTableMapper extendIdMappingTableMapper;

    @Autowired
    private IdMappingRelationMapper idMappingRelMapper;

    @Autowired
    private IdMappingRuleMapper idMappingRuleMapper;

    @Autowired
    private IdMappingDataTableMapper idMappingDataTableMapper;

    @Autowired
    private ExtendTaskInfoMapper taskInfoMapper;

    @Autowired
    private IdMappingGeneratorMapper idMappingGeneratorMapper;

    @Autowired
    private IdMappingResetCalculateService idMappingResetCalculateService;

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void initDefaultIdMapping(String tenantId) {
        clearIdMapping(tenantId);
        DataTableInfoCriteria dataTableInfoCriteria = new DataTableInfoCriteria();
        dataTableInfoCriteria.createCriteria()
                .andTenantidEqualTo(tenantId)
                .andIsPresetEqualTo(PresetEnum.PRESET.getCode());
        List<DataTableInfo> dataTableInfos = dataTableInfoMapper.selectByExample(dataTableInfoCriteria);
        Map<String, Long> presetDataTableNameMap = dataTableInfos.stream()
                .collect(Collectors.toMap(DataTableInfo::getTableName, DataTableInfo::getId, (v1, v2) -> v2));
        // 初始化默认的id-mapping 关系
        idMappingRelService.initDefaultIdMappingRel(tenantId, presetDataTableNameMap);

        Map<String, String> enFieldtoCnFieldMap = idMappingRelService.getEnFieldtoCnFieldMap(tenantId);
        idMappingRuleService.initDefaultIdMappingRule(tenantId, enFieldtoCnFieldMap);
        idMappingDateTableService.initDefaultIdMappingDataTable(tenantId);

        // 初始化id-mapping重置任务记录
        initIdMappingResetTask(tenantId);

        // 重新执行id-mapping任务
        rerunIdMapping(tenantId, Constants.SYSTEM_DEFAULT_USER_ID);
    }

    public void initIdMappingResetTask(String tenantId) {
        IdMappingGeneratorCriteria idMappingGeneratorCriteria = new IdMappingGeneratorCriteria();
        idMappingGeneratorCriteria.createCriteria()
                .andTenantIdEqualTo(tenantId).andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        long count = idMappingGeneratorMapper.countByExample(idMappingGeneratorCriteria);
        if (count > 0) {
            return;
        }
        Date now = new Date();
        TaskInfo taskInfo = new TaskInfo();
        taskInfo.setTaskType(TaskTypeEnum.ID_MAPPING_RESET.getCode());
        taskInfo.setTriggerMod(TriggerModeEnum.MANUAL.getCode());
        taskInfo.setDel(DelEnum.NOT_DELETED.getBoolean());
        taskInfo.setCreator(Constants.SYSTEM_DEFAULT_USER_ID);
        taskInfo.setModifier(Constants.SYSTEM_DEFAULT_USER_ID);
        taskInfo.setCreateTime(now);
        taskInfo.setUpdateTime(now);
        taskInfoMapper.insert(taskInfo);

        IdMappingGenerator idMappingGenerator = new IdMappingGenerator();
        idMappingGenerator.setTaskId(taskInfo.getId());
        idMappingGenerator.setTenantId(tenantId);
        idMappingGenerator.setCreateTime(now);
        idMappingGenerator.setUpdateTime(now);
        idMappingGenerator.setDel(DelEnum.NOT_DELETED.getBoolean());
        idMappingGenerator.setCreator(Constants.SYSTEM_DEFAULT_USER_ID);
        idMappingGenerator.setModifier(Constants.SYSTEM_DEFAULT_USER_ID);
        idMappingGenerator.setCalStatus(TaskExecStatusEnum.PENDING.getCode());
        idMappingGenerator.setTriggerMod(TriggerModeEnum.MANUAL.getCode());
        idMappingGeneratorMapper.insert(idMappingGenerator);
    }

    @Transactional(rollbackOn = Exception.class)
    public void clearIdMapping(String tenantId) {
        IdMappingRelationCriteria idMappingRelCriteria = new IdMappingRelationCriteria();
        idMappingRelCriteria.createCriteria().andTenantIdEqualTo(tenantId);
        idMappingRelMapper.deleteByExample(idMappingRelCriteria);

        IdMappingRuleCriteria idMappingRuleCriteria = new IdMappingRuleCriteria();
        idMappingRuleCriteria.createCriteria().andTenantIdEqualTo(tenantId);
        idMappingRuleMapper.deleteByExample(idMappingRuleCriteria);

        IdMappingDataTableCriteria idMappingDataTableCriteria = new IdMappingDataTableCriteria();
        idMappingDataTableCriteria.createCriteria().andTenantIdEqualTo(tenantId);
        idMappingDataTableMapper.deleteByExample(idMappingDataTableCriteria);
    }

    @Override
    public Boolean getSwitchStatus() {
        // 从redis里获取id-mapping开关状态
        String key = Constants.IDMAPPING_PREFIX + WebContextHolder.getTenantId();
        RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);
        return bucket != null && bucket.get() != null && "true".equals(bucket.get());
    }

    @Override
    public void setSwitchStatus(boolean open) {
        setSwitchStatus(open, WebContextHolder.getTenantId());
    }

    public void setSwitchStatus(boolean open, String tenantId) {
        // 校验是否可以开启
        if (open) {
            canSetSwitchStatus(tenantId);
        }

        // 设置id-mapping开关状态
        String key = Constants.IDMAPPING_PREFIX + tenantId;
        RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);
        if (bucket != null) {
            bucket.set(open ? "true" : "false");
        }

        // 如果开启成功，则添加缓存
        if (bucket != null && open) {
            addFieldToTableAndRefreshCache(tenantId);
        }
    }

    /**
     * 检查 id-mapping 配置是否存在错误 错误的情况下不运行打开
     */
    private void canSetSwitchStatus(String tenantId) {
        Map<String, String> enFieldtoCnFieldMap = idMappingRelService.getEnFieldtoCnFieldMap(tenantId);
        idMappingRuleService.validateIdMappingRule(tenantId, enFieldtoCnFieldMap);
        idMappingDateTableService.validateIdMappingDateTable(tenantId);
        idMappingRelService.validateIdMappingDateTable(tenantId);
    }

    @Override
    public void rerunIdMapping() {
        // 如果id-mapping 开启状态不允许变更
        String key = Constants.IDMAPPING_PREFIX + WebContextHolder.getTenantId();
        RBucket<String> bucket = redisson.getBucket(key, StringCodec.INSTANCE);
        if (bucket != null && bucket.get() != null && "true".equals(bucket.get())) {
            throw new DeepSightException.ParamsErrorException(ErrorCode.BAD_REQUEST, "ID-Mapping启动中，不允许重新运行");
        }

        rerunIdMapping(WebContextHolder.getTenantId(), WebContextHolder.getUserId());
    }

    public void rerunIdMapping(String tenantId, String userId) {

        // 重新运行时开启开关
        setSwitchStatus(true, tenantId);

        // 手动执行 - 如果没初始化则进行初始化
        initIdMappingResetTask(tenantId);
        idMappingResetCalculateService.execByManual(tenantId, userId);
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void createIdMappingRel(CreateIdMappingRelRequest request) {
        // 创建时需要动态获取优先级，防止并发问题
        String tenantId = WebContextHolder.getTenantId();
        RLock lock = redisson.getLock(idMappingLock(tenantId));
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }

        try {
            idMappingRelService.createIdMappingRel(request);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }

    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void updateIdMappingRel(UpdateIdMappingRelRequest request) {
        // 更新时需要动态获取优先级，防止并发问题
        String tenantId = WebContextHolder.getTenantId();
        RLock lock = redisson.getLock(idMappingLock(tenantId));
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }

        try {
            idMappingRelService.updateIdMappingRel(request);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public void deleteIdMappingRel(DeleteIdMappingRelRequest request) {
        Long idMappingRelId = request.getIdMappingRelId();
        String tenantId = WebContextHolder.getTenantId();
        RLock lock = redisson.getLock(idMappingLock(tenantId, String.valueOf(idMappingRelId)));
        if (!lock.tryLock()) {
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }

        try {
            idMappingRelService.deleteIdMappingRel(request);
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Override
    @Transactional(rollbackOn = Exception.class)
    public IdMappingRuleFieldsResponse listIdMappingRuleField() {
        String tenantId = WebContextHolder.getTenantId();
        Map<String, String> enFieldtoCnFieldMap = idMappingRelService.getEnFieldtoCnFieldMap(tenantId);
        return idMappingRuleService.listField(enFieldtoCnFieldMap);
    }

    public String idMappingLock(String... keys) {
        return StringUtils.join(keys, "-");
    }

    public String idMappingCatchKey(String tenantId, String dataTableName) {
        return String.format("%s_%sid_mapping_rule", tenantId, dataTableName);
    }

    /**
     * 添加id-mapping 规则缓存
     */
    private void addFieldToTableAndRefreshCache(String tenantId) {
        // 清除已删除的id-mapping 缓存
        List<IdMappingDataTableWithRule> needClearCacheIdMappingTable =
                extendIdMappingTableMapper.selectIdMappingDataTableWithRule(tenantId, DelEnum.DELETED.getBoolean());
        needClearCacheIdMappingTable.forEach(idMappingDataTableWithRule -> {
            String tableName = idMappingDataTableWithRule.getTableEnName();
            RBucket<Object> bucket = redisson.getBucket(idMappingCatchKey(tenantId, tableName));
            if (bucket != null) {
                bucket.delete();
            }
        });

        List<IdMappingDataTableWithRule> needAddCacheIdMappingTable =
                extendIdMappingTableMapper.selectIdMappingDataTableWithRule(tenantId, DelEnum.NOT_DELETED.getBoolean());
        
        // 添加字段到id-mapping 表
        List<String> needAddFields = needAddCacheIdMappingTable.stream()
                .flatMap(idMappingDataTableWithRule -> idMappingDataTableWithRule.getIdMappingRules().stream())
                .map(IdMappingRule::getEnField)
                .distinct()
                .toList();
        addFieldToIdMappingTable(tenantId, needAddFields);
        
        // 更新缓存
        needAddCacheIdMappingTable.forEach(idMappingDataTableWithRule -> {
            String tableName = idMappingDataTableWithRule.getTableEnName();
            Map<String, Object> cache = new HashMap<>();
            List<IdMappingRuleRedisDTO> idMappingRuleRedisDTOS = idMappingDataTableWithRule.getIdMappingRules()
                    .stream()
                    .map(IdMappingRuleRedisDTO::convertFrom).toList();
            cache.put(Constants.REDIS_ID_MAPPING_RULE_PROPERTY, idMappingRuleRedisDTOS);
            RBucket<String> bucket = redisson.getBucket(idMappingCatchKey(tenantId, tableName), StringCodec.INSTANCE);
            bucket.set(JsonUtils.toJsonUnchecked(cache));
        });
    }

    /**
     * 添加字段到id-mapping 表
     * @param tenantId 租户id
     * @param needAddEnFields 需要添加的字段
     */
    private void addFieldToIdMappingTable(String tenantId, List<String> needAddEnFields) {
        if (CollectionUtils.isEmpty(needAddEnFields)) {
            log.info("没有需要添加到id-mapping结果表的字段");
            return;
        }
        String idMappingTable = TenantUtils.generateIdMappingTableName(tenantId);
        Map<String, String> fieldSchema = dorisService.getFieldSchema(idMappingTable);
        List<String> exitedColumn = fieldSchema.keySet().stream()
                .map(field -> {
                    // 去除 . 需要转义
                    String handledField = field.replaceAll("\\.", StringUtils.EMPTY);
                    handledField = handledField.replaceAll(Constants.MINUTE, StringUtils.EMPTY);
                    handledField = handledField.replaceAll(idMappingTable, StringUtils.EMPTY);
                    return handledField;
                })
                .toList();

        needAddEnFields.stream()
                .filter(needAddEnField -> !exitedColumn.contains(needAddEnField))
                .forEach(needAddEnField -> {
                    dorisService.execSql(
                            String.format(Constants.ADD_FIELD_TO_ID_MAPPING_TEMPLATE, idMappingTable, needAddEnField));
                });
    }
}
