package com.baidu.keyue.deepsight.service.label.impl;

import com.baidu.keyue.deepsight.service.catalog.LabelCatalogService;
import com.baidu.keyue.deepsight.service.label.LabelPropertiesService;
import com.baidu.keyue.deepsight.enums.DelEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogRequest;
import com.baidu.keyue.deepsight.models.catalog.ListCatalogResponse;
import com.baidu.keyue.deepsight.models.rules.response.LabelCatalog;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.mapper.ExtendLabelMapper;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @className: LabelPropertiesServiceImpl
 * @description: 标签属性服务实现类 - 用于关联标签服务
 * @author: wangzhongcheng
 * @date: 2024/12/25 19:43
 */
@Service
public class LabelPropertiesServiceImpl implements LabelPropertiesService {

    @Autowired
    private ExtendLabelMapper labelMapper;

    @Autowired
    private LabelCatalogService labelCatalogService;

    @Override
    public List<LabelCatalog> getLabelCatalogs() {
        // 选择计算完成状态的标签
        ListCatalogRequest listCatalogRequest = new ListCatalogRequest();
        listCatalogRequest.setTenantId(String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId()));
        ListCatalogResponse catalogResult = labelCatalogService.list(listCatalogRequest);
        List<Long> catalogIds = catalogResult.flattenCatalogIds();
        List<LabelWithBLOBs> labels = successCatalogLabelComplete(catalogIds);
        if (CollectionUtils.isNotEmpty(labels)) {
            Map<Long, List<LabelWithBLOBs>> labelMap = labels.stream().collect(Collectors.groupingBy(LabelWithBLOBs::getCatalogId));
            catalogResult.fillLabel(labelMap);
        }
        return catalogResult.getResults()
                .stream()
                .map(LabelCatalog::convertFrom)
                .filter(LabelCatalog::isNotEmptyCatalog).toList();
    }

    public List<LabelWithBLOBs> successCatalogLabelComplete(List<Long> catalogIds) {
        if (CollectionUtils.isEmpty(catalogIds)) {
            return null;
        }
        // query label by catalogId
        LabelCriteria labelCriteria = new LabelCriteria();
        LabelCriteria.Criteria criteria = labelCriteria.createCriteria();
        criteria.andCatalogIdIn(catalogIds);
        criteria.andDelEqualTo(DelEnum.NOT_DELETED.getBoolean());
        return labelMapper.selectByExampleWithBLOBs(labelCriteria);
    }
}
