package com.baidu.keyue.deepsight.service.tool;

import com.baidu.kybase.sdk.message.enums.MessageTypeEnum;
import com.baidu.kybase.sdk.message.service.MessageApi;
import com.baidu.kybase.sdk.message.vo.MessageBody;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @className MessageService
 * @description MessageService
 * @date 2025/5/6 10:36
 */
@Slf4j
@Service
public class MessageService {

    @Autowired
    private MessageApi messageApi;

    public void pushMessage(String cloudId, MessageBody messageBody) {
        try {
            log.info("push msg to cloudId:{}, messageBody is {}", cloudId, messageBody);
            messageApi.pushMessage(messageBody);
        } catch (Exception e) {
            // 发送失败会抛出异常，业务方可以根据业务需求自行处理
            log.error("push msg fail", e);
        }
    }

}
