package com.baidu.keyue.deepsight.service.diffusion;

import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.customer.request.CustomerDiffusionTaskCreateRequest;
import com.baidu.keyue.deepsight.models.customer.request.CustomerDiffusionTaskListRequest;
import com.baidu.keyue.deepsight.models.customer.request.CustomerDiffusionTaskRetryRequest;
import com.baidu.keyue.deepsight.models.customer.response.CustomerDiffusionTaskCreateRes;
import com.baidu.keyue.deepsight.models.customer.response.CustomerDiffusionTaskRes;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;

import java.util.List;

/**
 * 人群扩散 Service Interface
 */
public interface GroupDiffusionService {
    /**
     * 创建人群扩散任务
     *
     * @param request
     * @return CustomerDiffusionTaskCreateRes
     */
    CustomerDiffusionTaskCreateRes createTask(CustomerDiffusionTaskCreateRequest request);

    /**
     * 根据 id 获人群扩散任务配置
     *
     * @param diffusionId
     * @return
     */
    List<CustomerDiffusionTask> getCustomerDiffusionTaskWithId(long diffusionId);

    /**
     * 人群扩散任务分页查询
     *
     * @param request 分页查询
     * @return 任务分页
     */
    BasePageResponse.Page<CustomerDiffusionTaskRes> getTaskPageList(CustomerDiffusionTaskListRequest request);

    /**
     * 人群扩散任务删除
     *
     * @param id       任务ID
     * @param tenantId 租户ID
     * @param userId   用户ID
     * @param userName
     */
    void deleteTask(Long id, String tenantId, String userId, String userName);

    /**
     * 查询任务详情
     *
     * @param id        任务ID
     * @param tenantId  租户ID
     * @param needCount 是否需要统计：种子人群、预测人群人数
     * @return 任务详情
     */
    CustomerDiffusionTaskRes getTaskDetail(Long id, String tenantId, boolean needCount);

    /**
     * 重新预测人群扩散
     *
     * @param request  任务ID
     * @param userId   操作者ID
     * @param tenantId 租户ID
     * @param userName 操作人
     */
    void retryTask(CustomerDiffusionTaskRetryRequest request, String userId, String tenantId, String userName);

    /**
     * 手动执行
     *
     * @param request  任务ID
     * @param userId   用户ID
     * @param tenantId 租户ID
     * @param userName
     */
    void execByManual(CustomerDiffusionTaskRetryRequest request, String userId, String tenantId, String userName);

    /**
     * 根据ID查询
     * 无人群名、人数统计结果
     *
     * @param id       任务ID
     * @param tenantId 租户ID
     * @return
     */
    CustomerDiffusionTaskRes getByIdAndTenantId(Long id, String tenantId);
}
