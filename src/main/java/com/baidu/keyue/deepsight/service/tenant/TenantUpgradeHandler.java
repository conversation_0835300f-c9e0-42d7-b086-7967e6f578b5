package com.baidu.keyue.deepsight.service.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.utils.FileUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @className TenantUpgradeHandler
 * @description 租户升级基类
 * @date 2025/3/10 10:29
 */
@Slf4j
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public abstract class TenantUpgradeHandler {

    private final DorisService dorisService;

    protected TenantUpgradeHandler nextHandlers;
    /**
     * 版本号
     */
    protected Integer version;

    protected List<String> sqlList;

    /**
     * 添加后续处理器
     *
     * @param nextHandler
     */
    public void addNextHandler(TenantUpgradeHandler nextHandler) {
        this.nextHandlers = nextHandler;
    }

    /**
     * 设置版本
     *
     * @param version
     */
    public void setVersion(Integer version) {
        this.version = version;
    }

    /**
     * 处理租户升级逻辑
     *
     * @param tenant 租户信息
     */
    public void handle(TenantDTO tenant) {
        if (needUpdateTenant(tenant.getTenantInfo())) {
            log.info("租户:{} 版本：{} 升级开始", tenant.getTenantId(), this.version);
            executeUpgrade(tenant);
            log.info("租户:{} 版本：{} 升级结束", tenant.getTenantId(), this.version);
        } else {
            log.info("租户:{} 版本：{} 升级跳过", tenant.getTenantId(), this.version);
        }
        next(tenant);
    }

    /**
     * 执行后续升级
     *
     * @param tenant
     */
    public void next(TenantDTO tenant) {
        if (nextHandlers != null) {
            nextHandlers.handle(tenant);
        }
    }

    // 是否需要升级租户
    protected abstract boolean needUpdateTenant(TenantInfo tenant);

    // 具体的升级逻辑
    protected abstract void executeUpgrade(TenantDTO tenant);

    /**
     * 初始化Doris存储的升级SQL语句
     *
     * @return SQL语句集合
     * @throws IOException 读取文件异常
     */
    public void initDorisSql(String filePath) throws IOException {
        String fileStr = FileUtil.readFileAsString(filePath);
        String[] split = fileStr.split(";");
        List<String> sqls = new ArrayList<>();
        for (String sql : split) {
            if (StrUtil.isNotBlank(sql)) {
                sqls.add(sql);
            }
        }
        this.sqlList = sqls;
    }

    /**
     * 执行Doris SQL
     * 执行建表
     */
    public void execDorisTableSql(String tenantId) {
        if (CollUtil.isEmpty(sqlList)) {
            return;
        }
        for (String sql : this.sqlList) {
            // 将^&符号作为租户ID替代符号
            String execSql = sql.replaceAll("\\^&", tenantId) + ";";
            if (execSql.toUpperCase().contains("CREATE TABLE")) {
                dorisService.operationSchema(execSql);
            }
        }
    }

    /**
     * 执行doris表升级操作
     * 字段添加、改名、删除等
     * 新租户使用最新模板初始化，无需升级
     * 如果是添加字段，先判断是否存在该字段，不存在，则添加
     * @param tenantDTO
     */
    public void execDorisSqlUpgrade(TenantDTO tenantDTO) {
        // 新租户跳过
        if (CollUtil.isEmpty(sqlList)) {
            return;
        }
        String tenantId = tenantDTO.getTenantId();
        for (String sql : this.sqlList) {
            // 将^&符号作为租户ID替代符号
            String execSql = sql.replaceAll("\\^&", tenantId) + ";";
            dorisService.operationSchema(execSql);
        }
    }
}
