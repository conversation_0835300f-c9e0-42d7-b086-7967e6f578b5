package com.baidu.keyue.deepsight.schedule;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.service.diffusion.GroupDiffusionCalculateService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @className CustomerDiffusionScheduler
 * @description 客群扩散任务
 * @date 2025/3/24 17:07
 */
@Slf4j
@Component
public class CustomerDiffusionScheduler {

    @Autowired
    private RedissonClient redisson;

    @Value("${switch.customerDiffusion:false}")
    private Boolean customerDiffusion;

    @Autowired
    private GroupDiffusionCalculateService diffusionService;

    @Autowired
    private TaskSchedulerService taskSchedulerService;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    /**
     * 定时触发客群扩散任务提交(处理待执行的任务)
     * 每两分钟触发一次：
     *  1、从 task_info 表拉 next_exec_date <= 当前时间 && 未删除 的任务
     *  2、规则解析 && 提交执行
     *  3、生成执行记录
     *  4、更新 next_exec_date 执行时间
     * */
    @Scheduled(cron = "0 0 * * * *")
    public void customerDiffusionScheduler() {
        if (!customerDiffusion) {
            log.info("customerDiffusionScheduler is disabled. quit...");
            return;
        }

        // get lock
        String lockKey = generateSchedulerLockKey("customerDiffusionRequestScheduler", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("customerDiffusionScheduler get lock failed.");
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }
        try {
            // 获取除已完成的任务，提交给扩散模型
            List<Pair<CustomerDiffusionTask, TaskInfo>> diffusionTasks =
                    diffusionService.pullTaskByStatus(TaskExecStatusEnum.SUCCESS);
            if (CollectionUtils.isEmpty(diffusionTasks)) {
                log.info("customerDiffusionScheduler got empty diffusionTask. quit...");
                return;
            }
            diffusionTasks.forEach(pair -> {
                // 1、任务执行
                // 2、修改任务状态为执行中
                try {
                    diffusionService.execGroupDiffusion(pair.getLeft().getId(), Constants.SCHEDULER_SYSTEM_USER_ID);
                } catch (Exception e) {
                    log.error("customerDiffusionScheduler exec group diffusion task failed, ", e);
                }
            });


        } catch (Exception e) {
            log.error("customerDiffusionScheduler exec task failed, ", e);
        } finally {
            lock.unlock();
        }
        log.info("customerDiffusionScheduler exec finished. quit...");
    }

    /**
     * 定时检查客群扩散模型的donefile(轮训计算中的任务)
     * 每一分钟触发一次：
     *  1、从 task_info 表拉 next_exec_date <= 当前时间 && 未删除 的任务
     *  2、规则解析 && 提交执行
     *  3、生成执行记录
     *  4、更新 next_exec_date 执行时间
     * */
    @Scheduled(cron = "0 0/1 * * * *")
    public void customerDiffusionCheckScheduler() {
        if (!customerDiffusion) {
            log.info("customerDiffusionScheduler of result scheduler is disabled. quit...");
            return;
        }
        // get lock
        String lockKey = generateSchedulerLockKey("customerDiffusionHandleScheduler", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("customerDiffusionScheduler of result get lock failed.");
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }
        try {
            // 查业务任务在执行中的，查出task_scheduler id
            // 获取计算中的任务，提交给扩散模型
            List<TaskSchedulerWithBLOBs> diffusionTasks =
                    diffusionService.pullRunningTask();

            diffusionTasks.forEach(task -> {
                // 任务修改为计算完成
                try {
                    diffusionService.fetchGroupDiffusionResult(task.getId());
                } catch (Exception e) {
                    log.error("customerDiffusionScheduler of result exec task failed, ", e);
                }
            });

        } catch (Exception e) {
            log.error("customerDiffusionScheduler of result exec task failed, ", e);
        } finally {
            lock.unlock();
        }
        log.info("customerDiffusionScheduler of result exec finished. quit...");
    }





}
