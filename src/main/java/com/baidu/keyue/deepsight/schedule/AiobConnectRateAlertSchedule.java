package com.baidu.keyue.deepsight.schedule;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.AiobTaskStatusEnum;
import com.baidu.keyue.deepsight.enums.AlertConfigTypeEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfigCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.mapper.AlertConfigMapper;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.service.tool.MessageService;
import com.baidu.keyue.deepsight.utils.MathUtils;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.baidu.kybase.sdk.message.enums.MessageTypeEnum;
import com.baidu.kybase.sdk.message.vo.MessageBody;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName AiobConnectRateAlertSchedule
 * @Description 外呼呼通率告警
 * <AUTHOR>
 * @Date 2025/7/7 4:11 PM
 */
@Slf4j
@Component
public class AiobConnectRateAlertSchedule {
    @Value("${switch.aiobConnectRateAlert:false}")
    public Boolean connectRateAlertEnable;

    @Resource
    private DorisService dorisService;

    @Resource
    private AlertConfigMapper alertConfigMapper;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private TenantInfoService tenantInfoService;

    @Resource
    private MessageService messageService;

    private final String redissonKey = "AIOB_CONNECT_RATE_ALERT";
    private static final String DATE_HOUR_FORMAT = "yyyy-MM-dd HH";


    @Scheduled(cron = "0 0 * * * ?")
    public void alertTask() {
        if (!connectRateAlertEnable) {
            return;
        }
        RLock lock = redissonClient.getLock(redissonKey);
        if (!lock.tryLock()) {
            return;
        }
        try {
            int pageSize = 100;
            Date now = new Date();
            AlertConfigCriteria criteria = new AlertConfigCriteria();
            criteria.createCriteria()
                    .andIsActiveEqualTo(Boolean.TRUE)
                    .andIsActiveEqualTo(true)
                    .andNextCheckTimeLessThanOrEqualTo(now);
            int count = (int) alertConfigMapper.countByExample(criteria);
            if (count <= 0) {
                log.info("aiob alert check count 0 skip");
                return;
            }
            int page = count % pageSize == 0 ? count % pageSize : count % pageSize + 1;
            for (int i = 0; i < page; i++) {
                List<AlertConfig> list = getAlertConfig(i * pageSize, pageSize, now);
                for (AlertConfig config : list) {
                    checkAlertData(now, config);
                }
            }
        } finally {
            lock.unlock();
            log.info("aiob alert check unlock");
        }
        log.info("aiob alert check over");
    }

    /**
     * 检查是否触发告警
     * 1.拨打次数>=配置值 && 接通率 < 配置值
     * 2.任务类型：已暂停、已完成状态则无需再预警
     * 3.机器人类型：处于未发布状态状态则无需预警（目前无此场景，绑定任务的机器人必须为发布状态）
     * 4.同类型+同类型源ID每天最多只告警一次
     *
     * @param now
     * @param config
     */
    public void checkAlertData(Date now, AlertConfig config) {
        try {
            String alertKey = String.join("_", 
                    "ALERT", config.getTenantId(), config.getConfigType(), config.getConfigTarget());
            RBucket<Object> bucket = redissonClient.getBucket(alertKey);
            if (bucket.isExists()) {
                log.info("{} had pushed, skip", alertKey);
                return;
            }
            String tenantId = config.getTenantId();
            TenantInfo tenantInfo = tenantInfoService.queryTenantInfo(tenantId);
            String query = ORMUtils.generateConnectionRateQuery(config, now);
            Map<String, Object> countMap = dorisService.selectList(query).get(0);
            Object dialCountObj = countMap.get("dial_count");
            Object connectRateObj = countMap.get("connect_rate");
            long dialCount = dialCountObj == null ? 0L : (long) dialCountObj;
            BigDecimal connectRate = connectRateObj == null ? new BigDecimal(0) 
                    : BigDecimal.valueOf(MathUtils.doubleToIntPercent((double) connectRateObj));
            // 如果是task或robot，查询详情：名称+状态
            String title = config.getConfigTarget() + "号线";
            boolean statusCheck = true;
            AlertConfigTypeEnum type = AlertConfigTypeEnum.createByValue(config.getConfigType());
            if (Objects.equals(AlertConfigTypeEnum.TASK, type) || Objects.equals(AlertConfigTypeEnum.ROBOT, type) ) {
                String detailSql = ORMUtils.generateSessionDetailQuery(config, type);
                List<Map<String, Object>> selectList = dorisService.selectList(detailSql);
                Map<String, Object> detail = selectList.get(0);
                title = Objects.equals(AlertConfigTypeEnum.TASK, type) ? 
                        detail.get("taskName") + "任务" : detail.get("robotName") + "机器人";
                if (Objects.equals(AlertConfigTypeEnum.TASK, type)) {
                    AiobTaskStatusEnum statusEnum = AiobTaskStatusEnum.createByObj(detail.get("taskStatus"));
                    statusCheck = Objects.equals(statusEnum, AiobTaskStatusEnum.RUNNING) 
                            || Objects.equals(statusEnum, AiobTaskStatusEnum.WAITING_RUN);
                }
            }
            // 拨打次数>=配置值 && 接通率<配置值
            if (dialCount > config.getDialCount() 
                    && connectRate.compareTo(config.getThresholdRate()) < 0 
                    && statusCheck) {
                // 记录写入Doris库
                String save = ORMUtils.generateConnectRateAlertSave(config);
                dorisService.execSql(save);
                MessageBody messageBody = new MessageBody();
                // 填入主账号accountId（也叫cloudId）， 将发送给发送给该主账号用户及其对应租户的所有超级管理员（租户管理员）
                messageBody.setAccountId(tenantInfo.getAccountid());
                // 设置消息类型为服务消息
                messageBody.setType(MessageTypeEnum.SERVICE_MSG.getType());
                // 设置消息标题
                messageBody.setTitle("号线接通率异常提醒");
                // 设置消息内容。注意，消息体里面包含点击消息的跳转文案和链接，消息内容为富文本
                String dateHour = DateUtil.format(now, DATE_HOUR_FORMAT);
                String content = String.format("[%s][%s]接通率低于阈值触发提醒，请及时查看。", title, dateHour);
                messageBody.setContent(content);
                log.debug("接通率告警:{}", content);
                messageService.pushMessage(tenantInfo.getAccountid(), messageBody);
                bucket.set("1", Duration.ofHours(24 - new DateTime().hour(true)));
            }
            // 更新下次执行时间
            AlertConfig updateConfig = new AlertConfig();
            updateConfig.setId(config.getId());
            updateConfig.setNextCheckTime(DateUtil.offsetHour(now, config.getAlertFreq()));
            alertConfigMapper.updateByPrimaryKeySelective(updateConfig);
        } catch (Exception e) {
            log.error("接通率告警配置检查异常，配置ID：{}", config.getId(), e);
        }
    }

    /**
     * 分批次获取告警哦配置
     *
     * @param offset
     * @param pageSize
     * @param now
     * @return
     */
    public List<AlertConfig> getAlertConfig(int offset, int pageSize, Date now) {
        String order = String.format("id ASC limit %d, %d", offset, pageSize);
        AlertConfigCriteria criteria = new AlertConfigCriteria();
        criteria.createCriteria()
                .andIsActiveEqualTo(Boolean.TRUE)
                .andIsActiveEqualTo(true)
                .andNextCheckTimeLessThanOrEqualTo(now);
        criteria.setOrderByClause(order);
        return alertConfigMapper.selectByExample(criteria);
    }
}
