package com.baidu.keyue.deepsight.schedule;

import java.util.List;

import com.baidu.keyue.deepsight.config.RedisConfiguration;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.service.dataprediction.DataPredictionCalService;
import com.baidu.keyue.deepsight.service.tasks.TaskSchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DataPredictionCalScheduler {

    @Autowired
    private RedisConfiguration redisConfiguration;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private TaskSchedulerService taskSchedulerService;

    @Value("${switch.dataPredictionSchedulerWithBSC:false}")
    private Boolean dataPredictionSchedulerWithBSC;

    @Autowired
    private DataPredictionCalService dataPredictionCalService;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    /**
     * 定时触发数据增强预测任务(BSC 模式)
     * 每一个小时触发一次：
     *  1、从 task_info 表拉 next_exec_date <= 当前时间 && 未删除 的任务
     *  2、规则解析 && 提交执行
     *  3、生成执行记录
     *  4、更新 next_exec_date 执行时间
     * */
    @Scheduled(cron = "0 0 * * * *")
    public void dataPredictionScheduler() {
        if (!dataPredictionSchedulerWithBSC) {
            log.info("dataPredictionScheduler is disabled. quit...");
            return;
        }

        log.info("dataPredictionScheduler start...");
        try {
            List<Pair<DataPredictionSourceWithBLOBs, TaskInfo>> predictionTaskPairs =
                    dataPredictionCalService.pullWaitExecTask();
            if (CollectionUtils.isEmpty(predictionTaskPairs)) {
                log.info("dataPredictionScheduler got empty paris. quit...");
                return;
            }
            log.info("dataPredictionScheduler pairs-size: {}", predictionTaskPairs.size());
            predictionTaskPairs.forEach(pair -> {
                // get lock
                String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "dataPredictionScheduler", "Lock",
                        String.valueOf(pair.getLeft().getId()), String.valueOf(pair.getRight().getId()));
                RLock lock = redisson.getLock(lockKey);
                if (!lock.tryLock()) {
                    log.warn("dataPredictionScheduler get lock failed.");
                    return;
                }
                try {
                    dataPredictionCalService.taskExec(pair.getLeft(), pair.getRight());
                } catch (Exception e) {
                    log.error("dataPredictionScheduler exec task failed, ", e);
                } finally {
                    lock.unlock();
                }
            });
        } catch (Exception e) {
            log.error("dataPredictionScheduler exec task failed, ", e);
        }

        log.info("dataPredictionScheduler exec finished. quit...");
    }
}
