package com.baidu.keyue.deepsight.schedule;

import java.util.List;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.service.memory.MemoryCalculateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MemoryCalScheduler {

    @Autowired
    private RedissonClient redisson;

    @Value("${switch.memoryCalScheduler:false}")
    private Boolean memoryCalScheduler;

    @Autowired
    private MemoryCalculateService memoryService;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    @Scheduled(fixedDelay = 60000L, initialDelay = 60000L)
    public void memoryCalFakeRealtime() {
        if (!memoryCalScheduler) {
            log.info("memoryCalFakeRealtime is disabled. quit...");
            return;
        }

        // get lock
        String lockKey = generateSchedulerLockKey("memoryCalScheduler", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("memoryCalFakeRealtime get lock failed.");
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }

        try {
            List<Pair<MemoryExtractWithBLOBs, TaskInfo>> memoryExtractTaskPairs = memoryService.pullWaitExecMemoryTask(TriggerModeEnum.REALTIME);
            taskExec(memoryExtractTaskPairs);
        } catch (Exception e) {
            log.error("memoryCalFakeRealtime exec task failed, ", e);
        } finally {
            lock.unlock();
        }

        log.info("memoryCalFakeRealtime exec finished. quit...");

    }

    private void taskExec(List<Pair<MemoryExtractWithBLOBs, TaskInfo>> memoryExtractTaskPairs) {
        if (CollectionUtils.isEmpty(memoryExtractTaskPairs)) {
            log.info("taskExec got empty labelTaskPairs. quit...");
            return;
        }
        memoryExtractTaskPairs.forEach(pair -> {
            try {
                memoryService.execByScheduler(pair.getLeft(), pair.getRight());
            } catch (Exception e) {
                log.error("taskExec exec task failed, ", e);
            }
        });
    }

    @Scheduled(cron = "0 0 * * * *")
    public void memoryCalSchedulerCron() {
        if (!memoryCalScheduler) {
            log.info("memoryCalSchedulerInBSCMod is disabled. quit...");
            return;
        }

        // get lock
        String lockKey = generateSchedulerLockKey("memoryCalScheduler", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("memoryCalSchedulerInBSCMod get lock failed.");
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }

        try {
            List<Pair<MemoryExtractWithBLOBs, TaskInfo>> memoryExtractTaskPairs = memoryService.pullWaitExecMemoryTask(TriggerModeEnum.CRON);
            taskExec(memoryExtractTaskPairs);
        } catch (Exception e) {
            log.error("memoryCalSchedulerInBSCMod exec task failed, ", e);
        } finally {
            lock.unlock();
        }

        log.info("memoryCalSchedulerInBSCMod exec finished. quit...");
    }

}
