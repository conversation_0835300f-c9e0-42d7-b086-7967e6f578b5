package com.baidu.keyue.deepsight.schedule;

import java.util.List;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.service.agg.AiobSessionMetricAggService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class MetricAggScheduler {

    private final RedissonClient redisson;
    private final AiobSessionMetricAggService aiobSessionMetricAggService;

    public MetricAggScheduler(RedissonClient redisson, AiobSessionMetricAggService aiobSessionMetricAggService) {
        this.redisson = redisson;
        this.aiobSessionMetricAggService = aiobSessionMetricAggService;
    }

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    /**
     * 统计外呼通话记录 90天内的统计指标
     */
    @Scheduled(cron = "0 0 2 * * *")
    public void aiobSessionMetricAgg() {
        log.info("aiobSessionMetricAgg task start");
        String lockKey = generateSchedulerLockKey("aiobSessionMetricAgg", "Lock");
        RLock lock = redisson.getLock(lockKey);
        if (!lock.tryLock()) {
            log.warn("aiobSessionMetricAgg get lock failed.");
            throw new DeepSightException.BusyRequestException(ErrorCode.BUSY_REQUEST);
        }

        try {
            // 通过 show tables 获取aiob_conversation_session_agg_（天级别指标明细）
            List<String> sessionAggTables = aiobSessionMetricAggService.getSessionAggTables();
            sessionAggTables.forEach(table -> {
                try {
                    aiobSessionMetricAggService.aiobSessionMetricAggExec(table);
                } catch (Exception e) {
                    log.error("aiobSessionMetricAgg exec task failed, table: {}", table, e);
                }
                log.info("aiobSessionMetricAgg exec task finished, table: {}", table);
            });

            // 统计 90天的外呼通话统计指标
        } catch (Exception e) {
            log.error("try { exec task failed, ", e);
        } finally {
            lock.unlock();
        }

        log.info("aiobSessionMetricAgg task finished.");
    }
}
