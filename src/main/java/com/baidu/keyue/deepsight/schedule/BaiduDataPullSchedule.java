package com.baidu.keyue.deepsight.schedule;

import java.util.List;

import com.baidu.keyue.deepsight.config.RedisConfiguration;
import com.baidu.keyue.deepsight.service.user.BaiduUserDataService;
import com.baidu.keyue.deepsight.service.user.UserProfileService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BaiduDataPullSchedule {

    @Autowired
    private RedisConfiguration redisConfiguration;

    @Autowired
    private RedissonClient redisson;

    @Autowired
    private BaiduUserDataService baiduUserDataService;

    @Autowired
    private UserProfileService userProfileService;

    @Value("${switch.pullBaiduData:false}")
    private Boolean pullBaiduData;

    public String generateSchedulerLockKey(String... keys) {
        return StringUtils.join(keys, "-");
    }

    /**
     * 从百度拉取用户真实数据
     * 每一个小时触发一次
     * */
    @Scheduled(cron = "0 0 * * * *")
    public void pullDataScheduler() {
        if (!pullBaiduData) {
            log.info("pullDataScheduler is disabled. quit...");
            return;
        }

        log.info("pullDataScheduler start...");
        try {
            // 每次执行都需要刷新下redis中mobile解码的secretKey
            baiduUserDataService.refreshSecretKey();

            List<String> allUserTable = baiduUserDataService.getAllUserTable();
            log.info("allUserTable:{}", allUserTable);

            allUserTable.forEach(table -> {
                log.info("pullDataScheduler start handle table:{}", table);

                String lockKey = generateSchedulerLockKey(redisConfiguration.getPrefix(), "pullDataScheduler", "Lock", table);
                RLock lock = redisson.getLock(lockKey);
                if (!lock.tryLock()) {
                    log.warn("pullDataScheduler get lock failed.");
                    return;
                }

                // 拉取所有mock_user_xxx表的用户mobile用于查询百度数据，然后将查询到的数据更新到mock_user_xxx表中
                try {
                    baiduUserDataService.handelBaiduDataProcess(table);
                } catch (Exception e) {
                    log.error("pullDataScheduler handelBaiduDataProcess exec task failed, table: {}, err: ", table, e);
                }

                try {
                    String tenantId = StringUtils.substringAfterLast(table, "_");
                    userProfileService.mergeUserProfileByTenantId(tenantId);
                } catch (Exception e) {
                    log.error("pullDataScheduler mergeUserProfileByTenantId exec task failed, table: {}, err: ", table, e);
                }
                lock.unlock();
            });
        } catch (Exception e) {
            log.error("pullDataScheduler exec task failed, ", e);
        }

        log.info("pullDataScheduler exec finished. quit...");
    }
}
