package com.baidu.keyue.deepsight.database.service.impl;

import com.baidu.keyue.deepsight.database.service.UserPropertiesService;
import com.baidu.keyue.deepsight.models.rules.response.UserPropertiesResult;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import com.baidu.keyue.deepsight.mysqldb.mapper.TableFieldMetaInfoMapper;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.baidu.keyue.deepsight.utils.TenantUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @className: UserPropertiesServiceImpl
 * @description: 用户属性服务实现类：查询可供选择的用户属性等功能
 * @author: wangzhongcheng
 * @date: 2024/12/25 19:25
 */
@Slf4j
@Service
public class UserPropertiesServiceImpl implements UserPropertiesService {
    private static final String USER_FILED_FROM_BAIDU_PATH = "/user_field_from_baidu.json";

    @Autowired
    private TableFieldMetaInfoMapper tableFieldMetaInfoMapper;

    private Set<String> userFiledFromBaidu;

    @PostConstruct
    public void init() {
        // 初始化百度用户字段
        try (InputStream stream = this.getClass().getResourceAsStream(USER_FILED_FROM_BAIDU_PATH)) {
            String configStr = StreamUtils.copyToString(stream, StandardCharsets.UTF_8);
            List<Map<String, String>> list = JsonUtils.toObject(configStr, new TypeReference<>() {});
            userFiledFromBaidu = new HashSet<>();
            list.forEach(map -> map.forEach((k, v) -> userFiledFromBaidu.add(v)));
            log.info("init user_field_from_baidu.json config success");
        } catch (Exception e) {
            log.error("init user_field_from_baidu.json config error", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<UserPropertiesResult> getUserProperties(String tenantId, Boolean includeBaidu) {
        // 获取用户属性字段
        TableFieldMetaInfoCriteria fieldMetaInfoCriteria = new TableFieldMetaInfoCriteria();
        TableFieldMetaInfoCriteria.Criteria criteria = fieldMetaInfoCriteria.createCriteria();
        criteria.andIsFilterCriteriaEqualTo(Boolean.TRUE);
        criteria.andTableEnNameEqualTo(TenantUtils.generateMockUserTableName(tenantId));
        if (!includeBaidu) {
            // 如果要求不包含百度字段，则过滤掉表中 from_baidu 为 true 的字段
            criteria.andFromBaiduEqualTo(Boolean.FALSE);
        }
        List<TableFieldMetaInfo> tableFieldMetaInfos = tableFieldMetaInfoMapper.selectByExampleWithBLOBs(
                fieldMetaInfoCriteria);

        // 封装用户属性筛选结果返回
        return tableFieldMetaInfos.stream().map(UserPropertiesResult::convertFrom).toList();
    }

    @Override
    public Boolean filedFromBaidu(String fieldName) {
        return userFiledFromBaidu.contains(fieldName);
    }
}
