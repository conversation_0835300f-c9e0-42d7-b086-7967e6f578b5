package com.baidu.keyue.deepsight.database.service.impl;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TaskTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.bsc.basic.BaseCalculateContext;
import com.baidu.keyue.deepsight.models.bsc.basic.BscTableIdentify;
import com.baidu.keyue.deepsight.models.bsc.label.BscLabelTaskDataField;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfoObj;
import com.baidu.keyue.deepsight.service.tasks.TaskInfoService;
import com.baidu.keyue.deepsight.utils.ThreadPoolUtils;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @className: BaseCalculateService
 * @description: T 计算上下文；Obj 任务对应的业务对象（标签、客群）
 * @author: wangzhongcheng
 * @date: 2025/1/20 20:44
 */
@Slf4j
@Data
public abstract class BaseCalculateService<T extends BaseCalculateContext, Obj extends TaskInfoObj> {

    @Autowired
    protected DorisService dorisService;

    @Autowired
    private TaskInfoService taskInfoService;

    private final Base64.Encoder encoder = Base64.getEncoder();

    /**
     * 拉取待执行任务
     * @param taskType 任务类型
     * @return
     */
    protected List<Pair<Obj, TaskInfo>> pullWaitExecTask(TaskTypeEnum taskType) {
        List<TaskInfo> taskInfoList = taskInfoService.pullWaitExecTask(taskType, TriggerModeEnum.CRON);
        Map<Long, TaskInfo> taskMap = taskInfoList.stream()
                .collect(Collectors.toMap(TaskInfo::getId, Function.identity(), (k1, k2) -> k2));
        if (taskMap.isEmpty()) {
            return null;
        }

        List<Obj> list = getWaitExecTaskObjList(taskMap.keySet());
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        return list.stream()
                .map(item -> {
                            Long taskId = item.getTaskInfoId();
                            if (Objects.isNull(taskId)) {
                                return null;
                            }
                            TaskInfo task = taskMap.get(taskId);
                            if (Objects.isNull(task)) {
                                return null;
                            }
                            return new ImmutablePair<>(item, task);
                        }
                ).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取待执行任务对象列表
     * @param taskIdList 任务ID列表
     * @return 任务对象列表
     */
    protected abstract List<Obj> getWaitExecTaskObjList(Set<Long> taskIdList);

    /**
     * 手动执行计算任务
     * @param obj 业务对象
     * @param userId 用户ID
     */
    public void execByManual(Obj obj, String userId) {
        // 获取标签详情 & 标签任务详情
        TaskInfo task = taskInfoService.getTaskDetailWithId(obj.getTaskInfoId());
        TaskTypeEnum taskType = TaskTypeEnum.getDescByCode(task.getTaskType());
        if (Objects.isNull(taskType)) {
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST, "任务类型不存在");
        }

        if (TaskExecStatusEnum.RUNNING.getCode().equals(obj.getCalStatus())) {
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST, "任务正在计算中");
        }

        // 任务 Context 初始化
        T calculateExecInstance = initCalculateExecInstance(obj, userId);
        log.info("{}, ExecByManual: obj: {}, task: {}, execId: {}, userId: {}",
                taskType.getDesc(), obj.getId(), task.getId(), calculateExecInstance.getExecId(), userId);

        // 修改任务执行状态
        onProcessingStatus(calculateExecInstance);

        // 执行任务
        ThreadPoolUtils.getSingleThreadPool().execute(() -> {
            taskCalculate(calculateExecInstance);
            if (Objects.nonNull(calculateExecInstance.getErr())) {
                // 前置任务失败
                onFailure(calculateExecInstance);
            }
        });
    }

    /**
     * 执行定时调度计算
     * @param obj
     * @param taskInfo
     */
    public void execByScheduler(Obj obj, TaskInfo taskInfo) {
        TaskTypeEnum taskType = TaskTypeEnum.getDescByCode(taskInfo.getTaskType());
        String userId = "CronScheduler";

        // 任务 Context 初始化
        T calculateExecInstance = initCalculateExecInstance(
                obj, userId);
        log.info("{} ExecByScheduler: obj: {}, task: {}, execId: {}, userId: {}",
                taskType.getDesc(), obj.getId(), obj.getTaskInfoId(), calculateExecInstance.getExecId(), userId);

        // 修改任务执行状态
        onProcessingStatus(calculateExecInstance);

        // 执行任务
        ThreadPoolUtils.getSingleThreadPool().execute(() -> {
            taskCalculate(calculateExecInstance);
            if (Objects.nonNull(calculateExecInstance.getErr())) {
                // 前置任务失败
                onFailure(calculateExecInstance);
            }
        });

        taskInfoService.updateNextCalDate(taskInfo);
    }

    /**
     * 执行计算任务
     * @param calculateExecInstance 计算执行实例
     */
    protected abstract void taskCalculate(T calculateExecInstance);

    /**
     * 初始化计算执行实例
     * @param obj 业务对象
     * @param userId 用户ID
     */
    protected abstract T initCalculateExecInstance(Obj obj, String userId);

    /**
     * 任务执行状态修改
     * @param calculateExecInstance 计算执行实例
     */
    protected abstract void onProcessingStatus(T calculateExecInstance);

    /**
     * 提交bsc任务后修改状态
     * @param calculateExecInstance 计算执行实例
     */
    protected abstract void onProcessing(T calculateExecInstance);

    /**
     * 构造flink table identify TODO 此处每个涉及到子查询的表都进行了一次表信息统计，此处可以优化；别名指向同表时可以通过多加映射完成
     * @param sourceTables 需要构造的表列表
     * @param tableSchema 表schema
     * @return
     */
    public void buildTableIdentify(Map<String, String> aliasMap,
                                   List<BscTableIdentify> sourceTables,
                                   Map<String, Map<String, String>> tableSchema) {
        for (Map.Entry<String, String> entry : aliasMap.entrySet()) {
            BscTableIdentify bscTableIdentify = new BscTableIdentify();
            // `deep_sight_dev`.`mock_user`
            String tableIdentify = StringUtils.replace(entry.getValue(), "`", "");
            // {`mobile`=varchar, `age`=varchar}
            Map<String, String> schemaMap = tableSchema.get(entry.getValue());

            String[] tmp = tableIdentify.split("\\.");
            String space = tmp[0];
            String table = tmp[1];
            bscTableIdentify.setSpace(space);
            bscTableIdentify.setTable(table);

            bscTableIdentify.setAs(entry.getKey());

            List<BscLabelTaskDataField> innerFields = Lists.newArrayList();
            for (String fieldName : schemaMap.keySet()) {
                String f = StringUtils.replace(fieldName, "`", "");
                String t = schemaMap.get(fieldName);
                innerFields.add(new BscLabelTaskDataField(f, t));
            }
            bscTableIdentify.setFields(innerFields);

            sourceTables.add(bscTableIdentify);
        }
    }

    /**
     * 执行失败后的处理逻辑
     */
    protected abstract void onFailure(T execContext);

    /**
     * 执行完成后的处理逻辑
     */
    protected abstract void onFinishedHandler(T execContext);

    /**
     * 解析contextJsonStr为CalculateContext
     */
    protected abstract T getCalculateContextByJsonStr(String contextJsonStr);


    /**
     * 获取BSC任务code
     */
    protected abstract String getBscJobCode(T execContext);

    /**
     * 获取计算资源ID
     */
    protected abstract String getBscResourceId();

}
