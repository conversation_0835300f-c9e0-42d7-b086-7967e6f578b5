package com.baidu.keyue.deepsight.database.common.func;

import com.baidu.keyue.deepsight.enums.AggregatorEnum;

/**
 * @className: AggFunc
 * @description: 聚类函数
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/21 18:38
 */
public class AggFunc {

    public static String agg(AggregatorEnum aggregator, String field) {
        switch (aggregator) {
            case COUNT:
                return count(field);
            case DISTANCE_COUNT:
                return distanceCount(field);
            case SUM:
                return sum(field);
            case AVG:
                return avg(field);
            case MAX:
                return max(field);
            case MIN:
                return min(field);
            default:
                throw new IllegalArgumentException("Invalid aggregator type");
        }
    }

    /**
     * 总次数
     */
    public static String count(String field) {
        return String.format("COUNT(%s)", field);
    }

    /**
     * 去重总次数
     */
    public static String distanceCount(String field) {
        return String.format("COUNT(DISTINCT %s)", field);
    }

    /**
     * 总和
     */
    public static String sum(String field) {
        return String.format("SUM(%s)", field);
    }

    /**
     * 平均值
     */
    public static String avg(String field) {
        return String.format("AVG(%s)", field);
    }

    /**
     * 最大值
     */
    public static String max(String field) {
        return String.format("MAX(%s)", field);
    }

    /**
     * 最小值
     */
    public static String min(String field) {
        return String.format("MIN(%s)", field);
    }

}
