package com.baidu.keyue.deepsight.database.service;

import com.baidu.keyue.deepsight.models.diffusion.dto.GradingDistributionDTO;
import com.baidu.keyue.deepsight.models.doris.TableDescribeDto;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * @className: DorisService
 * @description:
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2025/1/2 10:22
 */
public interface DorisService {

    /**
     * doris 检索，返回流式数据
     *
     * @param sql
     * @return
     */
    Flux<Map<String, Object>> queryDorisStreaming(String sql);

    /**
     * 查询数据
     *
     * @param sql 查询语句
     * @return
     */
    List<Map<String, Object>> selectList(String sql);

    /**
     * 统计数据条数
     */
    long getCount(String sql);

    /**
     * 获取单表数据条数
     *
     * @param tableName 表名
     * @return 数据条数
     */
    long getSingleTableCount(String tableName);

    /**
     * 运行删除/建表等sql
     */
    int execSql(String sql);

    /**
     * 创建标签生产临时表，可重复执行(CREATE TABLE IF NOT EXISTS)
     *
     * @param db         doris_space
     * @param tableName  doris_table
     * @param dupTableName 主键
     */
    void createLabelProcessTemporaryTable(String db, String tableName, String dupTableName);

    /**
     * 删除标签生产临时表
     *
     * @param db
     * @param tableName
     */
    void deleteLabelProcessTemporaryTable(String db, String tableName);

    /**
     * 从宽表中删除指定列
     *
     * @param db
     * @param tableName
     * @param fieldName
     */
    void deleteLabelProcessField(String db, String tableName, String fieldName);

    /**
     * 创建客群生产临时表，可重复执行(CREATE TABLE IF NOT EXISTS)
     *
     * @param db         doris_space
     * @param tableName  doris_table
     * @param primaryKey 主键
     * @param fieldName  字段名
     */
    void createCustomerProcessTemporaryTable(String db, String tableName, String primaryKey, String fieldName);

    /**
     * 获取表信息 tableName - `db.`table_name`
     */
    Map<String, String> getFieldSchema(String tableName);

    /**
     * 根据表前缀，查询表名
     *
     * @param prefix
     * @return 表名列表
     */
    List<String> showTablesWithPrefix(String prefix);

    /**
     * describe 获取表结构 map
     *
     * @param tableName
     * @return
     */
    Map<String, String> describeTable(String tableName);

    /**
     * 查询表结构
     *
     * @param tableName
     * @return
     */
    List<TableDescribeDto> describeTableSchema(String tableName);

    /**
     * Doris表字段是否存在
     *
     * @param tableName 表名 mock_user，不能包含`符号
     * @param column    字段名 id，不能包含`符号
     * @return 是否存在字段
     */
    boolean existColumn(String tableName, String column);

    /**
     * 表是否存在
     *
     * @param tableName 表名 mock_user，不能包含`符号
     * @return 表是否存在
     */
    boolean existTable(String tableName);

    /**
     * 删除数据表如果表存在
     *
     * @param tableName 表名 mock_user，不能包含`符号
     */
    void deleteTable(String tableName);

    /**
     * Doris schema操作
     * 添加：字段不存在则添加，存在则跳过且无异常
     * 删除:字段存在则删除，存在则跳过且无异常
     * 重命名：旧名存在且新名称不存在则重命名，否则跳过且无异常
     * 创建表直接创建
     * SQL语句必须格式严谨，每个SQL语句语法词但空格间隔
     *
     * @param execSql 执行的SQL
     */
    void operationSchema(String execSql);

    /**
     * 创建人群扩散临时表
     *
     * @param db
     * @param tableName
     */
    void createDiffusionCalculateTemporaryTable(String db, String tableName);

    /**
     * 删除人群扩散临时表
     *
     * @param db
     * @param tableName
     */
    void deleteDiffusionCalculateTemporaryTable(String db, String tableName);

    /**
     * 转换Doris值类型
     * 用与通过Doris数据同步API接口检查
     *
     * @param dataType Doris字段类型
     * @param data     数据
     * @return 转换后的数据
     */
    Object covertDorisValue(String dataType, Object data);

    /**
     * 对人群扩散结果进行分段统计 0.9-1.0、0.8-0.9 ... 0.0-0.1
     *
     * @param db doris 数据库
     * @param tableName 人群扩展结果临时表
     * @return 分段统计结果
     */
    List<GradingDistributionDTO> diffusionGradingStatistics(String db, String tableName);
}
