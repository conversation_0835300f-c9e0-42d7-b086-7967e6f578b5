package com.baidu.keyue.deepsight.database.redis;

import org.apache.commons.lang3.StringUtils;
import org.redisson.api.NameMapper;


public class RedissonCustomNameMapper implements NameMapper {

    private final String prefix;

    public RedissonCustomNameMapper(String prefix) {
        this.prefix = prefix;
    }

    @Override
    public String map(String name) {
        return prefix + ":" + name;
    }

    @Override
    public String unmap(String name) {
        return StringUtils.substring(name, prefix.length() + 1);
    }
}
