package com.baidu.keyue.deepsight.database.service;

import com.baidu.keyue.deepsight.models.rules.response.DatasetInfo;
import com.baidu.keyue.deepsight.models.rules.response.DatasetPropertiesResult;

import java.util.List;

/**
 * @className: DatasetPropertiesService
 * @description: 数据集属性服务接口 - 用于管理数据集的属性信息
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/25 19:44
 */
public interface DatasetPropertiesService {

    /**
     * 获取业务事件属性信息
     */
    List<DatasetInfo> getDatasetProperties();

    /**
     * 根据数据表ID获取数据集属性信息
     */
    List<DatasetPropertiesResult> getDatasetProperties(Long dataTableId, Boolean includeBaidu);

}
