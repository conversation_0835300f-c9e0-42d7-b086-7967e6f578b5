package com.baidu.keyue.deepsight.database.doris;

import com.baidu.keyue.deepsight.config.DorisConfiguration;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * @className: DorisConfig
 * @description:
 * @author: wangzhongcheng
 * @date: 2024/12/31 15:54
 */
@Configuration
@MapperScan(basePackages  = "com.baidu.keyue.deepsight.dorisdb.mapper" , sqlSessionFactoryRef = "dorisSqlSessionFactory")
public class DorisConfig {

    private static final String MAPPER_LOCATION = "classpath*:mapper/dorisdb/*.xml";

    @Bean("dorisDataSource")
    public DataSource getDb1DataSource(DorisConfiguration dorisConfiguration){
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(dorisConfiguration.getDriverClassName());
        dataSource.setJdbcUrl(dorisConfiguration.getUrl());
        dataSource.setUsername(dorisConfiguration.getUsername());
        dataSource.setPassword(dorisConfiguration.getPassword());
        return dataSource;
    }

    @Bean("dorisSqlSessionFactory")
    public SqlSessionFactory dorisSqlSessionFactory(@Qualifier("dorisDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION));
        return bean.getObject();
    }

    @Bean("dorisSqlSessionTemplate")
    public SqlSessionTemplate dorisSqlSessionTemplate(@Qualifier("dorisSqlSessionFactory") SqlSessionFactory sqlSessionFactory) {
        return new SqlSessionTemplate(sqlSessionFactory);
    }

}
