package com.baidu.keyue.deepsight.database.common.func;


import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @className: Func
 * @description: sql 函数封装类
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/21 17:40
 */
public class Func {

    public static String like(String field, String value, RuleNode ruleNode) {
        // 标签的like只支持flink的函数，在doris里面无法运行
        if (ruleNode.getType() == RuleTypeEnum.LABEL) {
            return String.format("(ARRAY_JOIN(%s, ',') LIKE %s)", field, value);
        }
        return String.format("(%s LIKE %s)", field, value);
    }
    public static String isNull(String field, RuleNode ruleNode) {
        if (ruleNode.getType() == RuleTypeEnum.LABEL) {
            return String.format("(CARDINALITY(%s) <= 0)", field);
        }
        return String.format("(%s IS NULL)", field);
    }

    public static String notNull(String field, RuleNode ruleNode) {
        if (ruleNode.getType() == RuleTypeEnum.LABEL) {
            return String.format("(CARDINALITY(%s) > 0)", field);
        }
        return String.format("(%s IS NOT NULL)", field);
    }

    public static String contain(String field, List<String> values, RuleNode ruleNode) {
        if (ruleNode.getType() == RuleTypeEnum.LABEL) {
            String filter = values.stream()
                    .map(item -> String.format("ARRAY_CONTAINS(%s, %s)", field, item))
                    .collect(Collectors.joining(" OR "));
            return String.format("(%s)", filter);
        }
        String filter = values.stream()
                .map(item -> String.format("%s IN (%s)", field, item))
                .collect(Collectors.joining(" OR "));
        return String.format("(%s)", filter);
    }
    public static String notContain(String field, List<String> values, RuleNode ruleNode) {
        if (ruleNode.getType() == RuleTypeEnum.LABEL) {
            String filter = values.stream()
                    .map(item -> String.format("NOT ARRAY_CONTAINS(%s, %s)", field, item))
                    .collect(Collectors.joining(" AND "));
            return String.format("(CARDINALITY(%s) > 0 AND %s)", field, filter);
        }
        return String.format("(%s NOT IN (%s))", field, StringUtils.join(values, ","));
    }

    public static String eq(String field, String value) {
        return String.format("(%s = %s)", field, value);
    }

    public static String notEq(String field, String value) {
        return String.format("(%s != %s)", field, value);
    }

    public static String gt(String field, String value) {
        return String.format("(%s > %s)", field, value);
    }

    public static String lt(String field, String value) {
        return String.format("(%s < %s)", field, value);
    }

    public static String ge(String field, String value) {
        return String.format("(%s >= %s)", field, value);
    }

    public static String le(String field, String value) {
        return String.format("(%s <= %s)", field, value);
    }

    public static String between(String field, String value1, String value2) {
        return String.format("(%s BETWEEN %s AND %s)", field, value1, value2);
    }

    /**
     * 距离当前时间
     * @param field 字段
     * @param value 条件: 1 DAY, 1 MONTH, 1 YEAR
     */
    public static String least(String field, String value) {
        String dateLeastFormat = "(%s >= NOW() - INTERVAL %s %s)";

        String[] leastDate = value.split(" ");

        return String.format(dateLeastFormat,
                field,
                leastDate[0].replaceAll("-", ""),
                leastDate[1]);
    }

}
