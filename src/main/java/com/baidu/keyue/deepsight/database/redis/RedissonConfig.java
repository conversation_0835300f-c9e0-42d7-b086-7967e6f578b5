package com.baidu.keyue.deepsight.database.redis;

import java.util.HashMap;
import java.util.Map;

import com.baidu.keyue.deepsight.config.RedisConfiguration;
import lombok.RequiredArgsConstructor;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.spring.cache.CacheConfig;
import org.redisson.spring.cache.RedissonSpringCacheManager;
import org.springframework.cache.CacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@RequiredArgsConstructor
@Configuration
public class RedissonConfig {

    private final RedisConfiguration redisConfiguration;

    @Bean
    public RedissonClient redisson() {
        Config config = new Config();
        config.useSingleServer()
                .setAddress(String.format("redis://%s:%s", redisConfiguration.getHost(), redisConfiguration.getPort()))
                .setPassword(redisConfiguration.getPassword())
                .setNameMapper(new RedissonCustomNameMapper(redisConfiguration.getPrefix()));
        return Redisson.create(config);
    }

    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redisConnectionFactory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new JdkSerializationRedisSerializer());
        redisTemplate.setValueSerializer(new JdkSerializationRedisSerializer());
        redisTemplate.setEnableTransactionSupport(true);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    /**
     * 配置缓存，以 LabelCatalog 为例，使用方式：@Cacheable(cacheNames = "LabelCatalog")
     * 实际在Redis中会有以下三个 key :
     * 1) "LabelCatalog"   这个是缓存的实际数据， 类型为 Hashmap， key 为缓存的实际key， value 为缓存内容
     * 2) "redisson__timeout__set:{LabelCatalog}"  这个是缓存的超时时间， 类型为 ZSet
     * 3) "redisson__idle__set:{LabelCatalog}"     这个是缓存的空闲时间， 类型为 ZSet
     * */
    @Bean
    public CacheManager cacheManager(RedissonClient redissonClient) {
        Map<String, CacheConfig> config = new HashMap<>();

        config.put("Label", new CacheConfig(10 * 60 * 1000, 5 * 60 * 1000));
        config.put("LabelCatalog", new CacheConfig(10 * 60 * 1000, 5 * 60 * 1000));
        config.put("DataTable", new CacheConfig(10 * 60 * 1000, 5 * 60 * 1000));

        return new RedissonSpringCacheManager(redissonClient, config);
    }
}
