package com.baidu.keyue.deepsight.database.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.kie.api.KieServices;
import org.kie.api.builder.KieBuilder;
import org.kie.api.builder.KieFileSystem;
import org.kie.api.builder.KieModule;
import org.kie.api.event.rule.DebugRuleRuntimeEventListener;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.kie.internal.io.ResourceFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import java.io.IOException;

/**
 * @className: KieUtil
 * @description:
 * @author: wangzhongcheng
 * @date: 2024/12/26 17:13
 */
@Slf4j
public class KieUtil {

    private static final String RULES_PATH = "com/baidu/rules/";
    public static final String BASE_RULES_PATH = "classpath*:";

    public static final KieContainer KIE_CONTAINER;

    static {
        KieServices kieServices = KieServices.Factory.get();

        // 获得Kie容器对象
        KieFileSystem kieFileSystem = kieServices.newKieFileSystem();
        for (Resource file : getRuleFiles()) {
            kieFileSystem.write(ResourceFactory.newClassPathResource(RULES_PATH + file.getFilename(), "UTF-8"));
        }

        KieBuilder kieBuilder = kieServices.newKieBuilder(kieFileSystem);
        kieBuilder.buildAll();

        KieModule kieModule = kieBuilder.getKieModule();
        KIE_CONTAINER = kieServices.newKieContainer(kieModule.getReleaseId());
    }

    /**
     * 获取kie session
     *
     */
    public static KieSession getKieSession() {
        // 获取kie session , 此处获取的是有状态的session
        KieSession kieSession = KIE_CONTAINER.newKieSession();
        // 添加监听器，便于观察日志
        kieSession.addEventListener(new DebugRuleRuntimeEventListener());
        return kieSession;
    }

    /**
     * 获取规则文件
     *
     * @return
     */
    private static Resource[] getRuleFiles() {
        ResourcePatternResolver resourcePatternResolver = new PathMatchingResourcePatternResolver();
        try {
            return resourcePatternResolver.getResources(BASE_RULES_PATH + RULES_PATH + "**/*.*");
        } catch (IOException e) {
            log.error("获取规则文件失败", e);
            throw new RuntimeException(e);
        }
    }

}
