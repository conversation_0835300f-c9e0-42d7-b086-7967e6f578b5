package com.baidu.keyue.deepsight.database.service;

import com.baidu.keyue.deepsight.models.rules.response.UserPropertiesResult;

import java.util.List;

/**
 * @className: UserPropertiesService
 * @description:
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/25 19:19
 */
public interface UserPropertiesService {

    List<UserPropertiesResult> getUserProperties(String tenantId, Boolean includeBaidu);

    Boolean filedFromBaidu(String fieldName);
}
