package com.baidu.keyue.deepsight.database.mysql;

import com.baidu.keyue.deepsight.config.MysqlConfiguration;
import com.zaxxer.hikari.HikariDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * @className: MysqlConfig
 * @description:
 * @author: wangz<PERSON><PERSON>
 * @date: 2024/12/31 16:00
 */
@Configuration
@MapperScan(basePackages = "com.baidu.keyue.deepsight.mysqldb.mapper", sqlSessionFactoryRef = "mysqlSqlSessionFactory")
public class MysqlConfig {

    private static final String MAPPER_LOCATION = "classpath*:mapper/mysqldb/*.xml";
    private static final String TYPE_ALIASES_PACKAGE = "com.baidu.keyue.deepsight.mysqldb.entity.*";

    @Primary
    @Bean(name="mysqlDataSource")
    public DataSource mysqlDataSource(MysqlConfiguration mysqlConfiguration) {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setDriverClassName(mysqlConfiguration.getDriverClassName());
        dataSource.setJdbcUrl(mysqlConfiguration.getUrl());
        dataSource.setUsername(mysqlConfiguration.getUsername());
        dataSource.setPassword(mysqlConfiguration.getPassword());
        return dataSource;
    }

    @Primary
    @Bean("mysqlSqlSessionFactory")
    public SqlSessionFactory mysqlSqlSessionFactory(@Qualifier("mysqlDataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        // mapper的xml形式文件位置必须要配置，不然将报错：no statement （这种错误也可能是mapper的xml中，namespace与项目的路径不一致导致）
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(MAPPER_LOCATION));
        bean.setTypeAliasesPackage(TYPE_ALIASES_PACKAGE);
        return bean.getObject();
    }

    @Primary
    @Bean("mysqlSqlSessionTemplate")
    public SqlSessionTemplate mysqlSqlSessionTemplate(@Qualifier("mysqlSqlSessionFactory") SqlSessionFactory sqlSessionFactory){
        return new SqlSessionTemplate(sqlSessionFactory);
    }

    // 配置事务管理器
    @Bean(name = "deepSightTransactionManager")
    public PlatformTransactionManager transactionManager1(@Qualifier("mysqlDataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

}
