package com.baidu.keyue.deepsight.database.service;

import com.baidu.keyue.deepsight.config.Constants;
import lombok.experimental.UtilityClass;

@UtilityClass
public class DorisUtils {

    /**
     * 通过客群 id 生成客群字段名
     * @param groupId
     * @return
     */
    public String generateCustomerGroupFieldName(long groupId) {
        return Constants.DORIS_CUSTOMER_GROUP_FIELD_PREFIX + groupId;
    }

    /**
     * 通过标签字段 id 生成标签字段名
     * @param labelFieldId
     * @return
     */
    public String generateLabelFieldName(long labelFieldId) {
        return String.format(Constants.DORIS_LABEL_FIELD_TEM, labelFieldId);
    }
}
