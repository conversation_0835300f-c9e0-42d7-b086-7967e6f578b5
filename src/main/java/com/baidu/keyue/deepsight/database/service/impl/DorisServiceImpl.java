package com.baidu.keyue.deepsight.database.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.DorisConfiguration;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.database.service.DorisService;
import com.baidu.keyue.deepsight.enums.MysqlToJavaTypeMapping;
import com.baidu.keyue.deepsight.models.diffusion.dto.GradingDistributionDTO;
import com.baidu.keyue.deepsight.models.doris.TableDescribeDto;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @className: DorisServiceImpl
 * @description:
 * @author: wangzhongcheng
 * @date: 2025/1/2 10:26
 */
@Slf4j
@Service
public class DorisServiceImpl implements DorisService {

    @Autowired
    @Qualifier("dorisDataSource")
    private DataSource dorisDataSource;

    @Resource
    private DorisConfiguration dorisConfiguration;

    @SneakyThrows
    @Override
    public Flux<Map<String, Object>> queryDorisStreaming(String sql) {
        log.info("queryDorisStreaming Executing SQL:\n{}", sql);

        return Flux.create(sink -> {
            try (Connection connection = dorisDataSource.getConnection();
                 Statement stmt = connection.createStatement();
                 ResultSet rs = stmt.executeQuery(sql)) {

                ResultSetMetaData metaData = rs.getMetaData();
                int columnCount = metaData.getColumnCount();

                while (rs.next()) {
                    Map<String, Object> rowMap = new HashMap<>();
                    for (int i = 1; i <= columnCount; i++) {
                        try {
                            rowMap.put(metaData.getColumnLabel(i), rs.getObject(i));
                        } catch (SQLException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    sink.next(rowMap);
                }
                sink.complete();
            } catch (SQLException e) {
                sink.error(e);
            }
        });
    }

    @Override
    public List<Map<String, Object>> selectList(String sql) {
        log.info("selectList Executing SQL:\n{}", sql);

        // 这里应该有具体的实现逻辑，例如执行SQL查询等。
        List<Map<String, Object>> resultList = new ArrayList<>();

        try (Connection connection = dorisDataSource.getConnection();
             Statement statement = connection.createStatement();
             ResultSet resultSet = statement.executeQuery(sql)) {

            // 获取列名
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();

            // 处理查询结果
            while (resultSet.next()) {
                Map<String, Object> rowMap = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    // 获取列名
                    String columnName = metaData.getColumnLabel(i);
                    // 获取列值
                    Object columnValue = resultSet.getObject(i);
                    rowMap.put(columnName, columnValue);
                }
                resultList.add(rowMap);  // 将每行数据加入到结果列表
            }
        } catch (Exception e) {
            log.error("Error executing SQL: ", e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据库查询执行异常");
        }

        return resultList;
    }

    @Override
    public long getCount(String sql) {
        log.info("getCount Executing SQL:\n{}", sql);
        long count = 0;
        try (Connection connection = dorisDataSource.getConnection();
             Statement statement = connection.createStatement();
             ResultSet resultSet = statement.executeQuery(sql)) {

            // 读取结果
            if (resultSet.next()) {
                // 获取第一列的值
                count = resultSet.getLong(1);
            }
        } catch (Exception e) {
            log.error("Error executing SQL: ", e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据库查询执行异常");
        }
        return count;
    }

    @Override
    public long getSingleTableCount(String tableName) {
        String sql = String.format("SELECT COUNT(*) FROM %s", tableName);
        return getCount(sql);
    }

    @Override
    public int execSql(String sql) {
        Connection connection = null;
        try {
            connection = dorisDataSource.getConnection();
            Statement statement = connection.createStatement();
            statement.execute(sql);
            return statement.getUpdateCount();
        } catch (Exception e) {
            log.error("Error executing SQL, sql: {}, err: ", sql, e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据库执行异常");
        } finally {
            try {
                if (connection != null && !connection.isClosed()) {
                    connection.close();
                }
            } catch (SQLException ex) {
                ex.printStackTrace();
            }
        }

    }

    @Override
    public void createLabelProcessTemporaryTable(
            String db, String tableName, String dupTableName) {
        String createTableTemplate = """
                CREATE TABLE IF NOT EXISTS `%s`.`%s` (
                `id` varchar(128) NULL,
                `label` varchar(150) NULL DEFAULT "",
                `priority` int NULL
                ) ENGINE=OLAP
                UNIQUE KEY(`id`)
                DISTRIBUTED BY HASH(`id`) BUCKETS 2
                PROPERTIES (
                "replication_allocation" = "tag.location.default: 3",
                "is_being_synced" = "false",
                "storage_medium" = "hdd",
                "storage_format" = "V2",
                "light_schema_change" = "true",
                "disable_auto_compaction" = "false",
                "enable_single_replica_compaction" = "false",
                "enable_mow_light_delete" = "false"
                )""";
        String sql = String.format(createTableTemplate, db, dupTableName);
        execSql(sql);

        createTableTemplate = """
                CREATE TABLE IF NOT EXISTS `%s`.`%s` (
                `id` varchar(128) NULL,
                `label` array<varchar(150)> NULL DEFAULT "[]"
                ) ENGINE=OLAP
                UNIQUE KEY(`id`)
                DISTRIBUTED BY HASH(`id`) BUCKETS 2
                PROPERTIES (
                "replication_allocation" = "tag.location.default: 3",
                "is_being_synced" = "false",
                "storage_medium" = "hdd",
                "storage_format" = "V2",
                "light_schema_change" = "true",
                "disable_auto_compaction" = "false",
                "enable_single_replica_compaction" = "false",
                "enable_mow_light_delete" = "false"
                )""";
        sql = String.format(createTableTemplate, db, tableName);
        execSql(sql);
    }

    @Override
    public void deleteLabelProcessTemporaryTable(String db, String tableName) {
        // 删表安全检查
        if (StringUtils.startsWith(tableName, Constants.DORIS_DEFAULT_LABEL_TABLE)) {
            log.warn("Cannot delete default mock_user table");
            return;
        }
        String sql = String.format("DROP TABLE IF EXISTS %s.%s", db, tableName);
        execSql(sql);
    }

    @Override
    public void deleteLabelProcessField(String db, String tableName, String fieldName) {
        String sql = String.format("ALTER TABLE %s.%s DROP COLUMN %s", db, tableName, fieldName);
        execSql(sql);
    }

    @Override
    public void createCustomerProcessTemporaryTable(String db, String tableName, String primaryKey, String fieldName) {
        String createTableTemplate = """
                CREATE TABLE IF NOT EXISTS `%s`.`%s` (
                `%s` varchar(512) NULL,
                `%s` varchar(150) NULL DEFAULT "0"
                ) ENGINE=OLAP
                UNIQUE KEY(`%s`)
                DISTRIBUTED BY HASH(`%s`) BUCKETS 2
                PROPERTIES (
                "replication_allocation" = "tag.location.default: 3",
                "is_being_synced" = "false",
                "storage_medium" = "hdd",
                "storage_format" = "V2",
                "light_schema_change" = "true",
                "disable_auto_compaction" = "false",
                "enable_single_replica_compaction" = "false",
                "enable_mow_light_delete" = "false"
                )""";
        String sql = String.format(createTableTemplate, db, tableName, primaryKey, fieldName, primaryKey, primaryKey);
        execSql(sql);
    }

    @Override
    public Map<String, String> getFieldSchema(String tableName) {
        Map<String, String> map = new HashMap<>();
        try (Connection connection = dorisDataSource.getConnection();) {
            // 获取数据库元数据
            DatabaseMetaData metaData = connection.getMetaData();

            // 获取表的字段信息
            try (ResultSet columns = metaData.getColumns(dorisConfiguration.getDb(), null, tableName, null)) {
                while (columns.next()) {
                    String columnName = String.format("%s.`%s`", tableName, columns.getString("COLUMN_NAME"));
                    String columnType = columns.getString("TYPE_NAME");
                    if (columnName.contains(Constants.DORIS_LABEL_PROCESS_FIELD_NAME_PREFIX)) {
                        map.put(columnName, "array");
                    } else if (columnName.contains(Constants.DORIS_CUSTOMER_GROUP_FIELD_PREFIX)) {
                        map.put(columnName, "varchar");
                    } else {
                        map.put(columnName, StringUtils.toRootLowerCase(columnType));
                    }
                }
            }
        } catch (Exception e) {
            log.error("Error executing SQL: ", e);
            throw new DeepSightException.ParamsErrorException(ErrorCode.INTERNAL_ERROR, "数据库查询执行异常");
        }
        return map;
    }

    @Override
    public List<String> showTablesWithPrefix(String prefix) {
        String sql = "SHOW TABLES LIKE '" + prefix + "_%'";
        List<Map<String, Object>> result = selectList(sql);
        List<String> tableNames = new ArrayList<>();
        for (Map<String, Object> map : result) {
            String tableName = null;
            for (String key : map.keySet()) {
                tableName = map.get(key).toString();
            }
            tableNames.add(tableName);
        }
        return tableNames;
    }

    @Override
    public Map<String, String> describeTable(String tableName) {
        String sql = String.format("describe %s", tableName);
        List<Map<String, Object>> result = selectList(sql);

        Map<String, String> tableSchema = new HashMap<>();

        for (Map<String, Object> map : result) {
            tableSchema.put(String.valueOf(map.get("Field")), String.valueOf(map.get("Type")));
        }
        return tableSchema;
    }

    @Override
    public List<TableDescribeDto> describeTableSchema(String tableName) {
        String sql = String.format("describe %s", tableName);
        List<Map<String, Object>> result = selectList(sql);

        return result.stream()
                .map(TableDescribeDto::fromMap)
                .collect(Collectors.toList());
    }

    @Override
    public boolean existColumn(String tableName, String column) {
        String querySql = String.format("SELECT column_name FROM information_schema.columns " +
                        "WHERE table_schema = '%s' AND table_name = '%s' AND column_name = '%s' LIMIT 1;",
                dorisConfiguration.getDb(), tableName, column);
        List<Map<String, Object>> maps = selectList(querySql);
        return CollUtil.isNotEmpty(maps);
    }

    @Override
    public boolean existTable(String tableName) {
        String querySql = String.format("SELECT table_name FROM information_schema.columns " +
                        "WHERE table_schema = '%s' AND table_name = '%s' LIMIT 1;",
                dorisConfiguration.getDb(), tableName);
        List<Map<String, Object>> maps = selectList(querySql);
        return CollUtil.isNotEmpty(maps);
    }

    @Override
    public void deleteTable(String tableName) {
        String deleteSql = String.format("DROP TABLE IF EXISTS `%s`;", tableName);
        execSql(deleteSql);
    }

    @Override
    public void operationSchema(String execSql) {
        String upperCase = execSql.toUpperCase();
        // 字段添加
        if (upperCase.contains("ADD COLUMN")) {
            String[] split = execSql.split(" ");
            String tableName = split[2].replaceAll("`", "");
            String column = split[5].replaceAll("`", "");
            if (!existColumn(tableName, column)) {
                execSql(execSql);
            } else {
                log.info("{} {} exists, add skip", tableName, column);
            }
        } else if (upperCase.contains("RENAME COLUMN")) {
            // 字段重命名
            String[] split = execSql.split(" ");
            String tableName = split[2].replaceAll("`", "");
            String oldColumn = split[split.length - 2].replaceAll("`", "").replaceAll(";", "");
            String column = split[split.length - 1].replaceAll("`", "").replaceAll(";", "");
            if (existColumn(tableName, oldColumn) && !existColumn(tableName, column)) {
                execSql(execSql);
            } else {
                log.info("{} {} exists, rename skip", tableName, column);
            }
        } else if (upperCase.contains("DROP COLUMN")) {
            // 删除字段
            String[] split = execSql.split(" ");
            String tableName = split[2].replaceAll("`", "");
            String column = split[split.length - 1].replaceAll("`", "").replaceAll(";", "");
            if (!existColumn(tableName, column)) {
                execSql(execSql);
            }
        } else {
            // 其他SQL，如建表
            execSql(execSql);
        }
    }

    /**
     * 获取表字段名
     * 表不存在则为空
     *
     * @param tableName 表名
     * @return
     */
    public Set<String> getFieldNames(String tableName) {
        Set<String> res = new HashSet<>();
        Map<String, String> fieldSchema = getFieldSchema(tableName);
        if (CollUtil.isEmpty(fieldSchema)) {
            return res;
        }
        for (String field : fieldSchema.keySet()) {
            res.add(field.split("\\.")[1].replaceAll("`", StringUtils.EMPTY));
        }
        return res;
    }

    @Override
    public void createDiffusionCalculateTemporaryTable(String db, String tableName) {
        String createTableTemplate = """
                CREATE TABLE IF NOT EXISTS `%s`.`%s` (
                    `oneId` VARCHAR(255) COMMENT "全局oneId",
                    `score` DECIMAL(10,8) NULL DEFAULT "0",
                    `deepsight_datetime` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "写入时间",
                    `deepsight_update_datetime` datetime NULL COMMENT "更新时间"
                ) ENGINE=OLAP
                UNIQUE KEY(`oneId`)
                DISTRIBUTED BY HASH(`oneId`) BUCKETS 2
                PROPERTIES (
                "replication_allocation" = "tag.location.default: 3",
                "is_being_synced" = "false",
                "storage_medium" = "hdd",
                "storage_format" = "V2",
                "light_schema_change" = "true",
                "disable_auto_compaction" = "false",
                "enable_single_replica_compaction" = "false",
                "enable_mow_light_delete" = "false"
                )""";
        String sql = String.format(createTableTemplate, db, tableName);
        execSql(sql);
    }

    @Override
    public void deleteDiffusionCalculateTemporaryTable(String db, String tableName) {
        String sql = String.format("DROP TABLE IF EXISTS %s.%s", db, tableName);
        execSql(sql);
    }

    @Override
    public Object covertDorisValue(String dataType, Object data) {
        MysqlToJavaTypeMapping mapping = MysqlToJavaTypeMapping.getJavaType(dataType);
        if (data == null || mapping == null) {
            return data;
        }
        String type = mapping.getType();
        switch (type) {
            // 数字类型
            case Constants.CONSTANT_NUMBER:
                if (!(data instanceof Number)) {
                    try {
                        String dataString = data.toString();
                        if (dataString.contains(".")) {
                            data = Double.parseDouble(dataString);
                        } else {
                            data = Long.parseLong(dataString);
                        }
                    } catch (Exception e) {
                        log.error("doris parser number Exception:{}", data, e);
                    }
                }
                break;
            case Constants.CONSTANT_STRING:
                if (!(data instanceof String)) {
                    data = data.toString();
                }
                break;
            case Constants.CONSTANT_BOOLEAN:
                if (!(data instanceof Boolean)) {
                    try {
                        data = Boolean.parseBoolean(data.toString());
                    } catch (Exception e) {
                        log.error("doris parser boolean Exception:{}", data, e);
                    }
                }
                break;
            case Constants.CONSTANT_JSON:
                try {
                    data = JsonUtils.toMap(data.toString());
                } catch (Exception e) {
                    log.error("parser json Exception:{}", data, e);
                }
                break;
            case Constants.CONSTANT_ARRAY:
                try {
                    data = JsonUtils.readType(data.toString(), new TypeReference<List<Object>>() {});
                } catch (Exception e) {
                    log.error("array json Exception:{}", data, e);
                }
                break;
            default:
                break;
        }
        return data;
    }

    @Override
    public List<GradingDistributionDTO> diffusionGradingStatistics(String db, String tableName) {
        List<GradingDistributionDTO> gradingDistributionDTOS =
                JsonUtils.toListUnchecked(Constants.DIFFUSION_GRADING_STATISTICS_TEMPLATE_JSON, List.class, GradingDistributionDTO.class);
        String diffusionGradingStatisticsSql = String.format("""
                SELECT
                    CONCAT(
                        CAST(FLOOR(score * 10)/10 AS DECIMAL(3,1)),
                        '-',
                        CAST(FLOOR(score * 10)/10 + 0.1 AS DECIMAL(3,1))
                    ) AS grade,
                    COUNT(*) AS 'count',
                    ROUND(COUNT(*) / SUM(COUNT(*)) OVER(), 4) AS percentage
                FROM `%s`.`%s`
                WHERE score BETWEEN 0 AND 1
                GROUP BY FLOOR(score * 10)
                ORDER BY FLOOR(score * 10) DESC;
                """, db, tableName);
        List<Map<String, Object>> dorisData = selectList(diffusionGradingStatisticsSql);
        Map<String, GradingDistributionDTO> gradingDistributionDTOMap = dorisData.stream().collect(Collectors.toMap(
                map -> map.get("grade").toString(),
                map -> new GradingDistributionDTO(
                        map.get("grade").toString(),
                        Long.parseLong(map.get("count").toString()),
                        Double.parseDouble(map.get("percentage").toString())
                ),
                (v1, v2) -> v2
        ));

        // 填补
        for (GradingDistributionDTO gradingDistributionDTO : gradingDistributionDTOS) {
            String grade = gradingDistributionDTO.getGrade();
            if (gradingDistributionDTOMap.containsKey(grade)) {
                gradingDistributionDTO.setCount(gradingDistributionDTOMap.get(grade).getCount());
                gradingDistributionDTO.setPercentage(gradingDistributionDTOMap.get(grade).getPercentage());
            }
        }
        return gradingDistributionDTOS;
    }

}
