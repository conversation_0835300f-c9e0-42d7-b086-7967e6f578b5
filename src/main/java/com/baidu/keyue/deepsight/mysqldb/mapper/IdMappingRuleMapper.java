package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRuleCriteria;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IdMappingRuleMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_rule
     *
     * @mbg.generated
     */
    long countByExample(IdMappingRuleCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_rule
     *
     * @mbg.generated
     */
    int deleteByExample(IdMappingRuleCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_rule
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_rule
     *
     * @mbg.generated
     */
    int insert(IdMappingRule record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_rule
     *
     * @mbg.generated
     */
    int insertSelective(IdMappingRule record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_rule
     *
     * @mbg.generated
     */
    List<IdMappingRule> selectByExample(IdMappingRuleCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_rule
     *
     * @mbg.generated
     */
    IdMappingRule selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_rule
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") IdMappingRule record, @Param("example") IdMappingRuleCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_rule
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") IdMappingRule record, @Param("example") IdMappingRuleCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_rule
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(IdMappingRule record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_rule
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(IdMappingRule record);
}