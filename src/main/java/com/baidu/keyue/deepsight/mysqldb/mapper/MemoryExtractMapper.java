package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtract;
import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MemoryExtractMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    long countByExample(MemoryExtractCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    int deleteByExample(MemoryExtractCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    int insert(MemoryExtractWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    int insertSelective(MemoryExtractWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    List<MemoryExtractWithBLOBs> selectByExampleWithBLOBs(MemoryExtractCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    List<MemoryExtract> selectByExample(MemoryExtractCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    MemoryExtractWithBLOBs selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") MemoryExtractWithBLOBs record, @Param("example") MemoryExtractCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") MemoryExtractWithBLOBs record, @Param("example") MemoryExtractCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") MemoryExtract record, @Param("example") MemoryExtractCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MemoryExtractWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(MemoryExtractWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MemoryExtract record);
    
    int updateCalTaskStatus(@Param("id") Long id, @Param("labelCalStatus") Byte labelCalStatus);
}