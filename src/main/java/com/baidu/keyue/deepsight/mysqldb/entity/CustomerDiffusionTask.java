package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table customer_diffusion_task
 *
 * @mbg.generated do_not_delete_during_merge
 */
public class CustomerDiffusionTask implements Serializable {
    /**
     * Database Column Remarks:
     *   预测扩散id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   预测任务名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.task_name
     *
     * @mbg.generated
     */
    private String taskName;

    /**
     * Database Column Remarks:
     *   种子人群id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.seed_group
     *
     * @mbg.generated
     */
    private Long seedGroup;

    /**
     * Database Column Remarks:
     *   预测人群id，逗号分隔
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.predict_group
     *
     * @mbg.generated
     */
    private String predictGroup;

    /**
     * Database Column Remarks:
     *   过滤规则:0不过滤 1剔除种子人群
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.filter_rule
     *
     * @mbg.generated
     */
    private Byte filterRule;

    /**
     * Database Column Remarks:
     *   特征筛选:0系统推荐 1覆盖率自定义
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.feature_select
     *
     * @mbg.generated
     */
    private Byte featureSelect;

    /**
     * Database Column Remarks:
     *   覆盖率阈值
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.threshold
     *
     * @mbg.generated
     */
    private Float threshold;

    /**
     * Database Column Remarks:
     *   判定标准:0根据相似度 1取前几个
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.judge_criteria
     *
     * @mbg.generated
     */
    private Byte judgeCriteria;

    /**
     * Database Column Remarks:
     *   判定标准：相似度
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.similarity
     *
     * @mbg.generated
     */
    private Float similarity;

    /**
     * Database Column Remarks:
     *   判定标准：取前几个
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.ranking
     *
     * @mbg.generated
     */
    private Integer ranking;

    /**
     * Database Column Remarks:
     *   更新触发类型:0:定时触发,1:手动触发
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.trigger_mod
     *
     * @mbg.generated
     */
    private Byte triggerMod;

    /**
     * Database Column Remarks:
     *   执行频率:0:每天,1:每周,2:每月
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.trigger_frequency
     *
     * @mbg.generated
     */
    private Byte triggerFrequency;

    /**
     * Database Column Remarks:
     *   执行频率json
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.trigger_frequency_value
     *
     * @mbg.generated
     */
    private String triggerFrequencyValue;

    /**
     * Database Column Remarks:
     *   计算状态: 0:待预测,1:预测中,2:预测成功,3:预测失败,4:计算取消
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.cal_status
     *
     * @mbg.generated
     */
    private Byte calStatus;

    /**
     * Database Column Remarks:
     *   上一次执行时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.last_cal_date
     *
     * @mbg.generated
     */
    private Date lastCalDate;

    /**
     * Database Column Remarks:
     *   任务ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.task_id
     *
     * @mbg.generated
     */
    private Long taskId;

    /**
     * Database Column Remarks:
     *   租户id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     * Database Column Remarks:
     *   打包到客群标识,0:未进行,1:已进行
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.group_package
     *
     * @mbg.generated
     */
    private Boolean groupPackage;

    /**
     * Database Column Remarks:
     *   删除标识,0:未删除,1:已删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     * Database Column Remarks:
     *   创建者
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     * Database Column Remarks:
     *   修改者
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   更新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   人群扩散客群 id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.customer_group_id
     *
     * @mbg.generated
     */
    private Long customerGroupId;

    /**
     * Database Column Remarks:
     *   创建人姓名
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.creator_name
     *
     * @mbg.generated
     */
    private String creatorName;

    /**
     * Database Column Remarks:
     *   更新人姓名
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_diffusion_task.modifier_name
     *
     * @mbg.generated
     */
    private String modifierName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.id
     *
     * @return the value of customer_diffusion_task.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.id
     *
     * @param id the value for customer_diffusion_task.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.task_name
     *
     * @return the value of customer_diffusion_task.task_name
     *
     * @mbg.generated
     */
    public String getTaskName() {
        return taskName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.task_name
     *
     * @param taskName the value for customer_diffusion_task.task_name
     *
     * @mbg.generated
     */
    public void setTaskName(String taskName) {
        this.taskName = taskName == null ? null : taskName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.seed_group
     *
     * @return the value of customer_diffusion_task.seed_group
     *
     * @mbg.generated
     */
    public Long getSeedGroup() {
        return seedGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.seed_group
     *
     * @param seedGroup the value for customer_diffusion_task.seed_group
     *
     * @mbg.generated
     */
    public void setSeedGroup(Long seedGroup) {
        this.seedGroup = seedGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.predict_group
     *
     * @return the value of customer_diffusion_task.predict_group
     *
     * @mbg.generated
     */
    public String getPredictGroup() {
        return predictGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.predict_group
     *
     * @param predictGroup the value for customer_diffusion_task.predict_group
     *
     * @mbg.generated
     */
    public void setPredictGroup(String predictGroup) {
        this.predictGroup = predictGroup == null ? null : predictGroup.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.filter_rule
     *
     * @return the value of customer_diffusion_task.filter_rule
     *
     * @mbg.generated
     */
    public Byte getFilterRule() {
        return filterRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.filter_rule
     *
     * @param filterRule the value for customer_diffusion_task.filter_rule
     *
     * @mbg.generated
     */
    public void setFilterRule(Byte filterRule) {
        this.filterRule = filterRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.feature_select
     *
     * @return the value of customer_diffusion_task.feature_select
     *
     * @mbg.generated
     */
    public Byte getFeatureSelect() {
        return featureSelect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.feature_select
     *
     * @param featureSelect the value for customer_diffusion_task.feature_select
     *
     * @mbg.generated
     */
    public void setFeatureSelect(Byte featureSelect) {
        this.featureSelect = featureSelect;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.threshold
     *
     * @return the value of customer_diffusion_task.threshold
     *
     * @mbg.generated
     */
    public Float getThreshold() {
        return threshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.threshold
     *
     * @param threshold the value for customer_diffusion_task.threshold
     *
     * @mbg.generated
     */
    public void setThreshold(Float threshold) {
        this.threshold = threshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.judge_criteria
     *
     * @return the value of customer_diffusion_task.judge_criteria
     *
     * @mbg.generated
     */
    public Byte getJudgeCriteria() {
        return judgeCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.judge_criteria
     *
     * @param judgeCriteria the value for customer_diffusion_task.judge_criteria
     *
     * @mbg.generated
     */
    public void setJudgeCriteria(Byte judgeCriteria) {
        this.judgeCriteria = judgeCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.similarity
     *
     * @return the value of customer_diffusion_task.similarity
     *
     * @mbg.generated
     */
    public Float getSimilarity() {
        return similarity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.similarity
     *
     * @param similarity the value for customer_diffusion_task.similarity
     *
     * @mbg.generated
     */
    public void setSimilarity(Float similarity) {
        this.similarity = similarity;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.ranking
     *
     * @return the value of customer_diffusion_task.ranking
     *
     * @mbg.generated
     */
    public Integer getRanking() {
        return ranking;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.ranking
     *
     * @param ranking the value for customer_diffusion_task.ranking
     *
     * @mbg.generated
     */
    public void setRanking(Integer ranking) {
        this.ranking = ranking;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.trigger_mod
     *
     * @return the value of customer_diffusion_task.trigger_mod
     *
     * @mbg.generated
     */
    public Byte getTriggerMod() {
        return triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.trigger_mod
     *
     * @param triggerMod the value for customer_diffusion_task.trigger_mod
     *
     * @mbg.generated
     */
    public void setTriggerMod(Byte triggerMod) {
        this.triggerMod = triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.trigger_frequency
     *
     * @return the value of customer_diffusion_task.trigger_frequency
     *
     * @mbg.generated
     */
    public Byte getTriggerFrequency() {
        return triggerFrequency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.trigger_frequency
     *
     * @param triggerFrequency the value for customer_diffusion_task.trigger_frequency
     *
     * @mbg.generated
     */
    public void setTriggerFrequency(Byte triggerFrequency) {
        this.triggerFrequency = triggerFrequency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.trigger_frequency_value
     *
     * @return the value of customer_diffusion_task.trigger_frequency_value
     *
     * @mbg.generated
     */
    public String getTriggerFrequencyValue() {
        return triggerFrequencyValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.trigger_frequency_value
     *
     * @param triggerFrequencyValue the value for customer_diffusion_task.trigger_frequency_value
     *
     * @mbg.generated
     */
    public void setTriggerFrequencyValue(String triggerFrequencyValue) {
        this.triggerFrequencyValue = triggerFrequencyValue == null ? null : triggerFrequencyValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.cal_status
     *
     * @return the value of customer_diffusion_task.cal_status
     *
     * @mbg.generated
     */
    public Byte getCalStatus() {
        return calStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.cal_status
     *
     * @param calStatus the value for customer_diffusion_task.cal_status
     *
     * @mbg.generated
     */
    public void setCalStatus(Byte calStatus) {
        this.calStatus = calStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.last_cal_date
     *
     * @return the value of customer_diffusion_task.last_cal_date
     *
     * @mbg.generated
     */
    public Date getLastCalDate() {
        return lastCalDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.last_cal_date
     *
     * @param lastCalDate the value for customer_diffusion_task.last_cal_date
     *
     * @mbg.generated
     */
    public void setLastCalDate(Date lastCalDate) {
        this.lastCalDate = lastCalDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.task_id
     *
     * @return the value of customer_diffusion_task.task_id
     *
     * @mbg.generated
     */
    public Long getTaskId() {
        return taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.task_id
     *
     * @param taskId the value for customer_diffusion_task.task_id
     *
     * @mbg.generated
     */
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.tenant_id
     *
     * @return the value of customer_diffusion_task.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.tenant_id
     *
     * @param tenantId the value for customer_diffusion_task.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.group_package
     *
     * @return the value of customer_diffusion_task.group_package
     *
     * @mbg.generated
     */
    public Boolean getGroupPackage() {
        return groupPackage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.group_package
     *
     * @param groupPackage the value for customer_diffusion_task.group_package
     *
     * @mbg.generated
     */
    public void setGroupPackage(Boolean groupPackage) {
        this.groupPackage = groupPackage;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.del
     *
     * @return the value of customer_diffusion_task.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.del
     *
     * @param del the value for customer_diffusion_task.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.creator
     *
     * @return the value of customer_diffusion_task.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.creator
     *
     * @param creator the value for customer_diffusion_task.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.modifier
     *
     * @return the value of customer_diffusion_task.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.modifier
     *
     * @param modifier the value for customer_diffusion_task.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.create_time
     *
     * @return the value of customer_diffusion_task.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.create_time
     *
     * @param createTime the value for customer_diffusion_task.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.update_time
     *
     * @return the value of customer_diffusion_task.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.update_time
     *
     * @param updateTime the value for customer_diffusion_task.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.customer_group_id
     *
     * @return the value of customer_diffusion_task.customer_group_id
     *
     * @mbg.generated
     */
    public Long getCustomerGroupId() {
        return customerGroupId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.customer_group_id
     *
     * @param customerGroupId the value for customer_diffusion_task.customer_group_id
     *
     * @mbg.generated
     */
    public void setCustomerGroupId(Long customerGroupId) {
        this.customerGroupId = customerGroupId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.creator_name
     *
     * @return the value of customer_diffusion_task.creator_name
     *
     * @mbg.generated
     */
    public String getCreatorName() {
        return creatorName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.creator_name
     *
     * @param creatorName the value for customer_diffusion_task.creator_name
     *
     * @mbg.generated
     */
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName == null ? null : creatorName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_diffusion_task.modifier_name
     *
     * @return the value of customer_diffusion_task.modifier_name
     *
     * @mbg.generated
     */
    public String getModifierName() {
        return modifierName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_diffusion_task.modifier_name
     *
     * @param modifierName the value for customer_diffusion_task.modifier_name
     *
     * @mbg.generated
     */
    public void setModifierName(String modifierName) {
        this.modifierName = modifierName == null ? null : modifierName.trim();
    }
}