package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class AccessToken implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_access_token.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_access_token.tenant_id
     *
     * @mbg.generated
     */
    private Long tenantId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_access_token.table_name
     *
     * @mbg.generated
     */
    private String tableName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_access_token.access_key
     *
     * @mbg.generated
     */
    private String accessKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_access_token.secret_key
     *
     * @mbg.generated
     */
    private String secretKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_access_token.access_key_desc
     *
     * @mbg.generated
     */
    private String accessKeyDesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_access_token.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table datatable_access_token
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_access_token.id
     *
     * @return the value of datatable_access_token.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_access_token.id
     *
     * @param id the value for datatable_access_token.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_access_token.tenant_id
     *
     * @return the value of datatable_access_token.tenant_id
     *
     * @mbg.generated
     */
    public Long getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_access_token.tenant_id
     *
     * @param tenantId the value for datatable_access_token.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_access_token.table_name
     *
     * @return the value of datatable_access_token.table_name
     *
     * @mbg.generated
     */
    public String getTableName() {
        return tableName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_access_token.table_name
     *
     * @param tableName the value for datatable_access_token.table_name
     *
     * @mbg.generated
     */
    public void setTableName(String tableName) {
        this.tableName = tableName == null ? null : tableName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_access_token.access_key
     *
     * @return the value of datatable_access_token.access_key
     *
     * @mbg.generated
     */
    public String getAccessKey() {
        return accessKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_access_token.access_key
     *
     * @param accessKey the value for datatable_access_token.access_key
     *
     * @mbg.generated
     */
    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey == null ? null : accessKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_access_token.secret_key
     *
     * @return the value of datatable_access_token.secret_key
     *
     * @mbg.generated
     */
    public String getSecretKey() {
        return secretKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_access_token.secret_key
     *
     * @param secretKey the value for datatable_access_token.secret_key
     *
     * @mbg.generated
     */
    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey == null ? null : secretKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_access_token.access_key_desc
     *
     * @return the value of datatable_access_token.access_key_desc
     *
     * @mbg.generated
     */
    public String getAccessKeyDesc() {
        return accessKeyDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_access_token.access_key_desc
     *
     * @param accessKeyDesc the value for datatable_access_token.access_key_desc
     *
     * @mbg.generated
     */
    public void setAccessKeyDesc(String accessKeyDesc) {
        this.accessKeyDesc = accessKeyDesc == null ? null : accessKeyDesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_access_token.create_time
     *
     * @return the value of datatable_access_token.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_access_token.create_time
     *
     * @param createTime the value for datatable_access_token.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}