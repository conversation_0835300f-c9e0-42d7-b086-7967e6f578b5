package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMetaCriteria;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AiobSopMetaMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    long countByExample(AiobSopMetaCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    int deleteByExample(AiobSopMetaCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    int insert(AiobSopMeta record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    int insertSelective(AiobSopMeta record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    List<AiobSopMeta> selectByExampleWithBLOBs(AiobSopMetaCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    List<AiobSopMeta> selectByExample(AiobSopMetaCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    AiobSopMeta selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") AiobSopMeta record, @Param("example") AiobSopMetaCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") AiobSopMeta record, @Param("example") AiobSopMetaCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") AiobSopMeta record, @Param("example") AiobSopMetaCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AiobSopMeta record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(AiobSopMeta record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AiobSopMeta record);
}