package com.baidu.keyue.deepsight.mysqldb.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 补充 IdMappingRelationMapper 实现不了的sql
 */
@Mapper
public interface ExtendIdMappingRelationMapper {

    /**
     * 查询ID对公共ID对条数：排除 dataTableId 统计
     *
     */
    long countPublicId(@Param("tenantId") String tenantId,
                       @Param("dataTableId") Long dataTableId,
                       @Param("enFields") List<String> enFields);

    /**
     * 查询ID对配置中已删除的数据集名
     * @param tenantId 租户ID
     * @return
     */
    List<String> listDelDataTableName(@Param("tenantId") String tenantId);
}