package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;

public class TaskFileImportWithBLOBs extends TaskFileImport implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.field_mapping
     *
     * @mbg.generated
     */
    private byte[] fieldMapping;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.first_row_data
     *
     * @mbg.generated
     */
    private byte[] firstRowData;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.field_mapping
     *
     * @return the value of task_file_import.field_mapping
     *
     * @mbg.generated
     */
    public byte[] getFieldMapping() {
        return fieldMapping;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.field_mapping
     *
     * @param fieldMapping the value for task_file_import.field_mapping
     *
     * @mbg.generated
     */
    public void setFieldMapping(byte[] fieldMapping) {
        this.fieldMapping = fieldMapping;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.first_row_data
     *
     * @return the value of task_file_import.first_row_data
     *
     * @mbg.generated
     */
    public byte[] getFirstRowData() {
        return firstRowData;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.first_row_data
     *
     * @param firstRowData the value for task_file_import.first_row_data
     *
     * @mbg.generated
     */
    public void setFirstRowData(byte[] firstRowData) {
        this.firstRowData = firstRowData;
    }
}