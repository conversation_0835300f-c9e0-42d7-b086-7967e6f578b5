package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGenerator;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingGeneratorCriteria;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IdMappingGeneratorMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_generator
     *
     * @mbg.generated
     */
    long countByExample(IdMappingGeneratorCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_generator
     *
     * @mbg.generated
     */
    int deleteByExample(IdMappingGeneratorCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_generator
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_generator
     *
     * @mbg.generated
     */
    int insert(IdMappingGenerator record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_generator
     *
     * @mbg.generated
     */
    int insertSelective(IdMappingGenerator record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_generator
     *
     * @mbg.generated
     */
    List<IdMappingGenerator> selectByExample(IdMappingGeneratorCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_generator
     *
     * @mbg.generated
     */
    IdMappingGenerator selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_generator
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") IdMappingGenerator record, @Param("example") IdMappingGeneratorCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_generator
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") IdMappingGenerator record, @Param("example") IdMappingGeneratorCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_generator
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(IdMappingGenerator record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_generator
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(IdMappingGenerator record);
}