package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.AiobRobotVersion;
import com.baidu.keyue.deepsight.mysqldb.entity.AiobRobotVersionCriteria;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AiobRobotVersionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_robot_version
     *
     * @mbg.generated
     */
    long countByExample(AiobRobotVersionCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_robot_version
     *
     * @mbg.generated
     */
    int deleteByExample(AiobRobotVersionCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_robot_version
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_robot_version
     *
     * @mbg.generated
     */
    int insert(AiobRobotVersion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_robot_version
     *
     * @mbg.generated
     */
    int insertSelective(AiobRobotVersion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_robot_version
     *
     * @mbg.generated
     */
    List<AiobRobotVersion> selectByExample(AiobRobotVersionCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_robot_version
     *
     * @mbg.generated
     */
    AiobRobotVersion selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_robot_version
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") AiobRobotVersion record, @Param("example") AiobRobotVersionCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_robot_version
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") AiobRobotVersion record, @Param("example") AiobRobotVersionCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_robot_version
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AiobRobotVersion record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table aiob_robot_version
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AiobRobotVersion record);
}