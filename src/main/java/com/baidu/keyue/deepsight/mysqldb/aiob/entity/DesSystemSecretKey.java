package com.baidu.keyue.deepsight.mysqldb.aiob.entity;

import java.io.Serializable;
import java.util.Date;

public class DesSystemSecretKey implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_system_secret_key.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_system_secret_key.tenant_id
     *
     * @mbg.generated
     */
    private Long tenantId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_system_secret_key.system_secret_key
     *
     * @mbg.generated
     */
    private String systemSecretKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_system_secret_key.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_system_secret_key.request_id
     *
     * @mbg.generated
     */
    private String requestId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table des_system_secret_key
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_system_secret_key.id
     *
     * @return the value of des_system_secret_key.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_system_secret_key.id
     *
     * @param id the value for des_system_secret_key.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_system_secret_key.tenant_id
     *
     * @return the value of des_system_secret_key.tenant_id
     *
     * @mbg.generated
     */
    public Long getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_system_secret_key.tenant_id
     *
     * @param tenantId the value for des_system_secret_key.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_system_secret_key.system_secret_key
     *
     * @return the value of des_system_secret_key.system_secret_key
     *
     * @mbg.generated
     */
    public String getSystemSecretKey() {
        return systemSecretKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_system_secret_key.system_secret_key
     *
     * @param systemSecretKey the value for des_system_secret_key.system_secret_key
     *
     * @mbg.generated
     */
    public void setSystemSecretKey(String systemSecretKey) {
        this.systemSecretKey = systemSecretKey == null ? null : systemSecretKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_system_secret_key.create_time
     *
     * @return the value of des_system_secret_key.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_system_secret_key.create_time
     *
     * @param createTime the value for des_system_secret_key.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_system_secret_key.request_id
     *
     * @return the value of des_system_secret_key.request_id
     *
     * @mbg.generated
     */
    public String getRequestId() {
        return requestId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_system_secret_key.request_id
     *
     * @param requestId the value for des_system_secret_key.request_id
     *
     * @mbg.generated
     */
    public void setRequestId(String requestId) {
        this.requestId = requestId == null ? null : requestId.trim();
    }
}