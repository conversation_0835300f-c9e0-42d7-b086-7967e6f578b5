package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfoCriteria;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TenantInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tenant_info
     *
     * @mbg.generated
     */
    long countByExample(TenantInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tenant_info
     *
     * @mbg.generated
     */
    int deleteByExample(TenantInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tenant_info
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tenant_info
     *
     * @mbg.generated
     */
    int insert(TenantInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tenant_info
     *
     * @mbg.generated
     */
    int insertSelective(TenantInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tenant_info
     *
     * @mbg.generated
     */
    List<TenantInfo> selectByExample(TenantInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tenant_info
     *
     * @mbg.generated
     */
    TenantInfo selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tenant_info
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TenantInfo record, @Param("example") TenantInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tenant_info
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TenantInfo record, @Param("example") TenantInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tenant_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TenantInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table tenant_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TenantInfo record);
}