package com.baidu.keyue.deepsight.mysqldb.aiob.entity;

import java.io.Serializable;
import java.util.Date;

public class DesSecretKey implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_secret_key.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_secret_key.tenant_id
     *
     * @mbg.generated
     */
    private Long tenantId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_secret_key.secret_key_no
     *
     * @mbg.generated
     */
    private Long secretKeyNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_secret_key.encryption_way
     *
     * @mbg.generated
     */
    private Byte encryptionWay;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_secret_key.secret_key
     *
     * @mbg.generated
     */
    private String secretKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_secret_key.remarks
     *
     * @mbg.generated
     */
    private String remarks;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_secret_key.state
     *
     * @mbg.generated
     */
    private Byte state;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_secret_key.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_secret_key.create_user_id
     *
     * @mbg.generated
     */
    private Long createUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_secret_key.last_update_time
     *
     * @mbg.generated
     */
    private Date lastUpdateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_secret_key.last_update_user_id
     *
     * @mbg.generated
     */
    private Long lastUpdateUserId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column des_secret_key.request_id
     *
     * @mbg.generated
     */
    private String requestId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table des_secret_key
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_secret_key.id
     *
     * @return the value of des_secret_key.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_secret_key.id
     *
     * @param id the value for des_secret_key.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_secret_key.tenant_id
     *
     * @return the value of des_secret_key.tenant_id
     *
     * @mbg.generated
     */
    public Long getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_secret_key.tenant_id
     *
     * @param tenantId the value for des_secret_key.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_secret_key.secret_key_no
     *
     * @return the value of des_secret_key.secret_key_no
     *
     * @mbg.generated
     */
    public Long getSecretKeyNo() {
        return secretKeyNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_secret_key.secret_key_no
     *
     * @param secretKeyNo the value for des_secret_key.secret_key_no
     *
     * @mbg.generated
     */
    public void setSecretKeyNo(Long secretKeyNo) {
        this.secretKeyNo = secretKeyNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_secret_key.encryption_way
     *
     * @return the value of des_secret_key.encryption_way
     *
     * @mbg.generated
     */
    public Byte getEncryptionWay() {
        return encryptionWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_secret_key.encryption_way
     *
     * @param encryptionWay the value for des_secret_key.encryption_way
     *
     * @mbg.generated
     */
    public void setEncryptionWay(Byte encryptionWay) {
        this.encryptionWay = encryptionWay;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_secret_key.secret_key
     *
     * @return the value of des_secret_key.secret_key
     *
     * @mbg.generated
     */
    public String getSecretKey() {
        return secretKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_secret_key.secret_key
     *
     * @param secretKey the value for des_secret_key.secret_key
     *
     * @mbg.generated
     */
    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey == null ? null : secretKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_secret_key.remarks
     *
     * @return the value of des_secret_key.remarks
     *
     * @mbg.generated
     */
    public String getRemarks() {
        return remarks;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_secret_key.remarks
     *
     * @param remarks the value for des_secret_key.remarks
     *
     * @mbg.generated
     */
    public void setRemarks(String remarks) {
        this.remarks = remarks == null ? null : remarks.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_secret_key.state
     *
     * @return the value of des_secret_key.state
     *
     * @mbg.generated
     */
    public Byte getState() {
        return state;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_secret_key.state
     *
     * @param state the value for des_secret_key.state
     *
     * @mbg.generated
     */
    public void setState(Byte state) {
        this.state = state;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_secret_key.create_time
     *
     * @return the value of des_secret_key.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_secret_key.create_time
     *
     * @param createTime the value for des_secret_key.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_secret_key.create_user_id
     *
     * @return the value of des_secret_key.create_user_id
     *
     * @mbg.generated
     */
    public Long getCreateUserId() {
        return createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_secret_key.create_user_id
     *
     * @param createUserId the value for des_secret_key.create_user_id
     *
     * @mbg.generated
     */
    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_secret_key.last_update_time
     *
     * @return the value of des_secret_key.last_update_time
     *
     * @mbg.generated
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_secret_key.last_update_time
     *
     * @param lastUpdateTime the value for des_secret_key.last_update_time
     *
     * @mbg.generated
     */
    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_secret_key.last_update_user_id
     *
     * @return the value of des_secret_key.last_update_user_id
     *
     * @mbg.generated
     */
    public Long getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_secret_key.last_update_user_id
     *
     * @param lastUpdateUserId the value for des_secret_key.last_update_user_id
     *
     * @mbg.generated
     */
    public void setLastUpdateUserId(Long lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column des_secret_key.request_id
     *
     * @return the value of des_secret_key.request_id
     *
     * @mbg.generated
     */
    public String getRequestId() {
        return requestId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column des_secret_key.request_id
     *
     * @param requestId the value for des_secret_key.request_id
     *
     * @mbg.generated
     */
    public void setRequestId(String requestId) {
        this.requestId = requestId == null ? null : requestId.trim();
    }
}