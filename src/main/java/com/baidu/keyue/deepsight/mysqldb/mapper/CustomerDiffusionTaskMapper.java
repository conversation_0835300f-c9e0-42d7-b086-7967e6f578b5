package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTaskCriteria;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CustomerDiffusionTaskMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    long countByExample(CustomerDiffusionTaskCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    int deleteByExample(CustomerDiffusionTaskCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    int insert(CustomerDiffusionTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    int insertSelective(CustomerDiffusionTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    List<CustomerDiffusionTask> selectByExample(CustomerDiffusionTaskCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    CustomerDiffusionTask selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") CustomerDiffusionTask record, @Param("example") CustomerDiffusionTaskCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") CustomerDiffusionTask record, @Param("example") CustomerDiffusionTaskCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(CustomerDiffusionTask record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(CustomerDiffusionTask record);
}