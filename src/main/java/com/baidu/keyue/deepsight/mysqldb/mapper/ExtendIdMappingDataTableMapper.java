package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTableWithRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExtendIdMappingDataTableMapper {

    List<IdMappingDataTableWithRule> selectIdMappingDataTableWithRule(@Param("tenantId") String tenantId,
                                                                      @Param("del") Boolean del);

}