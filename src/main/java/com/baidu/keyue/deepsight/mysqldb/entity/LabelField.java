package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class LabelField implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_field.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_field.field_type
     *
     * @mbg.generated
     */
    private String fieldType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_field.field_desc
     *
     * @mbg.generated
     */
    private String fieldDesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_field.label_table
     *
     * @mbg.generated
     */
    private String labelTable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_field.table_space
     *
     * @mbg.generated
     */
    private String tableSpace;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_field.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_field.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_field.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_field.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_field.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table label_field
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_field.id
     *
     * @return the value of label_field.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_field.id
     *
     * @param id the value for label_field.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_field.field_type
     *
     * @return the value of label_field.field_type
     *
     * @mbg.generated
     */
    public String getFieldType() {
        return fieldType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_field.field_type
     *
     * @param fieldType the value for label_field.field_type
     *
     * @mbg.generated
     */
    public void setFieldType(String fieldType) {
        this.fieldType = fieldType == null ? null : fieldType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_field.field_desc
     *
     * @return the value of label_field.field_desc
     *
     * @mbg.generated
     */
    public String getFieldDesc() {
        return fieldDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_field.field_desc
     *
     * @param fieldDesc the value for label_field.field_desc
     *
     * @mbg.generated
     */
    public void setFieldDesc(String fieldDesc) {
        this.fieldDesc = fieldDesc == null ? null : fieldDesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_field.label_table
     *
     * @return the value of label_field.label_table
     *
     * @mbg.generated
     */
    public String getLabelTable() {
        return labelTable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_field.label_table
     *
     * @param labelTable the value for label_field.label_table
     *
     * @mbg.generated
     */
    public void setLabelTable(String labelTable) {
        this.labelTable = labelTable == null ? null : labelTable.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_field.table_space
     *
     * @return the value of label_field.table_space
     *
     * @mbg.generated
     */
    public String getTableSpace() {
        return tableSpace;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_field.table_space
     *
     * @param tableSpace the value for label_field.table_space
     *
     * @mbg.generated
     */
    public void setTableSpace(String tableSpace) {
        this.tableSpace = tableSpace == null ? null : tableSpace.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_field.del
     *
     * @return the value of label_field.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_field.del
     *
     * @param del the value for label_field.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_field.creator
     *
     * @return the value of label_field.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_field.creator
     *
     * @param creator the value for label_field.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_field.modifier
     *
     * @return the value of label_field.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_field.modifier
     *
     * @param modifier the value for label_field.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_field.create_time
     *
     * @return the value of label_field.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_field.create_time
     *
     * @param createTime the value for label_field.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_field.update_time
     *
     * @return the value of label_field.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_field.update_time
     *
     * @param updateTime the value for label_field.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}