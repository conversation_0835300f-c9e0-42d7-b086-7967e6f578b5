package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class DataPredictionSource implements Serializable, TaskInfoObj {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.prompt_type
     *
     * @mbg.generated
     */
    private Byte promptType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.prediction_update_type
     *
     * @mbg.generated
     */
    private Boolean predictionUpdateType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.trigger_mod
     *
     * @mbg.generated
     */
    private Byte triggerMod;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.trigger_frequency
     *
     * @mbg.generated
     */
    private Byte triggerFrequency;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.trigger_frequency_value
     *
     * @mbg.generated
     */
    private String triggerFrequencyValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.description
     *
     * @mbg.generated
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.task
     *
     * @mbg.generated
     */
    private Long task;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.cal_status
     *
     * @mbg.generated
     */
    private Byte calStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.id
     *
     * @return the value of data_prediction_source.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.id
     *
     * @param id the value for data_prediction_source.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.tenant_id
     *
     * @return the value of data_prediction_source.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.tenant_id
     *
     * @param tenantId the value for data_prediction_source.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.prompt_type
     *
     * @return the value of data_prediction_source.prompt_type
     *
     * @mbg.generated
     */
    public Byte getPromptType() {
        return promptType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.prompt_type
     *
     * @param promptType the value for data_prediction_source.prompt_type
     *
     * @mbg.generated
     */
    public void setPromptType(Byte promptType) {
        this.promptType = promptType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.prediction_update_type
     *
     * @return the value of data_prediction_source.prediction_update_type
     *
     * @mbg.generated
     */
    public Boolean getPredictionUpdateType() {
        return predictionUpdateType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.prediction_update_type
     *
     * @param predictionUpdateType the value for data_prediction_source.prediction_update_type
     *
     * @mbg.generated
     */
    public void setPredictionUpdateType(Boolean predictionUpdateType) {
        this.predictionUpdateType = predictionUpdateType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.trigger_mod
     *
     * @return the value of data_prediction_source.trigger_mod
     *
     * @mbg.generated
     */
    public Byte getTriggerMod() {
        return triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.trigger_mod
     *
     * @param triggerMod the value for data_prediction_source.trigger_mod
     *
     * @mbg.generated
     */
    public void setTriggerMod(Byte triggerMod) {
        this.triggerMod = triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.trigger_frequency
     *
     * @return the value of data_prediction_source.trigger_frequency
     *
     * @mbg.generated
     */
    public Byte getTriggerFrequency() {
        return triggerFrequency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.trigger_frequency
     *
     * @param triggerFrequency the value for data_prediction_source.trigger_frequency
     *
     * @mbg.generated
     */
    public void setTriggerFrequency(Byte triggerFrequency) {
        this.triggerFrequency = triggerFrequency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.trigger_frequency_value
     *
     * @return the value of data_prediction_source.trigger_frequency_value
     *
     * @mbg.generated
     */
    public String getTriggerFrequencyValue() {
        return triggerFrequencyValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.trigger_frequency_value
     *
     * @param triggerFrequencyValue the value for data_prediction_source.trigger_frequency_value
     *
     * @mbg.generated
     */
    public void setTriggerFrequencyValue(String triggerFrequencyValue) {
        this.triggerFrequencyValue = triggerFrequencyValue == null ? null : triggerFrequencyValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.description
     *
     * @return the value of data_prediction_source.description
     *
     * @mbg.generated
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.description
     *
     * @param description the value for data_prediction_source.description
     *
     * @mbg.generated
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.task
     *
     * @return the value of data_prediction_source.task
     *
     * @mbg.generated
     */
    public Long getTask() {
        return task;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.task
     *
     * @param task the value for data_prediction_source.task
     *
     * @mbg.generated
     */
    public void setTask(Long task) {
        this.task = task;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.cal_status
     *
     * @return the value of data_prediction_source.cal_status
     *
     * @mbg.generated
     */
    public Byte getCalStatus() {
        return calStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.cal_status
     *
     * @param calStatus the value for data_prediction_source.cal_status
     *
     * @mbg.generated
     */
    public void setCalStatus(Byte calStatus) {
        this.calStatus = calStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.del
     *
     * @return the value of data_prediction_source.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.del
     *
     * @param del the value for data_prediction_source.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.creator
     *
     * @return the value of data_prediction_source.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.creator
     *
     * @param creator the value for data_prediction_source.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.modifier
     *
     * @return the value of data_prediction_source.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.modifier
     *
     * @param modifier the value for data_prediction_source.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.create_time
     *
     * @return the value of data_prediction_source.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.create_time
     *
     * @param createTime the value for data_prediction_source.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.update_time
     *
     * @return the value of data_prediction_source.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.update_time
     *
     * @param updateTime the value for data_prediction_source.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public Long getTaskInfoId() {
        return this.getTask();
    }

}