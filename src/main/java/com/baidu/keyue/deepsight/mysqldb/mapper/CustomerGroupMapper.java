package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroupCriteria;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CustomerGroupMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    long countByExample(CustomerGroupCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    int deleteByExample(CustomerGroupCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    int insert(CustomerGroup record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    int insertSelective(CustomerGroup record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    List<CustomerGroup> selectByExampleWithBLOBs(CustomerGroupCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    List<CustomerGroup> selectByExample(CustomerGroupCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    CustomerGroup selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") CustomerGroup record, @Param("example") CustomerGroupCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") CustomerGroup record, @Param("example") CustomerGroupCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") CustomerGroup record, @Param("example") CustomerGroupCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(CustomerGroup record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(CustomerGroup record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(CustomerGroup record);
}