package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionConfigCriteria;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DataPredictionConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_config
     *
     * @mbg.generated
     */
    long countByExample(DataPredictionConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_config
     *
     * @mbg.generated
     */
    int deleteByExample(DataPredictionConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_config
     *
     * @mbg.generated
     */
    int insert(DataPredictionConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_config
     *
     * @mbg.generated
     */
    int insertSelective(DataPredictionConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_config
     *
     * @mbg.generated
     */
    List<DataPredictionConfig> selectByExample(DataPredictionConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_config
     *
     * @mbg.generated
     */
    DataPredictionConfig selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") DataPredictionConfig record, @Param("example") DataPredictionConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_config
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") DataPredictionConfig record, @Param("example") DataPredictionConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(DataPredictionConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(DataPredictionConfig record);
}