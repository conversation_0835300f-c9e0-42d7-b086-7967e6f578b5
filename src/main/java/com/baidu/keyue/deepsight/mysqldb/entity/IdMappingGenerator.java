package com.baidu.keyue.deepsight.mysqldb.entity;

import org.apache.ibatis.annotations.Mapper;

import java.io.Serializable;
import java.util.Date;

@Mapper
public class IdMappingGenerator implements Serializable, TaskInfoObj {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.trigger_mod
     *
     * @mbg.generated
     */
    private Byte triggerMod;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.trigger_frequency
     *
     * @mbg.generated
     */
    private Byte triggerFrequency;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.trigger_frequency_value
     *
     * @mbg.generated
     */
    private String triggerFrequencyValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.cal_status
     *
     * @mbg.generated
     */
    private Byte calStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.last_cal_date
     *
     * @mbg.generated
     */
    private Date lastCalDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.task_id
     *
     * @mbg.generated
     */
    private Long taskId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_generator.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table id_mapping_generator
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.id
     *
     * @return the value of id_mapping_generator.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    @Override
    public Long getTaskInfoId() {
        return this.taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.id
     *
     * @param id the value for id_mapping_generator.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.trigger_mod
     *
     * @return the value of id_mapping_generator.trigger_mod
     *
     * @mbg.generated
     */
    public Byte getTriggerMod() {
        return triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.trigger_mod
     *
     * @param triggerMod the value for id_mapping_generator.trigger_mod
     *
     * @mbg.generated
     */
    public void setTriggerMod(Byte triggerMod) {
        this.triggerMod = triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.trigger_frequency
     *
     * @return the value of id_mapping_generator.trigger_frequency
     *
     * @mbg.generated
     */
    public Byte getTriggerFrequency() {
        return triggerFrequency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.trigger_frequency
     *
     * @param triggerFrequency the value for id_mapping_generator.trigger_frequency
     *
     * @mbg.generated
     */
    public void setTriggerFrequency(Byte triggerFrequency) {
        this.triggerFrequency = triggerFrequency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.trigger_frequency_value
     *
     * @return the value of id_mapping_generator.trigger_frequency_value
     *
     * @mbg.generated
     */
    public String getTriggerFrequencyValue() {
        return triggerFrequencyValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.trigger_frequency_value
     *
     * @param triggerFrequencyValue the value for id_mapping_generator.trigger_frequency_value
     *
     * @mbg.generated
     */
    public void setTriggerFrequencyValue(String triggerFrequencyValue) {
        this.triggerFrequencyValue = triggerFrequencyValue == null ? null : triggerFrequencyValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.cal_status
     *
     * @return the value of id_mapping_generator.cal_status
     *
     * @mbg.generated
     */
    public Byte getCalStatus() {
        return calStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.cal_status
     *
     * @param calStatus the value for id_mapping_generator.cal_status
     *
     * @mbg.generated
     */
    public void setCalStatus(Byte calStatus) {
        this.calStatus = calStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.del
     *
     * @return the value of id_mapping_generator.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.del
     *
     * @param del the value for id_mapping_generator.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.creator
     *
     * @return the value of id_mapping_generator.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.creator
     *
     * @param creator the value for id_mapping_generator.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.modifier
     *
     * @return the value of id_mapping_generator.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.modifier
     *
     * @param modifier the value for id_mapping_generator.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.create_time
     *
     * @return the value of id_mapping_generator.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.create_time
     *
     * @param createTime the value for id_mapping_generator.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.update_time
     *
     * @return the value of id_mapping_generator.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.update_time
     *
     * @param updateTime the value for id_mapping_generator.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.last_cal_date
     *
     * @return the value of id_mapping_generator.last_cal_date
     *
     * @mbg.generated
     */
    public Date getLastCalDate() {
        return lastCalDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.last_cal_date
     *
     * @param lastCalDate the value for id_mapping_generator.last_cal_date
     *
     * @mbg.generated
     */
    public void setLastCalDate(Date lastCalDate) {
        this.lastCalDate = lastCalDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.task_id
     *
     * @return the value of id_mapping_generator.task_id
     *
     * @mbg.generated
     */
    public Long getTaskId() {
        return taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.task_id
     *
     * @param taskId the value for id_mapping_generator.task_id
     *
     * @mbg.generated
     */
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_generator.tenant_id
     *
     * @return the value of id_mapping_generator.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_generator.tenant_id
     *
     * @param tenantId the value for id_mapping_generator.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }
}