package com.baidu.keyue.deepsight.mysqldb.aiob.mapper;

import com.baidu.keyue.deepsight.mysqldb.aiob.entity.DesSystemSecretKey;
import com.baidu.keyue.deepsight.mysqldb.aiob.entity.DesSystemSecretKeyCriteria;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface DesSystemSecretKeyMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_system_secret_key
     *
     * @mbg.generated
     */
    long countByExample(DesSystemSecretKeyCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_system_secret_key
     *
     * @mbg.generated
     */
    int deleteByExample(DesSystemSecretKeyCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_system_secret_key
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_system_secret_key
     *
     * @mbg.generated
     */
    int insert(DesSystemSecretKey record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_system_secret_key
     *
     * @mbg.generated
     */
    int insertSelective(DesSystemSecretKey record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_system_secret_key
     *
     * @mbg.generated
     */
    List<DesSystemSecretKey> selectByExample(DesSystemSecretKeyCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_system_secret_key
     *
     * @mbg.generated
     */
    DesSystemSecretKey selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_system_secret_key
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") DesSystemSecretKey record, @Param("example") DesSystemSecretKeyCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_system_secret_key
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") DesSystemSecretKey record, @Param("example") DesSystemSecretKeyCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_system_secret_key
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(DesSystemSecretKey record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_system_secret_key
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(DesSystemSecretKey record);
}