package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.TaskScheduler;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface TaskSchedulerMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    long countByExample(TaskSchedulerCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    int deleteByExample(TaskSchedulerCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    int insert(TaskSchedulerWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    int insertSelective(TaskSchedulerWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    List<TaskSchedulerWithBLOBs> selectByExampleWithBLOBs(TaskSchedulerCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    List<TaskScheduler> selectByExample(TaskSchedulerCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    TaskSchedulerWithBLOBs selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TaskSchedulerWithBLOBs record, @Param("example") TaskSchedulerCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") TaskSchedulerWithBLOBs record, @Param("example") TaskSchedulerCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TaskScheduler record, @Param("example") TaskSchedulerCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TaskSchedulerWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(TaskSchedulerWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TaskScheduler record);
    
    
    int updateSchedulerStatus(
            @Param("id") Long id,
            @Param("status") Byte status,
            @Param("updateTime") Date updateTime
    );

    List<TaskSchedulerWithBLOBs> queryLatestByTaskIds(@Param("taskIds") List<Long> taskIds);
}