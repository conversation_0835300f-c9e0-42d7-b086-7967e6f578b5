package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class TenantInfo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tenant_info.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tenant_info.tenantId
     *
     * @mbg.generated
     */
    private String tenantid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tenant_info.accountId
     *
     * @mbg.generated
     */
    private String accountid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tenant_info.userName
     *
     * @mbg.generated
     */
    private String username;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tenant_info.tenant_source
     *
     * @mbg.generated
     */
    private String tenantSource;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tenant_info.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tenant_info.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tenant_info.user_id
     *
     * @mbg.generated
     */
    private String userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column tenant_info.version
     *
     * @mbg.generated
     */
    private Integer version;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table tenant_info
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tenant_info.id
     *
     * @return the value of tenant_info.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tenant_info.id
     *
     * @param id the value for tenant_info.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tenant_info.tenantId
     *
     * @return the value of tenant_info.tenantId
     *
     * @mbg.generated
     */
    public String getTenantid() {
        return tenantid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tenant_info.tenantId
     *
     * @param tenantid the value for tenant_info.tenantId
     *
     * @mbg.generated
     */
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid == null ? null : tenantid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tenant_info.accountId
     *
     * @return the value of tenant_info.accountId
     *
     * @mbg.generated
     */
    public String getAccountid() {
        return accountid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tenant_info.accountId
     *
     * @param accountid the value for tenant_info.accountId
     *
     * @mbg.generated
     */
    public void setAccountid(String accountid) {
        this.accountid = accountid == null ? null : accountid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tenant_info.userName
     *
     * @return the value of tenant_info.userName
     *
     * @mbg.generated
     */
    public String getUsername() {
        return username;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tenant_info.userName
     *
     * @param username the value for tenant_info.userName
     *
     * @mbg.generated
     */
    public void setUsername(String username) {
        this.username = username == null ? null : username.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tenant_info.tenant_source
     *
     * @return the value of tenant_info.tenant_source
     *
     * @mbg.generated
     */
    public String getTenantSource() {
        return tenantSource;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tenant_info.tenant_source
     *
     * @param tenantSource the value for tenant_info.tenant_source
     *
     * @mbg.generated
     */
    public void setTenantSource(String tenantSource) {
        this.tenantSource = tenantSource == null ? null : tenantSource.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tenant_info.create_time
     *
     * @return the value of tenant_info.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tenant_info.create_time
     *
     * @param createTime the value for tenant_info.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tenant_info.update_time
     *
     * @return the value of tenant_info.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tenant_info.update_time
     *
     * @param updateTime the value for tenant_info.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tenant_info.user_id
     *
     * @return the value of tenant_info.user_id
     *
     * @mbg.generated
     */
    public String getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tenant_info.user_id
     *
     * @param userId the value for tenant_info.user_id
     *
     * @mbg.generated
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column tenant_info.version
     *
     * @return the value of tenant_info.version
     *
     * @mbg.generated
     */
    public Integer getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column tenant_info.version
     *
     * @param version the value for tenant_info.version
     *
     * @mbg.generated
     */
    public void setVersion(Integer version) {
        this.version = version;
    }
}