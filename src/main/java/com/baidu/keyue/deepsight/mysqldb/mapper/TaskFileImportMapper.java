package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImport;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportWithBLOBs;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TaskFileImportMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    long countByExample(TaskFileImportCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    int deleteByExample(TaskFileImportCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    int insert(TaskFileImportWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    int insertSelective(TaskFileImportWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    List<TaskFileImportWithBLOBs> selectByExampleWithBLOBs(TaskFileImportCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    List<TaskFileImport> selectByExample(TaskFileImportCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    TaskFileImportWithBLOBs selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TaskFileImportWithBLOBs record, @Param("example") TaskFileImportCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") TaskFileImportWithBLOBs record, @Param("example") TaskFileImportCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TaskFileImport record, @Param("example") TaskFileImportCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TaskFileImportWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(TaskFileImportWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TaskFileImport record);
}