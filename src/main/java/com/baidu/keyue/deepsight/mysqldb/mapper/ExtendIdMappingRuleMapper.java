package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 补充 IdMappingRuleMapper 实现不了的sql
 */
@Mapper
public interface ExtendIdMappingRuleMapper {

    /**
     * 批量插入
     *
     */
    long batchInsert(@Param("list")List<IdMappingRule> list);
}