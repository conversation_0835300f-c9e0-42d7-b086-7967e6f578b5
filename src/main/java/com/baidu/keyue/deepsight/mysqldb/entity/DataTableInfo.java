package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class DataTableInfo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.cn_name
     *
     * @mbg.generated
     */
    private String cnName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.en_name
     *
     * @mbg.generated
     */
    private String enName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.data_type
     *
     * @mbg.generated
     */
    private Integer dataType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.is_visable
     *
     * @mbg.generated
     */
    private Boolean isVisable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.is_preset
     *
     * @mbg.generated
     */
    private Byte isPreset;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.is_del
     *
     * @mbg.generated
     */
    private Byte isDel;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.table_name
     *
     * @mbg.generated
     */
    private String tableName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.tenantId
     *
     * @mbg.generated
     */
    private String tenantid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.db_type
     *
     * @mbg.generated
     */
    private String dbType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_info.description
     *
     * @mbg.generated
     */
    private String description;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.id
     *
     * @return the value of datatable_info.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.id
     *
     * @param id the value for datatable_info.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.cn_name
     *
     * @return the value of datatable_info.cn_name
     *
     * @mbg.generated
     */
    public String getCnName() {
        return cnName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.cn_name
     *
     * @param cnName the value for datatable_info.cn_name
     *
     * @mbg.generated
     */
    public void setCnName(String cnName) {
        this.cnName = cnName == null ? null : cnName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.en_name
     *
     * @return the value of datatable_info.en_name
     *
     * @mbg.generated
     */
    public String getEnName() {
        return enName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.en_name
     *
     * @param enName the value for datatable_info.en_name
     *
     * @mbg.generated
     */
    public void setEnName(String enName) {
        this.enName = enName == null ? null : enName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.data_type
     *
     * @return the value of datatable_info.data_type
     *
     * @mbg.generated
     */
    public Integer getDataType() {
        return dataType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.data_type
     *
     * @param dataType the value for datatable_info.data_type
     *
     * @mbg.generated
     */
    public void setDataType(Integer dataType) {
        this.dataType = dataType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.status
     *
     * @return the value of datatable_info.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.status
     *
     * @param status the value for datatable_info.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.is_visable
     *
     * @return the value of datatable_info.is_visable
     *
     * @mbg.generated
     */
    public Boolean getIsVisable() {
        return isVisable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.is_visable
     *
     * @param isVisable the value for datatable_info.is_visable
     *
     * @mbg.generated
     */
    public void setIsVisable(Boolean isVisable) {
        this.isVisable = isVisable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.is_preset
     *
     * @return the value of datatable_info.is_preset
     *
     * @mbg.generated
     */
    public Byte getIsPreset() {
        return isPreset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.is_preset
     *
     * @param isPreset the value for datatable_info.is_preset
     *
     * @mbg.generated
     */
    public void setIsPreset(Byte isPreset) {
        this.isPreset = isPreset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.is_del
     *
     * @return the value of datatable_info.is_del
     *
     * @mbg.generated
     */
    public Byte getIsDel() {
        return isDel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.is_del
     *
     * @param isDel the value for datatable_info.is_del
     *
     * @mbg.generated
     */
    public void setIsDel(Byte isDel) {
        this.isDel = isDel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.create_time
     *
     * @return the value of datatable_info.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.create_time
     *
     * @param createTime the value for datatable_info.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.update_time
     *
     * @return the value of datatable_info.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.update_time
     *
     * @param updateTime the value for datatable_info.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.table_name
     *
     * @return the value of datatable_info.table_name
     *
     * @mbg.generated
     */
    public String getTableName() {
        return tableName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.table_name
     *
     * @param tableName the value for datatable_info.table_name
     *
     * @mbg.generated
     */
    public void setTableName(String tableName) {
        this.tableName = tableName == null ? null : tableName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.tenantId
     *
     * @return the value of datatable_info.tenantId
     *
     * @mbg.generated
     */
    public String getTenantid() {
        return tenantid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.tenantId
     *
     * @param tenantid the value for datatable_info.tenantId
     *
     * @mbg.generated
     */
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid == null ? null : tenantid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.db_type
     *
     * @return the value of datatable_info.db_type
     *
     * @mbg.generated
     */
    public String getDbType() {
        return dbType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.db_type
     *
     * @param dbType the value for datatable_info.db_type
     *
     * @mbg.generated
     */
    public void setDbType(String dbType) {
        this.dbType = dbType == null ? null : dbType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_info.description
     *
     * @return the value of datatable_info.description
     *
     * @mbg.generated
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_info.description
     *
     * @param description the value for datatable_info.description
     *
     * @mbg.generated
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }
}