package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSource;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DataPredictionSourceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    long countByExample(DataPredictionSourceCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    int deleteByExample(DataPredictionSourceCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    int insert(DataPredictionSourceWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    int insertSelective(DataPredictionSourceWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    List<DataPredictionSourceWithBLOBs> selectByExampleWithBLOBs(DataPredictionSourceCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    List<DataPredictionSource> selectByExample(DataPredictionSourceCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    DataPredictionSourceWithBLOBs selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") DataPredictionSourceWithBLOBs record, @Param("example") DataPredictionSourceCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") DataPredictionSourceWithBLOBs record, @Param("example") DataPredictionSourceCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") DataPredictionSource record, @Param("example") DataPredictionSourceCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(DataPredictionSourceWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(DataPredictionSourceWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(DataPredictionSource record);
}