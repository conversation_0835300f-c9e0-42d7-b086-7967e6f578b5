package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class MemoryExtract implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.data_table_id
     *
     * @mbg.generated
     */
    private Long dataTableId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.dataset_name
     *
     * @mbg.generated
     */
    private String datasetName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.field_id
     *
     * @mbg.generated
     */
    private Long fieldId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.field_name
     *
     * @mbg.generated
     */
    private String fieldName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.prompt_type
     *
     * @mbg.generated
     */
    private Byte promptType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.trigger_frequency
     *
     * @mbg.generated
     */
    private Byte triggerFrequency;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.trigger_frequency_value
     *
     * @mbg.generated
     */
    private String triggerFrequencyValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.description
     *
     * @mbg.generated
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.status
     *
     * @mbg.generated
     */
    private Boolean status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.task
     *
     * @mbg.generated
     */
    private Long task;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.trigger_mod
     *
     * @mbg.generated
     */
    private Byte triggerMod;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.is_default
     *
     * @mbg.generated
     */
    private Boolean isDefault;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.cal_status
     *
     * @mbg.generated
     */
    private Byte calStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.field_name_en
     *
     * @mbg.generated
     */
    private String fieldNameEn;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.id
     *
     * @return the value of memory_extract.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.id
     *
     * @param id the value for memory_extract.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.tenant_id
     *
     * @return the value of memory_extract.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.tenant_id
     *
     * @param tenantId the value for memory_extract.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.data_table_id
     *
     * @return the value of memory_extract.data_table_id
     *
     * @mbg.generated
     */
    public Long getDataTableId() {
        return dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.data_table_id
     *
     * @param dataTableId the value for memory_extract.data_table_id
     *
     * @mbg.generated
     */
    public void setDataTableId(Long dataTableId) {
        this.dataTableId = dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.dataset_name
     *
     * @return the value of memory_extract.dataset_name
     *
     * @mbg.generated
     */
    public String getDatasetName() {
        return datasetName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.dataset_name
     *
     * @param datasetName the value for memory_extract.dataset_name
     *
     * @mbg.generated
     */
    public void setDatasetName(String datasetName) {
        this.datasetName = datasetName == null ? null : datasetName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.field_id
     *
     * @return the value of memory_extract.field_id
     *
     * @mbg.generated
     */
    public Long getFieldId() {
        return fieldId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.field_id
     *
     * @param fieldId the value for memory_extract.field_id
     *
     * @mbg.generated
     */
    public void setFieldId(Long fieldId) {
        this.fieldId = fieldId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.field_name
     *
     * @return the value of memory_extract.field_name
     *
     * @mbg.generated
     */
    public String getFieldName() {
        return fieldName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.field_name
     *
     * @param fieldName the value for memory_extract.field_name
     *
     * @mbg.generated
     */
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName == null ? null : fieldName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.prompt_type
     *
     * @return the value of memory_extract.prompt_type
     *
     * @mbg.generated
     */
    public Byte getPromptType() {
        return promptType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.prompt_type
     *
     * @param promptType the value for memory_extract.prompt_type
     *
     * @mbg.generated
     */
    public void setPromptType(Byte promptType) {
        this.promptType = promptType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.trigger_frequency
     *
     * @return the value of memory_extract.trigger_frequency
     *
     * @mbg.generated
     */
    public Byte getTriggerFrequency() {
        return triggerFrequency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.trigger_frequency
     *
     * @param triggerFrequency the value for memory_extract.trigger_frequency
     *
     * @mbg.generated
     */
    public void setTriggerFrequency(Byte triggerFrequency) {
        this.triggerFrequency = triggerFrequency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.trigger_frequency_value
     *
     * @return the value of memory_extract.trigger_frequency_value
     *
     * @mbg.generated
     */
    public String getTriggerFrequencyValue() {
        return triggerFrequencyValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.trigger_frequency_value
     *
     * @param triggerFrequencyValue the value for memory_extract.trigger_frequency_value
     *
     * @mbg.generated
     */
    public void setTriggerFrequencyValue(String triggerFrequencyValue) {
        this.triggerFrequencyValue = triggerFrequencyValue == null ? null : triggerFrequencyValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.description
     *
     * @return the value of memory_extract.description
     *
     * @mbg.generated
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.description
     *
     * @param description the value for memory_extract.description
     *
     * @mbg.generated
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.status
     *
     * @return the value of memory_extract.status
     *
     * @mbg.generated
     */
    public Boolean getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.status
     *
     * @param status the value for memory_extract.status
     *
     * @mbg.generated
     */
    public void setStatus(Boolean status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.task
     *
     * @return the value of memory_extract.task
     *
     * @mbg.generated
     */
    public Long getTask() {
        return task;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.task
     *
     * @param task the value for memory_extract.task
     *
     * @mbg.generated
     */
    public void setTask(Long task) {
        this.task = task;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.del
     *
     * @return the value of memory_extract.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.del
     *
     * @param del the value for memory_extract.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.creator
     *
     * @return the value of memory_extract.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.creator
     *
     * @param creator the value for memory_extract.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.modifier
     *
     * @return the value of memory_extract.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.modifier
     *
     * @param modifier the value for memory_extract.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.create_time
     *
     * @return the value of memory_extract.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.create_time
     *
     * @param createTime the value for memory_extract.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.update_time
     *
     * @return the value of memory_extract.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.update_time
     *
     * @param updateTime the value for memory_extract.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.trigger_mod
     *
     * @return the value of memory_extract.trigger_mod
     *
     * @mbg.generated
     */
    public Byte getTriggerMod() {
        return triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.trigger_mod
     *
     * @param triggerMod the value for memory_extract.trigger_mod
     *
     * @mbg.generated
     */
    public void setTriggerMod(Byte triggerMod) {
        this.triggerMod = triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.is_default
     *
     * @return the value of memory_extract.is_default
     *
     * @mbg.generated
     */
    public Boolean getIsDefault() {
        return isDefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.is_default
     *
     * @param isDefault the value for memory_extract.is_default
     *
     * @mbg.generated
     */
    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.cal_status
     *
     * @return the value of memory_extract.cal_status
     *
     * @mbg.generated
     */
    public Byte getCalStatus() {
        return calStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.cal_status
     *
     * @param calStatus the value for memory_extract.cal_status
     *
     * @mbg.generated
     */
    public void setCalStatus(Byte calStatus) {
        this.calStatus = calStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.field_name_en
     *
     * @return the value of memory_extract.field_name_en
     *
     * @mbg.generated
     */
    public String getFieldNameEn() {
        return fieldNameEn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.field_name_en
     *
     * @param fieldNameEn the value for memory_extract.field_name_en
     *
     * @mbg.generated
     */
    public void setFieldNameEn(String fieldNameEn) {
        this.fieldNameEn = fieldNameEn == null ? null : fieldNameEn.trim();
    }
}