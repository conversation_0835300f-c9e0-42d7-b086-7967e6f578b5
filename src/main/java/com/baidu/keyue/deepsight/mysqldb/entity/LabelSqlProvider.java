package com.baidu.keyue.deepsight.mysqldb.entity;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;


public class LabelSqlProvider {

    public String getLabelList(
            @Param("userId") String userId, @Param("labelName") String labelName, @Param("catalogId") Long catalogId,
            @Param("offset") Integer offset, @Param("limit") Integer limit) {
        SQL sql = new SQL();
        sql.SELECT_DISTINCT("*").FROM("label").WHERE("user_id = #{userId}");
        if (StringUtils.isNotBlank(labelName)) {
            sql.AND().WHERE("label_name LIKE CONCAT('%', #{catalogName}, '%')");
        }
        if (catalogId != null) {
            sql.AND().WHERE("catalog_id = #{catalogId}");
        }
        sql
                .AND().WHERE("del = 0")
                .LIMIT("#{limit}")
                .OFFSET("#{offset}")
                .ORDER_BY("id", "desc");
        return sql.toString();
    }

    public String countAllLabel(@Param("userId") String userId, @Param("labelName") String labelName, @Param("catalogId") Long catalogId) {
        SQL sql = new SQL();
        sql.SELECT("count(*)").FROM("label").WHERE("user_id = #{userId}");
        if (StringUtils.isNotBlank(labelName)) {
            sql.AND().WHERE("label_name LIKE CONCAT('%', #{catalogName}, '%')");
        }
        if (catalogId != null) {
            sql.AND().WHERE("catalog_id = #{catalogId}");
        }
        sql.AND().WHERE("del = 0");
        return sql.toString();
    }
}
