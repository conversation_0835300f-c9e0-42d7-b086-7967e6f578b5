package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.AccessToken;
import com.baidu.keyue.deepsight.mysqldb.entity.AccessTokenCriteria;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AccessTokenMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_access_token
     *
     * @mbg.generated
     */
    long countByExample(AccessTokenCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_access_token
     *
     * @mbg.generated
     */
    int deleteByExample(AccessTokenCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_access_token
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_access_token
     *
     * @mbg.generated
     */
    int insert(AccessToken record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_access_token
     *
     * @mbg.generated
     */
    int insertSelective(AccessToken record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_access_token
     *
     * @mbg.generated
     */
    List<AccessToken> selectByExample(AccessTokenCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_access_token
     *
     * @mbg.generated
     */
    AccessToken selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_access_token
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") AccessToken record, @Param("example") AccessTokenCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_access_token
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") AccessToken record, @Param("example") AccessTokenCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_access_token
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AccessToken record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_access_token
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AccessToken record);
}