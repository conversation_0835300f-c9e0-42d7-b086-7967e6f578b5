package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.SopUserConfigCriteria;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SopUserConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sop_user_config
     *
     * @mbg.generated
     */
    long countByExample(SopUserConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sop_user_config
     *
     * @mbg.generated
     */
    int deleteByExample(SopUserConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sop_user_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sop_user_config
     *
     * @mbg.generated
     */
    int insert(SopUserConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sop_user_config
     *
     * @mbg.generated
     */
    int insertSelective(SopUserConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sop_user_config
     *
     * @mbg.generated
     */
    List<SopUserConfig> selectByExample(SopUserConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sop_user_config
     *
     * @mbg.generated
     */
    SopUserConfig selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sop_user_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") SopUserConfig record, @Param("example") SopUserConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sop_user_config
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") SopUserConfig record, @Param("example") SopUserConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sop_user_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(SopUserConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table sop_user_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(SopUserConfig record);
}