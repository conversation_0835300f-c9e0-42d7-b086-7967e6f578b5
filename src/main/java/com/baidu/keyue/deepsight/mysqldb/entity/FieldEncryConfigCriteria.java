package com.baidu.keyue.deepsight.mysqldb.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class FieldEncryConfigCriteria {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    public FieldEncryConfigCriteria() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTableEnNameIsNull() {
            addCriterion("table_en_name is null");
            return (Criteria) this;
        }

        public Criteria andTableEnNameIsNotNull() {
            addCriterion("table_en_name is not null");
            return (Criteria) this;
        }

        public Criteria andTableEnNameEqualTo(String value) {
            addCriterion("table_en_name =", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameNotEqualTo(String value) {
            addCriterion("table_en_name <>", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameGreaterThan(String value) {
            addCriterion("table_en_name >", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameGreaterThanOrEqualTo(String value) {
            addCriterion("table_en_name >=", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameLessThan(String value) {
            addCriterion("table_en_name <", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameLessThanOrEqualTo(String value) {
            addCriterion("table_en_name <=", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameLike(String value) {
            addCriterion("table_en_name like", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameNotLike(String value) {
            addCriterion("table_en_name not like", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameIn(List<String> values) {
            addCriterion("table_en_name in", values, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameNotIn(List<String> values) {
            addCriterion("table_en_name not in", values, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameBetween(String value1, String value2) {
            addCriterion("table_en_name between", value1, value2, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameNotBetween(String value1, String value2) {
            addCriterion("table_en_name not between", value1, value2, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andEnFieldIsNull() {
            addCriterion("en_field is null");
            return (Criteria) this;
        }

        public Criteria andEnFieldIsNotNull() {
            addCriterion("en_field is not null");
            return (Criteria) this;
        }

        public Criteria andEnFieldEqualTo(String value) {
            addCriterion("en_field =", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldNotEqualTo(String value) {
            addCriterion("en_field <>", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldGreaterThan(String value) {
            addCriterion("en_field >", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldGreaterThanOrEqualTo(String value) {
            addCriterion("en_field >=", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldLessThan(String value) {
            addCriterion("en_field <", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldLessThanOrEqualTo(String value) {
            addCriterion("en_field <=", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldLike(String value) {
            addCriterion("en_field like", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldNotLike(String value) {
            addCriterion("en_field not like", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldIn(List<String> values) {
            addCriterion("en_field in", values, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldNotIn(List<String> values) {
            addCriterion("en_field not in", values, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldBetween(String value1, String value2) {
            addCriterion("en_field between", value1, value2, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldNotBetween(String value1, String value2) {
            addCriterion("en_field not between", value1, value2, "enField");
            return (Criteria) this;
        }

        public Criteria andSecretKeyIsNull() {
            addCriterion("secret_key is null");
            return (Criteria) this;
        }

        public Criteria andSecretKeyIsNotNull() {
            addCriterion("secret_key is not null");
            return (Criteria) this;
        }

        public Criteria andSecretKeyEqualTo(String value) {
            addCriterion("secret_key =", value, "secretKey");
            return (Criteria) this;
        }

        public Criteria andSecretKeyNotEqualTo(String value) {
            addCriterion("secret_key <>", value, "secretKey");
            return (Criteria) this;
        }

        public Criteria andSecretKeyGreaterThan(String value) {
            addCriterion("secret_key >", value, "secretKey");
            return (Criteria) this;
        }

        public Criteria andSecretKeyGreaterThanOrEqualTo(String value) {
            addCriterion("secret_key >=", value, "secretKey");
            return (Criteria) this;
        }

        public Criteria andSecretKeyLessThan(String value) {
            addCriterion("secret_key <", value, "secretKey");
            return (Criteria) this;
        }

        public Criteria andSecretKeyLessThanOrEqualTo(String value) {
            addCriterion("secret_key <=", value, "secretKey");
            return (Criteria) this;
        }

        public Criteria andSecretKeyLike(String value) {
            addCriterion("secret_key like", value, "secretKey");
            return (Criteria) this;
        }

        public Criteria andSecretKeyNotLike(String value) {
            addCriterion("secret_key not like", value, "secretKey");
            return (Criteria) this;
        }

        public Criteria andSecretKeyIn(List<String> values) {
            addCriterion("secret_key in", values, "secretKey");
            return (Criteria) this;
        }

        public Criteria andSecretKeyNotIn(List<String> values) {
            addCriterion("secret_key not in", values, "secretKey");
            return (Criteria) this;
        }

        public Criteria andSecretKeyBetween(String value1, String value2) {
            addCriterion("secret_key between", value1, value2, "secretKey");
            return (Criteria) this;
        }

        public Criteria andSecretKeyNotBetween(String value1, String value2) {
            addCriterion("secret_key not between", value1, value2, "secretKey");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andDataTableIdIsNull() {
            addCriterion("data_table_id is null");
            return (Criteria) this;
        }

        public Criteria andDataTableIdIsNotNull() {
            addCriterion("data_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andDataTableIdEqualTo(String value) {
            addCriterion("data_table_id =", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdNotEqualTo(String value) {
            addCriterion("data_table_id <>", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdGreaterThan(String value) {
            addCriterion("data_table_id >", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdGreaterThanOrEqualTo(String value) {
            addCriterion("data_table_id >=", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdLessThan(String value) {
            addCriterion("data_table_id <", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdLessThanOrEqualTo(String value) {
            addCriterion("data_table_id <=", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdLike(String value) {
            addCriterion("data_table_id like", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdNotLike(String value) {
            addCriterion("data_table_id not like", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdIn(List<String> values) {
            addCriterion("data_table_id in", values, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdNotIn(List<String> values) {
            addCriterion("data_table_id not in", values, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdBetween(String value1, String value2) {
            addCriterion("data_table_id between", value1, value2, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdNotBetween(String value1, String value2) {
            addCriterion("data_table_id not between", value1, value2, "dataTableId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table field_encry_config
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}