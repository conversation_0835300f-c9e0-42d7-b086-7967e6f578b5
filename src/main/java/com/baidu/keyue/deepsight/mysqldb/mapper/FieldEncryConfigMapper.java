package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.FieldEncryConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldEncryConfigCriteria;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface FieldEncryConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    long countByExample(FieldEncryConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    int deleteByExample(FieldEncryConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    int insert(FieldEncryConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    int insertSelective(FieldEncryConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    List<FieldEncryConfig> selectByExample(FieldEncryConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    FieldEncryConfig selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") FieldEncryConfig record, @Param("example") FieldEncryConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") FieldEncryConfig record, @Param("example") FieldEncryConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(FieldEncryConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(FieldEncryConfig record);
}