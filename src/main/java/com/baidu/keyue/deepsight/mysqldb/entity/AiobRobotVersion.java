package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table aiob_robot_version
 *
 * @mbg.generated do_not_delete_during_merge
 */
public class AiobRobotVersion implements Serializable {
    /**
     * Database Column Remarks:
     *   primary key
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_robot_version.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   租户 ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_robot_version.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     * Database Column Remarks:
     *   机器人ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_robot_version.robot_id
     *
     * @mbg.generated
     */
    private String robotId;

    /**
     * Database Column Remarks:
     *   机器人版本
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_robot_version.robot_version
     *
     * @mbg.generated
     */
    private String robotVersion;

    /**
     * Database Column Remarks:
     *   机器人版本名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_robot_version.robot_version_name
     *
     * @mbg.generated
     */
    private String robotVersionName;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_robot_version.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table aiob_robot_version
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_robot_version.id
     *
     * @return the value of aiob_robot_version.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_robot_version.id
     *
     * @param id the value for aiob_robot_version.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_robot_version.tenant_id
     *
     * @return the value of aiob_robot_version.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_robot_version.tenant_id
     *
     * @param tenantId the value for aiob_robot_version.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_robot_version.robot_id
     *
     * @return the value of aiob_robot_version.robot_id
     *
     * @mbg.generated
     */
    public String getRobotId() {
        return robotId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_robot_version.robot_id
     *
     * @param robotId the value for aiob_robot_version.robot_id
     *
     * @mbg.generated
     */
    public void setRobotId(String robotId) {
        this.robotId = robotId == null ? null : robotId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_robot_version.robot_version
     *
     * @return the value of aiob_robot_version.robot_version
     *
     * @mbg.generated
     */
    public String getRobotVersion() {
        return robotVersion;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_robot_version.robot_version
     *
     * @param robotVersion the value for aiob_robot_version.robot_version
     *
     * @mbg.generated
     */
    public void setRobotVersion(String robotVersion) {
        this.robotVersion = robotVersion == null ? null : robotVersion.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_robot_version.robot_version_name
     *
     * @return the value of aiob_robot_version.robot_version_name
     *
     * @mbg.generated
     */
    public String getRobotVersionName() {
        return robotVersionName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_robot_version.robot_version_name
     *
     * @param robotVersionName the value for aiob_robot_version.robot_version_name
     *
     * @mbg.generated
     */
    public void setRobotVersionName(String robotVersionName) {
        this.robotVersionName = robotVersionName == null ? null : robotVersionName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_robot_version.create_time
     *
     * @return the value of aiob_robot_version.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_robot_version.create_time
     *
     * @param createTime the value for aiob_robot_version.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}