package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfoCriteria;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface DataTableInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    long countByExample(DataTableInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    int deleteByExample(DataTableInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    int insert(DataTableInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    int insertSelective(DataTableInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    List<DataTableInfo> selectByExampleWithBLOBs(DataTableInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    List<DataTableInfo> selectByExample(DataTableInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    DataTableInfo selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") DataTableInfo record, @Param("example") DataTableInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") DataTableInfo record, @Param("example") DataTableInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") DataTableInfo record, @Param("example") DataTableInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(DataTableInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(DataTableInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(DataTableInfo record);
}