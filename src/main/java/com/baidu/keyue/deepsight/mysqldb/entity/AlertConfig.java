package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table alert_config
 *
 * @mbg.generated do_not_delete_during_merge
 */
public class AlertConfig implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column alert_config.id
     *
     * @mbg.generated
     */
    private Integer id;

    /**
     * Database Column Remarks:
     *   配置类型(task/caller/robot)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column alert_config.config_type
     *
     * @mbg.generated
     */
    private String configType;

    /**
     * Database Column Remarks:
     *   配置目标(task_id/caller_num/robot_id)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column alert_config.config_target
     *
     * @mbg.generated
     */
    private String configTarget;

    /**
     * Database Column Remarks:
     *   阈值接通率(%)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column alert_config.threshold_rate
     *
     * @mbg.generated
     */
    private BigDecimal thresholdRate;

    /**
     * Database Column Remarks:
     *   拨打次数
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column alert_config.dial_count
     *
     * @mbg.generated
     */
    private Integer dialCount;

    /**
     * Database Column Remarks:
     *   告警频率：1小时、12小时、24小时、72小时
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column alert_config.alert_freq
     *
     * @mbg.generated
     */
    private Integer alertFreq;

    /**
     * Database Column Remarks:
     *   是否激活
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column alert_config.is_active
     *
     * @mbg.generated
     */
    private Boolean isActive;

    /**
     * Database Column Remarks:
     *   更新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column alert_config.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   租户ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column alert_config.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     * Database Column Remarks:
     *   告警时间类型：24H(近24小时)/7D(近7天)/30D(近30天)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column alert_config.alert_time
     *
     * @mbg.generated
     */
    private String alertTime;

    /**
     * Database Column Remarks:
     *   下次检查告警时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column alert_config.next_check_time
     *
     * @mbg.generated
     */
    private Date nextCheckTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column alert_config.id
     *
     * @return the value of alert_config.id
     *
     * @mbg.generated
     */
    public Integer getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column alert_config.id
     *
     * @param id the value for alert_config.id
     *
     * @mbg.generated
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column alert_config.config_type
     *
     * @return the value of alert_config.config_type
     *
     * @mbg.generated
     */
    public String getConfigType() {
        return configType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column alert_config.config_type
     *
     * @param configType the value for alert_config.config_type
     *
     * @mbg.generated
     */
    public void setConfigType(String configType) {
        this.configType = configType == null ? null : configType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column alert_config.config_target
     *
     * @return the value of alert_config.config_target
     *
     * @mbg.generated
     */
    public String getConfigTarget() {
        return configTarget;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column alert_config.config_target
     *
     * @param configTarget the value for alert_config.config_target
     *
     * @mbg.generated
     */
    public void setConfigTarget(String configTarget) {
        this.configTarget = configTarget == null ? null : configTarget.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column alert_config.threshold_rate
     *
     * @return the value of alert_config.threshold_rate
     *
     * @mbg.generated
     */
    public BigDecimal getThresholdRate() {
        return thresholdRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column alert_config.threshold_rate
     *
     * @param thresholdRate the value for alert_config.threshold_rate
     *
     * @mbg.generated
     */
    public void setThresholdRate(BigDecimal thresholdRate) {
        this.thresholdRate = thresholdRate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column alert_config.dial_count
     *
     * @return the value of alert_config.dial_count
     *
     * @mbg.generated
     */
    public Integer getDialCount() {
        return dialCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column alert_config.dial_count
     *
     * @param dialCount the value for alert_config.dial_count
     *
     * @mbg.generated
     */
    public void setDialCount(Integer dialCount) {
        this.dialCount = dialCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column alert_config.alert_freq
     *
     * @return the value of alert_config.alert_freq
     *
     * @mbg.generated
     */
    public Integer getAlertFreq() {
        return alertFreq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column alert_config.alert_freq
     *
     * @param alertFreq the value for alert_config.alert_freq
     *
     * @mbg.generated
     */
    public void setAlertFreq(Integer alertFreq) {
        this.alertFreq = alertFreq;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column alert_config.is_active
     *
     * @return the value of alert_config.is_active
     *
     * @mbg.generated
     */
    public Boolean getIsActive() {
        return isActive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column alert_config.is_active
     *
     * @param isActive the value for alert_config.is_active
     *
     * @mbg.generated
     */
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column alert_config.update_time
     *
     * @return the value of alert_config.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column alert_config.update_time
     *
     * @param updateTime the value for alert_config.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column alert_config.tenant_id
     *
     * @return the value of alert_config.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column alert_config.tenant_id
     *
     * @param tenantId the value for alert_config.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column alert_config.alert_time
     *
     * @return the value of alert_config.alert_time
     *
     * @mbg.generated
     */
    public String getAlertTime() {
        return alertTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column alert_config.alert_time
     *
     * @param alertTime the value for alert_config.alert_time
     *
     * @mbg.generated
     */
    public void setAlertTime(String alertTime) {
        this.alertTime = alertTime == null ? null : alertTime.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column alert_config.next_check_time
     *
     * @return the value of alert_config.next_check_time
     *
     * @mbg.generated
     */
    public Date getNextCheckTime() {
        return nextCheckTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column alert_config.next_check_time
     *
     * @param nextCheckTime the value for alert_config.next_check_time
     *
     * @mbg.generated
     */
    public void setNextCheckTime(Date nextCheckTime) {
        this.nextCheckTime = nextCheckTime;
    }
}