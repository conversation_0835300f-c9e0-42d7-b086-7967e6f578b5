package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;

public class DataPredictionSourceWithBLOBs extends DataPredictionSource implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.data_source_list
     *
     * @mbg.generated
     */
    private String dataSourceList;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column data_prediction_source.prompt
     *
     * @mbg.generated
     */
    private String prompt;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table data_prediction_source
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.data_source_list
     *
     * @return the value of data_prediction_source.data_source_list
     *
     * @mbg.generated
     */
    public String getDataSourceList() {
        return dataSourceList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.data_source_list
     *
     * @param dataSourceList the value for data_prediction_source.data_source_list
     *
     * @mbg.generated
     */
    public void setDataSourceList(String dataSourceList) {
        this.dataSourceList = dataSourceList == null ? null : dataSourceList.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column data_prediction_source.prompt
     *
     * @return the value of data_prediction_source.prompt
     *
     * @mbg.generated
     */
    public String getPrompt() {
        return prompt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column data_prediction_source.prompt
     *
     * @param prompt the value for data_prediction_source.prompt
     *
     * @mbg.generated
     */
    public void setPrompt(String prompt) {
        this.prompt = prompt == null ? null : prompt.trim();
    }
}