package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;

public class MemoryExtractWithBLOBs extends MemoryExtract implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.prompt
     *
     * @mbg.generated
     */
    private String prompt;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column memory_extract.data_filter_rule
     *
     * @mbg.generated
     */
    private String dataFilterRule;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table memory_extract
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.prompt
     *
     * @return the value of memory_extract.prompt
     *
     * @mbg.generated
     */
    public String getPrompt() {
        return prompt;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.prompt
     *
     * @param prompt the value for memory_extract.prompt
     *
     * @mbg.generated
     */
    public void setPrompt(String prompt) {
        this.prompt = prompt == null ? null : prompt.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column memory_extract.data_filter_rule
     *
     * @return the value of memory_extract.data_filter_rule
     *
     * @mbg.generated
     */
    public String getDataFilterRule() {
        return dataFilterRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column memory_extract.data_filter_rule
     *
     * @param dataFilterRule the value for memory_extract.data_filter_rule
     *
     * @mbg.generated
     */
    public void setDataFilterRule(String dataFilterRule) {
        this.dataFilterRule = dataFilterRule == null ? null : dataFilterRule.trim();
    }
}