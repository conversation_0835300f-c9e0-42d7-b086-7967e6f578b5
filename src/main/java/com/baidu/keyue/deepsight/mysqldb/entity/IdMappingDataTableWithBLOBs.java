package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;

public class IdMappingDataTableWithBLOBs extends IdMappingDataTable implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_datatable.en_fields
     *
     * @mbg.generated
     */
    private String enFields;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_datatable.cn_fields
     *
     * @mbg.generated
     */
    private String cnFields;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_datatable.en_fields
     *
     * @return the value of id_mapping_datatable.en_fields
     *
     * @mbg.generated
     */
    public String getEnFields() {
        return enFields;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_datatable.en_fields
     *
     * @param enFields the value for id_mapping_datatable.en_fields
     *
     * @mbg.generated
     */
    public void setEnFields(String enFields) {
        this.enFields = enFields == null ? null : enFields.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_datatable.cn_fields
     *
     * @return the value of id_mapping_datatable.cn_fields
     *
     * @mbg.generated
     */
    public String getCnFields() {
        return cnFields;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_datatable.cn_fields
     *
     * @param cnFields the value for id_mapping_datatable.cn_fields
     *
     * @mbg.generated
     */
    public void setCnFields(String cnFields) {
        this.cnFields = cnFields == null ? null : cnFields.trim();
    }
}