package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelation;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelationCriteria;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IdMappingRelationMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    long countByExample(IdMappingRelationCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    int deleteByExample(IdMappingRelationCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    int insert(IdMappingRelation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    int insertSelective(IdMappingRelation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    List<IdMappingRelation> selectByExample(IdMappingRelationCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    IdMappingRelation selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") IdMappingRelation record, @Param("example") IdMappingRelationCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") IdMappingRelation record, @Param("example") IdMappingRelationCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(IdMappingRelation record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(IdMappingRelation record);
}