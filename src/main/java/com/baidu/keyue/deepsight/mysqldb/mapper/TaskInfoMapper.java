package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskInfoCriteria;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface TaskInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    long countByExample(TaskInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    int deleteByExample(TaskInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    int insert(TaskInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    int insertSelective(TaskInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    List<TaskInfo> selectByExampleWithBLOBs(TaskInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    List<TaskInfo> selectByExample(TaskInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    TaskInfo selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TaskInfo record, @Param("example") TaskInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") TaskInfo record, @Param("example") TaskInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TaskInfo record, @Param("example") TaskInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TaskInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(TaskInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table task_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TaskInfo record);

    
    int taskTriggerUpdate(
            @Param("id") Long id,
            @Param("triggerCron") String triggerCron,
            @Param("nextExecDate") Date nextExecDate,
            @Param("modifier") String modifier,
            @Param("updateTime") Date updateTime,
            @Param("triggerMod") Byte triggerMod
    );
    
    int updateTaskNextExecDate(@Param("id") Long id, @Param("nextExecDate") Date nextExecDate);
}