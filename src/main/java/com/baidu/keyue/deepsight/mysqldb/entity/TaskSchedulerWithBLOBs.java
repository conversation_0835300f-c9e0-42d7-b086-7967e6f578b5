package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;

public class TaskSchedulerWithBLOBs extends TaskScheduler implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_scheduler.body
     *
     * @mbg.generated
     */
    private String body;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_scheduler.message
     *
     * @mbg.generated
     */
    private String message;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table task_scheduler
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_scheduler.body
     *
     * @return the value of task_scheduler.body
     *
     * @mbg.generated
     */
    public String getBody() {
        return body;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_scheduler.body
     *
     * @param body the value for task_scheduler.body
     *
     * @mbg.generated
     */
    public void setBody(String body) {
        this.body = body == null ? null : body.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_scheduler.message
     *
     * @return the value of task_scheduler.message
     *
     * @mbg.generated
     */
    public String getMessage() {
        return message;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_scheduler.message
     *
     * @param message the value for task_scheduler.message
     *
     * @mbg.generated
     */
    public void setMessage(String message) {
        this.message = message == null ? null : message.trim();
    }
}