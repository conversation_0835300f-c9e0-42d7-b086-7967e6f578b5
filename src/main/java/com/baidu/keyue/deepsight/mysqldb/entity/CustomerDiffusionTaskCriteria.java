package com.baidu.keyue.deepsight.mysqldb.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CustomerDiffusionTaskCriteria {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    public CustomerDiffusionTaskCriteria() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTaskNameIsNull() {
            addCriterion("task_name is null");
            return (Criteria) this;
        }

        public Criteria andTaskNameIsNotNull() {
            addCriterion("task_name is not null");
            return (Criteria) this;
        }

        public Criteria andTaskNameEqualTo(String value) {
            addCriterion("task_name =", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotEqualTo(String value) {
            addCriterion("task_name <>", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameGreaterThan(String value) {
            addCriterion("task_name >", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameGreaterThanOrEqualTo(String value) {
            addCriterion("task_name >=", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameLessThan(String value) {
            addCriterion("task_name <", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameLessThanOrEqualTo(String value) {
            addCriterion("task_name <=", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameLike(String value) {
            addCriterion("task_name like", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotLike(String value) {
            addCriterion("task_name not like", value, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameIn(List<String> values) {
            addCriterion("task_name in", values, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotIn(List<String> values) {
            addCriterion("task_name not in", values, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameBetween(String value1, String value2) {
            addCriterion("task_name between", value1, value2, "taskName");
            return (Criteria) this;
        }

        public Criteria andTaskNameNotBetween(String value1, String value2) {
            addCriterion("task_name not between", value1, value2, "taskName");
            return (Criteria) this;
        }

        public Criteria andSeedGroupIsNull() {
            addCriterion("seed_group is null");
            return (Criteria) this;
        }

        public Criteria andSeedGroupIsNotNull() {
            addCriterion("seed_group is not null");
            return (Criteria) this;
        }

        public Criteria andSeedGroupEqualTo(Long value) {
            addCriterion("seed_group =", value, "seedGroup");
            return (Criteria) this;
        }

        public Criteria andSeedGroupNotEqualTo(Long value) {
            addCriterion("seed_group <>", value, "seedGroup");
            return (Criteria) this;
        }

        public Criteria andSeedGroupGreaterThan(Long value) {
            addCriterion("seed_group >", value, "seedGroup");
            return (Criteria) this;
        }

        public Criteria andSeedGroupGreaterThanOrEqualTo(Long value) {
            addCriterion("seed_group >=", value, "seedGroup");
            return (Criteria) this;
        }

        public Criteria andSeedGroupLessThan(Long value) {
            addCriterion("seed_group <", value, "seedGroup");
            return (Criteria) this;
        }

        public Criteria andSeedGroupLessThanOrEqualTo(Long value) {
            addCriterion("seed_group <=", value, "seedGroup");
            return (Criteria) this;
        }

        public Criteria andSeedGroupIn(List<Long> values) {
            addCriterion("seed_group in", values, "seedGroup");
            return (Criteria) this;
        }

        public Criteria andSeedGroupNotIn(List<Long> values) {
            addCriterion("seed_group not in", values, "seedGroup");
            return (Criteria) this;
        }

        public Criteria andSeedGroupBetween(Long value1, Long value2) {
            addCriterion("seed_group between", value1, value2, "seedGroup");
            return (Criteria) this;
        }

        public Criteria andSeedGroupNotBetween(Long value1, Long value2) {
            addCriterion("seed_group not between", value1, value2, "seedGroup");
            return (Criteria) this;
        }

        public Criteria andPredictGroupIsNull() {
            addCriterion("predict_group is null");
            return (Criteria) this;
        }

        public Criteria andPredictGroupIsNotNull() {
            addCriterion("predict_group is not null");
            return (Criteria) this;
        }

        public Criteria andPredictGroupEqualTo(String value) {
            addCriterion("predict_group =", value, "predictGroup");
            return (Criteria) this;
        }

        public Criteria andPredictGroupNotEqualTo(String value) {
            addCriterion("predict_group <>", value, "predictGroup");
            return (Criteria) this;
        }

        public Criteria andPredictGroupGreaterThan(String value) {
            addCriterion("predict_group >", value, "predictGroup");
            return (Criteria) this;
        }

        public Criteria andPredictGroupGreaterThanOrEqualTo(String value) {
            addCriterion("predict_group >=", value, "predictGroup");
            return (Criteria) this;
        }

        public Criteria andPredictGroupLessThan(String value) {
            addCriterion("predict_group <", value, "predictGroup");
            return (Criteria) this;
        }

        public Criteria andPredictGroupLessThanOrEqualTo(String value) {
            addCriterion("predict_group <=", value, "predictGroup");
            return (Criteria) this;
        }

        public Criteria andPredictGroupLike(String value) {
            addCriterion("predict_group like", value, "predictGroup");
            return (Criteria) this;
        }

        public Criteria andPredictGroupNotLike(String value) {
            addCriterion("predict_group not like", value, "predictGroup");
            return (Criteria) this;
        }

        public Criteria andPredictGroupIn(List<String> values) {
            addCriterion("predict_group in", values, "predictGroup");
            return (Criteria) this;
        }

        public Criteria andPredictGroupNotIn(List<String> values) {
            addCriterion("predict_group not in", values, "predictGroup");
            return (Criteria) this;
        }

        public Criteria andPredictGroupBetween(String value1, String value2) {
            addCriterion("predict_group between", value1, value2, "predictGroup");
            return (Criteria) this;
        }

        public Criteria andPredictGroupNotBetween(String value1, String value2) {
            addCriterion("predict_group not between", value1, value2, "predictGroup");
            return (Criteria) this;
        }

        public Criteria andFilterRuleIsNull() {
            addCriterion("filter_rule is null");
            return (Criteria) this;
        }

        public Criteria andFilterRuleIsNotNull() {
            addCriterion("filter_rule is not null");
            return (Criteria) this;
        }

        public Criteria andFilterRuleEqualTo(Byte value) {
            addCriterion("filter_rule =", value, "filterRule");
            return (Criteria) this;
        }

        public Criteria andFilterRuleNotEqualTo(Byte value) {
            addCriterion("filter_rule <>", value, "filterRule");
            return (Criteria) this;
        }

        public Criteria andFilterRuleGreaterThan(Byte value) {
            addCriterion("filter_rule >", value, "filterRule");
            return (Criteria) this;
        }

        public Criteria andFilterRuleGreaterThanOrEqualTo(Byte value) {
            addCriterion("filter_rule >=", value, "filterRule");
            return (Criteria) this;
        }

        public Criteria andFilterRuleLessThan(Byte value) {
            addCriterion("filter_rule <", value, "filterRule");
            return (Criteria) this;
        }

        public Criteria andFilterRuleLessThanOrEqualTo(Byte value) {
            addCriterion("filter_rule <=", value, "filterRule");
            return (Criteria) this;
        }

        public Criteria andFilterRuleIn(List<Byte> values) {
            addCriterion("filter_rule in", values, "filterRule");
            return (Criteria) this;
        }

        public Criteria andFilterRuleNotIn(List<Byte> values) {
            addCriterion("filter_rule not in", values, "filterRule");
            return (Criteria) this;
        }

        public Criteria andFilterRuleBetween(Byte value1, Byte value2) {
            addCriterion("filter_rule between", value1, value2, "filterRule");
            return (Criteria) this;
        }

        public Criteria andFilterRuleNotBetween(Byte value1, Byte value2) {
            addCriterion("filter_rule not between", value1, value2, "filterRule");
            return (Criteria) this;
        }

        public Criteria andFeatureSelectIsNull() {
            addCriterion("feature_select is null");
            return (Criteria) this;
        }

        public Criteria andFeatureSelectIsNotNull() {
            addCriterion("feature_select is not null");
            return (Criteria) this;
        }

        public Criteria andFeatureSelectEqualTo(Byte value) {
            addCriterion("feature_select =", value, "featureSelect");
            return (Criteria) this;
        }

        public Criteria andFeatureSelectNotEqualTo(Byte value) {
            addCriterion("feature_select <>", value, "featureSelect");
            return (Criteria) this;
        }

        public Criteria andFeatureSelectGreaterThan(Byte value) {
            addCriterion("feature_select >", value, "featureSelect");
            return (Criteria) this;
        }

        public Criteria andFeatureSelectGreaterThanOrEqualTo(Byte value) {
            addCriterion("feature_select >=", value, "featureSelect");
            return (Criteria) this;
        }

        public Criteria andFeatureSelectLessThan(Byte value) {
            addCriterion("feature_select <", value, "featureSelect");
            return (Criteria) this;
        }

        public Criteria andFeatureSelectLessThanOrEqualTo(Byte value) {
            addCriterion("feature_select <=", value, "featureSelect");
            return (Criteria) this;
        }

        public Criteria andFeatureSelectIn(List<Byte> values) {
            addCriterion("feature_select in", values, "featureSelect");
            return (Criteria) this;
        }

        public Criteria andFeatureSelectNotIn(List<Byte> values) {
            addCriterion("feature_select not in", values, "featureSelect");
            return (Criteria) this;
        }

        public Criteria andFeatureSelectBetween(Byte value1, Byte value2) {
            addCriterion("feature_select between", value1, value2, "featureSelect");
            return (Criteria) this;
        }

        public Criteria andFeatureSelectNotBetween(Byte value1, Byte value2) {
            addCriterion("feature_select not between", value1, value2, "featureSelect");
            return (Criteria) this;
        }

        public Criteria andThresholdIsNull() {
            addCriterion("threshold is null");
            return (Criteria) this;
        }

        public Criteria andThresholdIsNotNull() {
            addCriterion("threshold is not null");
            return (Criteria) this;
        }

        public Criteria andThresholdEqualTo(Float value) {
            addCriterion("threshold =", value, "threshold");
            return (Criteria) this;
        }

        public Criteria andThresholdNotEqualTo(Float value) {
            addCriterion("threshold <>", value, "threshold");
            return (Criteria) this;
        }

        public Criteria andThresholdGreaterThan(Float value) {
            addCriterion("threshold >", value, "threshold");
            return (Criteria) this;
        }

        public Criteria andThresholdGreaterThanOrEqualTo(Float value) {
            addCriterion("threshold >=", value, "threshold");
            return (Criteria) this;
        }

        public Criteria andThresholdLessThan(Float value) {
            addCriterion("threshold <", value, "threshold");
            return (Criteria) this;
        }

        public Criteria andThresholdLessThanOrEqualTo(Float value) {
            addCriterion("threshold <=", value, "threshold");
            return (Criteria) this;
        }

        public Criteria andThresholdIn(List<Float> values) {
            addCriterion("threshold in", values, "threshold");
            return (Criteria) this;
        }

        public Criteria andThresholdNotIn(List<Float> values) {
            addCriterion("threshold not in", values, "threshold");
            return (Criteria) this;
        }

        public Criteria andThresholdBetween(Float value1, Float value2) {
            addCriterion("threshold between", value1, value2, "threshold");
            return (Criteria) this;
        }

        public Criteria andThresholdNotBetween(Float value1, Float value2) {
            addCriterion("threshold not between", value1, value2, "threshold");
            return (Criteria) this;
        }

        public Criteria andJudgeCriteriaIsNull() {
            addCriterion("judge_criteria is null");
            return (Criteria) this;
        }

        public Criteria andJudgeCriteriaIsNotNull() {
            addCriterion("judge_criteria is not null");
            return (Criteria) this;
        }

        public Criteria andJudgeCriteriaEqualTo(Byte value) {
            addCriterion("judge_criteria =", value, "judgeCriteria");
            return (Criteria) this;
        }

        public Criteria andJudgeCriteriaNotEqualTo(Byte value) {
            addCriterion("judge_criteria <>", value, "judgeCriteria");
            return (Criteria) this;
        }

        public Criteria andJudgeCriteriaGreaterThan(Byte value) {
            addCriterion("judge_criteria >", value, "judgeCriteria");
            return (Criteria) this;
        }

        public Criteria andJudgeCriteriaGreaterThanOrEqualTo(Byte value) {
            addCriterion("judge_criteria >=", value, "judgeCriteria");
            return (Criteria) this;
        }

        public Criteria andJudgeCriteriaLessThan(Byte value) {
            addCriterion("judge_criteria <", value, "judgeCriteria");
            return (Criteria) this;
        }

        public Criteria andJudgeCriteriaLessThanOrEqualTo(Byte value) {
            addCriterion("judge_criteria <=", value, "judgeCriteria");
            return (Criteria) this;
        }

        public Criteria andJudgeCriteriaIn(List<Byte> values) {
            addCriterion("judge_criteria in", values, "judgeCriteria");
            return (Criteria) this;
        }

        public Criteria andJudgeCriteriaNotIn(List<Byte> values) {
            addCriterion("judge_criteria not in", values, "judgeCriteria");
            return (Criteria) this;
        }

        public Criteria andJudgeCriteriaBetween(Byte value1, Byte value2) {
            addCriterion("judge_criteria between", value1, value2, "judgeCriteria");
            return (Criteria) this;
        }

        public Criteria andJudgeCriteriaNotBetween(Byte value1, Byte value2) {
            addCriterion("judge_criteria not between", value1, value2, "judgeCriteria");
            return (Criteria) this;
        }

        public Criteria andSimilarityIsNull() {
            addCriterion("similarity is null");
            return (Criteria) this;
        }

        public Criteria andSimilarityIsNotNull() {
            addCriterion("similarity is not null");
            return (Criteria) this;
        }

        public Criteria andSimilarityEqualTo(Float value) {
            addCriterion("similarity =", value, "similarity");
            return (Criteria) this;
        }

        public Criteria andSimilarityNotEqualTo(Float value) {
            addCriterion("similarity <>", value, "similarity");
            return (Criteria) this;
        }

        public Criteria andSimilarityGreaterThan(Float value) {
            addCriterion("similarity >", value, "similarity");
            return (Criteria) this;
        }

        public Criteria andSimilarityGreaterThanOrEqualTo(Float value) {
            addCriterion("similarity >=", value, "similarity");
            return (Criteria) this;
        }

        public Criteria andSimilarityLessThan(Float value) {
            addCriterion("similarity <", value, "similarity");
            return (Criteria) this;
        }

        public Criteria andSimilarityLessThanOrEqualTo(Float value) {
            addCriterion("similarity <=", value, "similarity");
            return (Criteria) this;
        }

        public Criteria andSimilarityIn(List<Float> values) {
            addCriterion("similarity in", values, "similarity");
            return (Criteria) this;
        }

        public Criteria andSimilarityNotIn(List<Float> values) {
            addCriterion("similarity not in", values, "similarity");
            return (Criteria) this;
        }

        public Criteria andSimilarityBetween(Float value1, Float value2) {
            addCriterion("similarity between", value1, value2, "similarity");
            return (Criteria) this;
        }

        public Criteria andSimilarityNotBetween(Float value1, Float value2) {
            addCriterion("similarity not between", value1, value2, "similarity");
            return (Criteria) this;
        }

        public Criteria andRankingIsNull() {
            addCriterion("ranking is null");
            return (Criteria) this;
        }

        public Criteria andRankingIsNotNull() {
            addCriterion("ranking is not null");
            return (Criteria) this;
        }

        public Criteria andRankingEqualTo(Integer value) {
            addCriterion("ranking =", value, "ranking");
            return (Criteria) this;
        }

        public Criteria andRankingNotEqualTo(Integer value) {
            addCriterion("ranking <>", value, "ranking");
            return (Criteria) this;
        }

        public Criteria andRankingGreaterThan(Integer value) {
            addCriterion("ranking >", value, "ranking");
            return (Criteria) this;
        }

        public Criteria andRankingGreaterThanOrEqualTo(Integer value) {
            addCriterion("ranking >=", value, "ranking");
            return (Criteria) this;
        }

        public Criteria andRankingLessThan(Integer value) {
            addCriterion("ranking <", value, "ranking");
            return (Criteria) this;
        }

        public Criteria andRankingLessThanOrEqualTo(Integer value) {
            addCriterion("ranking <=", value, "ranking");
            return (Criteria) this;
        }

        public Criteria andRankingIn(List<Integer> values) {
            addCriterion("ranking in", values, "ranking");
            return (Criteria) this;
        }

        public Criteria andRankingNotIn(List<Integer> values) {
            addCriterion("ranking not in", values, "ranking");
            return (Criteria) this;
        }

        public Criteria andRankingBetween(Integer value1, Integer value2) {
            addCriterion("ranking between", value1, value2, "ranking");
            return (Criteria) this;
        }

        public Criteria andRankingNotBetween(Integer value1, Integer value2) {
            addCriterion("ranking not between", value1, value2, "ranking");
            return (Criteria) this;
        }

        public Criteria andTriggerModIsNull() {
            addCriterion("trigger_mod is null");
            return (Criteria) this;
        }

        public Criteria andTriggerModIsNotNull() {
            addCriterion("trigger_mod is not null");
            return (Criteria) this;
        }

        public Criteria andTriggerModEqualTo(Byte value) {
            addCriterion("trigger_mod =", value, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModNotEqualTo(Byte value) {
            addCriterion("trigger_mod <>", value, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModGreaterThan(Byte value) {
            addCriterion("trigger_mod >", value, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModGreaterThanOrEqualTo(Byte value) {
            addCriterion("trigger_mod >=", value, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModLessThan(Byte value) {
            addCriterion("trigger_mod <", value, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModLessThanOrEqualTo(Byte value) {
            addCriterion("trigger_mod <=", value, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModIn(List<Byte> values) {
            addCriterion("trigger_mod in", values, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModNotIn(List<Byte> values) {
            addCriterion("trigger_mod not in", values, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModBetween(Byte value1, Byte value2) {
            addCriterion("trigger_mod between", value1, value2, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerModNotBetween(Byte value1, Byte value2) {
            addCriterion("trigger_mod not between", value1, value2, "triggerMod");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyIsNull() {
            addCriterion("trigger_frequency is null");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyIsNotNull() {
            addCriterion("trigger_frequency is not null");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyEqualTo(Byte value) {
            addCriterion("trigger_frequency =", value, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyNotEqualTo(Byte value) {
            addCriterion("trigger_frequency <>", value, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyGreaterThan(Byte value) {
            addCriterion("trigger_frequency >", value, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyGreaterThanOrEqualTo(Byte value) {
            addCriterion("trigger_frequency >=", value, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyLessThan(Byte value) {
            addCriterion("trigger_frequency <", value, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyLessThanOrEqualTo(Byte value) {
            addCriterion("trigger_frequency <=", value, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyIn(List<Byte> values) {
            addCriterion("trigger_frequency in", values, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyNotIn(List<Byte> values) {
            addCriterion("trigger_frequency not in", values, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyBetween(Byte value1, Byte value2) {
            addCriterion("trigger_frequency between", value1, value2, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyNotBetween(Byte value1, Byte value2) {
            addCriterion("trigger_frequency not between", value1, value2, "triggerFrequency");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueIsNull() {
            addCriterion("trigger_frequency_value is null");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueIsNotNull() {
            addCriterion("trigger_frequency_value is not null");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueEqualTo(String value) {
            addCriterion("trigger_frequency_value =", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueNotEqualTo(String value) {
            addCriterion("trigger_frequency_value <>", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueGreaterThan(String value) {
            addCriterion("trigger_frequency_value >", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueGreaterThanOrEqualTo(String value) {
            addCriterion("trigger_frequency_value >=", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueLessThan(String value) {
            addCriterion("trigger_frequency_value <", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueLessThanOrEqualTo(String value) {
            addCriterion("trigger_frequency_value <=", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueLike(String value) {
            addCriterion("trigger_frequency_value like", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueNotLike(String value) {
            addCriterion("trigger_frequency_value not like", value, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueIn(List<String> values) {
            addCriterion("trigger_frequency_value in", values, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueNotIn(List<String> values) {
            addCriterion("trigger_frequency_value not in", values, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueBetween(String value1, String value2) {
            addCriterion("trigger_frequency_value between", value1, value2, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andTriggerFrequencyValueNotBetween(String value1, String value2) {
            addCriterion("trigger_frequency_value not between", value1, value2, "triggerFrequencyValue");
            return (Criteria) this;
        }

        public Criteria andCalStatusIsNull() {
            addCriterion("cal_status is null");
            return (Criteria) this;
        }

        public Criteria andCalStatusIsNotNull() {
            addCriterion("cal_status is not null");
            return (Criteria) this;
        }

        public Criteria andCalStatusEqualTo(Byte value) {
            addCriterion("cal_status =", value, "calStatus");
            return (Criteria) this;
        }

        public Criteria andCalStatusNotEqualTo(Byte value) {
            addCriterion("cal_status <>", value, "calStatus");
            return (Criteria) this;
        }

        public Criteria andCalStatusGreaterThan(Byte value) {
            addCriterion("cal_status >", value, "calStatus");
            return (Criteria) this;
        }

        public Criteria andCalStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("cal_status >=", value, "calStatus");
            return (Criteria) this;
        }

        public Criteria andCalStatusLessThan(Byte value) {
            addCriterion("cal_status <", value, "calStatus");
            return (Criteria) this;
        }

        public Criteria andCalStatusLessThanOrEqualTo(Byte value) {
            addCriterion("cal_status <=", value, "calStatus");
            return (Criteria) this;
        }

        public Criteria andCalStatusIn(List<Byte> values) {
            addCriterion("cal_status in", values, "calStatus");
            return (Criteria) this;
        }

        public Criteria andCalStatusNotIn(List<Byte> values) {
            addCriterion("cal_status not in", values, "calStatus");
            return (Criteria) this;
        }

        public Criteria andCalStatusBetween(Byte value1, Byte value2) {
            addCriterion("cal_status between", value1, value2, "calStatus");
            return (Criteria) this;
        }

        public Criteria andCalStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("cal_status not between", value1, value2, "calStatus");
            return (Criteria) this;
        }

        public Criteria andLastCalDateIsNull() {
            addCriterion("last_cal_date is null");
            return (Criteria) this;
        }

        public Criteria andLastCalDateIsNotNull() {
            addCriterion("last_cal_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastCalDateEqualTo(Date value) {
            addCriterion("last_cal_date =", value, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateNotEqualTo(Date value) {
            addCriterion("last_cal_date <>", value, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateGreaterThan(Date value) {
            addCriterion("last_cal_date >", value, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateGreaterThanOrEqualTo(Date value) {
            addCriterion("last_cal_date >=", value, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateLessThan(Date value) {
            addCriterion("last_cal_date <", value, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateLessThanOrEqualTo(Date value) {
            addCriterion("last_cal_date <=", value, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateIn(List<Date> values) {
            addCriterion("last_cal_date in", values, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateNotIn(List<Date> values) {
            addCriterion("last_cal_date not in", values, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateBetween(Date value1, Date value2) {
            addCriterion("last_cal_date between", value1, value2, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andLastCalDateNotBetween(Date value1, Date value2) {
            addCriterion("last_cal_date not between", value1, value2, "lastCalDate");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andGroupPackageIsNull() {
            addCriterion("group_package is null");
            return (Criteria) this;
        }

        public Criteria andGroupPackageIsNotNull() {
            addCriterion("group_package is not null");
            return (Criteria) this;
        }

        public Criteria andGroupPackageEqualTo(Boolean value) {
            addCriterion("group_package =", value, "groupPackage");
            return (Criteria) this;
        }

        public Criteria andGroupPackageNotEqualTo(Boolean value) {
            addCriterion("group_package <>", value, "groupPackage");
            return (Criteria) this;
        }

        public Criteria andGroupPackageGreaterThan(Boolean value) {
            addCriterion("group_package >", value, "groupPackage");
            return (Criteria) this;
        }

        public Criteria andGroupPackageGreaterThanOrEqualTo(Boolean value) {
            addCriterion("group_package >=", value, "groupPackage");
            return (Criteria) this;
        }

        public Criteria andGroupPackageLessThan(Boolean value) {
            addCriterion("group_package <", value, "groupPackage");
            return (Criteria) this;
        }

        public Criteria andGroupPackageLessThanOrEqualTo(Boolean value) {
            addCriterion("group_package <=", value, "groupPackage");
            return (Criteria) this;
        }

        public Criteria andGroupPackageIn(List<Boolean> values) {
            addCriterion("group_package in", values, "groupPackage");
            return (Criteria) this;
        }

        public Criteria andGroupPackageNotIn(List<Boolean> values) {
            addCriterion("group_package not in", values, "groupPackage");
            return (Criteria) this;
        }

        public Criteria andGroupPackageBetween(Boolean value1, Boolean value2) {
            addCriterion("group_package between", value1, value2, "groupPackage");
            return (Criteria) this;
        }

        public Criteria andGroupPackageNotBetween(Boolean value1, Boolean value2) {
            addCriterion("group_package not between", value1, value2, "groupPackage");
            return (Criteria) this;
        }

        public Criteria andDelIsNull() {
            addCriterion("del is null");
            return (Criteria) this;
        }

        public Criteria andDelIsNotNull() {
            addCriterion("del is not null");
            return (Criteria) this;
        }

        public Criteria andDelEqualTo(Boolean value) {
            addCriterion("del =", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelNotEqualTo(Boolean value) {
            addCriterion("del <>", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelGreaterThan(Boolean value) {
            addCriterion("del >", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelGreaterThanOrEqualTo(Boolean value) {
            addCriterion("del >=", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelLessThan(Boolean value) {
            addCriterion("del <", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelLessThanOrEqualTo(Boolean value) {
            addCriterion("del <=", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelIn(List<Boolean> values) {
            addCriterion("del in", values, "del");
            return (Criteria) this;
        }

        public Criteria andDelNotIn(List<Boolean> values) {
            addCriterion("del not in", values, "del");
            return (Criteria) this;
        }

        public Criteria andDelBetween(Boolean value1, Boolean value2) {
            addCriterion("del between", value1, value2, "del");
            return (Criteria) this;
        }

        public Criteria andDelNotBetween(Boolean value1, Boolean value2) {
            addCriterion("del not between", value1, value2, "del");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModifierIsNull() {
            addCriterion("modifier is null");
            return (Criteria) this;
        }

        public Criteria andModifierIsNotNull() {
            addCriterion("modifier is not null");
            return (Criteria) this;
        }

        public Criteria andModifierEqualTo(String value) {
            addCriterion("modifier =", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierNotEqualTo(String value) {
            addCriterion("modifier <>", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierGreaterThan(String value) {
            addCriterion("modifier >", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierGreaterThanOrEqualTo(String value) {
            addCriterion("modifier >=", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierLessThan(String value) {
            addCriterion("modifier <", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierLessThanOrEqualTo(String value) {
            addCriterion("modifier <=", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierLike(String value) {
            addCriterion("modifier like", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierNotLike(String value) {
            addCriterion("modifier not like", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierIn(List<String> values) {
            addCriterion("modifier in", values, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierNotIn(List<String> values) {
            addCriterion("modifier not in", values, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierBetween(String value1, String value2) {
            addCriterion("modifier between", value1, value2, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierNotBetween(String value1, String value2) {
            addCriterion("modifier not between", value1, value2, "modifier");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupIdIsNull() {
            addCriterion("customer_group_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupIdIsNotNull() {
            addCriterion("customer_group_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupIdEqualTo(Long value) {
            addCriterion("customer_group_id =", value, "customerGroupId");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupIdNotEqualTo(Long value) {
            addCriterion("customer_group_id <>", value, "customerGroupId");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupIdGreaterThan(Long value) {
            addCriterion("customer_group_id >", value, "customerGroupId");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupIdGreaterThanOrEqualTo(Long value) {
            addCriterion("customer_group_id >=", value, "customerGroupId");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupIdLessThan(Long value) {
            addCriterion("customer_group_id <", value, "customerGroupId");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupIdLessThanOrEqualTo(Long value) {
            addCriterion("customer_group_id <=", value, "customerGroupId");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupIdIn(List<Long> values) {
            addCriterion("customer_group_id in", values, "customerGroupId");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupIdNotIn(List<Long> values) {
            addCriterion("customer_group_id not in", values, "customerGroupId");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupIdBetween(Long value1, Long value2) {
            addCriterion("customer_group_id between", value1, value2, "customerGroupId");
            return (Criteria) this;
        }

        public Criteria andCustomerGroupIdNotBetween(Long value1, Long value2) {
            addCriterion("customer_group_id not between", value1, value2, "customerGroupId");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNull() {
            addCriterion("creator_name is null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIsNotNull() {
            addCriterion("creator_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorNameEqualTo(String value) {
            addCriterion("creator_name =", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotEqualTo(String value) {
            addCriterion("creator_name <>", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThan(String value) {
            addCriterion("creator_name >", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameGreaterThanOrEqualTo(String value) {
            addCriterion("creator_name >=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThan(String value) {
            addCriterion("creator_name <", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLessThanOrEqualTo(String value) {
            addCriterion("creator_name <=", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameLike(String value) {
            addCriterion("creator_name like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotLike(String value) {
            addCriterion("creator_name not like", value, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameIn(List<String> values) {
            addCriterion("creator_name in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotIn(List<String> values) {
            addCriterion("creator_name not in", values, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameBetween(String value1, String value2) {
            addCriterion("creator_name between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andCreatorNameNotBetween(String value1, String value2) {
            addCriterion("creator_name not between", value1, value2, "creatorName");
            return (Criteria) this;
        }

        public Criteria andModifierNameIsNull() {
            addCriterion("modifier_name is null");
            return (Criteria) this;
        }

        public Criteria andModifierNameIsNotNull() {
            addCriterion("modifier_name is not null");
            return (Criteria) this;
        }

        public Criteria andModifierNameEqualTo(String value) {
            addCriterion("modifier_name =", value, "modifierName");
            return (Criteria) this;
        }

        public Criteria andModifierNameNotEqualTo(String value) {
            addCriterion("modifier_name <>", value, "modifierName");
            return (Criteria) this;
        }

        public Criteria andModifierNameGreaterThan(String value) {
            addCriterion("modifier_name >", value, "modifierName");
            return (Criteria) this;
        }

        public Criteria andModifierNameGreaterThanOrEqualTo(String value) {
            addCriterion("modifier_name >=", value, "modifierName");
            return (Criteria) this;
        }

        public Criteria andModifierNameLessThan(String value) {
            addCriterion("modifier_name <", value, "modifierName");
            return (Criteria) this;
        }

        public Criteria andModifierNameLessThanOrEqualTo(String value) {
            addCriterion("modifier_name <=", value, "modifierName");
            return (Criteria) this;
        }

        public Criteria andModifierNameLike(String value) {
            addCriterion("modifier_name like", value, "modifierName");
            return (Criteria) this;
        }

        public Criteria andModifierNameNotLike(String value) {
            addCriterion("modifier_name not like", value, "modifierName");
            return (Criteria) this;
        }

        public Criteria andModifierNameIn(List<String> values) {
            addCriterion("modifier_name in", values, "modifierName");
            return (Criteria) this;
        }

        public Criteria andModifierNameNotIn(List<String> values) {
            addCriterion("modifier_name not in", values, "modifierName");
            return (Criteria) this;
        }

        public Criteria andModifierNameBetween(String value1, String value2) {
            addCriterion("modifier_name between", value1, value2, "modifierName");
            return (Criteria) this;
        }

        public Criteria andModifierNameNotBetween(String value1, String value2) {
            addCriterion("modifier_name not between", value1, value2, "modifierName");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table customer_diffusion_task
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}