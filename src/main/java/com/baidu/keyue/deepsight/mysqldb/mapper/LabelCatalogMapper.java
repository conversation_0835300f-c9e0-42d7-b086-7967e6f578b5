package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalog;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCatalogCriteria;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface LabelCatalogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_catalog
     *
     * @mbg.generated
     */
    long countByExample(LabelCatalogCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_catalog
     *
     * @mbg.generated
     */
    int deleteByExample(LabelCatalogCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_catalog
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_catalog
     *
     * @mbg.generated
     */
    int insert(LabelCatalog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_catalog
     *
     * @mbg.generated
     */
    int insertSelective(LabelCatalog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_catalog
     *
     * @mbg.generated
     */
    List<LabelCatalog> selectByExample(LabelCatalogCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_catalog
     *
     * @mbg.generated
     */
    LabelCatalog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_catalog
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") LabelCatalog record, @Param("example") LabelCatalogCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_catalog
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") LabelCatalog record, @Param("example") LabelCatalogCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_catalog
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(LabelCatalog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_catalog
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(LabelCatalog record);

    /**
     * 批量更新使用 sql 维护，mybatis 底层是 for 遍历更新
     * @param ids
     * @return
     */
    int catalogMoveDown(@Param("ids") List<Long> ids,
                        @Param("updateTime") Date updateTime,
                        @Param("modifier") String modifier);
}