package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfigCriteria;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AlertConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    long countByExample(AlertConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    int deleteByExample(AlertConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    int insert(AlertConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    int insertSelective(AlertConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    List<AlertConfig> selectByExample(AlertConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    AlertConfig selectByPrimaryKey(Integer id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") AlertConfig record, @Param("example") AlertConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") AlertConfig record, @Param("example") AlertConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(AlertConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(AlertConfig record);
}