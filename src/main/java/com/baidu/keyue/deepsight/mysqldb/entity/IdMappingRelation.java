package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class IdMappingRelation implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_relation.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_relation.preset
     *
     * @mbg.generated
     */
    private Boolean preset;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_relation.data_table_id
     *
     * @mbg.generated
     */
    private Long dataTableId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_relation.en_fields
     *
     * @mbg.generated
     */
    private String enFields;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_relation.cn_fields
     *
     * @mbg.generated
     */
    private String cnFields;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_relation.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_relation.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_relation.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_relation.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_relation.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_relation.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_relation.id
     *
     * @return the value of id_mapping_relation.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_relation.id
     *
     * @param id the value for id_mapping_relation.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_relation.preset
     *
     * @return the value of id_mapping_relation.preset
     *
     * @mbg.generated
     */
    public Boolean getPreset() {
        return preset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_relation.preset
     *
     * @param preset the value for id_mapping_relation.preset
     *
     * @mbg.generated
     */
    public void setPreset(Boolean preset) {
        this.preset = preset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_relation.data_table_id
     *
     * @return the value of id_mapping_relation.data_table_id
     *
     * @mbg.generated
     */
    public Long getDataTableId() {
        return dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_relation.data_table_id
     *
     * @param dataTableId the value for id_mapping_relation.data_table_id
     *
     * @mbg.generated
     */
    public void setDataTableId(Long dataTableId) {
        this.dataTableId = dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_relation.en_fields
     *
     * @return the value of id_mapping_relation.en_fields
     *
     * @mbg.generated
     */
    public String getEnFields() {
        return enFields;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_relation.en_fields
     *
     * @param enFields the value for id_mapping_relation.en_fields
     *
     * @mbg.generated
     */
    public void setEnFields(String enFields) {
        this.enFields = enFields == null ? null : enFields.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_relation.cn_fields
     *
     * @return the value of id_mapping_relation.cn_fields
     *
     * @mbg.generated
     */
    public String getCnFields() {
        return cnFields;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_relation.cn_fields
     *
     * @param cnFields the value for id_mapping_relation.cn_fields
     *
     * @mbg.generated
     */
    public void setCnFields(String cnFields) {
        this.cnFields = cnFields == null ? null : cnFields.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_relation.del
     *
     * @return the value of id_mapping_relation.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_relation.del
     *
     * @param del the value for id_mapping_relation.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_relation.creator
     *
     * @return the value of id_mapping_relation.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_relation.creator
     *
     * @param creator the value for id_mapping_relation.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_relation.modifier
     *
     * @return the value of id_mapping_relation.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_relation.modifier
     *
     * @param modifier the value for id_mapping_relation.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_relation.create_time
     *
     * @return the value of id_mapping_relation.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_relation.create_time
     *
     * @param createTime the value for id_mapping_relation.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_relation.update_time
     *
     * @return the value of id_mapping_relation.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_relation.update_time
     *
     * @param updateTime the value for id_mapping_relation.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_relation.tenant_id
     *
     * @return the value of id_mapping_relation.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_relation.tenant_id
     *
     * @param tenantId the value for id_mapping_relation.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }
}