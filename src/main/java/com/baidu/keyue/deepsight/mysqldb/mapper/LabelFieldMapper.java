package com.baidu.keyue.deepsight.mysqldb.mapper;

import java.util.List;

import com.baidu.keyue.deepsight.mysqldb.entity.LabelField;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelFieldCriteria;
import org.apache.ibatis.annotations.Param;

public interface LabelFieldMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_field
     *
     * @mbg.generated
     */
    long countByExample(LabelFieldCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_field
     *
     * @mbg.generated
     */
    int deleteByExample(LabelFieldCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_field
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_field
     *
     * @mbg.generated
     */
    int insert(LabelField record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_field
     *
     * @mbg.generated
     */
    int insertSelective(LabelField record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_field
     *
     * @mbg.generated
     */
    List<LabelField> selectByExample(LabelFieldCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_field
     *
     * @mbg.generated
     */
    LabelField selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_field
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") LabelField record, @Param("example") LabelFieldCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_field
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") LabelField record, @Param("example") LabelFieldCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_field
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(LabelField record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label_field
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(LabelField record);
}