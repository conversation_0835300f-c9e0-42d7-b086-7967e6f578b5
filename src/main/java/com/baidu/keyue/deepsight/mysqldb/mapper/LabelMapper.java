package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.Label;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelCriteria;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface LabelMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    long countByExample(LabelCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    int deleteByExample(LabelCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    int insert(LabelWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    int insertSelective(LabelWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    List<LabelWithBLOBs> selectByExampleWithBLOBs(LabelCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    List<Label> selectByExample(LabelCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    LabelWithBLOBs selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") LabelWithBLOBs record, @Param("example") LabelCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") LabelWithBLOBs record, @Param("example") LabelCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") Label record, @Param("example") LabelCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(LabelWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(LabelWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table label
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(Label record);

    int labelUpdate(
            @Param("id") Long id,
            @Param("catalogId") Long catalogId,
            @Param("labelName") String labelName,
            @Param("labelCalStatus") Byte labelCalStatus,
            @Param("labelValueUpdateMod") Byte labelValueUpdateMod,
            @Param("labelValueSaveMod") Byte labelValueSaveMod,
            @Param("triggerMod") Byte triggerMod,
            @Param("triggerFrequency") Byte triggerFrequency,
            @Param("triggerFrequencyValue") String triggerFrequencyValue,
            @Param("labelRule") String labelRule,
            @Param("modifier") String modifier,
            @Param("recalculate") Boolean recalculate,
            @Param("updateTime") Date updateTime
    );

    int updateLabelCalTaskResult(
            @Param("id") Long id,
            @Param("labelCalStatus") Byte labelCalStatus,
            @Param("distribution") String distribution,
            @Param("lastCalDate") Date lastCalDate,
            @Param("recalculate") Boolean recalculate
    );

    int updateLabelCalTaskStatus(
            @Param("id") Long id,
            @Param("labelCalStatus") Byte labelCalStatus, @Param("distribution") String distribution
    );
}