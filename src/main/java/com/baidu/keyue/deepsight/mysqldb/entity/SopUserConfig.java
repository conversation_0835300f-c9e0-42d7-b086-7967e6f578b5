package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table sop_user_config
 *
 * @mbg.generated do_not_delete_during_merge
 */
public class SopUserConfig implements Serializable {
    /**
     * Database Column Remarks:
     *   primary key
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   租户 ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     * Database Column Remarks:
     *   外呼任务ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.task_id
     *
     * @mbg.generated
     */
    private String taskId;

    /**
     * Database Column Remarks:
     *   版本
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.version
     *
     * @mbg.generated
     */
    private String version;

    /**
     * Database Column Remarks:
     *   核心指标:0:到达人数,1:到达次数
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.core_metric
     *
     * @mbg.generated
     */
    private Byte coreMetric;

    /**
     * Database Column Remarks:
     *   辅助指标:0:到达人数,1:到达次数
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.assist_metric
     *
     * @mbg.generated
     */
    private Byte assistMetric;

    /**
     * Database Column Remarks:
     *   挂断提醒阈值
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.warning_threshold
     *
     * @mbg.generated
     */
    private Integer warningThreshold;

    /**
     * Database Column Remarks:
     *   删除标识,0:未删除,1:已删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     * Database Column Remarks:
     *   创建者
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     * Database Column Remarks:
     *   修改者
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   更新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column sop_user_config.status
     *
     * @mbg.generated
     */
    private Byte status;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table sop_user_config
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.id
     *
     * @return the value of sop_user_config.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.id
     *
     * @param id the value for sop_user_config.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.tenant_id
     *
     * @return the value of sop_user_config.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.tenant_id
     *
     * @param tenantId the value for sop_user_config.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.task_id
     *
     * @return the value of sop_user_config.task_id
     *
     * @mbg.generated
     */
    public String getTaskId() {
        return taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.task_id
     *
     * @param taskId the value for sop_user_config.task_id
     *
     * @mbg.generated
     */
    public void setTaskId(String taskId) {
        this.taskId = taskId == null ? null : taskId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.version
     *
     * @return the value of sop_user_config.version
     *
     * @mbg.generated
     */
    public String getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.version
     *
     * @param version the value for sop_user_config.version
     *
     * @mbg.generated
     */
    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.core_metric
     *
     * @return the value of sop_user_config.core_metric
     *
     * @mbg.generated
     */
    public Byte getCoreMetric() {
        return coreMetric;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.core_metric
     *
     * @param coreMetric the value for sop_user_config.core_metric
     *
     * @mbg.generated
     */
    public void setCoreMetric(Byte coreMetric) {
        this.coreMetric = coreMetric;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.assist_metric
     *
     * @return the value of sop_user_config.assist_metric
     *
     * @mbg.generated
     */
    public Byte getAssistMetric() {
        return assistMetric;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.assist_metric
     *
     * @param assistMetric the value for sop_user_config.assist_metric
     *
     * @mbg.generated
     */
    public void setAssistMetric(Byte assistMetric) {
        this.assistMetric = assistMetric;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.warning_threshold
     *
     * @return the value of sop_user_config.warning_threshold
     *
     * @mbg.generated
     */
    public Integer getWarningThreshold() {
        return warningThreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.warning_threshold
     *
     * @param warningThreshold the value for sop_user_config.warning_threshold
     *
     * @mbg.generated
     */
    public void setWarningThreshold(Integer warningThreshold) {
        this.warningThreshold = warningThreshold;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.del
     *
     * @return the value of sop_user_config.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.del
     *
     * @param del the value for sop_user_config.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.creator
     *
     * @return the value of sop_user_config.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.creator
     *
     * @param creator the value for sop_user_config.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.modifier
     *
     * @return the value of sop_user_config.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.modifier
     *
     * @param modifier the value for sop_user_config.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.create_time
     *
     * @return the value of sop_user_config.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.create_time
     *
     * @param createTime the value for sop_user_config.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.update_time
     *
     * @return the value of sop_user_config.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.update_time
     *
     * @param updateTime the value for sop_user_config.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column sop_user_config.status
     *
     * @return the value of sop_user_config.status
     *
     * @mbg.generated
     */
    public Byte getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column sop_user_config.status
     *
     * @param status the value for sop_user_config.status
     *
     * @mbg.generated
     */
    public void setStatus(Byte status) {
        this.status = status;
    }
}