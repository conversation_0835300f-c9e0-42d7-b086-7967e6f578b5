package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfoCriteria;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TableFieldMetaInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    long countByExample(TableFieldMetaInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    int deleteByExample(TableFieldMetaInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    int insert(TableFieldMetaInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    int insertSelective(TableFieldMetaInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    List<TableFieldMetaInfo> selectByExampleWithBLOBs(TableFieldMetaInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    List<TableFieldMetaInfo> selectByExample(TableFieldMetaInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    TableFieldMetaInfo selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") TableFieldMetaInfo record, @Param("example") TableFieldMetaInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") TableFieldMetaInfo record, @Param("example") TableFieldMetaInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") TableFieldMetaInfo record, @Param("example") TableFieldMetaInfoCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TableFieldMetaInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(TableFieldMetaInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TableFieldMetaInfo record);
}