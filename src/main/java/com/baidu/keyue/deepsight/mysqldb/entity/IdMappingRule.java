package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class IdMappingRule implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.en_field
     *
     * @mbg.generated
     */
    private String enField;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.cn_field
     *
     * @mbg.generated
     */
    private String cnField;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.description
     *
     * @mbg.generated
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.field_type
     *
     * @mbg.generated
     */
    private Byte fieldType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.preset
     *
     * @mbg.generated
     */
    private Boolean preset;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.merge_policy
     *
     * @mbg.generated
     */
    private Byte mergePolicy;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.priority
     *
     * @mbg.generated
     */
    private Integer priority;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_rule.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table id_mapping_rule
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.id
     *
     * @return the value of id_mapping_rule.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.id
     *
     * @param id the value for id_mapping_rule.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.en_field
     *
     * @return the value of id_mapping_rule.en_field
     *
     * @mbg.generated
     */
    public String getEnField() {
        return enField;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.en_field
     *
     * @param enField the value for id_mapping_rule.en_field
     *
     * @mbg.generated
     */
    public void setEnField(String enField) {
        this.enField = enField == null ? null : enField.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.cn_field
     *
     * @return the value of id_mapping_rule.cn_field
     *
     * @mbg.generated
     */
    public String getCnField() {
        return cnField;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.cn_field
     *
     * @param cnField the value for id_mapping_rule.cn_field
     *
     * @mbg.generated
     */
    public void setCnField(String cnField) {
        this.cnField = cnField == null ? null : cnField.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.description
     *
     * @return the value of id_mapping_rule.description
     *
     * @mbg.generated
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.description
     *
     * @param description the value for id_mapping_rule.description
     *
     * @mbg.generated
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.field_type
     *
     * @return the value of id_mapping_rule.field_type
     *
     * @mbg.generated
     */
    public Byte getFieldType() {
        return fieldType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.field_type
     *
     * @param fieldType the value for id_mapping_rule.field_type
     *
     * @mbg.generated
     */
    public void setFieldType(Byte fieldType) {
        this.fieldType = fieldType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.preset
     *
     * @return the value of id_mapping_rule.preset
     *
     * @mbg.generated
     */
    public Boolean getPreset() {
        return preset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.preset
     *
     * @param preset the value for id_mapping_rule.preset
     *
     * @mbg.generated
     */
    public void setPreset(Boolean preset) {
        this.preset = preset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.merge_policy
     *
     * @return the value of id_mapping_rule.merge_policy
     *
     * @mbg.generated
     */
    public Byte getMergePolicy() {
        return mergePolicy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.merge_policy
     *
     * @param mergePolicy the value for id_mapping_rule.merge_policy
     *
     * @mbg.generated
     */
    public void setMergePolicy(Byte mergePolicy) {
        this.mergePolicy = mergePolicy;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.priority
     *
     * @return the value of id_mapping_rule.priority
     *
     * @mbg.generated
     */
    public Integer getPriority() {
        return priority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.priority
     *
     * @param priority the value for id_mapping_rule.priority
     *
     * @mbg.generated
     */
    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.tenant_id
     *
     * @return the value of id_mapping_rule.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.tenant_id
     *
     * @param tenantId the value for id_mapping_rule.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.creator
     *
     * @return the value of id_mapping_rule.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.creator
     *
     * @param creator the value for id_mapping_rule.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.modifier
     *
     * @return the value of id_mapping_rule.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.modifier
     *
     * @param modifier the value for id_mapping_rule.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.create_time
     *
     * @return the value of id_mapping_rule.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.create_time
     *
     * @param createTime the value for id_mapping_rule.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_rule.update_time
     *
     * @return the value of id_mapping_rule.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_rule.update_time
     *
     * @param updateTime the value for id_mapping_rule.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}