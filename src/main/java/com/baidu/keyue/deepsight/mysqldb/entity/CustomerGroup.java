package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class CustomerGroup implements Serializable, TaskInfoObj {
    /**
     * Database Column Remarks:
     *   客群id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   用户 ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.user_id
     *
     * @mbg.generated
     */
    private String userId;

    /**
     * Database Column Remarks:
     *   客群名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.customer_group_name
     *
     * @mbg.generated
     */
    private String customerGroupName;

    /**
     * Database Column Remarks:
     *   客群描述
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.customer_group_description
     *
     * @mbg.generated
     */
    private String customerGroupDescription;

    /**
     * Database Column Remarks:
     *   更新取值逻辑:0:每次重新计算,1:合并历史值
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.customer_group_value_update_mod
     *
     * @mbg.generated
     */
    private Byte customerGroupValueUpdateMod;

    /**
     * Database Column Remarks:
     *   更新触发类型:0:定时触发,1:手动触发
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.trigger_mod
     *
     * @mbg.generated
     */
    private Byte triggerMod;

    /**
     * Database Column Remarks:
     *   执行频率:0:每天,1:每周,2:每月
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.trigger_frequency
     *
     * @mbg.generated
     */
    private Byte triggerFrequency;

    /**
     * Database Column Remarks:
     *   执行频率json
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.trigger_frequency_value
     *
     * @mbg.generated
     */
    private String triggerFrequencyValue;

    /**
     * Database Column Remarks:
     *   计算状态: 0:待计算,1:计算中,2:计算成功,3:计算失败,4:计算取消
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.cal_status
     *
     * @mbg.generated
     */
    private Byte calStatus;

    /**
     * Database Column Remarks:
     *   删除标识,0:未删除,1:已删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     * Database Column Remarks:
     *   创建者
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     * Database Column Remarks:
     *   修改者
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   更新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   上一次执行时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.last_cal_date
     *
     * @mbg.generated
     */
    private Date lastCalDate;

    /**
     * Database Column Remarks:
     *   任务ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.task
     *
     * @mbg.generated
     */
    private Long task;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     * Database Column Remarks:
     *   0: 非预置 1：预置
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.preset
     *
     * @mbg.generated
     */
    private Boolean preset;

    /**
     * Database Column Remarks:
     *   分群方式：0:规则圈选 1:文件导入 2:模型预测
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.grouping_type
     *
     * @mbg.generated
     */
    private Byte groupingType;

    /**
     * Database Column Remarks:
     *   是否可配置标记：0：禁用 1：启用
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.config_tag
     *
     * @mbg.generated
     */
    private Boolean configTag;

    /**
     * Database Column Remarks:
     *   客群值规则json
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column customer_group.customer_group_rule
     *
     * @mbg.generated
     */
    private String customerGroupRule;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table customer_group
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.id
     *
     * @return the value of customer_group.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.id
     *
     * @param id the value for customer_group.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.user_id
     *
     * @return the value of customer_group.user_id
     *
     * @mbg.generated
     */
    public String getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.user_id
     *
     * @param userId the value for customer_group.user_id
     *
     * @mbg.generated
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.customer_group_name
     *
     * @return the value of customer_group.customer_group_name
     *
     * @mbg.generated
     */
    public String getCustomerGroupName() {
        return customerGroupName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.customer_group_name
     *
     * @param customerGroupName the value for customer_group.customer_group_name
     *
     * @mbg.generated
     */
    public void setCustomerGroupName(String customerGroupName) {
        this.customerGroupName = customerGroupName == null ? null : customerGroupName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.customer_group_description
     *
     * @return the value of customer_group.customer_group_description
     *
     * @mbg.generated
     */
    public String getCustomerGroupDescription() {
        return customerGroupDescription;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.customer_group_description
     *
     * @param customerGroupDescription the value for customer_group.customer_group_description
     *
     * @mbg.generated
     */
    public void setCustomerGroupDescription(String customerGroupDescription) {
        this.customerGroupDescription = customerGroupDescription == null ? null : customerGroupDescription.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.customer_group_value_update_mod
     *
     * @return the value of customer_group.customer_group_value_update_mod
     *
     * @mbg.generated
     */
    public Byte getCustomerGroupValueUpdateMod() {
        return customerGroupValueUpdateMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.customer_group_value_update_mod
     *
     * @param customerGroupValueUpdateMod the value for customer_group.customer_group_value_update_mod
     *
     * @mbg.generated
     */
    public void setCustomerGroupValueUpdateMod(Byte customerGroupValueUpdateMod) {
        this.customerGroupValueUpdateMod = customerGroupValueUpdateMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.trigger_mod
     *
     * @return the value of customer_group.trigger_mod
     *
     * @mbg.generated
     */
    public Byte getTriggerMod() {
        return triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.trigger_mod
     *
     * @param triggerMod the value for customer_group.trigger_mod
     *
     * @mbg.generated
     */
    public void setTriggerMod(Byte triggerMod) {
        this.triggerMod = triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.trigger_frequency
     *
     * @return the value of customer_group.trigger_frequency
     *
     * @mbg.generated
     */
    public Byte getTriggerFrequency() {
        return triggerFrequency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.trigger_frequency
     *
     * @param triggerFrequency the value for customer_group.trigger_frequency
     *
     * @mbg.generated
     */
    public void setTriggerFrequency(Byte triggerFrequency) {
        this.triggerFrequency = triggerFrequency;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.trigger_frequency_value
     *
     * @return the value of customer_group.trigger_frequency_value
     *
     * @mbg.generated
     */
    public String getTriggerFrequencyValue() {
        return triggerFrequencyValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.trigger_frequency_value
     *
     * @param triggerFrequencyValue the value for customer_group.trigger_frequency_value
     *
     * @mbg.generated
     */
    public void setTriggerFrequencyValue(String triggerFrequencyValue) {
        this.triggerFrequencyValue = triggerFrequencyValue == null ? null : triggerFrequencyValue.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.cal_status
     *
     * @return the value of customer_group.cal_status
     *
     * @mbg.generated
     */
    public Byte getCalStatus() {
        return calStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.cal_status
     *
     * @param calStatus the value for customer_group.cal_status
     *
     * @mbg.generated
     */
    public void setCalStatus(Byte calStatus) {
        this.calStatus = calStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.del
     *
     * @return the value of customer_group.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.del
     *
     * @param del the value for customer_group.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.creator
     *
     * @return the value of customer_group.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.creator
     *
     * @param creator the value for customer_group.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.modifier
     *
     * @return the value of customer_group.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.modifier
     *
     * @param modifier the value for customer_group.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.create_time
     *
     * @return the value of customer_group.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.create_time
     *
     * @param createTime the value for customer_group.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.update_time
     *
     * @return the value of customer_group.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.update_time
     *
     * @param updateTime the value for customer_group.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.last_cal_date
     *
     * @return the value of customer_group.last_cal_date
     *
     * @mbg.generated
     */
    public Date getLastCalDate() {
        return lastCalDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.last_cal_date
     *
     * @param lastCalDate the value for customer_group.last_cal_date
     *
     * @mbg.generated
     */
    public void setLastCalDate(Date lastCalDate) {
        this.lastCalDate = lastCalDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.task
     *
     * @return the value of customer_group.task
     *
     * @mbg.generated
     */
    public Long getTask() {
        return task;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.task
     *
     * @param task the value for customer_group.task
     *
     * @mbg.generated
     */
    public void setTask(Long task) {
        this.task = task;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.tenant_id
     *
     * @return the value of customer_group.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.tenant_id
     *
     * @param tenantId the value for customer_group.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.preset
     *
     * @return the value of customer_group.preset
     *
     * @mbg.generated
     */
    public Boolean getPreset() {
        return preset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.preset
     *
     * @param preset the value for customer_group.preset
     *
     * @mbg.generated
     */
    public void setPreset(Boolean preset) {
        this.preset = preset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.grouping_type
     *
     * @return the value of customer_group.grouping_type
     *
     * @mbg.generated
     */
    public Byte getGroupingType() {
        return groupingType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.grouping_type
     *
     * @param groupingType the value for customer_group.grouping_type
     *
     * @mbg.generated
     */
    public void setGroupingType(Byte groupingType) {
        this.groupingType = groupingType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.config_tag
     *
     * @return the value of customer_group.config_tag
     *
     * @mbg.generated
     */
    public Boolean getConfigTag() {
        return configTag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.config_tag
     *
     * @param configTag the value for customer_group.config_tag
     *
     * @mbg.generated
     */
    public void setConfigTag(Boolean configTag) {
        this.configTag = configTag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column customer_group.customer_group_rule
     *
     * @return the value of customer_group.customer_group_rule
     *
     * @mbg.generated
     */
    public String getCustomerGroupRule() {
        return customerGroupRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column customer_group.customer_group_rule
     *
     * @param customerGroupRule the value for customer_group.customer_group_rule
     *
     * @mbg.generated
     */
    public void setCustomerGroupRule(String customerGroupRule) {
        this.customerGroupRule = customerGroupRule == null ? null : customerGroupRule.trim();
    }

    @Override
    public Long getTaskInfoId() {
        return this.getTask();
    }
}