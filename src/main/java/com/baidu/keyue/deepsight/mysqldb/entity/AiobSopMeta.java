package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table aiob_sop_meta
 *
 * @mbg.generated do_not_delete_during_merge
 */
public class AiobSopMeta implements Serializable {
    /**
     * Database Column Remarks:
     *   primary key
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   租户 ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     * Database Column Remarks:
     *   外呼任务ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.task_id
     *
     * @mbg.generated
     */
    private String taskId;

    /**
     * Database Column Remarks:
     *   版本
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.version
     *
     * @mbg.generated
     */
    private String version;

    /**
     * Database Column Remarks:
     *   步骤 id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.step_id
     *
     * @mbg.generated
     */
    private String stepId;

    /**
     * Database Column Remarks:
     *   步骤名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.step_name
     *
     * @mbg.generated
     */
    private String stepName;

    /**
     * Database Column Remarks:
     *   节点 id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.node_id
     *
     * @mbg.generated
     */
    private String nodeId;

    /**
     * Database Column Remarks:
     *   节点名称
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.node_name
     *
     * @mbg.generated
     */
    private String nodeName;

    /**
     * Database Column Remarks:
     *   人工确认标识,0:未确认,1:已确认
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.manual_check
     *
     * @mbg.generated
     */
    private Boolean manualCheck;

    /**
     * Database Column Remarks:
     *   删除标识,0:未删除,1:已删除
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     * Database Column Remarks:
     *   创建者
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     * Database Column Remarks:
     *   修改者
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   更新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   对话规则
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column aiob_sop_meta.task_rule
     *
     * @mbg.generated
     */
    private String taskRule;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table aiob_sop_meta
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.id
     *
     * @return the value of aiob_sop_meta.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.id
     *
     * @param id the value for aiob_sop_meta.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.tenant_id
     *
     * @return the value of aiob_sop_meta.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.tenant_id
     *
     * @param tenantId the value for aiob_sop_meta.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.task_id
     *
     * @return the value of aiob_sop_meta.task_id
     *
     * @mbg.generated
     */
    public String getTaskId() {
        return taskId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.task_id
     *
     * @param taskId the value for aiob_sop_meta.task_id
     *
     * @mbg.generated
     */
    public void setTaskId(String taskId) {
        this.taskId = taskId == null ? null : taskId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.version
     *
     * @return the value of aiob_sop_meta.version
     *
     * @mbg.generated
     */
    public String getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.version
     *
     * @param version the value for aiob_sop_meta.version
     *
     * @mbg.generated
     */
    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.step_id
     *
     * @return the value of aiob_sop_meta.step_id
     *
     * @mbg.generated
     */
    public String getStepId() {
        return stepId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.step_id
     *
     * @param stepId the value for aiob_sop_meta.step_id
     *
     * @mbg.generated
     */
    public void setStepId(String stepId) {
        this.stepId = stepId == null ? null : stepId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.step_name
     *
     * @return the value of aiob_sop_meta.step_name
     *
     * @mbg.generated
     */
    public String getStepName() {
        return stepName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.step_name
     *
     * @param stepName the value for aiob_sop_meta.step_name
     *
     * @mbg.generated
     */
    public void setStepName(String stepName) {
        this.stepName = stepName == null ? null : stepName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.node_id
     *
     * @return the value of aiob_sop_meta.node_id
     *
     * @mbg.generated
     */
    public String getNodeId() {
        return nodeId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.node_id
     *
     * @param nodeId the value for aiob_sop_meta.node_id
     *
     * @mbg.generated
     */
    public void setNodeId(String nodeId) {
        this.nodeId = nodeId == null ? null : nodeId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.node_name
     *
     * @return the value of aiob_sop_meta.node_name
     *
     * @mbg.generated
     */
    public String getNodeName() {
        return nodeName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.node_name
     *
     * @param nodeName the value for aiob_sop_meta.node_name
     *
     * @mbg.generated
     */
    public void setNodeName(String nodeName) {
        this.nodeName = nodeName == null ? null : nodeName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.manual_check
     *
     * @return the value of aiob_sop_meta.manual_check
     *
     * @mbg.generated
     */
    public Boolean getManualCheck() {
        return manualCheck;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.manual_check
     *
     * @param manualCheck the value for aiob_sop_meta.manual_check
     *
     * @mbg.generated
     */
    public void setManualCheck(Boolean manualCheck) {
        this.manualCheck = manualCheck;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.del
     *
     * @return the value of aiob_sop_meta.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.del
     *
     * @param del the value for aiob_sop_meta.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.creator
     *
     * @return the value of aiob_sop_meta.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.creator
     *
     * @param creator the value for aiob_sop_meta.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.modifier
     *
     * @return the value of aiob_sop_meta.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.modifier
     *
     * @param modifier the value for aiob_sop_meta.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.create_time
     *
     * @return the value of aiob_sop_meta.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.create_time
     *
     * @param createTime the value for aiob_sop_meta.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.update_time
     *
     * @return the value of aiob_sop_meta.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.update_time
     *
     * @param updateTime the value for aiob_sop_meta.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column aiob_sop_meta.task_rule
     *
     * @return the value of aiob_sop_meta.task_rule
     *
     * @mbg.generated
     */
    public String getTaskRule() {
        return taskRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column aiob_sop_meta.task_rule
     *
     * @param taskRule the value for aiob_sop_meta.task_rule
     *
     * @mbg.generated
     */
    public void setTaskRule(String taskRule) {
        this.taskRule = taskRule == null ? null : taskRule.trim();
    }
}