package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTable;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingDataTableCriteria;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IdMappingDataTableMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    long countByExample(IdMappingDataTableCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    int deleteByExample(IdMappingDataTableCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    int insert(IdMappingDataTable record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    int insertSelective(IdMappingDataTable record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    List<IdMappingDataTable> selectByExample(IdMappingDataTableCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    IdMappingDataTable selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") IdMappingDataTable record, @Param("example") IdMappingDataTableCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") IdMappingDataTable record, @Param("example") IdMappingDataTableCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(IdMappingDataTable record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(IdMappingDataTable record);
}