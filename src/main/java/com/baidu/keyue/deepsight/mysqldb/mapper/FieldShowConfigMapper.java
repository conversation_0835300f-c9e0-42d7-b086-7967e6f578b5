package com.baidu.keyue.deepsight.mysqldb.mapper;

import com.baidu.keyue.deepsight.mysqldb.entity.FieldShowConfig;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldShowConfigCriteria;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface FieldShowConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    long countByExample(FieldShowConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    int deleteByExample(FieldShowConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    int insert(FieldShowConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    int insertSelective(FieldShowConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    List<FieldShowConfig> selectByExampleWithBLOBs(FieldShowConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    List<FieldShowConfig> selectByExample(FieldShowConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    FieldShowConfig selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") FieldShowConfig record, @Param("example") FieldShowConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") FieldShowConfig record, @Param("example") FieldShowConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") FieldShowConfig record, @Param("example") FieldShowConfigCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(FieldShowConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(FieldShowConfig record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(FieldShowConfig record);
}