package com.baidu.keyue.deepsight.mysqldb.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TableFieldMetaInfoCriteria {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    public TableFieldMetaInfoCriteria() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andTableEnNameIsNull() {
            addCriterion("table_en_name is null");
            return (Criteria) this;
        }

        public Criteria andTableEnNameIsNotNull() {
            addCriterion("table_en_name is not null");
            return (Criteria) this;
        }

        public Criteria andTableEnNameEqualTo(String value) {
            addCriterion("table_en_name =", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameNotEqualTo(String value) {
            addCriterion("table_en_name <>", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameGreaterThan(String value) {
            addCriterion("table_en_name >", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameGreaterThanOrEqualTo(String value) {
            addCriterion("table_en_name >=", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameLessThan(String value) {
            addCriterion("table_en_name <", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameLessThanOrEqualTo(String value) {
            addCriterion("table_en_name <=", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameLike(String value) {
            addCriterion("table_en_name like", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameNotLike(String value) {
            addCriterion("table_en_name not like", value, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameIn(List<String> values) {
            addCriterion("table_en_name in", values, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameNotIn(List<String> values) {
            addCriterion("table_en_name not in", values, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameBetween(String value1, String value2) {
            addCriterion("table_en_name between", value1, value2, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andTableEnNameNotBetween(String value1, String value2) {
            addCriterion("table_en_name not between", value1, value2, "tableEnName");
            return (Criteria) this;
        }

        public Criteria andDataTableIdIsNull() {
            addCriterion("data_table_id is null");
            return (Criteria) this;
        }

        public Criteria andDataTableIdIsNotNull() {
            addCriterion("data_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andDataTableIdEqualTo(Long value) {
            addCriterion("data_table_id =", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdNotEqualTo(Long value) {
            addCriterion("data_table_id <>", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdGreaterThan(Long value) {
            addCriterion("data_table_id >", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("data_table_id >=", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdLessThan(Long value) {
            addCriterion("data_table_id <", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdLessThanOrEqualTo(Long value) {
            addCriterion("data_table_id <=", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdIn(List<Long> values) {
            addCriterion("data_table_id in", values, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdNotIn(List<Long> values) {
            addCriterion("data_table_id not in", values, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdBetween(Long value1, Long value2) {
            addCriterion("data_table_id between", value1, value2, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdNotBetween(Long value1, Long value2) {
            addCriterion("data_table_id not between", value1, value2, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andEnFieldIsNull() {
            addCriterion("en_field is null");
            return (Criteria) this;
        }

        public Criteria andEnFieldIsNotNull() {
            addCriterion("en_field is not null");
            return (Criteria) this;
        }

        public Criteria andEnFieldEqualTo(String value) {
            addCriterion("en_field =", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldNotEqualTo(String value) {
            addCriterion("en_field <>", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldGreaterThan(String value) {
            addCriterion("en_field >", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldGreaterThanOrEqualTo(String value) {
            addCriterion("en_field >=", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldLessThan(String value) {
            addCriterion("en_field <", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldLessThanOrEqualTo(String value) {
            addCriterion("en_field <=", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldLike(String value) {
            addCriterion("en_field like", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldNotLike(String value) {
            addCriterion("en_field not like", value, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldIn(List<String> values) {
            addCriterion("en_field in", values, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldNotIn(List<String> values) {
            addCriterion("en_field not in", values, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldBetween(String value1, String value2) {
            addCriterion("en_field between", value1, value2, "enField");
            return (Criteria) this;
        }

        public Criteria andEnFieldNotBetween(String value1, String value2) {
            addCriterion("en_field not between", value1, value2, "enField");
            return (Criteria) this;
        }

        public Criteria andCnFieldIsNull() {
            addCriterion("cn_field is null");
            return (Criteria) this;
        }

        public Criteria andCnFieldIsNotNull() {
            addCriterion("cn_field is not null");
            return (Criteria) this;
        }

        public Criteria andCnFieldEqualTo(String value) {
            addCriterion("cn_field =", value, "cnField");
            return (Criteria) this;
        }

        public Criteria andCnFieldNotEqualTo(String value) {
            addCriterion("cn_field <>", value, "cnField");
            return (Criteria) this;
        }

        public Criteria andCnFieldGreaterThan(String value) {
            addCriterion("cn_field >", value, "cnField");
            return (Criteria) this;
        }

        public Criteria andCnFieldGreaterThanOrEqualTo(String value) {
            addCriterion("cn_field >=", value, "cnField");
            return (Criteria) this;
        }

        public Criteria andCnFieldLessThan(String value) {
            addCriterion("cn_field <", value, "cnField");
            return (Criteria) this;
        }

        public Criteria andCnFieldLessThanOrEqualTo(String value) {
            addCriterion("cn_field <=", value, "cnField");
            return (Criteria) this;
        }

        public Criteria andCnFieldLike(String value) {
            addCriterion("cn_field like", value, "cnField");
            return (Criteria) this;
        }

        public Criteria andCnFieldNotLike(String value) {
            addCriterion("cn_field not like", value, "cnField");
            return (Criteria) this;
        }

        public Criteria andCnFieldIn(List<String> values) {
            addCriterion("cn_field in", values, "cnField");
            return (Criteria) this;
        }

        public Criteria andCnFieldNotIn(List<String> values) {
            addCriterion("cn_field not in", values, "cnField");
            return (Criteria) this;
        }

        public Criteria andCnFieldBetween(String value1, String value2) {
            addCriterion("cn_field between", value1, value2, "cnField");
            return (Criteria) this;
        }

        public Criteria andCnFieldNotBetween(String value1, String value2) {
            addCriterion("cn_field not between", value1, value2, "cnField");
            return (Criteria) this;
        }

        public Criteria andFieldTypeIsNull() {
            addCriterion("field_type is null");
            return (Criteria) this;
        }

        public Criteria andFieldTypeIsNotNull() {
            addCriterion("field_type is not null");
            return (Criteria) this;
        }

        public Criteria andFieldTypeEqualTo(String value) {
            addCriterion("field_type =", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotEqualTo(String value) {
            addCriterion("field_type <>", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeGreaterThan(String value) {
            addCriterion("field_type >", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeGreaterThanOrEqualTo(String value) {
            addCriterion("field_type >=", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeLessThan(String value) {
            addCriterion("field_type <", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeLessThanOrEqualTo(String value) {
            addCriterion("field_type <=", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeLike(String value) {
            addCriterion("field_type like", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotLike(String value) {
            addCriterion("field_type not like", value, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeIn(List<String> values) {
            addCriterion("field_type in", values, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotIn(List<String> values) {
            addCriterion("field_type not in", values, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeBetween(String value1, String value2) {
            addCriterion("field_type between", value1, value2, "fieldType");
            return (Criteria) this;
        }

        public Criteria andFieldTypeNotBetween(String value1, String value2) {
            addCriterion("field_type not between", value1, value2, "fieldType");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andIsFilterCriteriaIsNull() {
            addCriterion("is_filter_criteria is null");
            return (Criteria) this;
        }

        public Criteria andIsFilterCriteriaIsNotNull() {
            addCriterion("is_filter_criteria is not null");
            return (Criteria) this;
        }

        public Criteria andIsFilterCriteriaEqualTo(Boolean value) {
            addCriterion("is_filter_criteria =", value, "isFilterCriteria");
            return (Criteria) this;
        }

        public Criteria andIsFilterCriteriaNotEqualTo(Boolean value) {
            addCriterion("is_filter_criteria <>", value, "isFilterCriteria");
            return (Criteria) this;
        }

        public Criteria andIsFilterCriteriaGreaterThan(Boolean value) {
            addCriterion("is_filter_criteria >", value, "isFilterCriteria");
            return (Criteria) this;
        }

        public Criteria andIsFilterCriteriaGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_filter_criteria >=", value, "isFilterCriteria");
            return (Criteria) this;
        }

        public Criteria andIsFilterCriteriaLessThan(Boolean value) {
            addCriterion("is_filter_criteria <", value, "isFilterCriteria");
            return (Criteria) this;
        }

        public Criteria andIsFilterCriteriaLessThanOrEqualTo(Boolean value) {
            addCriterion("is_filter_criteria <=", value, "isFilterCriteria");
            return (Criteria) this;
        }

        public Criteria andIsFilterCriteriaIn(List<Boolean> values) {
            addCriterion("is_filter_criteria in", values, "isFilterCriteria");
            return (Criteria) this;
        }

        public Criteria andIsFilterCriteriaNotIn(List<Boolean> values) {
            addCriterion("is_filter_criteria not in", values, "isFilterCriteria");
            return (Criteria) this;
        }

        public Criteria andIsFilterCriteriaBetween(Boolean value1, Boolean value2) {
            addCriterion("is_filter_criteria between", value1, value2, "isFilterCriteria");
            return (Criteria) this;
        }

        public Criteria andIsFilterCriteriaNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_filter_criteria not between", value1, value2, "isFilterCriteria");
            return (Criteria) this;
        }

        public Criteria andIsRequiredIsNull() {
            addCriterion("is_required is null");
            return (Criteria) this;
        }

        public Criteria andIsRequiredIsNotNull() {
            addCriterion("is_required is not null");
            return (Criteria) this;
        }

        public Criteria andIsRequiredEqualTo(Boolean value) {
            addCriterion("is_required =", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredNotEqualTo(Boolean value) {
            addCriterion("is_required <>", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredGreaterThan(Boolean value) {
            addCriterion("is_required >", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_required >=", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredLessThan(Boolean value) {
            addCriterion("is_required <", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredLessThanOrEqualTo(Boolean value) {
            addCriterion("is_required <=", value, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredIn(List<Boolean> values) {
            addCriterion("is_required in", values, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredNotIn(List<Boolean> values) {
            addCriterion("is_required not in", values, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredBetween(Boolean value1, Boolean value2) {
            addCriterion("is_required between", value1, value2, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsRequiredNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_required not between", value1, value2, "isRequired");
            return (Criteria) this;
        }

        public Criteria andIsSecreteIsNull() {
            addCriterion("is_secrete is null");
            return (Criteria) this;
        }

        public Criteria andIsSecreteIsNotNull() {
            addCriterion("is_secrete is not null");
            return (Criteria) this;
        }

        public Criteria andIsSecreteEqualTo(Boolean value) {
            addCriterion("is_secrete =", value, "isSecrete");
            return (Criteria) this;
        }

        public Criteria andIsSecreteNotEqualTo(Boolean value) {
            addCriterion("is_secrete <>", value, "isSecrete");
            return (Criteria) this;
        }

        public Criteria andIsSecreteGreaterThan(Boolean value) {
            addCriterion("is_secrete >", value, "isSecrete");
            return (Criteria) this;
        }

        public Criteria andIsSecreteGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_secrete >=", value, "isSecrete");
            return (Criteria) this;
        }

        public Criteria andIsSecreteLessThan(Boolean value) {
            addCriterion("is_secrete <", value, "isSecrete");
            return (Criteria) this;
        }

        public Criteria andIsSecreteLessThanOrEqualTo(Boolean value) {
            addCriterion("is_secrete <=", value, "isSecrete");
            return (Criteria) this;
        }

        public Criteria andIsSecreteIn(List<Boolean> values) {
            addCriterion("is_secrete in", values, "isSecrete");
            return (Criteria) this;
        }

        public Criteria andIsSecreteNotIn(List<Boolean> values) {
            addCriterion("is_secrete not in", values, "isSecrete");
            return (Criteria) this;
        }

        public Criteria andIsSecreteBetween(Boolean value1, Boolean value2) {
            addCriterion("is_secrete between", value1, value2, "isSecrete");
            return (Criteria) this;
        }

        public Criteria andIsSecreteNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_secrete not between", value1, value2, "isSecrete");
            return (Criteria) this;
        }

        public Criteria andFromBaiduIsNull() {
            addCriterion("from_baidu is null");
            return (Criteria) this;
        }

        public Criteria andFromBaiduIsNotNull() {
            addCriterion("from_baidu is not null");
            return (Criteria) this;
        }

        public Criteria andFromBaiduEqualTo(Boolean value) {
            addCriterion("from_baidu =", value, "fromBaidu");
            return (Criteria) this;
        }

        public Criteria andFromBaiduNotEqualTo(Boolean value) {
            addCriterion("from_baidu <>", value, "fromBaidu");
            return (Criteria) this;
        }

        public Criteria andFromBaiduGreaterThan(Boolean value) {
            addCriterion("from_baidu >", value, "fromBaidu");
            return (Criteria) this;
        }

        public Criteria andFromBaiduGreaterThanOrEqualTo(Boolean value) {
            addCriterion("from_baidu >=", value, "fromBaidu");
            return (Criteria) this;
        }

        public Criteria andFromBaiduLessThan(Boolean value) {
            addCriterion("from_baidu <", value, "fromBaidu");
            return (Criteria) this;
        }

        public Criteria andFromBaiduLessThanOrEqualTo(Boolean value) {
            addCriterion("from_baidu <=", value, "fromBaidu");
            return (Criteria) this;
        }

        public Criteria andFromBaiduIn(List<Boolean> values) {
            addCriterion("from_baidu in", values, "fromBaidu");
            return (Criteria) this;
        }

        public Criteria andFromBaiduNotIn(List<Boolean> values) {
            addCriterion("from_baidu not in", values, "fromBaidu");
            return (Criteria) this;
        }

        public Criteria andFromBaiduBetween(Boolean value1, Boolean value2) {
            addCriterion("from_baidu between", value1, value2, "fromBaidu");
            return (Criteria) this;
        }

        public Criteria andFromBaiduNotBetween(Boolean value1, Boolean value2) {
            addCriterion("from_baidu not between", value1, value2, "fromBaidu");
            return (Criteria) this;
        }

        public Criteria andIsVisableIsNull() {
            addCriterion("is_visable is null");
            return (Criteria) this;
        }

        public Criteria andIsVisableIsNotNull() {
            addCriterion("is_visable is not null");
            return (Criteria) this;
        }

        public Criteria andIsVisableEqualTo(Boolean value) {
            addCriterion("is_visable =", value, "isVisable");
            return (Criteria) this;
        }

        public Criteria andIsVisableNotEqualTo(Boolean value) {
            addCriterion("is_visable <>", value, "isVisable");
            return (Criteria) this;
        }

        public Criteria andIsVisableGreaterThan(Boolean value) {
            addCriterion("is_visable >", value, "isVisable");
            return (Criteria) this;
        }

        public Criteria andIsVisableGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_visable >=", value, "isVisable");
            return (Criteria) this;
        }

        public Criteria andIsVisableLessThan(Boolean value) {
            addCriterion("is_visable <", value, "isVisable");
            return (Criteria) this;
        }

        public Criteria andIsVisableLessThanOrEqualTo(Boolean value) {
            addCriterion("is_visable <=", value, "isVisable");
            return (Criteria) this;
        }

        public Criteria andIsVisableIn(List<Boolean> values) {
            addCriterion("is_visable in", values, "isVisable");
            return (Criteria) this;
        }

        public Criteria andIsVisableNotIn(List<Boolean> values) {
            addCriterion("is_visable not in", values, "isVisable");
            return (Criteria) this;
        }

        public Criteria andIsVisableBetween(Boolean value1, Boolean value2) {
            addCriterion("is_visable between", value1, value2, "isVisable");
            return (Criteria) this;
        }

        public Criteria andIsVisableNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_visable not between", value1, value2, "isVisable");
            return (Criteria) this;
        }

        public Criteria andFieldTagIsNull() {
            addCriterion("field_tag is null");
            return (Criteria) this;
        }

        public Criteria andFieldTagIsNotNull() {
            addCriterion("field_tag is not null");
            return (Criteria) this;
        }

        public Criteria andFieldTagEqualTo(Integer value) {
            addCriterion("field_tag =", value, "fieldTag");
            return (Criteria) this;
        }

        public Criteria andFieldTagNotEqualTo(Integer value) {
            addCriterion("field_tag <>", value, "fieldTag");
            return (Criteria) this;
        }

        public Criteria andFieldTagGreaterThan(Integer value) {
            addCriterion("field_tag >", value, "fieldTag");
            return (Criteria) this;
        }

        public Criteria andFieldTagGreaterThanOrEqualTo(Integer value) {
            addCriterion("field_tag >=", value, "fieldTag");
            return (Criteria) this;
        }

        public Criteria andFieldTagLessThan(Integer value) {
            addCriterion("field_tag <", value, "fieldTag");
            return (Criteria) this;
        }

        public Criteria andFieldTagLessThanOrEqualTo(Integer value) {
            addCriterion("field_tag <=", value, "fieldTag");
            return (Criteria) this;
        }

        public Criteria andFieldTagIn(List<Integer> values) {
            addCriterion("field_tag in", values, "fieldTag");
            return (Criteria) this;
        }

        public Criteria andFieldTagNotIn(List<Integer> values) {
            addCriterion("field_tag not in", values, "fieldTag");
            return (Criteria) this;
        }

        public Criteria andFieldTagBetween(Integer value1, Integer value2) {
            addCriterion("field_tag between", value1, value2, "fieldTag");
            return (Criteria) this;
        }

        public Criteria andFieldTagNotBetween(Integer value1, Integer value2) {
            addCriterion("field_tag not between", value1, value2, "fieldTag");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andValueTypeIsNull() {
            addCriterion("value_type is null");
            return (Criteria) this;
        }

        public Criteria andValueTypeIsNotNull() {
            addCriterion("value_type is not null");
            return (Criteria) this;
        }

        public Criteria andValueTypeEqualTo(String value) {
            addCriterion("value_type =", value, "valueType");
            return (Criteria) this;
        }

        public Criteria andValueTypeNotEqualTo(String value) {
            addCriterion("value_type <>", value, "valueType");
            return (Criteria) this;
        }

        public Criteria andValueTypeGreaterThan(String value) {
            addCriterion("value_type >", value, "valueType");
            return (Criteria) this;
        }

        public Criteria andValueTypeGreaterThanOrEqualTo(String value) {
            addCriterion("value_type >=", value, "valueType");
            return (Criteria) this;
        }

        public Criteria andValueTypeLessThan(String value) {
            addCriterion("value_type <", value, "valueType");
            return (Criteria) this;
        }

        public Criteria andValueTypeLessThanOrEqualTo(String value) {
            addCriterion("value_type <=", value, "valueType");
            return (Criteria) this;
        }

        public Criteria andValueTypeLike(String value) {
            addCriterion("value_type like", value, "valueType");
            return (Criteria) this;
        }

        public Criteria andValueTypeNotLike(String value) {
            addCriterion("value_type not like", value, "valueType");
            return (Criteria) this;
        }

        public Criteria andValueTypeIn(List<String> values) {
            addCriterion("value_type in", values, "valueType");
            return (Criteria) this;
        }

        public Criteria andValueTypeNotIn(List<String> values) {
            addCriterion("value_type not in", values, "valueType");
            return (Criteria) this;
        }

        public Criteria andValueTypeBetween(String value1, String value2) {
            addCriterion("value_type between", value1, value2, "valueType");
            return (Criteria) this;
        }

        public Criteria andValueTypeNotBetween(String value1, String value2) {
            addCriterion("value_type not between", value1, value2, "valueType");
            return (Criteria) this;
        }

        public Criteria andDataTypeIsNull() {
            addCriterion("data_type is null");
            return (Criteria) this;
        }

        public Criteria andDataTypeIsNotNull() {
            addCriterion("data_type is not null");
            return (Criteria) this;
        }

        public Criteria andDataTypeEqualTo(String value) {
            addCriterion("data_type =", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotEqualTo(String value) {
            addCriterion("data_type <>", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeGreaterThan(String value) {
            addCriterion("data_type >", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeGreaterThanOrEqualTo(String value) {
            addCriterion("data_type >=", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLessThan(String value) {
            addCriterion("data_type <", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLessThanOrEqualTo(String value) {
            addCriterion("data_type <=", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeLike(String value) {
            addCriterion("data_type like", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotLike(String value) {
            addCriterion("data_type not like", value, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeIn(List<String> values) {
            addCriterion("data_type in", values, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotIn(List<String> values) {
            addCriterion("data_type not in", values, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeBetween(String value1, String value2) {
            addCriterion("data_type between", value1, value2, "dataType");
            return (Criteria) this;
        }

        public Criteria andDataTypeNotBetween(String value1, String value2) {
            addCriterion("data_type not between", value1, value2, "dataType");
            return (Criteria) this;
        }

        public Criteria andIsShowValueIsNull() {
            addCriterion("is_show_value is null");
            return (Criteria) this;
        }

        public Criteria andIsShowValueIsNotNull() {
            addCriterion("is_show_value is not null");
            return (Criteria) this;
        }

        public Criteria andIsShowValueEqualTo(Boolean value) {
            addCriterion("is_show_value =", value, "isShowValue");
            return (Criteria) this;
        }

        public Criteria andIsShowValueNotEqualTo(Boolean value) {
            addCriterion("is_show_value <>", value, "isShowValue");
            return (Criteria) this;
        }

        public Criteria andIsShowValueGreaterThan(Boolean value) {
            addCriterion("is_show_value >", value, "isShowValue");
            return (Criteria) this;
        }

        public Criteria andIsShowValueGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_show_value >=", value, "isShowValue");
            return (Criteria) this;
        }

        public Criteria andIsShowValueLessThan(Boolean value) {
            addCriterion("is_show_value <", value, "isShowValue");
            return (Criteria) this;
        }

        public Criteria andIsShowValueLessThanOrEqualTo(Boolean value) {
            addCriterion("is_show_value <=", value, "isShowValue");
            return (Criteria) this;
        }

        public Criteria andIsShowValueIn(List<Boolean> values) {
            addCriterion("is_show_value in", values, "isShowValue");
            return (Criteria) this;
        }

        public Criteria andIsShowValueNotIn(List<Boolean> values) {
            addCriterion("is_show_value not in", values, "isShowValue");
            return (Criteria) this;
        }

        public Criteria andIsShowValueBetween(Boolean value1, Boolean value2) {
            addCriterion("is_show_value between", value1, value2, "isShowValue");
            return (Criteria) this;
        }

        public Criteria andIsShowValueNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_show_value not between", value1, value2, "isShowValue");
            return (Criteria) this;
        }

        public Criteria andNumberIsNull() {
            addCriterion("number is null");
            return (Criteria) this;
        }

        public Criteria andNumberIsNotNull() {
            addCriterion("number is not null");
            return (Criteria) this;
        }

        public Criteria andNumberEqualTo(Integer value) {
            addCriterion("number =", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotEqualTo(Integer value) {
            addCriterion("number <>", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThan(Integer value) {
            addCriterion("number >", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("number >=", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberLessThan(Integer value) {
            addCriterion("number <", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberLessThanOrEqualTo(Integer value) {
            addCriterion("number <=", value, "number");
            return (Criteria) this;
        }

        public Criteria andNumberIn(List<Integer> values) {
            addCriterion("number in", values, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotIn(List<Integer> values) {
            addCriterion("number not in", values, "number");
            return (Criteria) this;
        }

        public Criteria andNumberBetween(Integer value1, Integer value2) {
            addCriterion("number between", value1, value2, "number");
            return (Criteria) this;
        }

        public Criteria andNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("number not between", value1, value2, "number");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table datatable_meta_info
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}