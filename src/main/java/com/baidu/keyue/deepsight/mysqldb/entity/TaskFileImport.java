package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class TaskFileImport implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.group_id
     *
     * @mbg.generated
     */
    private String groupId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.source_name
     *
     * @mbg.generated
     */
    private String sourceName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.bos_name
     *
     * @mbg.generated
     */
    private String bosName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.bos_err_name
     *
     * @mbg.generated
     */
    private String bosErrName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.suffix_name
     *
     * @mbg.generated
     */
    private String suffixName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.write_type
     *
     * @mbg.generated
     */
    private String writeType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.mapping_type
     *
     * @mbg.generated
     */
    private String mappingType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.data_table_id
     *
     * @mbg.generated
     */
    private Long dataTableId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.status
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.operator_name
     *
     * @mbg.generated
     */
    private String operatorName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_file_import.message
     *
     * @mbg.generated
     */
    private String message;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table task_file_import
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.id
     *
     * @return the value of task_file_import.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.id
     *
     * @param id the value for task_file_import.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.group_id
     *
     * @return the value of task_file_import.group_id
     *
     * @mbg.generated
     */
    public String getGroupId() {
        return groupId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.group_id
     *
     * @param groupId the value for task_file_import.group_id
     *
     * @mbg.generated
     */
    public void setGroupId(String groupId) {
        this.groupId = groupId == null ? null : groupId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.source_name
     *
     * @return the value of task_file_import.source_name
     *
     * @mbg.generated
     */
    public String getSourceName() {
        return sourceName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.source_name
     *
     * @param sourceName the value for task_file_import.source_name
     *
     * @mbg.generated
     */
    public void setSourceName(String sourceName) {
        this.sourceName = sourceName == null ? null : sourceName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.bos_name
     *
     * @return the value of task_file_import.bos_name
     *
     * @mbg.generated
     */
    public String getBosName() {
        return bosName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.bos_name
     *
     * @param bosName the value for task_file_import.bos_name
     *
     * @mbg.generated
     */
    public void setBosName(String bosName) {
        this.bosName = bosName == null ? null : bosName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.bos_err_name
     *
     * @return the value of task_file_import.bos_err_name
     *
     * @mbg.generated
     */
    public String getBosErrName() {
        return bosErrName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.bos_err_name
     *
     * @param bosErrName the value for task_file_import.bos_err_name
     *
     * @mbg.generated
     */
    public void setBosErrName(String bosErrName) {
        this.bosErrName = bosErrName == null ? null : bosErrName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.suffix_name
     *
     * @return the value of task_file_import.suffix_name
     *
     * @mbg.generated
     */
    public String getSuffixName() {
        return suffixName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.suffix_name
     *
     * @param suffixName the value for task_file_import.suffix_name
     *
     * @mbg.generated
     */
    public void setSuffixName(String suffixName) {
        this.suffixName = suffixName == null ? null : suffixName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.write_type
     *
     * @return the value of task_file_import.write_type
     *
     * @mbg.generated
     */
    public String getWriteType() {
        return writeType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.write_type
     *
     * @param writeType the value for task_file_import.write_type
     *
     * @mbg.generated
     */
    public void setWriteType(String writeType) {
        this.writeType = writeType == null ? null : writeType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.mapping_type
     *
     * @return the value of task_file_import.mapping_type
     *
     * @mbg.generated
     */
    public String getMappingType() {
        return mappingType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.mapping_type
     *
     * @param mappingType the value for task_file_import.mapping_type
     *
     * @mbg.generated
     */
    public void setMappingType(String mappingType) {
        this.mappingType = mappingType == null ? null : mappingType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.tenant_id
     *
     * @return the value of task_file_import.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.tenant_id
     *
     * @param tenantId the value for task_file_import.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.data_table_id
     *
     * @return the value of task_file_import.data_table_id
     *
     * @mbg.generated
     */
    public Long getDataTableId() {
        return dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.data_table_id
     *
     * @param dataTableId the value for task_file_import.data_table_id
     *
     * @mbg.generated
     */
    public void setDataTableId(Long dataTableId) {
        this.dataTableId = dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.status
     *
     * @return the value of task_file_import.status
     *
     * @mbg.generated
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.status
     *
     * @param status the value for task_file_import.status
     *
     * @mbg.generated
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.del
     *
     * @return the value of task_file_import.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.del
     *
     * @param del the value for task_file_import.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.creator
     *
     * @return the value of task_file_import.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.creator
     *
     * @param creator the value for task_file_import.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.modifier
     *
     * @return the value of task_file_import.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.modifier
     *
     * @param modifier the value for task_file_import.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.operator_name
     *
     * @return the value of task_file_import.operator_name
     *
     * @mbg.generated
     */
    public String getOperatorName() {
        return operatorName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.operator_name
     *
     * @param operatorName the value for task_file_import.operator_name
     *
     * @mbg.generated
     */
    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName == null ? null : operatorName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.create_time
     *
     * @return the value of task_file_import.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.create_time
     *
     * @param createTime the value for task_file_import.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.update_time
     *
     * @return the value of task_file_import.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.update_time
     *
     * @param updateTime the value for task_file_import.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_file_import.message
     *
     * @return the value of task_file_import.message
     *
     * @mbg.generated
     */
    public String getMessage() {
        return message;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_file_import.message
     *
     * @param message the value for task_file_import.message
     *
     * @mbg.generated
     */
    public void setMessage(String message) {
        this.message = message == null ? null : message.trim();
    }
}