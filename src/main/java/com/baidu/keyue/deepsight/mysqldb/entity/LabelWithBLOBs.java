package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;

public class LabelWithBLOBs extends Label implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.label_rule
     *
     * @mbg.generated
     */
    private String labelRule;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label.distribution
     *
     * @mbg.generated
     */
    private String distribution;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table label
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.label_rule
     *
     * @return the value of label.label_rule
     *
     * @mbg.generated
     */
    public String getLabelRule() {
        return labelRule;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.label_rule
     *
     * @param labelRule the value for label.label_rule
     *
     * @mbg.generated
     */
    public void setLabelRule(String labelRule) {
        this.labelRule = labelRule == null ? null : labelRule.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label.distribution
     *
     * @return the value of label.distribution
     *
     * @mbg.generated
     */
    public String getDistribution() {
        return distribution;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label.distribution
     *
     * @param distribution the value for label.distribution
     *
     * @mbg.generated
     */
    public void setDistribution(String distribution) {
        this.distribution = distribution == null ? null : distribution.trim();
    }
}