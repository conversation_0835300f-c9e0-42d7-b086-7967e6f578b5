package com.baidu.keyue.deepsight.mysqldb.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AlertConfigCriteria {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    public AlertConfigCriteria() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andConfigTypeIsNull() {
            addCriterion("config_type is null");
            return (Criteria) this;
        }

        public Criteria andConfigTypeIsNotNull() {
            addCriterion("config_type is not null");
            return (Criteria) this;
        }

        public Criteria andConfigTypeEqualTo(String value) {
            addCriterion("config_type =", value, "configType");
            return (Criteria) this;
        }

        public Criteria andConfigTypeNotEqualTo(String value) {
            addCriterion("config_type <>", value, "configType");
            return (Criteria) this;
        }

        public Criteria andConfigTypeGreaterThan(String value) {
            addCriterion("config_type >", value, "configType");
            return (Criteria) this;
        }

        public Criteria andConfigTypeGreaterThanOrEqualTo(String value) {
            addCriterion("config_type >=", value, "configType");
            return (Criteria) this;
        }

        public Criteria andConfigTypeLessThan(String value) {
            addCriterion("config_type <", value, "configType");
            return (Criteria) this;
        }

        public Criteria andConfigTypeLessThanOrEqualTo(String value) {
            addCriterion("config_type <=", value, "configType");
            return (Criteria) this;
        }

        public Criteria andConfigTypeLike(String value) {
            addCriterion("config_type like", value, "configType");
            return (Criteria) this;
        }

        public Criteria andConfigTypeNotLike(String value) {
            addCriterion("config_type not like", value, "configType");
            return (Criteria) this;
        }

        public Criteria andConfigTypeIn(List<String> values) {
            addCriterion("config_type in", values, "configType");
            return (Criteria) this;
        }

        public Criteria andConfigTypeNotIn(List<String> values) {
            addCriterion("config_type not in", values, "configType");
            return (Criteria) this;
        }

        public Criteria andConfigTypeBetween(String value1, String value2) {
            addCriterion("config_type between", value1, value2, "configType");
            return (Criteria) this;
        }

        public Criteria andConfigTypeNotBetween(String value1, String value2) {
            addCriterion("config_type not between", value1, value2, "configType");
            return (Criteria) this;
        }

        public Criteria andConfigTargetIsNull() {
            addCriterion("config_target is null");
            return (Criteria) this;
        }

        public Criteria andConfigTargetIsNotNull() {
            addCriterion("config_target is not null");
            return (Criteria) this;
        }

        public Criteria andConfigTargetEqualTo(String value) {
            addCriterion("config_target =", value, "configTarget");
            return (Criteria) this;
        }

        public Criteria andConfigTargetNotEqualTo(String value) {
            addCriterion("config_target <>", value, "configTarget");
            return (Criteria) this;
        }

        public Criteria andConfigTargetGreaterThan(String value) {
            addCriterion("config_target >", value, "configTarget");
            return (Criteria) this;
        }

        public Criteria andConfigTargetGreaterThanOrEqualTo(String value) {
            addCriterion("config_target >=", value, "configTarget");
            return (Criteria) this;
        }

        public Criteria andConfigTargetLessThan(String value) {
            addCriterion("config_target <", value, "configTarget");
            return (Criteria) this;
        }

        public Criteria andConfigTargetLessThanOrEqualTo(String value) {
            addCriterion("config_target <=", value, "configTarget");
            return (Criteria) this;
        }

        public Criteria andConfigTargetLike(String value) {
            addCriterion("config_target like", value, "configTarget");
            return (Criteria) this;
        }

        public Criteria andConfigTargetNotLike(String value) {
            addCriterion("config_target not like", value, "configTarget");
            return (Criteria) this;
        }

        public Criteria andConfigTargetIn(List<String> values) {
            addCriterion("config_target in", values, "configTarget");
            return (Criteria) this;
        }

        public Criteria andConfigTargetNotIn(List<String> values) {
            addCriterion("config_target not in", values, "configTarget");
            return (Criteria) this;
        }

        public Criteria andConfigTargetBetween(String value1, String value2) {
            addCriterion("config_target between", value1, value2, "configTarget");
            return (Criteria) this;
        }

        public Criteria andConfigTargetNotBetween(String value1, String value2) {
            addCriterion("config_target not between", value1, value2, "configTarget");
            return (Criteria) this;
        }

        public Criteria andThresholdRateIsNull() {
            addCriterion("threshold_rate is null");
            return (Criteria) this;
        }

        public Criteria andThresholdRateIsNotNull() {
            addCriterion("threshold_rate is not null");
            return (Criteria) this;
        }

        public Criteria andThresholdRateEqualTo(BigDecimal value) {
            addCriterion("threshold_rate =", value, "thresholdRate");
            return (Criteria) this;
        }

        public Criteria andThresholdRateNotEqualTo(BigDecimal value) {
            addCriterion("threshold_rate <>", value, "thresholdRate");
            return (Criteria) this;
        }

        public Criteria andThresholdRateGreaterThan(BigDecimal value) {
            addCriterion("threshold_rate >", value, "thresholdRate");
            return (Criteria) this;
        }

        public Criteria andThresholdRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("threshold_rate >=", value, "thresholdRate");
            return (Criteria) this;
        }

        public Criteria andThresholdRateLessThan(BigDecimal value) {
            addCriterion("threshold_rate <", value, "thresholdRate");
            return (Criteria) this;
        }

        public Criteria andThresholdRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("threshold_rate <=", value, "thresholdRate");
            return (Criteria) this;
        }

        public Criteria andThresholdRateIn(List<BigDecimal> values) {
            addCriterion("threshold_rate in", values, "thresholdRate");
            return (Criteria) this;
        }

        public Criteria andThresholdRateNotIn(List<BigDecimal> values) {
            addCriterion("threshold_rate not in", values, "thresholdRate");
            return (Criteria) this;
        }

        public Criteria andThresholdRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("threshold_rate between", value1, value2, "thresholdRate");
            return (Criteria) this;
        }

        public Criteria andThresholdRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("threshold_rate not between", value1, value2, "thresholdRate");
            return (Criteria) this;
        }

        public Criteria andDialCountIsNull() {
            addCriterion("dial_count is null");
            return (Criteria) this;
        }

        public Criteria andDialCountIsNotNull() {
            addCriterion("dial_count is not null");
            return (Criteria) this;
        }

        public Criteria andDialCountEqualTo(Integer value) {
            addCriterion("dial_count =", value, "dialCount");
            return (Criteria) this;
        }

        public Criteria andDialCountNotEqualTo(Integer value) {
            addCriterion("dial_count <>", value, "dialCount");
            return (Criteria) this;
        }

        public Criteria andDialCountGreaterThan(Integer value) {
            addCriterion("dial_count >", value, "dialCount");
            return (Criteria) this;
        }

        public Criteria andDialCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("dial_count >=", value, "dialCount");
            return (Criteria) this;
        }

        public Criteria andDialCountLessThan(Integer value) {
            addCriterion("dial_count <", value, "dialCount");
            return (Criteria) this;
        }

        public Criteria andDialCountLessThanOrEqualTo(Integer value) {
            addCriterion("dial_count <=", value, "dialCount");
            return (Criteria) this;
        }

        public Criteria andDialCountIn(List<Integer> values) {
            addCriterion("dial_count in", values, "dialCount");
            return (Criteria) this;
        }

        public Criteria andDialCountNotIn(List<Integer> values) {
            addCriterion("dial_count not in", values, "dialCount");
            return (Criteria) this;
        }

        public Criteria andDialCountBetween(Integer value1, Integer value2) {
            addCriterion("dial_count between", value1, value2, "dialCount");
            return (Criteria) this;
        }

        public Criteria andDialCountNotBetween(Integer value1, Integer value2) {
            addCriterion("dial_count not between", value1, value2, "dialCount");
            return (Criteria) this;
        }

        public Criteria andAlertFreqIsNull() {
            addCriterion("alert_freq is null");
            return (Criteria) this;
        }

        public Criteria andAlertFreqIsNotNull() {
            addCriterion("alert_freq is not null");
            return (Criteria) this;
        }

        public Criteria andAlertFreqEqualTo(Integer value) {
            addCriterion("alert_freq =", value, "alertFreq");
            return (Criteria) this;
        }

        public Criteria andAlertFreqNotEqualTo(Integer value) {
            addCriterion("alert_freq <>", value, "alertFreq");
            return (Criteria) this;
        }

        public Criteria andAlertFreqGreaterThan(Integer value) {
            addCriterion("alert_freq >", value, "alertFreq");
            return (Criteria) this;
        }

        public Criteria andAlertFreqGreaterThanOrEqualTo(Integer value) {
            addCriterion("alert_freq >=", value, "alertFreq");
            return (Criteria) this;
        }

        public Criteria andAlertFreqLessThan(Integer value) {
            addCriterion("alert_freq <", value, "alertFreq");
            return (Criteria) this;
        }

        public Criteria andAlertFreqLessThanOrEqualTo(Integer value) {
            addCriterion("alert_freq <=", value, "alertFreq");
            return (Criteria) this;
        }

        public Criteria andAlertFreqIn(List<Integer> values) {
            addCriterion("alert_freq in", values, "alertFreq");
            return (Criteria) this;
        }

        public Criteria andAlertFreqNotIn(List<Integer> values) {
            addCriterion("alert_freq not in", values, "alertFreq");
            return (Criteria) this;
        }

        public Criteria andAlertFreqBetween(Integer value1, Integer value2) {
            addCriterion("alert_freq between", value1, value2, "alertFreq");
            return (Criteria) this;
        }

        public Criteria andAlertFreqNotBetween(Integer value1, Integer value2) {
            addCriterion("alert_freq not between", value1, value2, "alertFreq");
            return (Criteria) this;
        }

        public Criteria andIsActiveIsNull() {
            addCriterion("is_active is null");
            return (Criteria) this;
        }

        public Criteria andIsActiveIsNotNull() {
            addCriterion("is_active is not null");
            return (Criteria) this;
        }

        public Criteria andIsActiveEqualTo(Boolean value) {
            addCriterion("is_active =", value, "isActive");
            return (Criteria) this;
        }

        public Criteria andIsActiveNotEqualTo(Boolean value) {
            addCriterion("is_active <>", value, "isActive");
            return (Criteria) this;
        }

        public Criteria andIsActiveGreaterThan(Boolean value) {
            addCriterion("is_active >", value, "isActive");
            return (Criteria) this;
        }

        public Criteria andIsActiveGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_active >=", value, "isActive");
            return (Criteria) this;
        }

        public Criteria andIsActiveLessThan(Boolean value) {
            addCriterion("is_active <", value, "isActive");
            return (Criteria) this;
        }

        public Criteria andIsActiveLessThanOrEqualTo(Boolean value) {
            addCriterion("is_active <=", value, "isActive");
            return (Criteria) this;
        }

        public Criteria andIsActiveIn(List<Boolean> values) {
            addCriterion("is_active in", values, "isActive");
            return (Criteria) this;
        }

        public Criteria andIsActiveNotIn(List<Boolean> values) {
            addCriterion("is_active not in", values, "isActive");
            return (Criteria) this;
        }

        public Criteria andIsActiveBetween(Boolean value1, Boolean value2) {
            addCriterion("is_active between", value1, value2, "isActive");
            return (Criteria) this;
        }

        public Criteria andIsActiveNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_active not between", value1, value2, "isActive");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andAlertTimeIsNull() {
            addCriterion("alert_time is null");
            return (Criteria) this;
        }

        public Criteria andAlertTimeIsNotNull() {
            addCriterion("alert_time is not null");
            return (Criteria) this;
        }

        public Criteria andAlertTimeEqualTo(String value) {
            addCriterion("alert_time =", value, "alertTime");
            return (Criteria) this;
        }

        public Criteria andAlertTimeNotEqualTo(String value) {
            addCriterion("alert_time <>", value, "alertTime");
            return (Criteria) this;
        }

        public Criteria andAlertTimeGreaterThan(String value) {
            addCriterion("alert_time >", value, "alertTime");
            return (Criteria) this;
        }

        public Criteria andAlertTimeGreaterThanOrEqualTo(String value) {
            addCriterion("alert_time >=", value, "alertTime");
            return (Criteria) this;
        }

        public Criteria andAlertTimeLessThan(String value) {
            addCriterion("alert_time <", value, "alertTime");
            return (Criteria) this;
        }

        public Criteria andAlertTimeLessThanOrEqualTo(String value) {
            addCriterion("alert_time <=", value, "alertTime");
            return (Criteria) this;
        }

        public Criteria andAlertTimeLike(String value) {
            addCriterion("alert_time like", value, "alertTime");
            return (Criteria) this;
        }

        public Criteria andAlertTimeNotLike(String value) {
            addCriterion("alert_time not like", value, "alertTime");
            return (Criteria) this;
        }

        public Criteria andAlertTimeIn(List<String> values) {
            addCriterion("alert_time in", values, "alertTime");
            return (Criteria) this;
        }

        public Criteria andAlertTimeNotIn(List<String> values) {
            addCriterion("alert_time not in", values, "alertTime");
            return (Criteria) this;
        }

        public Criteria andAlertTimeBetween(String value1, String value2) {
            addCriterion("alert_time between", value1, value2, "alertTime");
            return (Criteria) this;
        }

        public Criteria andAlertTimeNotBetween(String value1, String value2) {
            addCriterion("alert_time not between", value1, value2, "alertTime");
            return (Criteria) this;
        }

        public Criteria andNextCheckTimeIsNull() {
            addCriterion("next_check_time is null");
            return (Criteria) this;
        }

        public Criteria andNextCheckTimeIsNotNull() {
            addCriterion("next_check_time is not null");
            return (Criteria) this;
        }

        public Criteria andNextCheckTimeEqualTo(Date value) {
            addCriterion("next_check_time =", value, "nextCheckTime");
            return (Criteria) this;
        }

        public Criteria andNextCheckTimeNotEqualTo(Date value) {
            addCriterion("next_check_time <>", value, "nextCheckTime");
            return (Criteria) this;
        }

        public Criteria andNextCheckTimeGreaterThan(Date value) {
            addCriterion("next_check_time >", value, "nextCheckTime");
            return (Criteria) this;
        }

        public Criteria andNextCheckTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("next_check_time >=", value, "nextCheckTime");
            return (Criteria) this;
        }

        public Criteria andNextCheckTimeLessThan(Date value) {
            addCriterion("next_check_time <", value, "nextCheckTime");
            return (Criteria) this;
        }

        public Criteria andNextCheckTimeLessThanOrEqualTo(Date value) {
            addCriterion("next_check_time <=", value, "nextCheckTime");
            return (Criteria) this;
        }

        public Criteria andNextCheckTimeIn(List<Date> values) {
            addCriterion("next_check_time in", values, "nextCheckTime");
            return (Criteria) this;
        }

        public Criteria andNextCheckTimeNotIn(List<Date> values) {
            addCriterion("next_check_time not in", values, "nextCheckTime");
            return (Criteria) this;
        }

        public Criteria andNextCheckTimeBetween(Date value1, Date value2) {
            addCriterion("next_check_time between", value1, value2, "nextCheckTime");
            return (Criteria) this;
        }

        public Criteria andNextCheckTimeNotBetween(Date value1, Date value2) {
            addCriterion("next_check_time not between", value1, value2, "nextCheckTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table alert_config
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table alert_config
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}