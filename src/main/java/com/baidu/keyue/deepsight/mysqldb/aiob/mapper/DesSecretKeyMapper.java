package com.baidu.keyue.deepsight.mysqldb.aiob.mapper;

import com.baidu.keyue.deepsight.mysqldb.aiob.entity.DesSecretKey;
import com.baidu.keyue.deepsight.mysqldb.aiob.entity.DesSecretKeyCriteria;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface DesSecretKeyMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_secret_key
     *
     * @mbg.generated
     */
    long countByExample(DesSecretKeyCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_secret_key
     *
     * @mbg.generated
     */
    int deleteByExample(DesSecretKeyCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_secret_key
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_secret_key
     *
     * @mbg.generated
     */
    int insert(DesSecret<PERSON>ey record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_secret_key
     *
     * @mbg.generated
     */
    int insertSelective(DesSecretKey record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_secret_key
     *
     * @mbg.generated
     */
    List<DesSecretKey> selectByExample(DesSecretKeyCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_secret_key
     *
     * @mbg.generated
     */
    DesSecretKey selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_secret_key
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") DesSecretKey record, @Param("example") DesSecretKeyCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_secret_key
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") DesSecretKey record, @Param("example") DesSecretKeyCriteria example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_secret_key
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(DesSecretKey record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table des_secret_key
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(DesSecretKey record);
}