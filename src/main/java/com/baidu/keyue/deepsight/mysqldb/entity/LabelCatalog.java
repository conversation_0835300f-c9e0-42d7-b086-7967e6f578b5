package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class LabelCatalog implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_catalog.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_catalog.parent_id
     *
     * @mbg.generated
     */
    private Long parentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_catalog.catalog_name
     *
     * @mbg.generated
     */
    private String catalogName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_catalog.sort
     *
     * @mbg.generated
     */
    private Long sort;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_catalog.user_id
     *
     * @mbg.generated
     */
    private String userId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_catalog.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_catalog.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_catalog.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_catalog.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_catalog.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_catalog.label_count
     *
     * @mbg.generated
     */
    private Integer labelCount;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column label_catalog.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table label_catalog
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_catalog.id
     *
     * @return the value of label_catalog.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_catalog.id
     *
     * @param id the value for label_catalog.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_catalog.parent_id
     *
     * @return the value of label_catalog.parent_id
     *
     * @mbg.generated
     */
    public Long getParentId() {
        return parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_catalog.parent_id
     *
     * @param parentId the value for label_catalog.parent_id
     *
     * @mbg.generated
     */
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_catalog.catalog_name
     *
     * @return the value of label_catalog.catalog_name
     *
     * @mbg.generated
     */
    public String getCatalogName() {
        return catalogName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_catalog.catalog_name
     *
     * @param catalogName the value for label_catalog.catalog_name
     *
     * @mbg.generated
     */
    public void setCatalogName(String catalogName) {
        this.catalogName = catalogName == null ? null : catalogName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_catalog.sort
     *
     * @return the value of label_catalog.sort
     *
     * @mbg.generated
     */
    public Long getSort() {
        return sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_catalog.sort
     *
     * @param sort the value for label_catalog.sort
     *
     * @mbg.generated
     */
    public void setSort(Long sort) {
        this.sort = sort;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_catalog.user_id
     *
     * @return the value of label_catalog.user_id
     *
     * @mbg.generated
     */
    public String getUserId() {
        return userId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_catalog.user_id
     *
     * @param userId the value for label_catalog.user_id
     *
     * @mbg.generated
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_catalog.del
     *
     * @return the value of label_catalog.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_catalog.del
     *
     * @param del the value for label_catalog.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_catalog.creator
     *
     * @return the value of label_catalog.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_catalog.creator
     *
     * @param creator the value for label_catalog.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_catalog.modifier
     *
     * @return the value of label_catalog.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_catalog.modifier
     *
     * @param modifier the value for label_catalog.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_catalog.create_time
     *
     * @return the value of label_catalog.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_catalog.create_time
     *
     * @param createTime the value for label_catalog.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_catalog.update_time
     *
     * @return the value of label_catalog.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_catalog.update_time
     *
     * @param updateTime the value for label_catalog.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_catalog.label_count
     *
     * @return the value of label_catalog.label_count
     *
     * @mbg.generated
     */
    public Integer getLabelCount() {
        return labelCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_catalog.label_count
     *
     * @param labelCount the value for label_catalog.label_count
     *
     * @mbg.generated
     */
    public void setLabelCount(Integer labelCount) {
        this.labelCount = labelCount;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column label_catalog.tenant_id
     *
     * @return the value of label_catalog.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column label_catalog.tenant_id
     *
     * @param tenantId the value for label_catalog.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }
}