package com.baidu.keyue.deepsight.mysqldb.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class IdMappingRelationCriteria {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    public IdMappingRelationCriteria() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPresetIsNull() {
            addCriterion("preset is null");
            return (Criteria) this;
        }

        public Criteria andPresetIsNotNull() {
            addCriterion("preset is not null");
            return (Criteria) this;
        }

        public Criteria andPresetEqualTo(Boolean value) {
            addCriterion("preset =", value, "preset");
            return (Criteria) this;
        }

        public Criteria andPresetNotEqualTo(Boolean value) {
            addCriterion("preset <>", value, "preset");
            return (Criteria) this;
        }

        public Criteria andPresetGreaterThan(Boolean value) {
            addCriterion("preset >", value, "preset");
            return (Criteria) this;
        }

        public Criteria andPresetGreaterThanOrEqualTo(Boolean value) {
            addCriterion("preset >=", value, "preset");
            return (Criteria) this;
        }

        public Criteria andPresetLessThan(Boolean value) {
            addCriterion("preset <", value, "preset");
            return (Criteria) this;
        }

        public Criteria andPresetLessThanOrEqualTo(Boolean value) {
            addCriterion("preset <=", value, "preset");
            return (Criteria) this;
        }

        public Criteria andPresetIn(List<Boolean> values) {
            addCriterion("preset in", values, "preset");
            return (Criteria) this;
        }

        public Criteria andPresetNotIn(List<Boolean> values) {
            addCriterion("preset not in", values, "preset");
            return (Criteria) this;
        }

        public Criteria andPresetBetween(Boolean value1, Boolean value2) {
            addCriterion("preset between", value1, value2, "preset");
            return (Criteria) this;
        }

        public Criteria andPresetNotBetween(Boolean value1, Boolean value2) {
            addCriterion("preset not between", value1, value2, "preset");
            return (Criteria) this;
        }

        public Criteria andDataTableIdIsNull() {
            addCriterion("data_table_id is null");
            return (Criteria) this;
        }

        public Criteria andDataTableIdIsNotNull() {
            addCriterion("data_table_id is not null");
            return (Criteria) this;
        }

        public Criteria andDataTableIdEqualTo(Long value) {
            addCriterion("data_table_id =", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdNotEqualTo(Long value) {
            addCriterion("data_table_id <>", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdGreaterThan(Long value) {
            addCriterion("data_table_id >", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdGreaterThanOrEqualTo(Long value) {
            addCriterion("data_table_id >=", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdLessThan(Long value) {
            addCriterion("data_table_id <", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdLessThanOrEqualTo(Long value) {
            addCriterion("data_table_id <=", value, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdIn(List<Long> values) {
            addCriterion("data_table_id in", values, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdNotIn(List<Long> values) {
            addCriterion("data_table_id not in", values, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdBetween(Long value1, Long value2) {
            addCriterion("data_table_id between", value1, value2, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andDataTableIdNotBetween(Long value1, Long value2) {
            addCriterion("data_table_id not between", value1, value2, "dataTableId");
            return (Criteria) this;
        }

        public Criteria andEnFieldsIsNull() {
            addCriterion("en_fields is null");
            return (Criteria) this;
        }

        public Criteria andEnFieldsIsNotNull() {
            addCriterion("en_fields is not null");
            return (Criteria) this;
        }

        public Criteria andEnFieldsEqualTo(String value) {
            addCriterion("en_fields =", value, "enFields");
            return (Criteria) this;
        }

        public Criteria andEnFieldsNotEqualTo(String value) {
            addCriterion("en_fields <>", value, "enFields");
            return (Criteria) this;
        }

        public Criteria andEnFieldsGreaterThan(String value) {
            addCriterion("en_fields >", value, "enFields");
            return (Criteria) this;
        }

        public Criteria andEnFieldsGreaterThanOrEqualTo(String value) {
            addCriterion("en_fields >=", value, "enFields");
            return (Criteria) this;
        }

        public Criteria andEnFieldsLessThan(String value) {
            addCriterion("en_fields <", value, "enFields");
            return (Criteria) this;
        }

        public Criteria andEnFieldsLessThanOrEqualTo(String value) {
            addCriterion("en_fields <=", value, "enFields");
            return (Criteria) this;
        }

        public Criteria andEnFieldsLike(String value) {
            addCriterion("en_fields like", value, "enFields");
            return (Criteria) this;
        }

        public Criteria andEnFieldsNotLike(String value) {
            addCriterion("en_fields not like", value, "enFields");
            return (Criteria) this;
        }

        public Criteria andEnFieldsIn(List<String> values) {
            addCriterion("en_fields in", values, "enFields");
            return (Criteria) this;
        }

        public Criteria andEnFieldsNotIn(List<String> values) {
            addCriterion("en_fields not in", values, "enFields");
            return (Criteria) this;
        }

        public Criteria andEnFieldsBetween(String value1, String value2) {
            addCriterion("en_fields between", value1, value2, "enFields");
            return (Criteria) this;
        }

        public Criteria andEnFieldsNotBetween(String value1, String value2) {
            addCriterion("en_fields not between", value1, value2, "enFields");
            return (Criteria) this;
        }

        public Criteria andCnFieldsIsNull() {
            addCriterion("cn_fields is null");
            return (Criteria) this;
        }

        public Criteria andCnFieldsIsNotNull() {
            addCriterion("cn_fields is not null");
            return (Criteria) this;
        }

        public Criteria andCnFieldsEqualTo(String value) {
            addCriterion("cn_fields =", value, "cnFields");
            return (Criteria) this;
        }

        public Criteria andCnFieldsNotEqualTo(String value) {
            addCriterion("cn_fields <>", value, "cnFields");
            return (Criteria) this;
        }

        public Criteria andCnFieldsGreaterThan(String value) {
            addCriterion("cn_fields >", value, "cnFields");
            return (Criteria) this;
        }

        public Criteria andCnFieldsGreaterThanOrEqualTo(String value) {
            addCriterion("cn_fields >=", value, "cnFields");
            return (Criteria) this;
        }

        public Criteria andCnFieldsLessThan(String value) {
            addCriterion("cn_fields <", value, "cnFields");
            return (Criteria) this;
        }

        public Criteria andCnFieldsLessThanOrEqualTo(String value) {
            addCriterion("cn_fields <=", value, "cnFields");
            return (Criteria) this;
        }

        public Criteria andCnFieldsLike(String value) {
            addCriterion("cn_fields like", value, "cnFields");
            return (Criteria) this;
        }

        public Criteria andCnFieldsNotLike(String value) {
            addCriterion("cn_fields not like", value, "cnFields");
            return (Criteria) this;
        }

        public Criteria andCnFieldsIn(List<String> values) {
            addCriterion("cn_fields in", values, "cnFields");
            return (Criteria) this;
        }

        public Criteria andCnFieldsNotIn(List<String> values) {
            addCriterion("cn_fields not in", values, "cnFields");
            return (Criteria) this;
        }

        public Criteria andCnFieldsBetween(String value1, String value2) {
            addCriterion("cn_fields between", value1, value2, "cnFields");
            return (Criteria) this;
        }

        public Criteria andCnFieldsNotBetween(String value1, String value2) {
            addCriterion("cn_fields not between", value1, value2, "cnFields");
            return (Criteria) this;
        }

        public Criteria andDelIsNull() {
            addCriterion("del is null");
            return (Criteria) this;
        }

        public Criteria andDelIsNotNull() {
            addCriterion("del is not null");
            return (Criteria) this;
        }

        public Criteria andDelEqualTo(Boolean value) {
            addCriterion("del =", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelNotEqualTo(Boolean value) {
            addCriterion("del <>", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelGreaterThan(Boolean value) {
            addCriterion("del >", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelGreaterThanOrEqualTo(Boolean value) {
            addCriterion("del >=", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelLessThan(Boolean value) {
            addCriterion("del <", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelLessThanOrEqualTo(Boolean value) {
            addCriterion("del <=", value, "del");
            return (Criteria) this;
        }

        public Criteria andDelIn(List<Boolean> values) {
            addCriterion("del in", values, "del");
            return (Criteria) this;
        }

        public Criteria andDelNotIn(List<Boolean> values) {
            addCriterion("del not in", values, "del");
            return (Criteria) this;
        }

        public Criteria andDelBetween(Boolean value1, Boolean value2) {
            addCriterion("del between", value1, value2, "del");
            return (Criteria) this;
        }

        public Criteria andDelNotBetween(Boolean value1, Boolean value2) {
            addCriterion("del not between", value1, value2, "del");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModifierIsNull() {
            addCriterion("modifier is null");
            return (Criteria) this;
        }

        public Criteria andModifierIsNotNull() {
            addCriterion("modifier is not null");
            return (Criteria) this;
        }

        public Criteria andModifierEqualTo(String value) {
            addCriterion("modifier =", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierNotEqualTo(String value) {
            addCriterion("modifier <>", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierGreaterThan(String value) {
            addCriterion("modifier >", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierGreaterThanOrEqualTo(String value) {
            addCriterion("modifier >=", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierLessThan(String value) {
            addCriterion("modifier <", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierLessThanOrEqualTo(String value) {
            addCriterion("modifier <=", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierLike(String value) {
            addCriterion("modifier like", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierNotLike(String value) {
            addCriterion("modifier not like", value, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierIn(List<String> values) {
            addCriterion("modifier in", values, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierNotIn(List<String> values) {
            addCriterion("modifier not in", values, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierBetween(String value1, String value2) {
            addCriterion("modifier between", value1, value2, "modifier");
            return (Criteria) this;
        }

        public Criteria andModifierNotBetween(String value1, String value2) {
            addCriterion("modifier not between", value1, value2, "modifier");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNull() {
            addCriterion("tenant_id is null");
            return (Criteria) this;
        }

        public Criteria andTenantIdIsNotNull() {
            addCriterion("tenant_id is not null");
            return (Criteria) this;
        }

        public Criteria andTenantIdEqualTo(String value) {
            addCriterion("tenant_id =", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotEqualTo(String value) {
            addCriterion("tenant_id <>", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThan(String value) {
            addCriterion("tenant_id >", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdGreaterThanOrEqualTo(String value) {
            addCriterion("tenant_id >=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThan(String value) {
            addCriterion("tenant_id <", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLessThanOrEqualTo(String value) {
            addCriterion("tenant_id <=", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdLike(String value) {
            addCriterion("tenant_id like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotLike(String value) {
            addCriterion("tenant_id not like", value, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdIn(List<String> values) {
            addCriterion("tenant_id in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotIn(List<String> values) {
            addCriterion("tenant_id not in", values, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdBetween(String value1, String value2) {
            addCriterion("tenant_id between", value1, value2, "tenantId");
            return (Criteria) this;
        }

        public Criteria andTenantIdNotBetween(String value1, String value2) {
            addCriterion("tenant_id not between", value1, value2, "tenantId");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table id_mapping_relation
     *
     * @mbg.generated do_not_delete_during_merge
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table id_mapping_relation
     *
     * @mbg.generated
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}