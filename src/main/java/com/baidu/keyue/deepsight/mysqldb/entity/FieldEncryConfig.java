package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class FieldEncryConfig implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_encry_config.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_encry_config.table_en_name
     *
     * @mbg.generated
     */
    private String tableEnName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_encry_config.en_field
     *
     * @mbg.generated
     */
    private String enField;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_encry_config.secret_key
     *
     * @mbg.generated
     */
    private String secretKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_encry_config.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_encry_config.data_table_id
     *
     * @mbg.generated
     */
    private String dataTableId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table field_encry_config
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_encry_config.id
     *
     * @return the value of field_encry_config.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_encry_config.id
     *
     * @param id the value for field_encry_config.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_encry_config.table_en_name
     *
     * @return the value of field_encry_config.table_en_name
     *
     * @mbg.generated
     */
    public String getTableEnName() {
        return tableEnName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_encry_config.table_en_name
     *
     * @param tableEnName the value for field_encry_config.table_en_name
     *
     * @mbg.generated
     */
    public void setTableEnName(String tableEnName) {
        this.tableEnName = tableEnName == null ? null : tableEnName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_encry_config.en_field
     *
     * @return the value of field_encry_config.en_field
     *
     * @mbg.generated
     */
    public String getEnField() {
        return enField;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_encry_config.en_field
     *
     * @param enField the value for field_encry_config.en_field
     *
     * @mbg.generated
     */
    public void setEnField(String enField) {
        this.enField = enField == null ? null : enField.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_encry_config.secret_key
     *
     * @return the value of field_encry_config.secret_key
     *
     * @mbg.generated
     */
    public String getSecretKey() {
        return secretKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_encry_config.secret_key
     *
     * @param secretKey the value for field_encry_config.secret_key
     *
     * @mbg.generated
     */
    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey == null ? null : secretKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_encry_config.create_time
     *
     * @return the value of field_encry_config.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_encry_config.create_time
     *
     * @param createTime the value for field_encry_config.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_encry_config.data_table_id
     *
     * @return the value of field_encry_config.data_table_id
     *
     * @mbg.generated
     */
    public String getDataTableId() {
        return dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_encry_config.data_table_id
     *
     * @param dataTableId the value for field_encry_config.data_table_id
     *
     * @mbg.generated
     */
    public void setDataTableId(String dataTableId) {
        this.dataTableId = dataTableId == null ? null : dataTableId.trim();
    }
}