package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class TableFieldMetaInfo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.table_en_name
     *
     * @mbg.generated
     */
    private String tableEnName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.data_table_id
     *
     * @mbg.generated
     */
    private Long dataTableId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.en_field
     *
     * @mbg.generated
     */
    private String enField;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.cn_field
     *
     * @mbg.generated
     */
    private String cnField;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.field_type
     *
     * @mbg.generated
     */
    private String fieldType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.description
     *
     * @mbg.generated
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.is_filter_criteria
     *
     * @mbg.generated
     */
    private Boolean isFilterCriteria;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.is_required
     *
     * @mbg.generated
     */
    private Boolean isRequired;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.is_secrete
     *
     * @mbg.generated
     */
    private Boolean isSecrete;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.from_baidu
     *
     * @mbg.generated
     */
    private Boolean fromBaidu;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.is_visable
     *
     * @mbg.generated
     */
    private Boolean isVisable;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.field_tag
     *
     * @mbg.generated
     */
    private Integer fieldTag;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.value_type
     *
     * @mbg.generated
     */
    private String valueType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.data_type
     *
     * @mbg.generated
     */
    private String dataType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.is_show_value
     *
     * @mbg.generated
     */
    private Boolean isShowValue;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.number
     *
     * @mbg.generated
     */
    private Integer number;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column datatable_meta_info.config_infos
     *
     * @mbg.generated
     */
    private String configInfos;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table datatable_meta_info
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.id
     *
     * @return the value of datatable_meta_info.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.id
     *
     * @param id the value for datatable_meta_info.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.table_en_name
     *
     * @return the value of datatable_meta_info.table_en_name
     *
     * @mbg.generated
     */
    public String getTableEnName() {
        return tableEnName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.table_en_name
     *
     * @param tableEnName the value for datatable_meta_info.table_en_name
     *
     * @mbg.generated
     */
    public void setTableEnName(String tableEnName) {
        this.tableEnName = tableEnName == null ? null : tableEnName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.data_table_id
     *
     * @return the value of datatable_meta_info.data_table_id
     *
     * @mbg.generated
     */
    public Long getDataTableId() {
        return dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.data_table_id
     *
     * @param dataTableId the value for datatable_meta_info.data_table_id
     *
     * @mbg.generated
     */
    public void setDataTableId(Long dataTableId) {
        this.dataTableId = dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.en_field
     *
     * @return the value of datatable_meta_info.en_field
     *
     * @mbg.generated
     */
    public String getEnField() {
        return enField;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.en_field
     *
     * @param enField the value for datatable_meta_info.en_field
     *
     * @mbg.generated
     */
    public void setEnField(String enField) {
        this.enField = enField == null ? null : enField.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.cn_field
     *
     * @return the value of datatable_meta_info.cn_field
     *
     * @mbg.generated
     */
    public String getCnField() {
        return cnField;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.cn_field
     *
     * @param cnField the value for datatable_meta_info.cn_field
     *
     * @mbg.generated
     */
    public void setCnField(String cnField) {
        this.cnField = cnField == null ? null : cnField.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.field_type
     *
     * @return the value of datatable_meta_info.field_type
     *
     * @mbg.generated
     */
    public String getFieldType() {
        return fieldType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.field_type
     *
     * @param fieldType the value for datatable_meta_info.field_type
     *
     * @mbg.generated
     */
    public void setFieldType(String fieldType) {
        this.fieldType = fieldType == null ? null : fieldType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.description
     *
     * @return the value of datatable_meta_info.description
     *
     * @mbg.generated
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.description
     *
     * @param description the value for datatable_meta_info.description
     *
     * @mbg.generated
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.is_filter_criteria
     *
     * @return the value of datatable_meta_info.is_filter_criteria
     *
     * @mbg.generated
     */
    public Boolean getIsFilterCriteria() {
        return isFilterCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.is_filter_criteria
     *
     * @param isFilterCriteria the value for datatable_meta_info.is_filter_criteria
     *
     * @mbg.generated
     */
    public void setIsFilterCriteria(Boolean isFilterCriteria) {
        this.isFilterCriteria = isFilterCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.is_required
     *
     * @return the value of datatable_meta_info.is_required
     *
     * @mbg.generated
     */
    public Boolean getIsRequired() {
        return isRequired;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.is_required
     *
     * @param isRequired the value for datatable_meta_info.is_required
     *
     * @mbg.generated
     */
    public void setIsRequired(Boolean isRequired) {
        this.isRequired = isRequired;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.is_secrete
     *
     * @return the value of datatable_meta_info.is_secrete
     *
     * @mbg.generated
     */
    public Boolean getIsSecrete() {
        return isSecrete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.is_secrete
     *
     * @param isSecrete the value for datatable_meta_info.is_secrete
     *
     * @mbg.generated
     */
    public void setIsSecrete(Boolean isSecrete) {
        this.isSecrete = isSecrete;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.from_baidu
     *
     * @return the value of datatable_meta_info.from_baidu
     *
     * @mbg.generated
     */
    public Boolean getFromBaidu() {
        return fromBaidu;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.from_baidu
     *
     * @param fromBaidu the value for datatable_meta_info.from_baidu
     *
     * @mbg.generated
     */
    public void setFromBaidu(Boolean fromBaidu) {
        this.fromBaidu = fromBaidu;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.is_visable
     *
     * @return the value of datatable_meta_info.is_visable
     *
     * @mbg.generated
     */
    public Boolean getIsVisable() {
        return isVisable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.is_visable
     *
     * @param isVisable the value for datatable_meta_info.is_visable
     *
     * @mbg.generated
     */
    public void setIsVisable(Boolean isVisable) {
        this.isVisable = isVisable;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.field_tag
     *
     * @return the value of datatable_meta_info.field_tag
     *
     * @mbg.generated
     */
    public Integer getFieldTag() {
        return fieldTag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.field_tag
     *
     * @param fieldTag the value for datatable_meta_info.field_tag
     *
     * @mbg.generated
     */
    public void setFieldTag(Integer fieldTag) {
        this.fieldTag = fieldTag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.create_time
     *
     * @return the value of datatable_meta_info.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.create_time
     *
     * @param createTime the value for datatable_meta_info.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.update_time
     *
     * @return the value of datatable_meta_info.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.update_time
     *
     * @param updateTime the value for datatable_meta_info.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.value_type
     *
     * @return the value of datatable_meta_info.value_type
     *
     * @mbg.generated
     */
    public String getValueType() {
        return valueType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.value_type
     *
     * @param valueType the value for datatable_meta_info.value_type
     *
     * @mbg.generated
     */
    public void setValueType(String valueType) {
        this.valueType = valueType == null ? null : valueType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.data_type
     *
     * @return the value of datatable_meta_info.data_type
     *
     * @mbg.generated
     */
    public String getDataType() {
        return dataType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.data_type
     *
     * @param dataType the value for datatable_meta_info.data_type
     *
     * @mbg.generated
     */
    public void setDataType(String dataType) {
        this.dataType = dataType == null ? null : dataType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.is_show_value
     *
     * @return the value of datatable_meta_info.is_show_value
     *
     * @mbg.generated
     */
    public Boolean getIsShowValue() {
        return isShowValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.is_show_value
     *
     * @param isShowValue the value for datatable_meta_info.is_show_value
     *
     * @mbg.generated
     */
    public void setIsShowValue(Boolean isShowValue) {
        this.isShowValue = isShowValue;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.number
     *
     * @return the value of datatable_meta_info.number
     *
     * @mbg.generated
     */
    public Integer getNumber() {
        return number;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.number
     *
     * @param number the value for datatable_meta_info.number
     *
     * @mbg.generated
     */
    public void setNumber(Integer number) {
        this.number = number;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column datatable_meta_info.config_infos
     *
     * @return the value of datatable_meta_info.config_infos
     *
     * @mbg.generated
     */
    public String getConfigInfos() {
        return configInfos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column datatable_meta_info.config_infos
     *
     * @param configInfos the value for datatable_meta_info.config_infos
     *
     * @mbg.generated
     */
    public void setConfigInfos(String configInfos) {
        this.configInfos = configInfos == null ? null : configInfos.trim();
    }
}