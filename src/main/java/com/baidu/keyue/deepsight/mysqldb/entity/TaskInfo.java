package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class TaskInfo implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_info.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_info.task_type
     *
     * @mbg.generated
     */
    private Byte taskType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_info.task_desc
     *
     * @mbg.generated
     */
    private String taskDesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_info.trigger_cron
     *
     * @mbg.generated
     */
    private String triggerCron;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_info.next_exec_date
     *
     * @mbg.generated
     */
    private Date nextExecDate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_info.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_info.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_info.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_info.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_info.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_info.trigger_mod
     *
     * @mbg.generated
     */
    private Byte triggerMod;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column task_info.task_conf
     *
     * @mbg.generated
     */
    private String taskConf;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table task_info
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_info.id
     *
     * @return the value of task_info.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_info.id
     *
     * @param id the value for task_info.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_info.task_type
     *
     * @return the value of task_info.task_type
     *
     * @mbg.generated
     */
    public Byte getTaskType() {
        return taskType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_info.task_type
     *
     * @param taskType the value for task_info.task_type
     *
     * @mbg.generated
     */
    public void setTaskType(Byte taskType) {
        this.taskType = taskType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_info.task_desc
     *
     * @return the value of task_info.task_desc
     *
     * @mbg.generated
     */
    public String getTaskDesc() {
        return taskDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_info.task_desc
     *
     * @param taskDesc the value for task_info.task_desc
     *
     * @mbg.generated
     */
    public void setTaskDesc(String taskDesc) {
        this.taskDesc = taskDesc == null ? null : taskDesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_info.trigger_cron
     *
     * @return the value of task_info.trigger_cron
     *
     * @mbg.generated
     */
    public String getTriggerCron() {
        return triggerCron;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_info.trigger_cron
     *
     * @param triggerCron the value for task_info.trigger_cron
     *
     * @mbg.generated
     */
    public void setTriggerCron(String triggerCron) {
        this.triggerCron = triggerCron == null ? null : triggerCron.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_info.next_exec_date
     *
     * @return the value of task_info.next_exec_date
     *
     * @mbg.generated
     */
    public Date getNextExecDate() {
        return nextExecDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_info.next_exec_date
     *
     * @param nextExecDate the value for task_info.next_exec_date
     *
     * @mbg.generated
     */
    public void setNextExecDate(Date nextExecDate) {
        this.nextExecDate = nextExecDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_info.del
     *
     * @return the value of task_info.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_info.del
     *
     * @param del the value for task_info.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_info.creator
     *
     * @return the value of task_info.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_info.creator
     *
     * @param creator the value for task_info.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_info.modifier
     *
     * @return the value of task_info.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_info.modifier
     *
     * @param modifier the value for task_info.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_info.create_time
     *
     * @return the value of task_info.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_info.create_time
     *
     * @param createTime the value for task_info.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_info.update_time
     *
     * @return the value of task_info.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_info.update_time
     *
     * @param updateTime the value for task_info.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_info.trigger_mod
     *
     * @return the value of task_info.trigger_mod
     *
     * @mbg.generated
     */
    public Byte getTriggerMod() {
        return triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_info.trigger_mod
     *
     * @param triggerMod the value for task_info.trigger_mod
     *
     * @mbg.generated
     */
    public void setTriggerMod(Byte triggerMod) {
        this.triggerMod = triggerMod;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column task_info.task_conf
     *
     * @return the value of task_info.task_conf
     *
     * @mbg.generated
     */
    public String getTaskConf() {
        return taskConf;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column task_info.task_conf
     *
     * @param taskConf the value for task_info.task_conf
     *
     * @mbg.generated
     */
    public void setTaskConf(String taskConf) {
        this.taskConf = taskConf == null ? null : taskConf.trim();
    }
}