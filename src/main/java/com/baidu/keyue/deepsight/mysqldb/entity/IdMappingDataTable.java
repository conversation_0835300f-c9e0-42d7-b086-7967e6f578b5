package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

public class IdMappingDataTable implements Serializable {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_datatable.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_datatable.data_table_id
     *
     * @mbg.generated
     */
    private Long dataTableId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_datatable.del
     *
     * @mbg.generated
     */
    private Boolean del;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_datatable.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_datatable.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_datatable.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_datatable.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_datatable.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column id_mapping_datatable.preset
     *
     * @mbg.generated
     */
    private Boolean preset;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table id_mapping_datatable
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_datatable.id
     *
     * @return the value of id_mapping_datatable.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_datatable.id
     *
     * @param id the value for id_mapping_datatable.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_datatable.data_table_id
     *
     * @return the value of id_mapping_datatable.data_table_id
     *
     * @mbg.generated
     */
    public Long getDataTableId() {
        return dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_datatable.data_table_id
     *
     * @param dataTableId the value for id_mapping_datatable.data_table_id
     *
     * @mbg.generated
     */
    public void setDataTableId(Long dataTableId) {
        this.dataTableId = dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_datatable.del
     *
     * @return the value of id_mapping_datatable.del
     *
     * @mbg.generated
     */
    public Boolean getDel() {
        return del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_datatable.del
     *
     * @param del the value for id_mapping_datatable.del
     *
     * @mbg.generated
     */
    public void setDel(Boolean del) {
        this.del = del;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_datatable.creator
     *
     * @return the value of id_mapping_datatable.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_datatable.creator
     *
     * @param creator the value for id_mapping_datatable.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_datatable.modifier
     *
     * @return the value of id_mapping_datatable.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_datatable.modifier
     *
     * @param modifier the value for id_mapping_datatable.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_datatable.create_time
     *
     * @return the value of id_mapping_datatable.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_datatable.create_time
     *
     * @param createTime the value for id_mapping_datatable.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_datatable.update_time
     *
     * @return the value of id_mapping_datatable.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_datatable.update_time
     *
     * @param updateTime the value for id_mapping_datatable.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_datatable.tenant_id
     *
     * @return the value of id_mapping_datatable.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_datatable.tenant_id
     *
     * @param tenantId the value for id_mapping_datatable.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column id_mapping_datatable.preset
     *
     * @return the value of id_mapping_datatable.preset
     *
     * @mbg.generated
     */
    public Boolean getPreset() {
        return preset;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column id_mapping_datatable.preset
     *
     * @param preset the value for id_mapping_datatable.preset
     *
     * @mbg.generated
     */
    public void setPreset(Boolean preset) {
        this.preset = preset;
    }
}