package com.baidu.keyue.deepsight.mysqldb.entity;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table field_show_config
 *
 * @mbg.generated do_not_delete_during_merge
 */
public class FieldShowConfig implements Serializable {
    /**
     * Database Column Remarks:
     *   id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_show_config.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   数据集ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_show_config.data_table_id
     *
     * @mbg.generated
     */
    private Long dataTableId;

    /**
     * Database Column Remarks:
     *   租户ID
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_show_config.tenant_id
     *
     * @mbg.generated
     */
    private String tenantId;

    /**
     * Database Column Remarks:
     *   创建人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_show_config.creator
     *
     * @mbg.generated
     */
    private String creator;

    /**
     * Database Column Remarks:
     *   更新人
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_show_config.modifier
     *
     * @mbg.generated
     */
    private String modifier;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_show_config.create_time
     *
     * @mbg.generated
     */
    private Date createTime;

    /**
     * Database Column Remarks:
     *   更新时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_show_config.update_time
     *
     * @mbg.generated
     */
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   展示列，逗号分隔
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column field_show_config.show_field
     *
     * @mbg.generated
     */
    private String showField;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table field_show_config
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_show_config.id
     *
     * @return the value of field_show_config.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_show_config.id
     *
     * @param id the value for field_show_config.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_show_config.data_table_id
     *
     * @return the value of field_show_config.data_table_id
     *
     * @mbg.generated
     */
    public Long getDataTableId() {
        return dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_show_config.data_table_id
     *
     * @param dataTableId the value for field_show_config.data_table_id
     *
     * @mbg.generated
     */
    public void setDataTableId(Long dataTableId) {
        this.dataTableId = dataTableId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_show_config.tenant_id
     *
     * @return the value of field_show_config.tenant_id
     *
     * @mbg.generated
     */
    public String getTenantId() {
        return tenantId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_show_config.tenant_id
     *
     * @param tenantId the value for field_show_config.tenant_id
     *
     * @mbg.generated
     */
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId == null ? null : tenantId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_show_config.creator
     *
     * @return the value of field_show_config.creator
     *
     * @mbg.generated
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_show_config.creator
     *
     * @param creator the value for field_show_config.creator
     *
     * @mbg.generated
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_show_config.modifier
     *
     * @return the value of field_show_config.modifier
     *
     * @mbg.generated
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_show_config.modifier
     *
     * @param modifier the value for field_show_config.modifier
     *
     * @mbg.generated
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_show_config.create_time
     *
     * @return the value of field_show_config.create_time
     *
     * @mbg.generated
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_show_config.create_time
     *
     * @param createTime the value for field_show_config.create_time
     *
     * @mbg.generated
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_show_config.update_time
     *
     * @return the value of field_show_config.update_time
     *
     * @mbg.generated
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_show_config.update_time
     *
     * @param updateTime the value for field_show_config.update_time
     *
     * @mbg.generated
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column field_show_config.show_field
     *
     * @return the value of field_show_config.show_field
     *
     * @mbg.generated
     */
    public String getShowField() {
        return showField;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column field_show_config.show_field
     *
     * @param showField the value for field_show_config.show_field
     *
     * @mbg.generated
     */
    public void setShowField(String showField) {
        this.showField = showField == null ? null : showField.trim();
    }
}