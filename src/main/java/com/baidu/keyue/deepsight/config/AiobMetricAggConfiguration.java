package com.baidu.keyue.deepsight.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "aiob-agg")
public class AiobMetricAggConfiguration {
    /**
     * 是否启用聚合分析
     */
    private Boolean status;
    /**
     * 统计覆盖天数，默认 90天
     */
    private Integer day;
}
