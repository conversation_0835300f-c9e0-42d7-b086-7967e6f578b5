package com.baidu.keyue.deepsight.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @ClassName BosConfig
 * @Description BOS 配置
 * <AUTHOR>
 * @Date 2025/3/12 4:27 PM
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "bos")
public class BosConfig {
    private String accessKeyId;
    private String secretAccessKey;
    private String endpoint;
    private String env;
    private Bucket bucket;
    private String stsUrl;
    
    @Data
    public static class Bucket{
        private String dataSync;
        private String diffusion;
    }
}
