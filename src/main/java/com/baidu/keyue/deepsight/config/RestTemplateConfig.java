package com.baidu.keyue.deepsight.config;


import com.baidu.keyue.deepsight.safesdk.SafeHttpRequestFactory;
import com.baidu.keyue.deepsight.safesdk.SsrfProtectionInterceptor;
import com.baidu.keyue.deepsight.safesdk.SsrfUrlValidator;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.netty.http.client.HttpClient;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class RestTemplateConfig {
    
    @Resource
    private SafeHttpRequestFactory safeHttpRequestFactory;
    @Resource
    private SsrfProtectionInterceptor ssrfProtectionInterceptor;

    @Value("${maxHeaderSize:32}")
    private int maxHeaderSize;
    @Resource
    private SsrfUrlValidator ssrfUrlValidator;

    /**
     * 创建RestTemplate对象
     *
     * @return RestTemplate类型的对象实例
     */
    @Bean
    public RestTemplate restTemplate() {
        RestTemplate restTemplate = new RestTemplate(safeHttpRequestFactory);
        restTemplate.getMessageConverters().set(1, new StringHttpMessageConverter(StandardCharsets.UTF_8));
        Map<String, String> header = new HashMap<>();
        // header.put("Content-Type", "application/json");
        // ssrf安全改造
        restTemplate.getInterceptors().add(ssrfProtectionInterceptor);
        return restTemplate;
    }
    @Bean
    public WebClient webClient() {
        HttpClient httpClient =
                HttpClient.create().httpResponseDecoder(spec -> spec.maxHeaderSize(maxHeaderSize * 1024));
        return WebClient.builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .filter((request, next) -> {
                    URI uri = request.url();
                    ssrfUrlValidator.validateUrl(uri.toString());
                    return next.exchange(request);
                })
                .baseUrl("https://qianfan.baidubce.com")
                .build();
    }


}
