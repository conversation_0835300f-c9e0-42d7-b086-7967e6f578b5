package com.baidu.keyue.deepsight.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @className: CustomerCalculateConfiguration
 * @description: 客户计算配置类
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2025/1/16 16:50
 */
@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "customer-calculate")
public class CustomerCalculateConfiguration {
    /**
     * 抽样数量
     */
    private Integer sampleNumber = 1000;
}
