package com.baidu.keyue.deepsight.config;

import com.baidu.keyue.deepsight.models.bsc.basic.BscKafkaConfig;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "memory-calculate")
public class MemoryCalculateConfiguration {

    // 模型接口
    private String modelUrl;

    // bsc kafka 配置
    private BscKafkaConfig kafkaConfig;

}
