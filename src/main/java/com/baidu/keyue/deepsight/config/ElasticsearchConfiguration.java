package com.baidu.keyue.deepsight.config;


import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @className ESConfig
 * @description es配置
 * @date 2025/1/8 17:00
 */
@Configuration
public class ElasticsearchConfiguration {

    @Value("${spring.data.elasticsearch.client.reactive.hosts:*************}")
    private String esHost;
    @Value("${spring.data.elasticsearch.client.reactive.port:8203}")
    private int esPort;
    @Value("${spring.data.elasticsearch.client.reactive.username:elastic}")
    private String username;
    @Value("${spring.data.elasticsearch.client.reactive.password:bwfCP6CEQqKQ7F37ZbXH}")
    private String password;




    @Bean
    public RestHighLevelClient restHighLevelClient() {
        HttpHost httpHost = new HttpHost(esHost, esPort, HttpHost.DEFAULT_SCHEME_NAME);
        if (StringUtils.isBlank(username)) {
            // 用户名为空
            RestHighLevelClient client = new RestHighLevelClient(
                    RestClient.builder(httpHost
                    )
            );
            return client;
        } else {
            RestClientBuilder builder = RestClient.builder(httpHost);
            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY,
                    new UsernamePasswordCredentials(username, password));
            builder.setHttpClientConfigCallback(httpClientBuilder ->
                    httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider)
            );
            RestHighLevelClient client = new RestHighLevelClient(builder);
            return client;
        }



    }

//    @Bean
//    public RestHighLevelClient restHighLevelClient() {
//        // AUTHORIZATION鉴权配置
//        Header[] headers = new Header[] {
//                new BasicHeader(HttpHeaders.AUTHORIZATION, "Basic cG9ydHJheTpwb3J0cmF5XzIwMjU=")
//        };
//
//        RestClientBuilder builder = RestClient.builder(new HttpHost("***********", 8200))
//                .setDefaultHeaders(headers);
//
//        return new RestHighLevelClient(builder);
//    }




}
