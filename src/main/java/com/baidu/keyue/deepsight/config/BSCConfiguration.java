package com.baidu.keyue.deepsight.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(prefix = "bsc")
public class BSCConfiguration {
    private String endpoint;
    private String vpcId;
    private String subnetId;
    private String logicalZone;
    private String securityGroupId;
    private String cidr;
    private String jobPrefix;
}
