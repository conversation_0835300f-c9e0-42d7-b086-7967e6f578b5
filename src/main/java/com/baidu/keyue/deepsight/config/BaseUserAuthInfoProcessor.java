package com.baidu.keyue.deepsight.config;

/**
 * @ClassName BaseUserAuthInfoProcessor
 * @Description 继承基座的用户管理类，注册至springboot里
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/3/6 20:25
 */

import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.utils.XID;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import com.baidu.kybase.sdk.user.handler.UserAuthProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class BaseUserAuthInfoProcessor implements UserAuthProcessor {

    @Autowired
    private TenantInfoService tenantInfoService;

    @Value("${app-version}")
    private Integer apiVersion;

    /**
     * 会话用户信息处理
     * @param userAuthInfo
     */
    @Override
    public void before(UserAuthInfo userAuthInfo) {
        DeepSightWebContext deepSightWebContext = new DeepSightWebContext(userAuthInfo);
        String requestId = XID.generateRequestID();
        deepSightWebContext.setRequestId(requestId);

        String tenantId = String.valueOf(userAuthInfo.getTenantId());
        log.debug("tenant login success.tenantId is {}", tenantId);
        // 设置全局租户信息
        WebContextHolder.setDeepSightWebContext(deepSightWebContext);
        TenantInfo tenantInfo = tenantInfoService.queryTenantInfo(tenantId);
        
        // 租户信息查不到，则执行初始化租户逻辑
        if (ObjectUtils.isEmpty(tenantInfo) || !Objects.equals(tenantInfo.getVersion(), apiVersion)) {
            TenantDTO tenantDTO = new TenantDTO();
            tenantDTO.setTenantId(tenantId);
            tenantDTO.setAuthInfo(userAuthInfo);
            tenantDTO.setType(Constants.TENANT_LOGIN_TYPE);
            tenantDTO.setTenantInfo(tenantInfo);
            try {
                tenantInfoService.initOrUpgradeTenant(tenantDTO);
            } catch (Exception exception) {
                log.error("租户{}初始化或升级失败, exception:", tenantId, exception);
            }
        }
    }

    /**
     * 数据清理
     * 注：userAuthInfo不为null才会执行清理
     */
    @Override
    public void after() {
        // 应用方的清理逻辑
        WebContextHolder.clean();
        MDC.clear();
    }
}