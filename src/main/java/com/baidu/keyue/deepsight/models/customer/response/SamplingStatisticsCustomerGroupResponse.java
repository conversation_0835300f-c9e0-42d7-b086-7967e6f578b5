package com.baidu.keyue.deepsight.models.customer.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: SamplingStatisticsCustomerGroupResponse
 * @description: 抽样统计客群人数
 * @author: wangzhongcheng
 * @date: 2025/2/28 17:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SamplingStatisticsCustomerGroupResponse {

    private long count;

}
