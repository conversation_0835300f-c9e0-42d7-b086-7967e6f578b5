package com.baidu.keyue.deepsight.models.customer.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: SamplingStatisticsCustomerGroupRequest
 * @description: 抽样统计客群人数
 * @author: wangz<PERSON><PERSON>
 * @date: 2025/2/28 17:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SamplingStatisticsCustomerGroupRequest {

    @NotNull(message = "客群ID不能为空")
    private Long customerGroupId;

}
