package com.baidu.keyue.deepsight.models.customer.request;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName CustomerGroupAnalysisRequest
 * @Description 客群分析请求体
 * <AUTHOR> (<EMAIL>)
 * @Date 2024/12/30 20:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ListCustomerGroupAnalysisRequest extends BasePageRequest {

    /**
     * 客群ID列表
     */
    private List<Long> customerGroupIds;

    /**
     * 客群名称
     */
    private String customerGroupName;
}