package com.baidu.keyue.deepsight.models.sop.aiob;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName AiobDiagramVersionRecordResp
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/6/9 11:11
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiobDiagramVersionRecordResp {
    private int code;
    private String msg;
    private long time;
    private List<AiobDiagramVersionRecordResp.AiobDiagramRecordData> data;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AiobDiagramRecordData {
        /**
         * 版本id
         */
        private String id;
        /**
         * 机器人id
         */
        private String agentId;
        /**
         * 版本号
         */
        private String versionName;
        /**
         * 创建时间
         */
        private String createTime;
        /**
         * 创建人id
         */
        private String createUserId;
        /**
         * 创建人名称
         */
        private String createUserName;
        /**
         * 发布状态
         */
        private int publishStatus;
        /**
         * 是否为线上版本
         */
        private boolean onlineVersion;
    }
    /**
     * 是否请求成功
     */
    public Boolean isSuccess() {
        return code == 200;
    }
}
