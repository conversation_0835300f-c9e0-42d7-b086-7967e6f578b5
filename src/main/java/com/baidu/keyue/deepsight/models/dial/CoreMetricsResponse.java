package com.baidu.keyue.deepsight.models.dial;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CoreMetricsResponse {
    /**
     * 昨日拨打次数
     */
    private Long yesterdayCallCount;
    /**
     * 昨日接通次数
     */
    private Long yesterdayConnectedCount;
    /**
     * 昨日接通率
     */
    private Integer yesterdayConnectionRate;

    /**
     * 近30天触发告警天数
     */
    private Integer last30DaysAlertDays;
}
