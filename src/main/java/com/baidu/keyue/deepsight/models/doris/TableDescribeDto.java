package com.baidu.keyue.deepsight.models.doris;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TableDescribeDto {
    private String fieldName;
    private String fieldType;
    private Boolean nullable;
    private Boolean keyField;
    private String defaultValue;

    public static TableDescribeDto fromMap(Map<String, Object> map) {
        TableDescribeDto d = new TableDescribeDto();
        d.setFieldName(String.valueOf(map.get("Field")));
        d.setFieldType(String.valueOf(map.get("Type")));
        d.setNullable("YES".equals(String.valueOf(map.get("Null"))));
        d.setKeyField("true".equals(String.valueOf(map.get("Key"))));
        d.setDefaultValue(String.valueOf(map.get("Default")));
        return d;
    }

    public Boolean defaultNull() {
        return "NULL".equals(defaultValue) || "null".equals(defaultValue);
    }

    public Boolean defaultBlankString() {
        return "".equals(defaultValue);
    }

    public Boolean defaultBlankArray() {
        return "[]".equals(defaultValue);
    }
}
