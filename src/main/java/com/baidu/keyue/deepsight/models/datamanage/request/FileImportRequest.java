package com.baidu.keyue.deepsight.models.datamanage.request;

import com.baidu.keyue.deepsight.enums.ImportMappingTypeEnum;
import com.baidu.keyue.deepsight.enums.ImportTypeEnum;
import com.baidu.keyue.deepsight.models.datamanage.dto.FieldMappingDto;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * @ClassName FileImportDataRequest
 * @Description 文件导入数据请求体
 * <AUTHOR>
 * @Date 2025/2/12 3:35 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class FileImportRequest {

    /**
     * 数据集ID
     */
    @NotNull(message = "数据集ID不能为空")
    private Long dataTableId;

    /**
     * 数据写入模式：COVER-覆盖式写入，INCREMENT-增量式写入（默认）
     */
    private ImportTypeEnum importTypeEnum = ImportTypeEnum.INCREMENT;

    /**
     * 字段映射模式：EQUAL_NAME-同名映射（默认），AI_SEMANTEME-AI语义映射
     */
    private ImportMappingTypeEnum mappingTypeEnum = ImportMappingTypeEnum.EQUAL_NAME;

    /**
     * 文件ids
     */
    @NotNull
    @NotBlank(message = "任务组ID不能为空")
    private String groupId;

    /**
     * 字段映射
     */
    @NotEmpty(message = "字段映射不能为空")
    private List<FieldMappingDto> fieldMappings;
}
