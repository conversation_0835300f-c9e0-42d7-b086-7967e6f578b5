package com.baidu.keyue.deepsight.models.datamanage.dto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

/**
 * @ClassName FileDetailDto
 * @Description 上传文件信息
 * <AUTHOR>
 * @Date 2025/3/12 4:50 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
public class FileDetailDto {
    /**
     * 原始文件名
     */
    @NotBlank(message = "文件名不能为空")
    private String fileName;
    /**
     * BOS文件key：/dev/20250312/xxxx.csv
     */
    @NotBlank(message = "文件BOS key不能为空")
    private String bosKey;
}
