package com.baidu.keyue.deepsight.models.dial;

import cn.hutool.core.date.DateTime;
import com.baidu.keyue.deepsight.enums.DateLineTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConnectionRateTrendRequest {
    /**
     * 时间类型：hour / day
     */
    @NotNull(message = "时间类型不能为空")
    private DateLineTypeEnum type;

    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    
    /**
     * 目标字段名：callerNum/taskId/robotId
     */
    private String targetFieldName;
    /**
     * 对应目标字段值
     */
    private List<String> configTargets;

    public ConnectionRateTrendRequest(DateLineTypeEnum type) {
        this.type = type;
    }

    public ConnectionRateTrendRequest(DateLineTypeEnum type, DateTime startTime, DateTime endTime) {
        this.type = type;
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
