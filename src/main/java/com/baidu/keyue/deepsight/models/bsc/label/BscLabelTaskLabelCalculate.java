package com.baidu.keyue.deepsight.models.bsc.label;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BscLabelTaskLabelCalculate {
    private String labelValue;
    private String ruleSql;
    private Integer priority;

    public BscLabelTaskLabelCalculate(String sql) {
        this.ruleSql = sql;
    }
}
