package com.baidu.keyue.deepsight.models.datamanage.dto;

import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * @ClassName RowDataDto
 * @Description 文件解析行数据
 * <AUTHOR>
 * @Date 2025/2/17 7:00 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RowDataDto {

    @JsonIgnore
    private final Set<String> boolSet = new HashSet<>(Arrays.asList("0", "1", "true", "false", "TRUE", "FALSE"));

    /**
     * 字段英文名
     */
    private String enName;

    /**
     * 字段中文名
     */
    private String cnName;

    /**
     * 字段值
     */
    private Object data;

    /**
     * 字段类型，目前只能判断int\bigint\boolean\string
     */
    private String dataType;


    public RowDataDto(String enName, String cnName, Object data) {
        this.enName = enName;
        this.cnName = cnName;
        this.data = data;
        this.dataType = getDataType(data);
    }

    public String getDataType(Object data) {
        if (data == null) {
            return "string";
        }
        String dataStr = data.toString();
        if (dataStr.contains(".")) {
            try {
                Double.parseDouble(dataStr);
                return "double";
            } catch (Exception ignored) {
            }
        }
        if (boolSet.contains(dataStr)) {
            return "boolean";
        }
        try {
            Integer.parseInt(dataStr);
            return "int";
        } catch (Exception ignored) {
        }
        try {
            Long.parseLong(dataStr);
            return "bigint";
        } catch (Exception ignored) {
        }
        if (JSONUtil.isTypeJSONArray(dataStr)) {
            return "array";
        }
        if (JSONUtil.isTypeJSON(dataStr)) {
            return "json";
        }
        return "string";
    }
}
