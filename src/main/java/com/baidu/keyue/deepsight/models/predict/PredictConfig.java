package com.baidu.keyue.deepsight.models.predict;

import com.baidu.keyue.deepsight.enums.PredictTypeEnum;
import com.baidu.keyue.deepsight.enums.SwitchEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PredictConfig {
    /**
     * 预测内容ID
     */
    private Long id;

    /**
     * 预测内容
     */
    private String predictType;

    /**
     * 启用状态
     */
    private SwitchEnum status;

    /**
     * 预测使用的数据集名称，多个用 、 分隔
     */
    private String datasetsName;

    /**
     * 预测使用的字段名称，多个用 、 分隔
     */
    private String fieldsName;

    /**
     * 预测方法
     */
    private String predictMethod = "大模型分析";

    /**
     * 描述
     */
    private String description;
}
