package com.baidu.keyue.deepsight.models.sop.aiob;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * *@Author: dongjiacheng01
 * *@Description:
 * *@Date: 17:16 2025/5/26
 */
@Data
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiobDiagramVersionRecordViewReq {

    private String versionId;

    private String agentId;

}
