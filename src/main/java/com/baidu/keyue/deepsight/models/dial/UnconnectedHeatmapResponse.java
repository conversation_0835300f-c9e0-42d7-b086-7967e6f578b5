package com.baidu.keyue.deepsight.models.dial;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UnconnectedHeatmapResponse {
    /**
     * 未接通总次数
     */
    private Integer totalUnconnectedCount;

    /**
     * 热力图数据（7天×24小时矩阵）
     * 外层List表示星期（0=周日，6=周六）
     * 内层List表示小时（0-23）
     */
    private List<List<Integer>> heatmapData;

    /**
     * 小时标签列表
     */
    private final List<String> hourLabels = IntStream.range(0, 24)
            .mapToObj(h -> String.format("%02d:00", h)).toList();

    /**
     * 星期标签列表
     */
    private final List<String> dayLabels = Arrays.asList(
            "周日", "周一", "周二", "周三", "周四", "周五", "周六");

    public UnconnectedHeatmapResponse(List<Map<String, Object>> list) {
        heatmapData = IntStream.range(0, 7)
                .mapToObj(i -> IntStream.range(0, 24)
                        .mapToObj(j -> 0)
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());
        totalUnconnectedCount = 0;
        for (Map<String, Object> map : list) {
            Integer weekday = (Integer) map.get("weekday");
            Integer hour = (Integer) map.get("hour");
            int count = ((Long) map.get("count")).intValue();
            heatmapData.get(weekday % 7).set(hour, count);
            totalUnconnectedCount += count;
        }
    }
}
