package com.baidu.keyue.deepsight.models.label;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collections;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LabelDistribute {
    private static final Logger LOG = LoggerFactory.getLogger(LabelDistribute.class);

    /**
     * 覆盖率占比，eg: "8.50%"
     */
    private String coverageRate;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 命中记录数
     */
    private List<LabelDistributeDetail> labels;

    /**
     * 构建空结果查询(无标签计算记录)
     * 构建基础信息、标签值星系、命中记录数初始化信息
     * @param label 标签信息
     * @return
     */
    public static LabelDistribute buildEmpty(LabelWithBLOBs label) {
        LabelDistribute labelDistribute = new LabelDistribute();
        labelDistribute.setTotal(0L);
        labelDistribute.setCoverageRate("0.00%");
        labelDistribute.setLabels(Collections.emptyList());
        try {
            LabelRule labelRule = JSONUtil.toBean(label.getLabelRule(), LabelRule.class);
            List<LabelValueRule> labelValueRules = labelRule.getLabelValueRules();
            if (CollUtil.isNotEmpty(labelValueRules)) {
                List<LabelDistributeDetail> labels = labelValueRules.stream()
                        .map(rule -> new LabelDistributeDetail(rule.getLabelValue(), 0L, "0.0%"))
                        .toList();
                labelDistribute.setLabels(labels);
            }
        } catch (Exception exception) {
            LOG.error("解析标签规则映射标签命中记录数异常:", exception);
        }
        return labelDistribute;
    }
}
