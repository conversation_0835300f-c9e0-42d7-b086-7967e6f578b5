package com.baidu.keyue.deepsight.models.bsc.inner;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BscJob {
    /**
     * 作业ID
     */
    private String jobId;
    /**
     * 作业名称，只能包含大小写英文字母，数字，下划线_，横行-
     */
    private String name;
    /**
     * 作业类型
     */
    private String type;
    /**
     * SQL or JAR
     */
    private String apiType;
    /**
     * STREAM or BATCH
     */
    private String jobType;
    /**
     * FLINK or SPARK
     */
    private String engineType;
    /**
     * 引擎版本
     */
    private String engineVersion;
    /**
     * base64编码之后的code
     */
    private String code;
    /**
     * 作业属性
     */
//    private Object properties;
    /**
     * 作业所在地域,如保定区域为bd
     */
//    private String region;
    /**
     * 创建人
     */
//    private String createBy;
    /**
     * 创建时间
     */
//    private Date createAt;
    /**
     * 更新人
     */
//    private String updateBy;
    /**
     * 更新时间
     */
//    private Date updateAt;
    /**
     * 删除时间
     */
//    private Date deleteAt;
    /**
     * 是否能提交
     */
//    private Boolean canCommit;
    /**
     * 账户名称
     */
    private String accountId;
    /**
     * 作业描述
     */
    private String description;
}
