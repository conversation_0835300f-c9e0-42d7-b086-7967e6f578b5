package com.baidu.keyue.deepsight.models.catalog;

import java.util.List;

import com.baidu.keyue.deepsight.models.label.LabelBrief;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CatalogDetail {
    /**
     * 标签目录 id
     */
    private Long catalogId;
    /**
     * 标签目录名
     */
    private String catalogName;
    /**
     * 标签数量统计
     */
    private Integer count;
    /**
     * 子级标签目录
     */
    private List<CatalogDetail> children;

    /**
     * 当前目录下的标签
     */
    private List<LabelBrief> labels;
}
