package com.baidu.keyue.deepsight.models.customer.response;

import com.baidu.keyue.deepsight.enums.GroupingTypeEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName CustomerGroupAnalysisItemResponse
 * @Description 客群分析返回体
 * <AUTHOR> (<EMAIL>)
 * @Date 2024/12/30 20:04
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerGroupAnalysisItemResponse {

    /**
     * 客群id
     */
    private Long customerGroupId;

    /**
     * 客群名称
     */
    private String customerGroupName;

    /**
     * 状态
     */
    private TaskExecStatusEnum status;

    /**
     * 失败原因
     */
    private String errorMessage;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 数据表id - 客群数据表id
     */
    private Long dataTableId;

    /**
     * 创建时间, eg: "2024-12-23 12:23:00"
     */
    private String createTime;

    /**
     * 是否为预置客群: true-预置客群, false-非预置客群
     */
    private Boolean isPreset;

    /**
     * 分群方式：RULE_CIRCLE-规则圈选 FILE_IMPORT-文件导入 MODEL_PREDICTION-模型预测
     */
    private GroupingTypeEnum groupingType;

    /**
     * 配置按钮操作标签：true-可操作，false-不能操作
     */
    private Boolean configTag;
}
