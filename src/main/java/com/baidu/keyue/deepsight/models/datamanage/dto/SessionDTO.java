package com.baidu.keyue.deepsight.models.datamanage.dto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
/**
 * <AUTHOR>
 * @className SessionDTO
 * @description sessionDto
 * @date 2025/1/10 16:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class SessionDTO {
    private String sessionId;

    private String memberId;

    private String taskName;

    private String taskId;

    private String taskType;

    private String taskTypeDesc;

    private Integer soundRecord;

    private String sessionType;

    private String tenantId;

    private String tenantName;

    private String accountId;

    private String robotId;

    private String robotName;

    private String cmdStatus;

    private String mobile;

    private String mobileProvince;

    private String mobileCity;

    private String callerNum;

    private String callerProvince;

    private String callerCity;

    private String sipCode;

    private String sipName;

    private String sipInfo;

    private Integer didOwner;

    private String action;

    private Boolean isRinging;

    private Boolean isAnswer;

    private String hangupReason;

    private String intent;

    private Integer callTimes;

    private Integer callType;

    private Integer completeType;

    private String contactId;

    private String contactUUID;

    private String dialogVar;

    private Integer durationTimeLen;

    private Integer endType;

    private String endTypeReason;

    private String executiveStrategy;

    private String extJson;

    private Long fileId;

    private Boolean isComplete;

    private Boolean isRobotHangup;

    private String markInfo;

    private Integer memberCallTimes;

    private Integer secretType;

    private String secretKey;

    private Integer smsStatus;

    private String smsVar;

    private String transResult;

    private Integer sysType;

    private String aiGenStatus;

    private String aiGenResult;

    private String aiAbnormalTaskStatus;

    private String aiAbnormalAuditStatus;

    private String abnormalAuditResult;

    private String aiGenAbnormalResult;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date talkingStartTime;

    private Integer talkingTimeLen;

    private Integer talkingTurn;

    private String audioCallPath;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date ringStartTime;

    private Integer ringingTimeLen;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Long createTimePoint;

    private Integer robotScene;

    private String robotSceneName;

    private String location;

    private String callerLocation;

    private String fromSource;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    private String collectInfo;

    private String customTagList;

    private Integer isResult;

    private String nodeInfo;

    private String promptVar;

    private Integer textType;

    private String matchType;

    private Integer createHour;

    private Integer extensionResult;

    private String tagExtractInfo;

    private String batchMarking;

    private String conversationContent;

    private Integer recordCount;
}
