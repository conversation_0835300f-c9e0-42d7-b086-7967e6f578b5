package com.baidu.keyue.deepsight.models.rules.response;

import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.enums.TableFieldValueEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @className: DatasetPropertiesResult
 * @description:
 * @author: wangzhongcheng
 * @date: 2024/12/25 19:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DatasetPropertiesResult extends BaseProperties {

    @JsonIgnore
    private Long dataTableId;

    private String enName;
    private String description;

    private List<FilterEnumInfo> enums;

    /**
     * 将数据集配置的字段转为可选数据类型
     * @param tableFieldMetaInfo 字段信息
     * @return DatasetPropertiesResult 数据筛选可选字段
     */
    public static DatasetPropertiesResult convertFrom(TableFieldMetaInfo tableFieldMetaInfo) {
        DatasetPropertiesResult datasetPropertiesResult = new DatasetPropertiesResult();
        datasetPropertiesResult.setDataTableId(tableFieldMetaInfo.getDataTableId());
        datasetPropertiesResult.setFieldId(tableFieldMetaInfo.getId());
        datasetPropertiesResult.setCname(tableFieldMetaInfo.getCnField());
        datasetPropertiesResult.setEnName(tableFieldMetaInfo.getEnField());
        datasetPropertiesResult.setDescription(tableFieldMetaInfo.getDescription());
        datasetPropertiesResult.setDataType(FilterTypeEnum.convertFrom(tableFieldMetaInfo.getFieldType()).getType());
        switch (TableFieldTagEnum.getByCode(tableFieldMetaInfo.getFieldTag())) {
            case PRIMARY, ID -> datasetPropertiesResult.setIsIdKey(true);
            case MEASURE -> datasetPropertiesResult.setIsMeasure(true);
        }

        if (TableFieldValueEnum.getByValueType(tableFieldMetaInfo.getValueType()) == TableFieldValueEnum.ENUM
                && StringUtils.isNotEmpty(tableFieldMetaInfo.getConfigInfos())) {
            List<FilterEnumInfo> filterEnumInfos = JsonUtils.toListUnchecked(
                    tableFieldMetaInfo.getConfigInfos(), List.class, FilterEnumInfo.class);
            datasetPropertiesResult.setEnums(filterEnumInfos);
        }
        return datasetPropertiesResult;
    }

}
