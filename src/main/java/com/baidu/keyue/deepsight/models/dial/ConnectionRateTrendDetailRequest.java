package com.baidu.keyue.deepsight.models.dial;

import com.baidu.keyue.deepsight.enums.DialMetricQueryTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConnectionRateTrendDetailRequest {

    /**
     * 时间类型：24H(24小时)/30D(30天)
     * @example 24H
     */
    private String timeType;
    
    private String id;
    
    private DialMetricQueryTypeEnum type;

    /**
     * 号线num（可选）
     * @example "task-456"
     */
    private String lineNum;

    /**
     * 任务id（可选）
     * @example "task-456"
     */
    private String taskId;

    /**
     * 机器人id（可选）
     * @example "robot-789"
     */
    private String robotId;

    public void setId(String id) {
        this.id = id;
        if (this.type != null) {
            switch (this.type) {
                case LINE -> this.lineNum = id;
                case ROBOT -> this.robotId = id;
                case TASK -> this.taskId = id;
            }
        }
    }
}
