package com.baidu.keyue.deepsight.models.datamanage.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName FileImportTaskDeleteRequest
 * @Description 文件导入数据任务删除
 * <AUTHOR>
 * @Date 2025/2/21 11:36 AM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileImportTaskDeleteRequest {

    /**
     * 任务组ID
     */
    private String groupId;
    /**
     * 文件ids
     */
    private List<Long> fileIds;

    /**
     * 数据集ID
     */
    @NotNull(message = "数据集ID不能为空")
    private Long dataTableId;
}
