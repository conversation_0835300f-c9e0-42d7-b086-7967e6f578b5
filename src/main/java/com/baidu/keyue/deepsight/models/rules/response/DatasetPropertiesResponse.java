package com.baidu.keyue.deepsight.models.rules.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @className: DatasetPropertiesResponse
 * @description: 业务事件信息的数据明细响应
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/25 19:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetPropertiesResponse {

    /**
     * 业务事件信息列表
     */
    private List<DatasetInfo> list;

}
