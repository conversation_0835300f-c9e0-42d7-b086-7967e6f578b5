package com.baidu.keyue.deepsight.models.datamanage.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @className TableSyncDetailResponse
 * @description 数据接入信息
 * @date 2025/2/8, 11:00
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TableSyncDetailResponse {

    /**
     * 请求地址
     */
    private String url;

    /**
     * 鉴权信息
     */
    private String auth;

    /**
     * 数据集名称
     */
    private String tableName;

    /**
     * 接入状态
     */
    private Integer status;

    /**
     * curl数据接入 信息
     */
    private String curlContent;

    /**
     * python数据接入 信息
     */
    private String pythonContent;

    /**
     * java数据接入 信息
     */
    private String javaContent;





}
