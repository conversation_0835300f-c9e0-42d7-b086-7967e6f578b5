package com.baidu.keyue.deepsight.models.echopath;

import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class EchoPathResp {
    private String requestId;
    private String code;
    private String message;
    private Object data;
    private BasePageResponse.Page<Object> page;
}
