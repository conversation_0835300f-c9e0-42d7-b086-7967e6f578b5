package com.baidu.keyue.deepsight.models.customer.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName DeleteCustomerRequest
 * @Description 删除用户请求体
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/1/8 20:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeleteCustomerRequest {
    /**
     * 客群ID
     */
    @NotNull(message = "customerGroupId不能为空")
    private Long customerGroupId;

    /**
     * 数据主键id
     */
    @NotEmpty(message = "ids不能为空")
    private List<String> ids;

    /**
     * 数据操作 DELETE:删除
     */
    private String op;
}
