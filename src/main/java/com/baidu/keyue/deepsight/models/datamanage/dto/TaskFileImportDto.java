package com.baidu.keyue.deepsight.models.datamanage.dto;

import cn.hutool.core.util.StrUtil;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskFileImportWithBLOBs;
import com.baidu.keyue.deepsight.utils.GzipUtils;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName TaskFileImportResponse
 * @Description 文件导入数据返回体
 * <AUTHOR>
 * @Date 2025/2/14 3:35 PM
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskFileImportDto implements Serializable {

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 任务组ID
     */
    private String groupId;

    /**
     * 源文件命名
     */
    private String sourceName;

    /**
     * BOS文件key
     */
    private String bosName;

    /**
     * 文件后缀名
     */
    private String suffixName;

    /**
     * 字段映射模式：EQUAL_NAME-同名映射（默认），AI_SEMANTEME-AI语义映射
     */
    private String mappingType;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 数据集ID
     */
    private Long dataTableId;

    /**
     * 任务状态：0-已上传，1-导入中，2-导入完成，3-导入失败
     */
    private Integer status;

    /**
     * 删除标记：
     */
    private Boolean del;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     *  更新时间
     */
    private Date updateTime;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 写入模式：COVER-覆盖式写入，INCREMENT-增量式写入
     */
    private String writeType;

    /**
     * 操作者姓名
     */
    private String operatorName;

    /**
     * 错误数据文件key
     */
    private String bosErrName;

    /**
     * 字段映射
     */
    private List<FieldMappingDto> fieldMapping;
    /**
     * 首行数据
     */
    private List<RowDataDto> rowData;

    public TaskFileImportDto(TaskFileImportWithBLOBs taskFileImport) {
        BeanUtils.copyProperties(taskFileImport, this);
        byte[] mapping = taskFileImport.getFieldMapping();
        String mappingStr = GzipUtils.decompressToString(mapping);
        if (StrUtil.isNotBlank(mappingStr)) {
            this.fieldMapping = JsonUtils.readType(mappingStr, new TypeReference<>() {
            });
        }
        byte[] firstRowData = taskFileImport.getFirstRowData();
        String rowDataStr = GzipUtils.decompressToString(firstRowData);
        if (StrUtil.isNotBlank(rowDataStr)) {
            this.rowData = JsonUtils.readType(rowDataStr, new TypeReference<>() {
            });
        }
    }
}