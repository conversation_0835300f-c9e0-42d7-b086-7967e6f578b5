package com.baidu.keyue.deepsight.models.datamanage.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName AiMappingDto
 * @Description Ai映射结果DTO
 * <AUTHOR>
 * @Date 2025/2/21 5:10 PM
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AiMappingDto {
    /**
     * 输出详情
     */
    private DataDetail data;
    private Integer code;
    private String msg;
    private String desc;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static  class DataDetail {
        /**
         * LLM输出，需要从中解析
         */
        private String output;
        /**
         * 其他字段暂时不关注
         */
        private String reasoningContent;
        private Date startTime;
        private Date endTime;
        private Integer promptTokens;
        private Integer completionTokens;
        private Integer totalTokens;
        private Integer recordID;
        private String sessionID;
    }
}
