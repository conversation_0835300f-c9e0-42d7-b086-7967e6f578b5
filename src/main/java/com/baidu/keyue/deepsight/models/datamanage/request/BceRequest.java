package com.baidu.keyue.deepsight.models.datamanage.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.Set;

/**
 * @ClassName BceRequest
 * @Description BceRequest
 * <AUTHOR>
 * @Date 2025/3/12 2:48 PM
 */
@Data
public class BceRequest<REQUSETBODY> implements Serializable {

    /**
     * 请求url：https://xxx.bce.baidu.com/v1/objectkey?duration=xx
     */
    private String url;
    /**
     * 请求方式
     */
    private String httpMethod;
    /**
     * string
     */
    private Map<String, String> header;
    /**
     * 参与签名的header
     */
    private Set<String> waitSignHeader;
    /**
     * 当前请求时间
     */
    private Date requestDate;
    /**
     * 请求体
     */
    private REQUSETBODY requestBody;
}
