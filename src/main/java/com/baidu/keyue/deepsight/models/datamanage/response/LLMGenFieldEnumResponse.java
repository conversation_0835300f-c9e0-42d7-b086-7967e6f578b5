package com.baidu.keyue.deepsight.models.datamanage.response;

import com.baidu.keyue.deepsight.models.datamanage.dto.FieldEnumMappingDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className LLMGenFieldEnumResponse
 * @description 大模型生成字段枚举
 * @date 2025/2/13 15:05
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LLMGenFieldEnumResponse {

    /**
     * 高级配置
     */
    private List<FieldEnumMappingDTO> configInfos;
}
