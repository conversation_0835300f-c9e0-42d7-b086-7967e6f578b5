package com.baidu.keyue.deepsight.models.memory;

import java.util.List;

import com.baidu.keyue.deepsight.enums.PromptTypeEnum;
import com.baidu.keyue.deepsight.enums.SwitchEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.YesEnum;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MemoryDataSourceDetail {

    /**
     * 记忆提取配置 ID
     */
    private Long id;

    /**
     * 数据集ID
     */
    private Long datasetId;
    /**
     * 数据集名称
     */
    private String datasetName;

    /**
     * 字段ID
     */
    private Long fieldId;
    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段名称-en
     */
    private String fieldNameEn;

    /**
     * 指令类型
     */
    private PromptTypeEnum promptType;
    /**
     * 抽取指令
     */
    private String prompt;

    /**
     * 抽取方式
     */
    private TriggerModeEnum triggerMode;
    /**
     * 执行频率
     */
    private TriggerFrequencyEnum triggerFrequency;

    /**
     * 执行频率具体时间配置
     * */
    private TriggerFrequencyValue triggerFrequencyValue;

    /**
     * 描述
     */
    private String description;

    /**
     * 抽取数据范围规则
     */
    private List<RuleFilter> dataFilterRule;

    /**
     * 启用状态
     */
    private SwitchEnum status;

    /**
     * 是否是默认预设数据集，默认预设数据集不能删除
     */
    private YesEnum isDefault;

    /**
     * 执行状态
     */
    private TaskExecStatusEnum execStatus;

    public static MemoryDataSourceDetail from(MemoryExtractWithBLOBs record) {
        MemoryDataSourceDetail detail = new MemoryDataSourceDetail();
        detail.setId(record.getId());
        detail.setDatasetId(record.getDataTableId());
        detail.setDatasetName(record.getDatasetName());
        detail.setFieldId(record.getFieldId());
        detail.setFieldName(record.getFieldName());
        detail.setFieldNameEn(record.getFieldNameEn());
        detail.setPrompt(record.getPrompt());
        detail.setPromptType(PromptTypeEnum.getByCode(record.getPromptType()));
        TriggerModeEnum triggerModeEnum = TriggerModeEnum.getByCode(record.getTriggerMod());
        detail.setTriggerMode(triggerModeEnum);
        detail.setTriggerFrequency(TriggerFrequencyEnum.getByCode(record.getTriggerFrequency()));
        if (StringUtils.isNotBlank(record.getTriggerFrequencyValue())) {
            detail.setTriggerFrequencyValue(JsonUtils.toObjectWithoutException(record.getTriggerFrequencyValue(), TriggerFrequencyValue.class));
        }

        detail.setDescription(record.getDescription());
        SwitchEnum switchEnum = SwitchEnum.getByBoolean(record.getStatus());
        detail.setStatus(switchEnum);
        detail.setIsDefault(YesEnum.getByBoolean(record.getIsDefault()));
        if (StringUtils.isNotBlank(record.getDataFilterRule())) {
            detail.setDataFilterRule(JsonUtils.toListUnchecked(record.getDataFilterRule(), List.class, RuleFilter.class));
        }

        if (TriggerModeEnum.REALTIME.equals(triggerModeEnum) && SwitchEnum.ON.equals(switchEnum)) {
            detail.setExecStatus(TaskExecStatusEnum.RUNNING);
        } else {
            detail.setExecStatus(TaskExecStatusEnum.getByCode(record.getCalStatus()));
        }
        return detail;
    }

}
