package com.baidu.keyue.deepsight.models.sop.nodepredict;

import com.baidu.keyue.deepsight.models.sop.SOPProgress;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * *@Author: dongjiacheng01
 * *@Description: 节点预测算子 - 规则总结接口响应
 * *@Date: 16:39 2025/5/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NodeProcessSummaryResponse {

    private String status;

    private String message;

    private List<Item> results;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Item {

        private String step;

        private List<String> nodes;

        public SOPProgress.SOPStepWithNodes toResponseItem() {
            SOPProgress.SOPStepWithNodes nodeItem = new SOPProgress.SOPStepWithNodes();
            nodeItem.setStep(step);
            nodeItem.setNodes(nodes);
            return nodeItem;
        }

    }

    public Boolean success() {
        return StringUtils.equalsIgnoreCase(status, "success");
    }

}
