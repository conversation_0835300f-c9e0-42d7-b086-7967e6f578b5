package com.baidu.keyue.deepsight.models.meg;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MEGIdMappingResponse {

    @JsonProperty("response_items")
    private List<ResponseItem> responseItems;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ResponseItem {
        @JsonProperty("external_mapping_records")
        private List<ExternalMappingRecord> externalMappingRecords;
        private Id id;
        private List<String> status;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Id {
        private String type;
        private String value;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ExternalMappingRecord {
        @JsonProperty("mapping_ids")
        private List<MappingId> mappingIds;
        @JsonProperty("mapping_type")
        private String mappingType;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MappingId {
        @JsonProperty("data_source")
        private String dataSource;
        @JsonProperty("peer_id")
        private PeerId peerId;
        private Confi confi;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Confi {
        private String src;
        private int val;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PeerId {
        @JsonProperty("dev_index")
        private int devIndex;
        private Product product;

        private String type;
        private String value;

        @JsonProperty("site_tag")
        private String siteTag;
        private Long timestamp;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Product {
        @JsonProperty("prod_type")
        private List<String> prodType;
    }

    public MEGIds parseMEGIds() {
        MEGIds megIds = new MEGIds();
        if (CollectionUtils.isEmpty(responseItems)) {
            return megIds;
        }
        ResponseItem responseItem = responseItems.get(0);
        if (CollectionUtils.isEmpty(responseItem.getExternalMappingRecords())) {
            return megIds;
        }
        parserItems(megIds, responseItem);

        return megIds;
    }

    private void parserItems(MEGIds megIds, ResponseItem responseItem) {
        for (ExternalMappingRecord externalMappingRecord : responseItem.getExternalMappingRecords()) {
            String mappingType = externalMappingRecord.getMappingType();
            List<MappingId> mappingIds = externalMappingRecord.getMappingIds();
            if (CollectionUtils.isEmpty(mappingIds)) {
                continue;
            }

            List<String> ids = mappingIds.stream().map(item -> {
                        if (Objects.isNull(item.getPeerId())) {
                            return null;
                        }
                        return item.getPeerId().getValue();
                    })
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());

            megIds.append(mappingType, ids);
        }
    }

    public List<MEGIds> parseMultiMEGIds() {
        if (CollectionUtils.isEmpty(responseItems)) {
            return Lists.newArrayList();
        }
        return responseItems.stream().map(item -> {
            MEGIds megIds = new MEGIds();
            parserItems(megIds, item);
            return megIds;
        }).collect(Collectors.toList());
    }
}
