package com.baidu.keyue.deepsight.models.bsc;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取作业列表参数
 * */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BscListJobsRequest {

    /**
     * 作业所在地域,如保定区域为bd
     */
    private String region;
    private Integer page;
    private Integer pageSize;
    private String name;
    private String orderBy;
    /**
     * asc或desc,顺序或倒序
     */
    private String order;

    private List<Map<String, String>> keyWords;
}
