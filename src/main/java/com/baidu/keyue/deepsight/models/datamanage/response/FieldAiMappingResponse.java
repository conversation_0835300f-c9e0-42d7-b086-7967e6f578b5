package com.baidu.keyue.deepsight.models.datamanage.response;

import com.baidu.keyue.deepsight.models.datamanage.dto.FieldMappingDto;
import com.baidu.keyue.deepsight.models.datamanage.dto.RowDataDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName FieldAiMappingResponse
 * @Description 字段映射响应
 * <AUTHOR>
 * @Date 2025/2/21 4:54 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FieldAiMappingResponse {

    /**
     * 映射结果
     */
    private List<FieldMappingDto> mappingRes;

    /**
     * excel字段数据
     */
    private List<RowDataDto> excelFields;

    /**
     * 数据集字段数据
     */
    private List<VisibleFieldResponse> dorisFields;
}
