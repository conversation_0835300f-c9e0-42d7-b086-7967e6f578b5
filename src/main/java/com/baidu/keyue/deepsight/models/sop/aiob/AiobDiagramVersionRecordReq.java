package com.baidu.keyue.deepsight.models.sop.aiob;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @ClassName AiobDiagramVersionRecordReq
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/6/9 10:58
 */
@Data
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AiobDiagramVersionRecordReq {
    private String versionId;

    private String agentId;

    private Integer pageNo;

    private Integer pageSize;
}
