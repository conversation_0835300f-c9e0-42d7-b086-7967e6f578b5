package com.baidu.keyue.deepsight.models.meg;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MEGBaiduData {
    /**
     * idmapping 结果
     */
    private MEGIds megIds;
    /**
     * 画像结果
     */
    private List<Attribute> attributes;

    public boolean isEmpty() {
        return megIds.isEmpty() && CollectionUtils.isEmpty(attributes);
    }
}
