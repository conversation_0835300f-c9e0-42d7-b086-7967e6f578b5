package com.baidu.keyue.deepsight.models.datamanage.request;

import com.baidu.keyue.deepsight.models.datamanage.dto.FileDetailDto;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName FileImportSaveRequest
 * @Description 文件导入创建任务请求
 * <AUTHOR>
 * @Date 2025/3/12 10:44 AM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileImportSaveRequest {

    /**
     * 文件key
     */
    @NotEmpty(message = "文件详情不能为空")
    private @Valid List<FileDetailDto> files;

    /**
     * 数据集ID
     */
    @NotNull(message = "数据集ID不能为空")
    private Long dataTableId;
    
    /**
     * 任务组ID
     */
    private String groupId;
    
}
