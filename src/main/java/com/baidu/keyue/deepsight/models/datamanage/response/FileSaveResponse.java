package com.baidu.keyue.deepsight.models.datamanage.response;

import com.baidu.keyue.deepsight.models.datamanage.dto.RowDataDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * @ClassName FileUploadResponse
 * @Description 文件保存返回
 * <AUTHOR>
 * @Date 2025/2/13 6:58 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FileSaveResponse {

    /**
     * 提示信息
     */
    private String message;

    /**
     * 任务组ID
     */
    private String groupId;

    /**
     * 上传详情
     */
    private List<SaveDetail> uploadDetails;
    
    /**
     * 行数据
     */
    private List<RowDataDto> rowData;
    
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SaveDetail {
        /**
         * 文件ID
         */
        private Long fileId;

        /**
         * 文件名
         */
        private String fileName;
    }
}
