package com.baidu.keyue.deepsight.models.sop.aiob;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * *@Author: dongjiacheng01
 * *@Description: 外呼【根据画布版本获取画布流程】接口响应
 * *@Date: 16:49 2025/5/26
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
public class AiobDiagramVersionRecordViewResp {

    private int code;
    private String msg;
    private long time;
    private AiobDiagramRecordData data;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
    public static class AiobDiagramRecordData {

        private AiobDiagramRecordMenu menu;
        private List<AiobDiagramRecordTopic> topicList;

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
        public static class AiobDiagramRecordMenu {

            private String id;
            private int version;
            private AiobDiagramRecordStructure structure;

            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            @JsonInclude(JsonInclude.Include.NON_NULL)
            @JsonIgnoreProperties(ignoreUnknown = true)
            @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
            public static class AiobDiagramRecordStructure {

                private String startTopic;
                @JsonProperty("topicIDs")
                private List<String> topicIds;
                private Map<String, AiobDiagramRecordDiagram> diagrams;


                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                @JsonInclude(JsonInclude.Include.NON_NULL)
                @JsonIgnoreProperties(ignoreUnknown = true)
                @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                public static class AiobDiagramRecordDiagram {

                    @JsonProperty("sourceID")
                    private String sourceId;
                    private String name;
                    private String type;
                    private List<AiobDiagramRecordMenuItem> menuItems;

                    @Data
                    @AllArgsConstructor
                    @NoArgsConstructor
                    @JsonInclude(JsonInclude.Include.NON_NULL)
                    @JsonIgnoreProperties(ignoreUnknown = true)
                    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                    public static class AiobDiagramRecordMenuItem {

                        private String type;
                        @JsonProperty("sourceID")
                        private String sourceId;
                        private String intentNameEn;
                        private String intentNameZh;

                    }
                }
            }
        }

        @Data
        @AllArgsConstructor
        @NoArgsConstructor
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonIgnoreProperties(ignoreUnknown = true)
        @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
        public static class AiobDiagramRecordTopic {
            private String id;
            private AiobDiagramRecordContent content;

            @Data
            @AllArgsConstructor
            @NoArgsConstructor
            @JsonInclude(JsonInclude.Include.NON_NULL)
            @JsonIgnoreProperties(ignoreUnknown = true)
            @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
            public static class AiobDiagramRecordContent {

                @JsonProperty("sourceID")
                private String sourceId;
                private String edges;
                private Map<String, AiobDiagramRecordNode> nodes;

                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                @JsonInclude(JsonInclude.Include.NON_NULL)
                @JsonIgnoreProperties(ignoreUnknown = true)
                @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                public static class AiobDiagramRecordNode {

                    private String type;
                    @JsonProperty("nodeID")
                    private String nodeId;
                    private String blockId;
                    private String name;
                    private List<Double> coords;
                    private AiobDiagramRecordTopicData data;

                    @Data
                    @AllArgsConstructor
                    @NoArgsConstructor
                    @JsonInclude(JsonInclude.Include.NON_NULL)
                    @JsonIgnoreProperties(ignoreUnknown = true)
                    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                    public static class AiobDiagramRecordTopicData {

                        private String intent;
                        private String intentNameZh;
                        private String label;
                        private List<String> steps;
                        private boolean hubSlot;
                        private String sourceHandle;
                        private AiobDiagramRecordPortsV2 portsV2;

                        @Data
                        @AllArgsConstructor
                        @NoArgsConstructor
                        @JsonInclude(JsonInclude.Include.NON_NULL)
                        @JsonIgnoreProperties(ignoreUnknown = true)
                        @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                        public static class AiobDiagramRecordPortsV2 {

                            private AiobDiagramRecordBuiltin builtIn;
                            private List<AiobDiagramRecordBuiltin.AiobDiagramRecordNext> dynamic;

                            @Data
                            @AllArgsConstructor
                            @NoArgsConstructor
                            @JsonInclude(JsonInclude.Include.NON_NULL)
                            @JsonIgnoreProperties(ignoreUnknown = true)
                            @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                            public static class AiobDiagramRecordBuiltin {

                                private AiobDiagramRecordNext next;
                                private AiobDiagramRecordFail fail;

                                @Data
                                @AllArgsConstructor
                                @NoArgsConstructor
                                @JsonInclude(JsonInclude.Include.NON_NULL)
                                @JsonIgnoreProperties(ignoreUnknown = true)
                                @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                                public static class AiobDiagramRecordNext {

                                    private String type;
                                    private String target;
                                    private String targetTopicId;
                                    private String id;
                                    private AiobDiagramRecordNextData data;

                                    @Data
                                    @AllArgsConstructor
                                    @NoArgsConstructor
                                    @JsonInclude(JsonInclude.Include.NON_NULL)
                                    @JsonIgnoreProperties(ignoreUnknown = true)
                                    @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                                    public static class AiobDiagramRecordNextData {
                                        private String points;
                                    }
                                }

                                @Data
                                @AllArgsConstructor
                                @NoArgsConstructor
                                @JsonInclude(JsonInclude.Include.NON_NULL)
                                @JsonIgnoreProperties(ignoreUnknown = true)
                                @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                                public static class AiobDiagramRecordFail {

                                    private String type;
                                    private String targetTopicId;
                                    private String id;

                                }

                            }
                        }
                    }
                }

                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                @JsonInclude(JsonInclude.Include.NON_NULL)
                @JsonIgnoreProperties(ignoreUnknown = true)
                @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                public static class AiobDiagramRecordEdgeWrap {
                    private List<AiobDiagramRecordEdge> edges;
                }

                @Data
                @AllArgsConstructor
                @NoArgsConstructor
                @JsonInclude(JsonInclude.Include.NON_NULL)
                @JsonIgnoreProperties(ignoreUnknown = true)
                @JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
                public static class AiobDiagramRecordEdge {
                    private String id;
                    private String source;
                    private String sourceHandle;
                    private String target;
                    private String targetHandle;
                    private String type;
                }
            }
        }
    }


    /**
     * 是否请求成功
     */
    public Boolean isSuccess() {
        return code == 200;
    }

}