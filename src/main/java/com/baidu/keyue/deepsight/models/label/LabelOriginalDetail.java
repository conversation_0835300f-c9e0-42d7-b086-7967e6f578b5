package com.baidu.keyue.deepsight.models.label;

import com.baidu.keyue.deepsight.enums.ExecModEnum;
import com.baidu.keyue.deepsight.enums.LabelValueSaveModEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LabelOriginalDetail {

    /**
     * 标签ID
     */
    private Long labelId;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签目录 ID
     */
    private Long catalogId;

    /**
     * 生产方式
     */
    private ExecModEnum execMod;

    /**
     * 更新方式
     */
    private TriggerModeEnum triggerMod;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间, eg: "2024-12-23 12:23:00"
     */
    private String createTime;

    /**
     * 状态
     */
    private TaskExecStatusEnum status;

    /**
     * 失败原因
     */
    private String errorMessage;

    /**
     * 标签更新取值逻辑
     */
    private UpdateModEnum updateMod;

    /**
     * 标签值保存类型
     */
    private LabelValueSaveModEnum labelValueSaveMod;

    /**
     * 执行频率
     */
    private TriggerFrequencyEnum triggerFrequency;

    /**
     * 执行频率具体时间配置
     * */
    private TriggerFrequencyValue triggerFrequencyValue;

    /**
     * 最新一次计算时间，eg：“2024-12-22”
     */
    private String lastTaskDate;

    /**
     * 标签值规则
     * */
    private LabelRule labelRule;
}
