package com.baidu.keyue.deepsight.models.label;

import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.service.rules.RuleParseService;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;


/**
 * 标签规则
 * 包含多个标签值的生产规则
 *
 * */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LabelRule {

    /**
     * 标签值及其对应规则, 列表先后顺序表示其优先级及展示的前后顺序
     * */
    private List<LabelValueRule> labelValueRules;

    public List<String> labelStringValueParser() {
        if (CollectionUtils.isNotEmpty(labelValueRules)) {
            return labelValueRules.stream().map(LabelValueRule::getLabelValue).toList();
        }
        return Lists.newArrayList();
    }

    public void checkLabelRule(RuleParseService ruleParseService) {
        if (CollectionUtils.isEmpty(labelValueRules)) {
            throw new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST, "标签未添加标签值");
        }
        HashMap<String, Boolean> labelValueMap = new HashMap<>();
        labelValueRules.forEach(labelValueRule -> {
            // 标签值检查
            if (StringUtils.isBlank(labelValueRule.getLabelValue())) {
                throw new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST, "标签值为空");
            }
            if (labelValueMap.containsKey(labelValueRule.getLabelValue())) {
                throw new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST, "标签值重复");
            } else {
                labelValueMap.put(labelValueRule.getLabelValue(), true);
            }

            // ruleGroup 检查
            RuleGroup ruleGroup = labelValueRule.getRuleGroup();
            if (Objects.isNull(ruleGroup) || (CollectionUtils.isEmpty(ruleGroup.getRuleNodes()) && CollectionUtils.isEmpty(ruleGroup.getRuleGroups()))) {
                throw new DeepSightException.LabelOperatorFailedException(ErrorCode.BAD_REQUEST,
                        String.format("标签值(%s)条件组为空", labelValueRule.getLabelValue()));
            }
            try {
                ruleParseService.parseRuleGroup(ruleGroup, new AtomicInteger(0));
            } catch (Exception e) {
                throw new DeepSightException.LabelOperatorFailedException(
                        ErrorCode.BAD_REQUEST, String.format("标签规则(%s)解析失败", labelValueRule.getLabelValue()));
            }
        });
    }
}
