package com.baidu.keyue.deepsight.models.sop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * *@Author: dongjiacheng01
 * *@Description: SOP节点预测Request
 * *@Date: 16:23 2025/5/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SOPNodePredictRequest {

    @NotBlank(message = "外呼任务id不可为空")
    private String taskId;

    @NotBlank(message = "对话规则不可为空")
    private String rule;

    /**
     * 外呼任务机器人版本
     */
    private String robotVer;
}
