package com.baidu.keyue.deepsight.models.datamanage.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * @ClassName FieldAiMappingRequest
 * @Description Ai语义字段映射请求
 * <AUTHOR>
 * @Date 2025/2/21 3:27 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FieldAiMappingRequest {


    /**
     * 默认，"ernie-4.0-turbo-8k"
     */
    private String model = "ernie-4.0-turbo-8k";

    /**
     * LLM请求参数
     */
    private List<Messages> messages;

    @JsonProperty("disable_search")
    private boolean disableSearch = false;
    @JsonProperty("enable_citation")
    private boolean enableCitation = false;
    @JsonProperty("max_completion_tokens")
    private Integer maxCompletionTokens = 2048;
    private Boolean stream = false;

    public FieldAiMappingRequest(Messages messages) {
        this.messages = Collections.singletonList(messages);
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Messages {
        /**
         * 默认user
         */
        public String role = "user";
        /**
         * 主要输入的prompt
         */
        public String content;

        public Messages(String content) {
            this.content = content;
        }
    }
}
