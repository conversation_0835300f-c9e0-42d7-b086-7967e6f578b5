package com.baidu.keyue.deepsight.models.customer.request;

import com.baidu.keyue.deepsight.enums.DiffusionFilterEnum;
import com.baidu.keyue.deepsight.enums.FeatureSelectEnum;
import com.baidu.keyue.deepsight.enums.JudgeCriteriaEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.Set;

/**
 * @ClassName CustomerDiffusionTaskCreateRequest
 * @Description 人群扩散任务创创建请求
 * <AUTHOR>
 * @Date 2025/3/24 4:58 PM
 */
@Data
public class CustomerDiffusionTaskCreateRequest implements Serializable {

    /**
     * 预测任务名称
     */
    @NotBlank(message = "预测任务名称不能为空")
    @Length(max = 50, message = "预测任务名称最长允许50")
    private String taskName;

    /**
     * 种子人群id
     */
    @NotNull(message = "种子人群id不能为空")
    private Long seedGroup;

    /**
     * 预测人群id，逗号分隔
     */
    @NotEmpty(message = "预测人群id不能为空")
    private Set<Long> predictGroup;

    /**
     * 过滤规则:NOT_FILTER-不过滤 REMOVE_SEED_GROUP-剔除种子人群
     */
    @NotNull(message = "过滤规则不能为空")
    private DiffusionFilterEnum filterRule;

    /**
     * 特征筛选:SYSTEM_RECOMMEND-系统推荐 COVER_CUSTOMIZE-覆盖率自定义
     */
    @NotNull(message = "特征筛选不能为空")
    private FeatureSelectEnum featureSelect;

    /**
     * 覆盖率阈值
     */
    private Float threshold;

    /**
     * 判定标准:SIMILARITY-根据相似度 RANKING-取前几个
     */
    @NotNull(message = "判定标准不能为空")
    private JudgeCriteriaEnum judgeCriteria;

    /**
     * 判定标准：相似度
     */
    private Float similarity;

    /**
     * 判定标准：取前几个
     */
    private Integer ranking;

    /**
     * 更新触发类型:CRON-定时更新 MANUAL-手动更新 REALTIME-实时更新
     */
    @NotNull(message = "更新触发类型不能为空")
    private TriggerModeEnum triggerMod;

    /**
     * 执行频率:DAY-每日,WEEK-每周,MONTH-每月
     */
    private TriggerFrequencyEnum triggerFrequency;

    /**
     * 执行频率json
     */
    @Valid
    private TriggerFrequencyValue triggerFrequencyValue;
}
