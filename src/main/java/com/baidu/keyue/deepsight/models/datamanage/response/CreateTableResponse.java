package com.baidu.keyue.deepsight.models.datamanage.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @className CreateTableResponse
 * @description 创建数据表返回结果
 * @date 2025/2/13 10:55
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateTableResponse {

    /**
     * 数据表id
     */
    private Long dataTableId;

    /**
     * 数据集状态 0未提交 1创建中 2已创建 3创建失败
     */
    private Integer dataStatus;

}
