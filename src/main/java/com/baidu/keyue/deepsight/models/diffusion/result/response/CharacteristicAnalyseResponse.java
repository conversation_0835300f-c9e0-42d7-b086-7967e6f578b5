package com.baidu.keyue.deepsight.models.diffusion.result.response;

import com.baidu.keyue.deepsight.models.diffusion.DiffusionCalculateContext;
import com.baidu.keyue.deepsight.models.diffusion.dto.DiffusionCharacteristicDTO;
import com.baidu.keyue.deepsight.models.diffusion.dto.GradingDistributionDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * @className: CustomerDiffusionCharacteristicResponse
 * @description: 人群扩散特征分析结果
 * @author: wangzhongcheng
 * @date: 2025/3/25 19:33
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CharacteristicAnalyseResponse {

    /**
     * 分段柱状图展示列表
     */
    private List<GradingDistributionDTO> gradingDistributionList;

    /**
     * 特征展示列表
     */
    private List<DiffusionCharacteristicDTO> characteristicList;

    /**
     * 将扩散计算上下文转换为扩散特征分析结果
     *
     */
    public static CharacteristicAnalyseResponse convertFrom(DiffusionCalculateContext diffusionCalculateContext) {
        CharacteristicAnalyseResponse analyseResponse = new CharacteristicAnalyseResponse();
        analyseResponse.setCharacteristicList(diffusionCalculateContext.getFeatures());
        analyseResponse.setGradingDistributionList(diffusionCalculateContext.getGradingDistributionList());
        return analyseResponse;
    }

    /**
     * 获取默认的扩散特征分析结果
     */
    public static CharacteristicAnalyseResponse getDefaultResponse() {
        CharacteristicAnalyseResponse characteristicAnalyseResponse = new CharacteristicAnalyseResponse();
        characteristicAnalyseResponse.setGradingDistributionList(new ArrayList<>());
        characteristicAnalyseResponse.setCharacteristicList(new ArrayList<>());
        return characteristicAnalyseResponse;
    }

}
