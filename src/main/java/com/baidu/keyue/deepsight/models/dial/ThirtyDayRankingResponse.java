package com.baidu.keyue.deepsight.models.dial;

import cn.hutool.core.collection.CollUtil;
import com.baidu.keyue.deepsight.enums.AiobLineSourceEnum;
import com.baidu.keyue.deepsight.enums.AiobLineStatusEnum;
import com.baidu.keyue.deepsight.enums.AiobRobotTypeEnum;
import com.baidu.keyue.deepsight.enums.AiobTaskStatusEnum;
import com.baidu.keyue.deepsight.enums.AlertConfigTypeEnum;
import com.baidu.keyue.deepsight.utils.MathUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ThirtyDayRankingResponse {
    /**
     * 号线id/任务id/机器人id
     */
    private String id;
    /**
     * 号线id
     */
    private String lineId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 机器人id
     */
    private String robotId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 机器人名称
     */
    private String robotName;

    /**
     * 来源：PLATFORM(来自平台)/CUSTOMER(客户自有)
     */
    private AiobLineSourceEnum source;

    /**
     * 号线状态：ENABLED(启用中)/BANNED(已禁止)/ARREARS(已欠费)/DISABLED(已停用)
     */
    private AiobLineStatusEnum lineStatus;

    /**
     * 任务状态：/RUNNING(执行中)/PAUSED(已暂停)/COMPLETED(已完成)
     */
    private AiobTaskStatusEnum taskStatus;

    /**
     * 机器人类型：/QUICK_SCENES(快捷场景)/FLEX_CANVAS(灵活画布)
     */
    private AiobRobotTypeEnum robotType;

    /**
     * 拨打次数
     */
    private Long callCount;

    /**
     * 呼通次数
     */
    private Long connectedCount;

    /**
     * 接通率(0-100)
     */
    private Integer connectedRate;

    /**
     * 近12小时接通率
     */
    private List<ConnectionRateTrendResponse> connectedRateTrend;

    /**
     * 接通率告警阈值(0-100)
     */
    private Integer connectedRateAlarmThreshold;

    /**
     * 告警天数
     */
    private Long alarmDays;

    /**
     * 未接通次数
     */
    private Long unconnectedCount;

    /**
     * 号线原因未接通占比(0-100)
     */
    private Integer lineReasonRate;
    /**
     * 平台原因未接通占比(0-100)
     */
    private Integer platformReasonRate;

    public ThirtyDayRankingResponse(Map<String, Object> data,
                                    ThirtyDayRankingRequest request, 
                                    Map<String, Map<String, Object>> detailMap, 
                                    Map<String, Integer> alertMap, 
                                    Map<Object, List<Map<String, Object>>> connectionRateTrendMap,
                                    List<String> dateLine) {
        AlertConfigTypeEnum type = request.getType();
        String dataId = String.valueOf(data.get("id"));
        Map<String, Object> detail = detailMap.getOrDefault(dataId, Map.of());
        switch (type) {
            case LINE -> {
                this.id = dataId;
                this.lineId = dataId;
                this.source = AiobLineSourceEnum.createByObj(detail.get("didOwner"));
                this.lineStatus = AiobLineStatusEnum.createByObj(detail.get("lineStatus"));
                Object lineReason = data.get("lineReason");
                this.lineReasonRate = lineReason == null ? 0 : MathUtils.doubleToIntPercent((Double) lineReason);
            }
            case TASK -> {
                this.id = dataId;
                this.taskId = dataId;
                this.taskName = detail.get("taskName") == null ? null : detail.get("taskName").toString();
                this.taskStatus = AiobTaskStatusEnum.createByObj(detail.get("taskStatus"));
                Object platFormReason = data.get("platFormReason");
                this.platformReasonRate = platFormReason == null ? 0 : MathUtils.doubleToIntPercent((Double) platFormReason);
            }
            case ROBOT -> {
                this.id = dataId;
                this.robotId = dataId;
                this.robotType = AiobRobotTypeEnum.createByObj(detail.get("robotScene"));
            }
        }
        Object dialCount = data.get("dial_count");
        Object connectedCountObj = data.get("connected_count");
        Object connectRate = data.get("connect_rate");
        Object alarmDays = data.get("alarmDays");
        Object unconnectedCount = data.get("un_connected_count");
        this.callCount = dialCount == null ? 0 : (Long) dialCount;
        this.connectedCount = connectedCountObj == null ? 0 : (Long) connectedCountObj;
        this.connectedRate = connectRate == null ? 0 : MathUtils.doubleToIntPercent((Double) connectRate);
        this.alarmDays = alarmDays == null ? 0 : (Long) alarmDays;
        this.unconnectedCount = unconnectedCount == null ? 0 : (Long) unconnectedCount;
        this.connectedRateAlarmThreshold = alertMap.get(this.id);
        // 接通率趋势
        List<Map<String, Object>> rateList = connectionRateTrendMap.get(this.id);
        if (CollUtil.isNotEmpty(rateList)) {
            Map<Object, Map<String, Object>> rateMap = rateList.stream()
                    .collect(Collectors.toMap(map -> map.get("dataTime"), map -> map, (k1, k2) -> k2));
            this.connectedRateTrend = dateLine.stream().map(dateKey -> {
                Map<String, Object> rate = rateMap.get(dateKey);
                return rate == null ? new ConnectionRateTrendResponse(dateKey) : new ConnectionRateTrendResponse(rate);
            }).toList();
        } else {
            this.connectedRateTrend = dateLine.stream().map(ConnectionRateTrendResponse::new).toList();
        }
    }
}
