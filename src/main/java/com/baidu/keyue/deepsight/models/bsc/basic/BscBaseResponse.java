package com.baidu.keyue.deepsight.models.bsc.basic;

import java.util.Map;
import java.util.Objects;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BscBaseResponse {
    private Integer status;
    private Boolean success;
    private Map<String, String> message;

    public Boolean isSuccess() {
        return Objects.nonNull(this.success) && this.success;
    }
}
