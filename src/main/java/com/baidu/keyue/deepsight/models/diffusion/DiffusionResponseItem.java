package com.baidu.keyue.deepsight.models.diffusion;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DiffusionResponseItem {
    /**
     * oneID
     */
    @JsonProperty("oneid")
    private String oneId;
    /**
     * 预测得分
     */
    private Float score;
}
