package com.baidu.keyue.deepsight.models.rules.response;

import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import lombok.Data;

/**
 * @className: BaseProperties
 * @description: 基础字段属性
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/25 19:02
 */
@Data
public class BaseProperties {

    /**
     * 字段ID: 字段唯一标识，如果是标签类型需要再查一次数据库
     */
    private Long fieldId;

    /**
     * 字段名称
     */
    private String cname;

    /**
     * 字段类型
     */
    private String dataType;

    /**
     * 是否度量标识
     */
    private Boolean isMeasure = false;

    /**
     * 是否主键标识
     */
    private Boolean isIdKey = false;

}
