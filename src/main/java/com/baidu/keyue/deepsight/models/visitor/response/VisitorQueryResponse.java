package com.baidu.keyue.deepsight.models.visitor.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * @ClassName VisitorSaveResponse
 * @Description 访客添加返回
 * <AUTHOR>
 * @Date 2025/2/20 6:49 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitorQueryResponse {
    /**
     * 数据列表
     */
    public List<Map<String, Object>> dataList;
}
