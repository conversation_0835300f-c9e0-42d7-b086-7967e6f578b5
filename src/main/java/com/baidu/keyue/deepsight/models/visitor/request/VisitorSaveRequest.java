package com.baidu.keyue.deepsight.models.visitor.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @ClassName VisitorInsertRequest
 * @Description 访客添加请求体
 * <AUTHOR>
 * @Date 2025/2/20 6:34 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitorSaveRequest {
    /**
     * 访客数据
     */
    @NotNull(message = "访客数据不能为空")
    @NotEmpty(message = "访客数据不能为空")
    private Map<String, Object> data;

    @NotNull(message = "更新类型不能为空")
    private Boolean fullUpdate;

    /**
     * 租户ID
     */
    private String tenantId;
    
    private Boolean isDorisData = false;

    public VisitorSaveRequest(Map<String, Object> data) {
        this.data = data;
    }
}
