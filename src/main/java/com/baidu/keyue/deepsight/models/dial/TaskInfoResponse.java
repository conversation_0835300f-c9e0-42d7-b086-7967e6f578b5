package com.baidu.keyue.deepsight.models.dial;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskInfoResponse {
    /**
     * 任务id
     * @example "task-456"
     */
    private String taskId;

    /**
     * 任务名称
     * @example "五一促销活动"
     */
    private String taskName;

    public TaskInfoResponse(Map<String, Object> map) {
        Object taskId = map.get("taskId");
        Object taskName = map.get("taskName");
        this.taskId = (taskId == null) ? "" : taskId.toString();
        this.taskName = (taskName == null) ? "" : taskName.toString();
    }
}
