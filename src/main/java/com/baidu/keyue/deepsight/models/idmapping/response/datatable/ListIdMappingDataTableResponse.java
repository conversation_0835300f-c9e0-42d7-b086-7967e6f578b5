package com.baidu.keyue.deepsight.models.idmapping.response.datatable;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * @className: ListIdMappingDataTableResponse
 * @description: id mapping 数据表规则查询 响应
 * @author: chenwenyu03
 * @date: 2025/3/11 10:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ListIdMappingDataTableResponse {

    /**
     * 数据表id
     */
    private Long dataTableId;

    /**
     * 数据表中文名
     */
    private String tableName;

    /**
     * 是否预置
     */
    private Boolean preset;

    /**
     * 关联 id 对字段列表
     */
    private List<String> tableEnFields = new ArrayList<>();

    /**
     * 关联 id 对字段中文列表
     */
    private List<String> tableCnFields = new ArrayList<>();

    /**
     * 创建时间, eg: "2024-12-23 12:23:00"
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
