package com.baidu.keyue.deepsight.models.dial;

import com.baidu.keyue.deepsight.enums.AiobLineSourceEnum;
import com.baidu.keyue.deepsight.enums.AiobLineStatusEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LineDetailResponse {
    /**
     * 来源：PLATFORM(来自平台)/CUSTOMER(客户自有)
     */
    private AiobLineSourceEnum source;

    /**
     * 号线状态：ENABLED(启用中)/BANNED(已禁止)/ARREARS(已欠费)/DISABLED(已停用)
     */
    private AiobLineStatusEnum lineStatus;


    public LineDetailResponse(Map<String, Object> data) {
        this.source = AiobLineSourceEnum.createByObj(data.get("source"));
        if (this.source == null) {
            this.source = AiobLineSourceEnum.ALL;
        }
        this.lineStatus = AiobLineStatusEnum.createByObj(data.get("lineStatus"));
        if (this.lineStatus == null) {
            this.lineStatus = AiobLineStatusEnum.ALL;
        }
    }
}
