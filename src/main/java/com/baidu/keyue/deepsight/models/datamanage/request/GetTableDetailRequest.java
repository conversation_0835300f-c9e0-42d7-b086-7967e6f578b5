package com.baidu.keyue.deepsight.models.datamanage.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName GetTableDetailRequest
 * @Description 获取数据表详情的请求体
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/1/23 16:38
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetTableDetailRequest {
    /**
     * 数据表id
     */
    @NotNull(message = "dataTableId不能为空")
    private Long dataTableId;
}
