package com.baidu.keyue.deepsight.models.visitor.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName QueryRequest
 * @Description 访客查询请求
 * <AUTHOR>
 * @Date 2025/2/20 3:48 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitorQueryRequest {

    /**
     * 手机号码
     */
    private Long mobile;
    /**
     * id
     */
    private String id;
}
