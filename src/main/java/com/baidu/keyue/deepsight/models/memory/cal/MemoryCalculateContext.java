package com.baidu.keyue.deepsight.models.memory.cal;

import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.bsc.basic.BaseCalculateContext;
import com.baidu.keyue.deepsight.mysqldb.entity.MemoryExtractWithBLOBs;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MemoryCalculateContext extends BaseCalculateContext {
    private MemoryExtractWithBLOBs memory;
    private TriggerModeEnum triggerMode;
    private String ruleSql;
    private Integer count;
    private String tenantId;

    /**
     * 非实时任务
     * @return boolean
     */
    public boolean notRealtimeTask() {
        return !TriggerModeEnum.REALTIME.equals(triggerMode);
    }
}
