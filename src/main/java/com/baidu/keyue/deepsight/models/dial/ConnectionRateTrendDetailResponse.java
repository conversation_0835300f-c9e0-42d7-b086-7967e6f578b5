package com.baidu.keyue.deepsight.models.dial;

import cn.hutool.core.date.DateUtil;
import com.baidu.keyue.deepsight.enums.AlertTimeTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConnectionRateTrendDetailResponse {
    /**
     * 时间点(格式根据时间类型不同而不同)
     * 24小时模式: "HH:mm"
     * 30天模式: "yyyy-MM-dd"
     * @example "2025-05-12" 或 "14:00"
     */
    private String time;

    /**
     * 总拨打次数
     */
    private Integer totalCalls;

    /**
     * 接通率(0-100, 保留两位小数)
     */
    private Double connectedCallsRate;

    public ConnectionRateTrendDetailResponse(Map<String, Object> map) {
        Object time = map.get("time");
        Object totalCalls= map.get("totalCalls");
        Object connectedCallsRate = map.get("connectedCallsRate");
        this.time = time.toString();
        this.totalCalls = (totalCalls == null) ? 0 : ((Long) totalCalls).intValue();
        this.connectedCallsRate = (connectedCallsRate == null) ? 0.00 : ((BigDecimal) connectedCallsRate).doubleValue();
    }

    public static List<ConnectionRateTrendDetailResponse> convert(List<Map<String, Object>> recordList,
                                                                  String timeType, Date now) {
        // 将原始 List 转为 Map<time, a>，方便查找
        Map<String, Map<String, Object>> timeToRecordMap = recordList.stream()
                .collect(Collectors.toMap(
                        m -> m.get("time").toString(),
                        m -> m
                ));

        // 构造过去 24 小时时间点
        List<ConnectionRateTrendDetailResponse> result = new ArrayList<>();
        if (timeType.equals(AlertTimeTypeEnum.DAY_30.getValue())) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            Date date = DateUtil.beginOfDay(now);
            for (int i = 1; i <= 30; i++) {
                String formattedTime = DateUtil.format(DateUtil.offsetDay(date, -i), formatter);
                Map<String, Object> obj = timeToRecordMap.getOrDefault(formattedTime, new HashMap<>(
                        Map.of("time", formattedTime)
                ));
                result.add(new ConnectionRateTrendDetailResponse(obj));
            }
        } else {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:00:00");
            Date date = DateUtil.beginOfHour(now);
            for (int i = 1; i <= 24; i++) {
                String formattedTime = DateUtil.format(DateUtil.offsetHour(date, -i), formatter);
                Map<String, Object> obj = timeToRecordMap.getOrDefault(formattedTime, new HashMap<>(
                        Map.of("time", formattedTime)
                ));
                result.add(new ConnectionRateTrendDetailResponse(obj));
            }
        }
        Collections.reverse(result);
        return result;
    }

}
