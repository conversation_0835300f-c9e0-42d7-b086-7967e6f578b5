package com.baidu.keyue.deepsight.models.customer.request;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @ClassName CustomerDiffusionTaskListRequest
 * @Description 人群扩扩散任务分页查询
 * <AUTHOR>
 * @Date 2025/3/25 1:55 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerDiffusionTaskListRequest extends BasePageRequest implements Serializable {

    /**
     * 任务名
     */
    private String taskName;
}
