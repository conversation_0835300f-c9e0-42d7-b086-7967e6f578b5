package com.baidu.keyue.deepsight.models.memory;

import cn.hutool.core.date.DateTime;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserMemoryRowByDay extends UserMemoryRow {
    private String week;
    private String day;
    private String hour;

    private String dayWithWeek;

    public void dateComplete(DateTime extractDate) {
        this.setWeek(extractDate.dayOfWeekEnum().toChinese("周"));
        this.setDay(DatetimeUtils.dateFormat(extractDate));
        this.setHour(DatetimeUtils.hourFormat(extractDate));

        this.setDayWithWeek(this.getDay() + " " + this.getWeek());
    }

}
