package com.baidu.keyue.deepsight.models.rules.dto;

import java.util.List;

import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: RuleNode
 * @description:
 * @author: wangzhong<PERSON>
 * @date: 2024/12/21 16:02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RuleNode {

    /**
     * 规则类型
     */
    private RuleTypeEnum type;

    /**
     * 数据表id
     */
    private Long dataTableId;

    /**
     * doris数据表名称
     */
    @JsonIgnore
    private String dorisTableName;

    /**
     * 前端预留字段
     */
    private String feExt;

    /**
     * 过滤条件列表
     * 目前事实表只会有一个过滤条件，和一个时间过滤条件，filters 已经能满足需求
     * 后续需要加子查询，需要加一个 List<RuleNode> 属性以及子查询逻辑关系 and or
     */
    private List<RuleFilter> filters;

    public static RuleNode clone(RuleNode ruleNode) {
        String ruleNodeJsonStr = JsonUtils.toJsonUnchecked(ruleNode);
        return JsonUtils.toObjectWithoutException(ruleNodeJsonStr, RuleNode.class);
    }
}
