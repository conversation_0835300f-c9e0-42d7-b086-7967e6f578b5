package com.baidu.keyue.deepsight.models.datamanage.request;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Getter;
import lombok.Setter;


/**
 * @ClassName DataManageListRequest
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2024/12/24 12:02
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetTableListRequest extends BasePageRequest {


    /**
     * 数据类型 0事实数据 1维度数据
     */
    @Max(1)
    @Min(0)
    private Integer tableType;

    /**
     * 数据集名称
     */
    private String tableName;

}
