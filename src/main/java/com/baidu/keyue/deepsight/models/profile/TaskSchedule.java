package com.baidu.keyue.deepsight.models.profile;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskSchedule {

    /**
     * 任务ID
     */
    String taskId;

    /**
     * 任务stage ID
     */
    String taskStageId;

    /**
     * 任务stage ID
     */
    String echoPathId;

    /**
     * 任务类型
     */
    String type;

    /**
     * 任务名称
     */
    String name;

    /**
     * 执行状态
     */
    Integer status;

    /**
     *  执行时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date execTime;
}
