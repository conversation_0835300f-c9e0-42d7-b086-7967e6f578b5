package com.baidu.keyue.deepsight.models.bsc;

import com.baidu.keyue.deepsight.models.bsc.basic.BSCPage;
import com.baidu.keyue.deepsight.models.bsc.basic.BscBaseResponse;
import com.baidu.keyue.deepsight.models.bsc.inner.BscJob;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 获取作业列表参数
 * */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BscListJobsResponse extends BscBaseResponse {
    private BSCPage<BscJob> page;
}
