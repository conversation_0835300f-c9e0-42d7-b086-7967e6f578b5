package com.baidu.keyue.deepsight.models.memory;

import java.util.Date;

import com.baidu.keyue.deepsight.enums.MemoryTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserMemoryRow {
    private Long id;
    private String userId;
    private Date extractDate;
    private Long memoryId;
    private Long datasetId;
    private String memoryContent;
    private MemoryTypeEnum memoryType;
    private Date deepsightDatetime;
}
