package com.baidu.keyue.deepsight.models.bsc.inner;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BscInstanceDetail {
    private String instanceId;
    private String jobId;
    private String name; // 作业名称
    private String type; // 作业类型
    private String jobType; // STREAM/BATCH
    private String engineType; // SPARK/FLINK
    private String engineVersion; // 引擎版本
    private String code; // 作业正文
    private BscJobProperties properties; // 作业的属性
    private String startTime; // 启动时间
    private String endTime; // 结束时间
    private Long runningTime; // 运行时长(ms)
    private String status; // 状态 NEW、STARTING、RUNNING、STOPING、STOPPED、FINISHED、SUCCEEDED、FAILED、KILLED、UNKNOWN
    private String exceptions; // 异常信息
    private String checkpoints; // checkpoints

}
