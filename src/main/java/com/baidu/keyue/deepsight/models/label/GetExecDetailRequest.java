package com.baidu.keyue.deepsight.models.label;

import com.baidu.keyue.deepsight.annotation.LongIdConstraint;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetExecDetailRequest {

    /**
     * 标签ID
     */
    @LongIdConstraint(message = "标签ID无效")
    private Long execId;
}
