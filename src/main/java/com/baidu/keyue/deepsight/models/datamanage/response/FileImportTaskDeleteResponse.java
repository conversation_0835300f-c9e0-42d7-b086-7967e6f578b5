package com.baidu.keyue.deepsight.models.datamanage.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName FileImportTaskDeleteResponse
 * @Description 文件导入数据任务删除响应
 * <AUTHOR>
 * @Date 2025/2/24 11:58 AM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FileImportTaskDeleteResponse {
    /**
     * 数量
     */
    private Integer count;
}
