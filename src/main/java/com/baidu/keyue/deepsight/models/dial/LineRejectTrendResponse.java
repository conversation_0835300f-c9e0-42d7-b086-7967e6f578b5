package com.baidu.keyue.deepsight.models.dial;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LineRejectTrendResponse {
    /**
     * 时间点
     */
    private String time;

    /**
     * 未接通次数
     */
    private Integer rejectCount;

    /**
     * 号线原因导致未接通占比详情
     */
    private List<RejectReasonResponse> rejectReasons;

    public static List<LineRejectTrendResponse> convert(List<Map<String, Object>> rejectCountList,
                                                 List<Map<String, Object>> rejectReasonsList,
                                                 Date now) {
        // 将原始 List 转为 Map<time, a>，方便查找
        Map<String, Map<String, Object>> timeToRecordMap = rejectCountList.stream()
                .collect(Collectors.toMap(
                        m -> m.get("time").toString(),
                        m -> m
                ));

        Map<String, LineRejectTrendResponse> result = new HashMap<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        Date date = DateUtil.beginOfDay(now);
        for (int i = 1; i <= 30; i++) {
            String formattedTime = DateUtil.format(DateUtil.offsetDay(date, -i), formatter);
            Map<String, Object> map = timeToRecordMap.getOrDefault(formattedTime, new HashMap<>(
                    Map.of("time", formattedTime)
            ));
            LineRejectTrendResponse response = new LineRejectTrendResponse();
            Object time = map.get("time");
            Object count = map.get("count");
            response.setTime(time.toString());
            response.setRejectCount((count == null) ? 0 : ((Long) count).intValue());
            response.setRejectReasons(new ArrayList<>());
            result.put(formattedTime, response);
        }

        for (Map<String, Object> map : rejectReasonsList) {
            String time = map.get("time").toString();
            LineRejectTrendResponse response = result.get(time);
            if (response != null) {
                response.getRejectReasons().add(new RejectReasonResponse(map));
            }
        }
        return result.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .map(Map.Entry::getValue)
            .collect(Collectors.toList());
    }
}
