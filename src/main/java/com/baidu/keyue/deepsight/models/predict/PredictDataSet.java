package com.baidu.keyue.deepsight.models.predict;

import com.baidu.keyue.deepsight.annotation.LongIdConstraint;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PredictDataSet {

    /**
     * 数据集ID
     */
    @LongIdConstraint
    private Long datasetId;
    /**
     * 数据集名称
     */
    private String datasetName;

    /**
     * 字段ID
     */
    @LongIdConstraint
    private Long fieldId;
    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 是否是预置数据集，如果是预置则不可删除
     */
    private Boolean isPreset = false;
}
