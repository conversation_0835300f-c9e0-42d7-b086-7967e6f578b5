package com.baidu.keyue.deepsight.models.bsc.datapredict;

import java.util.HashSet;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据预测消息，发送出去供flink任务消费
 */
@Data
public class DataPredictMessage {
    private Long datasetId;
    private String modelUrl;
    /**
     * 更新预测结果模式
     * 0：覆盖 1：如果之前有值则不更新
     */
    private Integer mode = 0;
    private HashSet<String> predictAttributes;

    private String oneId;
    private String originalMobile;
    private String externalId;
    private String tenantId;
    private String prompt;
    private List<Content> contents;

    /**
     * 消息体
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Content {
        private String text;
        private String type;
    }
}
