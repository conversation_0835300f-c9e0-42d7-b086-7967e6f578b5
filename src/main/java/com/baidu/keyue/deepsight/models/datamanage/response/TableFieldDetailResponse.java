package com.baidu.keyue.deepsight.models.datamanage.response;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.baidu.keyue.deepsight.enums.TableFieldValueEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className DataTableDetailResponse
 * @description 数据表字段详情结果
 * @date 2024/12/25 16:56
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TableFieldDetailResponse {

    /**
     * 数据表id
     */
    private Long dataTableId;

    /**
     * 中文名称
     */
    private String cnName;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 数据类型 string单值 strings多值 number数字类 time时间类
     */
    private String fieldType;

    /**
     * 字段描述
     */
    private String description;

    /**
     * 是否必填
     */
    private Boolean isRequired;

    /**
     * 字段标记 0无 1主键 2事件 3度量 4敏感 5分区 6id
     */
    private Integer fieldTag;

    /**
     * 字段取值类型 如strings:enum/text
     */
    private String valueType;

    /**
     * 数据库字段类型
     */
    private String dataType;

    /**
     * 作为筛选条件
     */
    private Boolean isFilterCriteria;

    /**
     * 是否展示value值
     */
    private Boolean isShowValue;

    /**
     * 高级配置
     */
    private List<Map<String, String>> configInfos;

    public TableFieldDetailResponse(TableFieldMetaInfo metaInfo) {
        this.setDataTableId(metaInfo.getDataTableId());
        this.setFieldTag(metaInfo.getFieldTag());
        this.setFieldType(metaInfo.getFieldType());
        this.setDescription(metaInfo.getDescription());
        this.setCnName(metaInfo.getCnField());
        this.setEnName(metaInfo.getEnField());
        this.setIsShowValue(metaInfo.getIsShowValue());
        this.setIsFilterCriteria(metaInfo.getIsFilterCriteria());
        if (TableFieldValueEnum.ENUM.getValueType().equals(metaInfo.getValueType())
                && null == metaInfo.getIsShowValue() ) {
            this.setIsShowValue(false);
        } else {
            this.setIsShowValue(metaInfo.getIsShowValue());
        }
        this.setValueType(metaInfo.getValueType());
        this.setDataType(metaInfo.getDataType());
        this.setIsRequired(metaInfo.getIsRequired());
        if (StringUtils.isNotEmpty(metaInfo.getConfigInfos())) {
            this.setConfigInfos(JSON.parseObject(metaInfo.getConfigInfos(),
                    new TypeReference<List<Map<String, String>>>() {}));
        }
    }




}
