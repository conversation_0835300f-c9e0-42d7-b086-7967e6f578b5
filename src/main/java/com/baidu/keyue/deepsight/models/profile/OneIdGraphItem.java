package com.baidu.keyue.deepsight.models.profile;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class OneIdGraphItem {

    /**
     * 名称
     */
    private String name;
    /**
     * 优先级
     */
    private Integer priority;
    /**
     * 原始值列表
     */
    private List<String> values;
}
