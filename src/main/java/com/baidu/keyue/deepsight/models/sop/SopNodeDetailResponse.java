package com.baidu.keyue.deepsight.models.sop;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SopNodeDetailResponse {
    /**
     * 节点列表
     */
    private List<SopNodeWithHangup> nodes;

    /**
     * 边列表
     */
    private List<SopEdge> edges;

    /**
     * 每日下游流量分布
     */
    private List<SopNodeDailyDistribute> dailyDistribute;
}
