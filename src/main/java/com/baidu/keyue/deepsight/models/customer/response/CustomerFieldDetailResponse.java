package com.baidu.keyue.deepsight.models.customer.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName CustomerFieldDetailResponse
 * @Description 客群分析的用户字段返回体
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/1/9 14:56
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerFieldDetailResponse {
    /**
     * 数据表id
     */
    private Long dataTableId;

    /**
     * 中文名称
     */
    private String cnName;

    /**
     * 英文名称
     */
    private String enName;
}
