package com.baidu.keyue.deepsight.models.datamanage.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className TableFieldConfigReq
 * @description 数据表高级配置更新请求
 * @date 2024/12/25 17:18
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateTableFieldConfigRequest {

    /**
     * 数据表id
     */
    @NotNull(message = "dataTableId不能为空")
    private Long dataTableId;

    /**
     * 数据字段英文名
     */
    private String enName;

    /**
     * 字段取值类型 如string:enum/text
     */
    private String valueType;

    /**
     * 是否用作筛选条件，true的字段会在数据筛选器中显示
     */
    private Boolean isFilterCriteria;

    /**
     * 字段标记 0无 1主键 2事件 3度量 4敏感 5分区 6id
     */
    private Integer fieldTag;

    /**
     * 高级配置信息
     */
    private List<Map<String, String>> configInfos;


    /**
     * 数据记录展示value值
     */
    private Boolean  isShowValue;






}
