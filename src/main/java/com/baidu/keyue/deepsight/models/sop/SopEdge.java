package com.baidu.keyue.deepsight.models.sop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SopEdge {

    /**
     * 起始节点 id
     */
    private String fromNodeId;

    /**
     * 到达节点 id
     */
    private String endNodeId;

    /**
     * 边权重
     */
    private Long weight;

    /**
     * 边权重uv
     */
    private Long weightUv;

    public SopEdge(String fromNodeId, String endNodeId, Long weight) {
        this.fromNodeId = fromNodeId;
        this.endNodeId = endNodeId;
        this.weight = weight;
    }
}
