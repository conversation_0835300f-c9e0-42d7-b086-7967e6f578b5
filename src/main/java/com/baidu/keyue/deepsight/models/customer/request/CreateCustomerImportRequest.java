package com.baidu.keyue.deepsight.models.customer.request;

import com.baidu.keyue.deepsight.enums.MatchPoliciesEnum;
import com.baidu.keyue.deepsight.models.datamanage.dto.FileDetailDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * @ClassName CreateCustomerImportRequest
 * @Description 客群导入请求体
 * <AUTHOR>
 * @Date 2025/3/25 2:25 PM
 */
@Data
public class CreateCustomerImportRequest {
    
    /**
     * 客群名称
     */
    @NotBlank(message = "客群名称不能为空")
    private String name;
    
    /**
     * 文件key
     */
    @NotEmpty(message = "文件详情不能为空")
    @Size(max = 3, message = "单次最多上传三个文件")
    private @Valid List<FileDetailDto> files;
    /**
     * 匹配策略：ALL-保留全部用户 ONLY_MATCH-仅保留匹配用户
     */
    @NotNull(message = "匹配策略不能为空")
    private MatchPoliciesEnum matchPolicies;
    
    @JsonIgnore
    private String tenantId;
}
