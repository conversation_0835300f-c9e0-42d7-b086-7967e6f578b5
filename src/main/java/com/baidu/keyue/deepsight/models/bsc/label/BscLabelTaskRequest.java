package com.baidu.keyue.deepsight.models.bsc.label;

import java.util.List;
import java.util.Map;

import com.baidu.keyue.deepsight.models.bsc.basic.BscTableIdentify;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BscLabelTaskRequest {
    /**
     * 任务执行 id
     */
    private Long execId;

    /**
     *     REPLACE((byte) 0, "每次更新重新计算"),
     *     MERGE((byte) 1, "每次更新合并历史数据");
     */
    private Byte updateModEnum;

    /**
     *     SINGLE((byte) 0, "单值"),
     *     MULTI((byte) 1, "多值");
     */
    private Byte saveModEnum;

    /**
     * source 表元数据
     */
    private List<BscTableIdentify> sourceTable = Lists.newArrayList();

    /**
     * sink 表元数据
     */
    private List<BscTableIdentify> sinkTable = Lists.newArrayList();

    /**
     * 计算规则
     * */
    private List<BscLabelTaskLabelCalculate> calculates = Lists.newArrayList();

    private Map<String, Integer> valuePriorityMap;
}
