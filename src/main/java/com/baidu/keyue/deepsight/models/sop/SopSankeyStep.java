package com.baidu.keyue.deepsight.models.sop;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SopSankeyStep {
    /**
     * 步骤 id
     */
    private String stepId;
    /**
     * 步骤名
     */
    private String stepName;
    /**
     * 步骤下的所有节点
     */
    private List<SopNode> nodes;
}
