package com.baidu.keyue.deepsight.models.customer.request;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName GetCustomerListRequest
 * @Description 客群用户列表请求体
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/1/10 15:11
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetCustomerListRequest extends BasePageRequest {
    /**
     * 客群ID
     */
    private Long customerGroupId;

    /**
     * 过滤条件
     */
    private List<RuleFilter> filters;
}
