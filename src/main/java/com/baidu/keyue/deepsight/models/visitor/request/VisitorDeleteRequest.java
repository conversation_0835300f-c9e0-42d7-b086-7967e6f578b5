package com.baidu.keyue.deepsight.models.visitor.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName VisitorInsertRequest
 * @Description 访客写入请求体
 * <AUTHOR>
 * @Date 2025/2/20 3:51 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitorDeleteRequest {

    /**
     * 手机号码
     */
    private Long mobile;
    /**
     * id
     */
    private String id;
}
