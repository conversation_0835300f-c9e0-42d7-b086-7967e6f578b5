package com.baidu.keyue.deepsight.models.rules.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @className: UserPropertiesResponse
 * @description: 用户属性响应
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/25 18:57
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPropertiesResponse {

    /**
     * 用户属性结果列表
     */
    List<UserPropertiesResult> list;

}
