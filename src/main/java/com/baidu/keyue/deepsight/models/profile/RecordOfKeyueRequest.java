package com.baidu.keyue.deepsight.models.profile;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName RecordOfKeyueRequest
 * @Description 客服对话内容记录查询请求
 * <AUTHOR>
 * @Date 2025/3/13 6:12 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RecordOfKeyueRequest extends BasePageRequest {
    /**
     * user_id
     */
    private String userId;

    /**
     * oneId
     */
    private String oneId;
}
