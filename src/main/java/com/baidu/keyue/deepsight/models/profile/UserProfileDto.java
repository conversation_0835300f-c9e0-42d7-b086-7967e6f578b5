package com.baidu.keyue.deepsight.models.profile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.utils.ORMUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserProfileDto {
    private String oneId;
    private String tenantId;

    private Map<String, Object> mergedMockUserData;
    private Map<String, Object> userProfileRow;

    private boolean oneIdExist;

    public UserProfileDto(String tenantId, String oneId) {
        this.oneId = oneId;
        this.tenantId = tenantId;
        this.mergedMockUserData = new HashMap<>();
        this.userProfileRow = new HashMap<>();
    }

    public void mergeMockUserData(List<Map<String, Object>> rows) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }

        Map<String, Object> result = rows.get(0);

        for (int i = 1; i < rows.size(); i++) {
            Map<String, Object> row = rows.get(i);
            row.forEach((key, value) -> {
                if (Objects.nonNull(value) && Objects.isNull(result.get(key))) {
                    result.put(key, value);
                }
            });
        }

        mergedMockUserData = result;
    }

    public boolean isEmptyMockUserData() {
        return mergedMockUserData.isEmpty();
    }

    public void mergeProfileRow(Map<String, Object> mockProfileRow) {
        if (mockProfileRow.isEmpty()) {
            userProfileRow = mergedMockUserData;
            oneIdExist = false;
        } else {
            oneIdExist = true;
            userProfileRow = mockProfileRow;
            mergedMockUserData.forEach((key, value) -> {
                if (Objects.nonNull(value) && Objects.isNull(userProfileRow.get(key))) {
                    userProfileRow.put(key, value);
                }
            });
        }
    }

    public String genSql(String tableName, Map<String, String> userProfileTableSchema) {
        userProfileTableSchema.remove(Constants.TABLE_USER_ONE_ID);
        userProfileTableSchema.remove(Constants.DORIS_DEFAULT_DATA_INSERT_DATE_FIELD);
        userProfileTableSchema.remove(Constants.DORIS_DEFAULT_DATA_UPDATE_DATE_FIELD);
        if (oneIdExist) {
            return ORMUtils.genUpdateUserProfileSql(this, tableName, oneId, userProfileTableSchema);
        } else {
            return ORMUtils.genInsertUserProfileSql(this, tableName, oneId, userProfileTableSchema);
        }
    }
}
