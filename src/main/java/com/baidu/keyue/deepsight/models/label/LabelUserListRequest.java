package com.baidu.keyue.deepsight.models.label;

import cn.hutool.core.collection.CollUtil;
import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.enums.FuncEnum;
import com.baidu.keyue.deepsight.enums.LogicEnum;
import com.baidu.keyue.deepsight.enums.RuleTypeEnum;
import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.baidu.keyue.deepsight.models.rules.dto.RuleNode;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName LabelUserListRequest
 * @Description 标签下用户列表查询
 * <AUTHOR>
 * @Date 2025/5/8 3:07 PM
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LabelUserListRequest extends BasePageRequest {
    /**
     * 标签ID
     */
    @NotNull(message = "标签ID不能为空")
    private Long labelId;

    /**
     * 标签对应的字段
     */
    private Long labelFieldId;
    
    /**
     * 标签值
     */
    private List<String> labelValues;

    /**
     * 过滤条件
     */
    private List<RuleFilter> filters;

    public List<RuleFilter> getFilters(String labelFieldName) {
        List<RuleFilter> ruleFilters = new ArrayList<>();
        boolean empty = CollUtil.isEmpty(this.labelValues);
        RuleFilter filter = new RuleFilter();
        filter.setType(FilterTypeEnum.STRING);
        filter.setFieldId(labelFieldId);
        filter.setFieldDataType("array");
        // 默认不传值，查询所有标签值用户，即标签字段不为[],否则就是包含查询
        filter.setFunction(empty ? FuncEnum.IS_NOT_NULL : FuncEnum.CONTAIN);
        filter.setParams(empty ? null : labelValues);
        filter.setFiled(labelFieldName);
        ruleFilters.add(filter);
        return ruleFilters;

    }

    public RuleGroup buildRuleGroup(Long tableId, String labelFieldName) {
        RuleGroup ruleGroup = new RuleGroup();
        ruleGroup.setRelation(LogicEnum.AND);
        List<RuleGroup> ruleGroups = new ArrayList<>();
        // 构造 标签ruleNode
        RuleNode labelruleNode = RuleNode.builder()
                .dataTableId(tableId)
                .filters(this.getFilters(labelFieldName))
                .type(RuleTypeEnum.LABEL).build();
        RuleGroup labelRuleGroup = new RuleGroup();
        labelRuleGroup.setRelation(LogicEnum.AND);
        labelRuleGroup.setRuleNodes(List.of(labelruleNode));
        ruleGroups.add(labelRuleGroup);
        if (CollUtil.isNotEmpty(this.filters)) {
            RuleNode filterRuleNode = RuleNode.builder()
                    .dataTableId(tableId)
                    .filters(this.filters)
                    .type(RuleTypeEnum.DATASET).build();
            RuleGroup otherlRuleGroup = new RuleGroup();
            otherlRuleGroup.setRelation(LogicEnum.AND);
            otherlRuleGroup.setRuleNodes(List.of(filterRuleNode));
            ruleGroups.add(otherlRuleGroup);
        }
        // 构建组合过滤
        ruleGroup.setRuleGroups(ruleGroups);
        return ruleGroup;
    }
}
