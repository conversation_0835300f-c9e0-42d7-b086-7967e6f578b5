package com.baidu.keyue.deepsight.models.idmapping.response.rule;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @className: IdMappingRuleItemResponse
 * @description: id mapping 字段列表查询响应
 * @author: chenwenyu03
 * @date: 2025/3/10 15:51
 */
@Data
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdMappingRuleFieldsResponse {
    /**
     * 提取字段列表
     */
    List<IdMappingRuleItemResponse> fields;

    /**
     * 已使用的优先级
     */
    List<Integer> usedPriorities;
}
