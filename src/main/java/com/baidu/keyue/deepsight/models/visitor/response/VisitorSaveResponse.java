package com.baidu.keyue.deepsight.models.visitor.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName VisitorSaveResponse
 * @Description 访客添加返回
 * <AUTHOR>
 * @Date 2025/2/20 6:49 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VisitorSaveResponse {
    /**
     * 数据ID
     */
    public String id;
}
