package com.baidu.keyue.deepsight.models.catalog;

import com.baidu.keyue.deepsight.annotation.CatalogNameConstraint;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class NewCatalogRequest {

    /**
     * 标签目录名称
     */
    @CatalogNameConstraint
    private String catalogName;

    /**
     * 标签目录-父级目录 ID
     */
    private Long parentId = 0L;


    public Boolean notRootCatalog() {
        return parentId != 0;
    }
}
