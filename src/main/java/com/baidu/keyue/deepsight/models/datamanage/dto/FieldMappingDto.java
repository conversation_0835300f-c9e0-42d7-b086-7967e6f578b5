package com.baidu.keyue.deepsight.models.datamanage.dto;

import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.validation.annotation.Validated;

/**
 * @ClassName FieldMappingDto
 * @Description 字段映射DTO
 * <AUTHOR>
 * @Date 2025/2/21 3:33 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Validated
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FieldMappingDto {
    /**
     * Excel源字段英文名
     */
    private String sourceEnName;
    /**
     * Excel源字段中文名
     */
    private String sourceCnName;
    /**
     * Excel源标字段类型
     */
    private String sourceType;
    /**
     * 目标字段英文名
     */
    @NotBlank(message = "数据集字段字段英文名不能为空")
    private String tagEnName;
    /**
     * 数据集字段中文名
     */
    private String tagCnName;
    
    /**
     * 目标字段类型
     */
    @NotBlank(message = "数据集字段字段类型不能为空")
    private String tagType;

    /**
     * Doris字段是否必填：true-是，false-否
     */
    private Boolean tagIsRequired;

    public FieldMappingDto(RowDataDto excelField, VisibleFieldResponse field) {
        this.tagEnName = field.getEnName();
        this.tagCnName = field.getCnName();
        this.tagType = field.getDataType();
        this.tagIsRequired = field.getIsRequired();
        if (excelField != null) {
            this.sourceEnName = excelField.getEnName();
            this.sourceCnName = excelField.getCnName();
            this.sourceType = excelField.getDataType();
        }
    }

    public FieldMappingDto(VisibleFieldResponse field) {
        this.tagEnName = field.getEnName();
        this.tagCnName = field.getCnName();
        this.tagType = field.getDataType();
        this.tagIsRequired = field.getIsRequired();
    }
}
