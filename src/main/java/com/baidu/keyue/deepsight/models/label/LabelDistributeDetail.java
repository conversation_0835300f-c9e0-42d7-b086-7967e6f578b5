package com.baidu.keyue.deepsight.models.label;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LabelDistributeDetail {

    /**
     * 标签名
     */
    private String labelName;

    /**
     * 总记录数
     */
    private Long count;

    /**
     * 占比，eg：“20%”
     */
    private String percent;
}
