package com.baidu.keyue.deepsight.models.rules.response;

import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.enums.TableFieldValueEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * @className: UserPropertiesResult
 * @description: 用户属性
 * @author: wangzhongcheng
 * @date: 2024/12/25 19:09
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class UserPropertiesResult extends BaseProperties {

    private List<FilterEnumInfo> enums;

    /**
     * 将数据集配置的字段转为可选数据类型
     * @param tableFieldMetaInfo 字段信息
     * @return DatasetPropertiesResult 数据筛选可选字段
     */
    public static UserPropertiesResult convertFrom(TableFieldMetaInfo tableFieldMetaInfo) {
        UserPropertiesResult userPropertiesResult = new UserPropertiesResult();
        userPropertiesResult.setFieldId(tableFieldMetaInfo.getId());
        userPropertiesResult.setCname(tableFieldMetaInfo.getCnField());
        userPropertiesResult.setDataType(FilterTypeEnum.convertFrom(tableFieldMetaInfo.getFieldType()).getType());
        // 用户基础属性不参与聚合操作
        userPropertiesResult.setIsIdKey(false);
        userPropertiesResult.setIsMeasure(false);

        if (TableFieldValueEnum.ENUM.equals(TableFieldValueEnum.getByValueType(tableFieldMetaInfo.getValueType()))
                && StringUtils.isNotEmpty(tableFieldMetaInfo.getConfigInfos())) {
            List<FilterEnumInfo> filterEnumInfos = JsonUtils.toListUnchecked(
                    tableFieldMetaInfo.getConfigInfos(), List.class, FilterEnumInfo.class);
            userPropertiesResult.setEnums(filterEnumInfos);
        }
        return userPropertiesResult;
    }

}
