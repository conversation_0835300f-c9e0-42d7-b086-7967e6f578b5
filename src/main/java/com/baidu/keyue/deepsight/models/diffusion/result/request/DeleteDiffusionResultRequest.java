package com.baidu.keyue.deepsight.models.diffusion.result.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: DeleteDiffusionResultRequest
 * @description: 删除扩散结果请求
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2025/3/27 20:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeleteDiffusionResultRequest {

    /**
     * 全局唯一ID
     */
    @NotNull(message = "全局唯一ID不能为空")
    private String oneId;

    @NotNull(message = "人群扩散任务ID不能为空")
    private Long customerDiffusionTaskId;

}
