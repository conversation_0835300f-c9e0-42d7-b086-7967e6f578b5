package com.baidu.keyue.deepsight.models.customer.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

/**
 * @ClassName CustomerDorisResponse
 * @Description 客群分析的dorisSql返回体
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerDorisResponse {
    /**
     * 总数
     */
    private Long count;

    /**
     * 总数sql
     */
    private String countSql;

    /**
     * 分页查询sql
     */
    private String selectSql;
    /**
     * doris表名
     */
    private String dorisTableName;

    /**
     * 数据表ID dataTableInfo.id
     */
    private Long dataTableId;
}
