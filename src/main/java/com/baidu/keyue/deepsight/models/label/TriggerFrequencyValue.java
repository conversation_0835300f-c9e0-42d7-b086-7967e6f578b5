package com.baidu.keyue.deepsight.models.label;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TriggerFrequencyValue {
    /**
     * 小时[0 ~ 23]， 默认 1
     * */
    @Range(min = 0, max = 23, message = "hour必须在0-23之间")
    private Integer hour;

    /**
     * 天[1 ~ 31]， 默认 1
     */
    @Range(min = 1, max = 31, message = "dayOfMonth必须在1-31之间")
    private Integer dayOfMonth;

    /**
     * 星期[1 ~ 7]， 默认 1
     */
    @Range(min = 1, max = 7, message = "dayOfWeek必须在1-7之间")
    private Integer dayOfWeek;
}
