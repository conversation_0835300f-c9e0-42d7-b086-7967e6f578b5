package com.baidu.keyue.deepsight.models.idmapping.dto;

import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelation;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @className: IdMappingRelFieldDTO
 * @description: id对的抽取字段对象
 * @author: wangzhongcheng
 * @date: 2025/3/7 15:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdMappingRelFieldDTO {

    /**
     * 表英文名
     */
    private String tableEnField;

    /**
     * 表中文名
     */
    private String tableCnField;

    /**
     * 是否推荐字段：true-ID推荐字段，false-非ID推荐字段
     */
    private Boolean idRecommended;

    /**
     * 将ID对抽取实体对象转换为ID对字段传输对象列表。
     *
     * @param idMappingRelation ID对抽取实体对象
     * @return 转换后的数据传输对象列表
     */
    public static List<IdMappingRelFieldDTO> convertFrom(IdMappingRelation idMappingRelation) {
        List<IdMappingRelFieldDTO> idMappingRelFieldDTOS = new ArrayList<>();
        List<String> enFields = JsonUtils.toListUnchecked(idMappingRelation.getEnFields(), List.class, String.class);
        List<String> cnFields = JsonUtils.toListUnchecked(idMappingRelation.getCnFields(), List.class, String.class);

        for (int i = 0; i < enFields.size(); i++) {
            IdMappingRelFieldDTO idMappingRelFieldDTO = new IdMappingRelFieldDTO();
            idMappingRelFieldDTO.setTableEnField(enFields.get(i));
            idMappingRelFieldDTO.setTableCnField(cnFields.get(i));
            idMappingRelFieldDTOS.add(idMappingRelFieldDTO);
        }

        return idMappingRelFieldDTOS;
    }

    /**
     * 将TableFieldMetaInfo对象列表转换为ID对字段传输对象列表。
     *
     * @param tableFieldMetaInfos TableFieldMetaInfo对象列表，包含字段的元信息
     * @return 转换后的数据传输对象列表
     */
    public static List<IdMappingRelFieldDTO> convertFrom(List<TableFieldMetaInfo> tableFieldMetaInfos) {
        return tableFieldMetaInfos.stream().map(tableFieldMetaInfo -> {
            IdMappingRelFieldDTO idMappingRelFieldDTO = new IdMappingRelFieldDTO();
            Integer fieldTag = tableFieldMetaInfo.getFieldTag();
            idMappingRelFieldDTO.setTableEnField(tableFieldMetaInfo.getEnField());
            idMappingRelFieldDTO.setTableCnField(tableFieldMetaInfo.getCnField());
            // 判断字段是否为ID推荐字段
            Boolean idRecommended = !Objects.isNull(fieldTag) &&
                    TableFieldTagEnum.getByCode(fieldTag) == TableFieldTagEnum.ID;

            idMappingRelFieldDTO.setIdRecommended(idRecommended);
            return idMappingRelFieldDTO;
        }).toList();
    }
}
