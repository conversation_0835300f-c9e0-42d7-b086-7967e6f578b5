package com.baidu.keyue.deepsight.models.customer.response;

import com.baidu.keyue.deepsight.enums.GroupingTypeEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerGroupDetailResponse {

    /**
     * 客群ID
     */
    private Long customerGroupId;

    /**
     * 数据表id - 客群数据表id
     */
    private Long dataTableId;

    /**
     * 客群名称
     */
    private String customerGroupName;

    /**
     * 客群描述
     */
    private String customerGroupDescription;


    /**
     * 更新方式
     */
    private TriggerModeEnum triggerMod;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间, eg: "2024-12-23 12:23:00"
     */
    private String createTime;

    /**
     * 状态
     */
    private TaskExecStatusEnum status;


    /**
     * 客群更新取值逻辑
     */
    private UpdateModEnum updateMod;

    /**
     * 执行频率
     */
    private TriggerFrequencyEnum triggerFrequency;

    /**
     * 执行频率具体时间配置
     * */
    private TriggerFrequencyValue triggerFrequencyValue;

    /**
     * 最新一次计算时间，eg：“2024-12-22”
     */
    private String lastTaskDate;

    /**
     * 客群值规则
     * */
    private RuleGroup customerGroupRule;

    /**
     * 是否为预置客群: true-预置客群, false-非预置客群
     */
    private Boolean isPreset;

    /**
     * 分群方式:RULE_CIRCLE-规则圈选 FILE_IMPORT-文件导入 MODEL_PREDICTION-模型预测
     */
    private GroupingTypeEnum groupingType;

    /**
     * 是否配置标签: true-配置按钮启用, false-配置置灰
     */
    private Boolean configTag;

}
