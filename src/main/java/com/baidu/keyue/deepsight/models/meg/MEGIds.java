package com.baidu.keyue.deepsight.models.meg;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.common.collect.Lists;
import lombok.Data;

@Data
public class MEGIds {
    private Map<String, List<String>> idsMap;

    public MEGIds() {
        this.idsMap = new HashMap<>();
    }

    public void append(String type, List<String> ids) {
        this.idsMap.put(type, ids);
    }

    public List<String> get(MEGIdEnum e) {
        return this.idsMap.getOrDefault(e.getName(), Lists.newArrayList());
    }

    public boolean isEmpty() {
        return this.idsMap.isEmpty();
    }
}
