package com.baidu.keyue.deepsight.models.bsc.basic;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BSCPage<T> {
    private Integer pageNo;
    private Integer pageSize;
    private Integer totalCount;
    private String orderBy;
    private List<T> result;
}
