package com.baidu.keyue.deepsight.models.datamanage.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @className ElasticMapping
 * @description mapping类型字段列表
 * @date 2025/3/5 11:54
 */
@Data
public class ElasticMapping {

    /**
     * float 类型
     */
    private List<String> floatFields;
    /**
     * int 类型
     */
    private List<String> intFields;
    /**
     * boolean 类型
     */
    private List<String> boolFields;

    /**
     * date 类型
     */
    private List<String> dateFields;
    /**
     * es keyword类型字段（不可模糊查询），其他自适应为text/long类型
     */
    private List<String> keywords;
    /**
     * es不索引字段，不可被查询。
     */
    private List<String> noIndex;
    /**
     * es object类型字段
     */
    private List<String> objects;
    /**
     * es 7.9+模糊查询字段
     */
    private List<String> wildcards;
    /**
     * ik切词插件字段列表
     */
    private List<String> ikFields;
    /**
     * 多类型字段
     */
    private List<String> multiFields;
}
