package com.baidu.keyue.deepsight.models.dial;

import cn.hutool.core.date.DateTime;
import com.baidu.keyue.deepsight.enums.AiobLineSourceEnum;
import com.baidu.keyue.deepsight.enums.AiobLineStatusEnum;
import com.baidu.keyue.deepsight.enums.AiobRobotTypeEnum;
import com.baidu.keyue.deepsight.enums.AiobSortFieldEnum;
import com.baidu.keyue.deepsight.enums.AiobTaskStatusEnum;
import com.baidu.keyue.deepsight.enums.AlertConfigTypeEnum;
import com.baidu.keyue.deepsight.enums.SortTypeEnum;
import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ThirtyDayRankingRequest extends BasePageRequest {
    /**
     * 类型：LINE(号线)/TASK(任务)/ROBOT(机器人)
     */
    @NotNull(message = "类型不能为空")
    private AlertConfigTypeEnum type;

    /**
     * 来源：ALL(全部来源)/PLATFORM(来自平台)/CUSTOMER(客户自有)
     */
    private AiobLineSourceEnum source;

    /**
     * 号线状态：ALL(全部状态)/ENABLED(启用中)/BANNED(已禁止)/ARREARS(已欠费)/DISABLED(已停用)
     */
    private AiobLineStatusEnum lineStatus;

    /**
     * 任务状态：ALL(全部状态)/RUNNING(执行中)/PAUSED(已暂停)/COMPLETED(已完成)
     */
    private AiobTaskStatusEnum taskStatus;

    /**
     * 机器人类型：ALL(全部类型)/QUICK_SCENES(快捷场景)/FLEX_CANVAS(灵活画布)
     */
    private AiobRobotTypeEnum robotType;

    /**
     * 排序字段：CALL_COUNT(拨打次数)/CONNECTED_COUNT(呼通次数)/CONNECTED_RATE(接通率)/ALARM_DAYS(告警天数)
     * /UNCONNECTED_COUNT(未接通次数)/LINE_REASON_RATE(号线原因未接通占比)/PLATFORM_REASON_RATE(平台原因未接通占比)
     */
    private AiobSortFieldEnum sortField;

    /**
     * 排序：ASC(正排)/DESC(倒排)
     */
    private SortTypeEnum sortOrder;
    
    private DateTime startTime;
    
    private DateTime endTime;
}
