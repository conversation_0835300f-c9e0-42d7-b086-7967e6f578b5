package com.baidu.keyue.deepsight.models.catalog;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.baidu.keyue.deepsight.models.label.LabelBrief;
import com.baidu.keyue.deepsight.models.label.LabelRule;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ListCatalogResponse {

    /**
     * 标签目录列表
     */
    private List<CatalogDetail> results;

    public List<Long> flattenCatalogIds() {
        if (results != null) {
            return flat(results);
        }
        return null;
    }

    private static List<Long> flat(List<CatalogDetail> results) {
        List<Long> ids = Lists.newArrayList();
        results.forEach(result -> {
            if (CollectionUtils.isNotEmpty(result.getChildren())) {
                ids.addAll(flat(result.getChildren()));
            }
            ids.add(result.getCatalogId());
        });
        return ids;
    }

    public void fillLabel(Map<Long, List<LabelWithBLOBs>> labelMap) {
        fill(labelMap, results);
    }

    public void fillLabelAndCount(Map<Long, List<LabelWithBLOBs>> labelMap) {
        fillCount(labelMap, results);
    }

    public void fillLabelWithFilter(Map<Long, List<LabelWithBLOBs>> labelMap) {
        fill(labelMap, results);
        results = emptyFilter(results);
    }

    private List<CatalogDetail> emptyFilter(List<CatalogDetail> results) {
        return results.stream()
                .filter(res -> res.getCount() > 0)
                .peek(item -> {
                    if (CollectionUtils.isNotEmpty(item.getChildren())) {
                        item.setChildren(emptyFilter(item.getChildren()));
                    }
                })
                .toList();
    }

    public void fill(Map<Long, List<LabelWithBLOBs>> labelMap, List<CatalogDetail> results) {
        results.forEach(res -> {
            List<LabelWithBLOBs> labels = labelMap.get(res.getCatalogId());
            if (CollectionUtils.isNotEmpty(labels)) {
                res.setLabels(labels.stream()
                        .map(i -> {
                            LabelBrief brief = new LabelBrief(
                                    i.getId(), i.getField(), i.getLabelName(), Lists.newArrayList());

                            LabelRule rule = JsonUtils.toObjectWithoutException(i.getLabelRule(), LabelRule.class);
                            if (Objects.nonNull(rule)) {
                                brief.setLabelValueRange(rule.labelStringValueParser());
                            }
                            return brief;
                        }).toList()
                );
                res.setCount(labels.size());
            } else {
                res.setCount(0);
            }

            if (CollectionUtils.isNotEmpty(res.getChildren())) {
                fill(labelMap, res.getChildren());

                res.getChildren().forEach(child -> {
                    if (Objects.nonNull(child.getCount())) {
                        res.setCount(res.getCount() + child.getCount());
                    }
                });
            }
        });
    }

    public void fillCount(Map<Long, List<LabelWithBLOBs>> labelMap, List<CatalogDetail> results) {
        results.forEach(res -> {
            List<LabelWithBLOBs> labels = labelMap.get(res.getCatalogId());
            if (CollectionUtils.isNotEmpty(labels)) {
                res.setCount(labels.size());
            } else {
                res.setCount(0);
            }

            if (CollectionUtils.isNotEmpty(res.getChildren())) {
                fillCount(labelMap, res.getChildren());

                res.getChildren().forEach(child -> {
                    if (Objects.nonNull(child.getCount())) {
                        res.setCount(res.getCount() + child.getCount());
                    }
                });
            }
        });
    }

}
