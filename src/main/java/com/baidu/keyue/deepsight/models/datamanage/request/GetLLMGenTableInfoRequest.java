package com.baidu.keyue.deepsight.models.datamanage.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @className GetAiGenFieldsRequest
 * @description ai生成字段请求
 * @date 2025/2/13 14:58
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetLLMGenTableInfoRequest {


    /**
     * promptContent
     */
    @NotNull(message = "promptContent不能为空")
    private String promptContent;

}
