package com.baidu.keyue.deepsight.models.datamanage.response;

import com.baidu.keyue.deepsight.enums.ImportStatusEnum;
import com.baidu.keyue.deepsight.models.datamanage.dto.TaskFileImportDto;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ClassName TaskFileImportResponse
 * @Description 文件导入数据返回体
 * <AUTHOR>
 * @Date 2025/2/14 3:35 PM
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FileImportTaskQueryResponse implements Serializable {


    /**
     * 任务组ID
     */
    private String groupId;

    /**
     * 任务状态：1-导入中时不可删除，其他状态可删除
     */
    private Integer status;
    /**
     * 卡片创建时间
     */
    private Date createTime;

    /**
     * 卡片任务列表
     */
    private List<TaskFileImportDto> taskList;

    public FileImportTaskQueryResponse(String groupId, List<TaskFileImportDto> taskList) {
        this.groupId = groupId;
        this.taskList = taskList;
        this.status = ImportStatusEnum.IMPORT_SUCCESS.getStatus();
        for (TaskFileImportDto taskFileImportDto : taskList) {
            if (ImportStatusEnum.IMPORT_FAIL.getStatus().equals(taskFileImportDto.getStatus())) {
                this.status = ImportStatusEnum.IMPORT_FAIL.getStatus();
                break;
            }
        }
        this.createTime = taskList.get(0).getCreateTime();
    }
}