package com.baidu.keyue.deepsight.models.label;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LabelBrief {

    /**
     * 标签ID
     */
    private Long labelId;

    /**
     * 宽表字段ID，数据筛选展示使用
     */
    @JsonIgnore
    private Long field;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签值范围
     */
    private List<String> labelValueRange;
}
