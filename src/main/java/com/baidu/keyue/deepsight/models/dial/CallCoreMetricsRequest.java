package com.baidu.keyue.deepsight.models.dial;

import com.baidu.keyue.deepsight.enums.DateLineTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CallCoreMetricsRequest {
    /**
     * 类型：LINE(号线)/TASK(任务)/ROBOT(机器人)/为空表示统计该租户下全部数据
     * @example LINE
     */
    private String type;

    /**
     * 类型对应的id
     * @example "line-123"
     */
    private String id;

    /**
     * 时间类型：hour / day
     */
    private DateLineTypeEnum dateType;
}
