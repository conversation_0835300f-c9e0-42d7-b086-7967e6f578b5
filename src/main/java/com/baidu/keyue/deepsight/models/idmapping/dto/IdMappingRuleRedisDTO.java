package com.baidu.keyue.deepsight.models.idmapping.dto;

import com.baidu.keyue.deepsight.enums.IdMappingRuleFiledTypeEnum;
import com.baidu.keyue.deepsight.enums.IdMappingRuleMergePolicyEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: IdMappingRuleRedisDTO
 * @description: id映射规则redis dto
 * @author: wangzhong<PERSON>
 * @date: 2025/3/17 17:21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdMappingRuleRedisDTO {

    private String enField;

    private Integer fieldType;

    private Integer mergePolicy;

    private Integer priority;

    public static IdMappingRuleRedisDTO convertFrom(IdMappingRule idMappingRule) {
        IdMappingRuleFiledTypeEnum fieldType = IdMappingRuleFiledTypeEnum.valueOfCode(idMappingRule.getFieldType());
        IdMappingRuleMergePolicyEnum mergePolicy =
                IdMappingRuleMergePolicyEnum.valueOfCode(idMappingRule.getMergePolicy());
        IdMappingRuleRedisDTO idMappingRuleRedisDTO = new IdMappingRuleRedisDTO();
        idMappingRuleRedisDTO.setEnField(idMappingRule.getEnField());
        idMappingRuleRedisDTO.setFieldType(fieldType.getInteger());
        idMappingRuleRedisDTO.setMergePolicy(mergePolicy.getInteger());
        idMappingRuleRedisDTO.setPriority(idMappingRule.getPriority());
        return idMappingRuleRedisDTO;
    }
}
