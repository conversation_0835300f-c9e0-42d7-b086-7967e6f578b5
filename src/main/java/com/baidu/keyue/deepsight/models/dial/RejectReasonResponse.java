package com.baidu.keyue.deepsight.models.dial;

import com.baidu.keyue.deepsight.enums.AiobFailReasonEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RejectReasonResponse {
    /**
     * 未接通原因id
     * @example 2
     */
    private Integer reasonIndex;

    /**
     * 原因
     * @example "呼叫频率超限"
     */
    private String reason;

    /**
     * 该原因占比(0-100)
     */
    private Double percentage;

    /**
     * 该原因出现次数
     */
    private Integer count;

    public RejectReasonResponse(Map<String, Object> map) {
        Object reason = map.get("reason");
        Object count= map.get("count");
        Object percentage = map.get("ratio");
        this.reason = reason.toString();
        this.count = (count == null) ? 0 : ((Long) count).intValue();
        this.percentage = (percentage == null) ? 0.00 : ((BigDecimal) percentage).doubleValue();
        AiobFailReasonEnum aiobFailReasonEnum = AiobFailReasonEnum.createByTypeName(this.reason);
        if (aiobFailReasonEnum != null) {
            this.reasonIndex = aiobFailReasonEnum.getDicId();
        }
    }

}
