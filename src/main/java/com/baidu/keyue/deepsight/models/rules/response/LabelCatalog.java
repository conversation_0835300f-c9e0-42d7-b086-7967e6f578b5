package com.baidu.keyue.deepsight.models.rules.response;

import com.baidu.keyue.deepsight.models.catalog.CatalogDetail;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * @className: LabelCatalog
 * @description:
 * @author: wangzhongcheng
 * @date: 2024/12/24 23:37
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LabelCatalog {

    /**
     * 标签目录ID
     */
    private Long id;

    /**
     * 标签目录名称
     */
    private String name;

    /**
     * 子目录列表
     */
    private List<LabelCatalog> children;

    /**
     * 标签属性值列表
     */
    private List<LabelPropertiesResult> subLabels;

    /**
     * 将CatalogDetail转换为LabelCatalog，包括子目录及标签
     * @param catalogDetail
     * @return
     */
    public static LabelCatalog convertFrom(CatalogDetail catalogDetail) {
        List<LabelCatalog> children = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(catalogDetail.getChildren())) {
            children = catalogDetail.getChildren()
                    .stream()
                    .map(LabelCatalog::convertFrom)
                    .filter(LabelCatalog::isNotEmptyCatalog).toList();
        }

        List<LabelPropertiesResult> subLabels = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(catalogDetail.getLabels())) {
            subLabels = catalogDetail.getLabels().stream().map(LabelPropertiesResult::convertFrom).toList();
        }

        return LabelCatalog.builder()
                .id(catalogDetail.getCatalogId())
                .name(catalogDetail.getCatalogName())
                .children(children)
                .subLabels(subLabels).build();
    }

    /**
     * 判断当前目录是否为空目录: 没有子目录也没有标签
     * @return true: 空目录, false: 非空目录
     */
    @JsonIgnore
    public boolean isEmptyCatalog() {
        return CollectionUtils.isEmpty(children) && CollectionUtils.isEmpty(subLabels);
    }

    /**
     * 判断当前目录是否非空目录: 有子目录或标签
     * @return true: 非空目录, false: 空目录
     */
    @JsonIgnore
    public boolean isNotEmptyCatalog() {
        return !isEmptyCatalog();
    }

}
