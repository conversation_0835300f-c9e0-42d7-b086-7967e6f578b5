package com.baidu.keyue.deepsight.models.idmapping.response.relation;

import com.baidu.keyue.deepsight.models.idmapping.dto.IdMappingRelFieldDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @className: IdMappingDatasetFieldListResponse
 * @description: ID对
 * @author: wangzhong<PERSON>
 * @date: 2025/3/7 19:29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdMappingDatasetFieldListResponse {

    /**
     * ID对可选字段列表
     */
    private List<IdMappingRelFieldDTO> list;

    /**
     * 将字段实体对象转为前端展示对象
     *
     * @param tableFields 字段实体
     * @return
     */
    public static IdMappingDatasetFieldListResponse convertFrom(List<TableFieldMetaInfo> tableFields) {
        return new IdMappingDatasetFieldListResponse(IdMappingRelFieldDTO.convertFrom(tableFields));
    }

}
