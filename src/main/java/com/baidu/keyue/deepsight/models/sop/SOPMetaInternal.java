package com.baidu.keyue.deepsight.models.sop;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SOPMetaInternal {
    /**
     * 步骤id
     */
    private String stepId;
    /**
     * 步骤名称
     */
    private String stepName;
    /**
     * 节点列表
     */
    private List<SopMetaNode> nodes;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SopMetaNode {
        /**
         * 节点 id
         */
        private String nodeId;
        /**
         * 节点名称
         */
        private String nodeName;
    }

}
