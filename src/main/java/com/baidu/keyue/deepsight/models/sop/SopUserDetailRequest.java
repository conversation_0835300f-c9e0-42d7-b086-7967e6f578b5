package com.baidu.keyue.deepsight.models.sop;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @className SopUserDetailRequest
 * @description SopUserDetailRequest
 * @date 2025/5/22 15:45
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SopUserDetailRequest extends BasePageRequest {

    /**
     * 当前节点
     */
    @NotBlank(message = "当前节点不能为空")
    private String currNodeId;

    /**
     * 数据表id
     */
    private Long dataTableId;

    /**
     * 过滤条件
     */
    private List<RuleFilter> filters;

    /**
     * 是否为意向标签模拟节点
     */
    private Boolean intent = false;

    /**
     * 外呼任务id
     */
    @NotBlank(message = "taskId不能为空")
    private String taskId;

    /**
     * 外呼任务机器人 id
     */
    private String robotId;
    /**
     * 外呼任务机器人 版本
     */
    private String robotVer;

    /**
     * 画布主题
     */
    private String topicId;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;


}
