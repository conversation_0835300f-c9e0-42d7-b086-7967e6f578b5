package com.baidu.keyue.deepsight.models.customer.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName CustomerDiffusionTaskDeleteRequest
 * @Description 人群扩散任务删除请求
 * <AUTHOR>
 * @Date 2025/3/25 1:57 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDiffusionTaskDeleteRequest implements Serializable {
    
    @NotNull(message = "任务ID不能为空")
    private Long id;
}
