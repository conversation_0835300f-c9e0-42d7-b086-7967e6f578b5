package com.baidu.keyue.deepsight.models.datamanage.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName ShowConfigQueryRequest
 * @Description 展示列配置查询请求体
 * <AUTHOR>
 * @Date 2025/5/9 2:56 PM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FieldShowConfigQueryRequest {
    /**
     * 数据集ID
     */
    @NotNull(message = "数据集ID不能为空")
    private Long dataTableId;
}
