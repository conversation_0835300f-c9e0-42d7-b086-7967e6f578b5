package com.baidu.keyue.deepsight.models.customer.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @ClassName CustomerGroupDetaiListlRequest
 * @Description 客群详情列表请求体
 * <AUTHOR>
 * @Date 2025/2/24
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerGroupDetaiListlRequest {
    /**
     * 客群ID列表
     */
    private List<Long> customerGroupIds;

}
