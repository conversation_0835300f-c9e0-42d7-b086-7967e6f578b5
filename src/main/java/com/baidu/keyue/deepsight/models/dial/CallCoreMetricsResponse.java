package com.baidu.keyue.deepsight.models.dial;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CallCoreMetricsResponse {
    /**
     * 昨日接通率(0-100)
     */
    private Double connectedRate;

    /**
     * 近30天接通率告警天数
     */
    private Integer alertDays;

    /**
     * 告警阈值(0-100)
     */
    private Integer alertThreshold;

    /**
     * 昨日拨打次数
     */
    private Integer callCount;

    /**
     * 昨日未接通次数
     */
    private Integer unconnectedCount;

    /**
     * 昨日接通次数
     */
    private Integer connectedCount;

    /**
     * 号线原因未接通占比(0-100)
     */
    private Integer lineReasonUnconnectedRate;

    public CallCoreMetricsResponse(Map<String, Object> map, Integer alertDays, Integer connectedRateThreshold) {
        Object connectedRate = map.get("connectedRate");
        Object callCount = map.get("callCount");
        Object unconnectedCount = map.get("unconnectedCount");
        Object connectedCount = map.get("connectedCount");
        Object lineReasonUnconnectedRate = map.get("lineReasonUnconnectedRate");
        // 处理null
        this.connectedRate = (connectedRate == null) ? 0.0 : ((BigDecimal) connectedRate).doubleValue();
        this.callCount = (callCount == null) ? 0 : ((Long) callCount).intValue();
        this.unconnectedCount = (unconnectedCount == null) ? 0 : ((Long) unconnectedCount).intValue();
        this.connectedCount = (connectedCount == null) ? 0 : ((Long) connectedCount).intValue();
        this.lineReasonUnconnectedRate = (lineReasonUnconnectedRate == null) ? 0 :
                ((BigDecimal) lineReasonUnconnectedRate).intValue();
        this.alertDays = alertDays;
        this.alertThreshold = connectedRateThreshold;
    }
}
