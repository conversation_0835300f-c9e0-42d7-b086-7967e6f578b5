package com.baidu.keyue.deepsight.models.customer.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @ClassName CreateCustomerImportResponse
 * @Description 客群导入返回体
 * <AUTHOR>
 * @Date 2025/3/25 2:40 PM
 */
@Data
public class CreateCustomerImportResponse {
    
    /**
     * 客群ID
     */
    private Long customerGroupId;

    /**
     * 错误信息
     */
    private List<String> message = new ArrayList<>();

    /**
     * 错误数据
     */
    private List<Map<String, Object>> errorList;

    /**
     * 匹配字段
     */
    private Set<String> interNames = new HashSet<>();
    
    /**
     * 导入成功条数
     */
    private AtomicLong successCount = new AtomicLong(0);
    /**
     * 导入失败条数
     */
    private AtomicLong failCount = new AtomicLong(0);
}
