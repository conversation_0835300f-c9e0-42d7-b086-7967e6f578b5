package com.baidu.keyue.deepsight.models.diffusion.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

/**
 * @className: DiffusionCharacteristicDTO
 * @description: 扩散模型特征属性信息
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2025/3/25 19:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DiffusionCharacteristicDTO implements Comparable<DiffusionCharacteristicDTO> {

    /**
     * 特征属性名
     */
    @JsonProperty("key")
    private String attribute;

    /**
     * 特征属性信息: 权重
     */
    private Float score;

    @Override
    public int compareTo(@NotNull DiffusionCharacteristicDTO other) {
        // 按score降序排列（从大到小）
        return Float.compare(other.getScore(), this.getScore());
    }
}
