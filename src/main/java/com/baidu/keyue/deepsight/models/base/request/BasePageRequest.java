package com.baidu.keyue.deepsight.models.base.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

/**
 * @className: BasePageRequest
 * @description: 通用分页请求结构体
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BasePageRequest {

    /**
     * 分页页码，默认 1
     */
    @Range(min = 1, message = "pageNo必须大于0")
    private Integer pageNo = 1;

    /**
     * 分页大小，默认 10
     */
    @Range(min = 1, max = 100, message = "pageSize必须在1-100之间")
    private Integer pageSize = 10;

    /**
     * 获取偏移量
     * @return 偏移量
     */
    public int getOffset(){
        return (this.pageNo - 1) * this.pageSize;
    }

}