package com.baidu.keyue.deepsight.models.datamanage.request;

import com.baidu.keyue.deepsight.enums.FileTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName FileImportTaskListRequest
 * @Description 文件导入任务列表查询请求体
 * <AUTHOR>
 * @Date 2025/2/14 6:16 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileImportTaskListRequest {
    /**
     * 数据集ID
     */
    @NotNull(message = "数据集ID不能为空")
    private Long dataTableId;

    /**
     * 文件类型枚举
     */
    private FileTypeEnum fileTypeEnum;
}
