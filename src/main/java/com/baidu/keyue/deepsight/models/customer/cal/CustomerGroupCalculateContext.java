package com.baidu.keyue.deepsight.models.customer.cal;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.models.bsc.basic.BaseCalculateContext;
import com.baidu.keyue.deepsight.models.bsc.label.BscLabelTaskRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Objects;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class CustomerGroupCalculateContext extends BaseCalculateContext {

    // exec info
    private CustomerGroup customerGroup;
    private BscLabelTaskRequest bscLabelTaskRequest;

    public String generateFieldName(Long fieldId) {
        if (Objects.isNull(fieldId)) {
            return "";
        }
        return String.format(Constants.DORIS_CUSTOMER_GROUP_FIELD_TEM, fieldId);
    }

    public String generateTemporaryTableName() {
        return String.format(Constants.DORIS_CUSTOMER_GROUP_TEM_TABLE_TEM, getExecId());
    }

    public String generateBscJobName() {
        return String.format(Constants.BSC_JOB_NAME_TEM, getFieldName(), getExecId());
    }

}
