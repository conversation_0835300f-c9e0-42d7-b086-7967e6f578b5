package com.baidu.keyue.deepsight.models.memory;

import com.baidu.keyue.deepsight.annotation.LongIdConstraint;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateMemoryDataSourceRequest {

    @LongIdConstraint(message = "记忆抽取配置ID无效")
    private Long id;

    /**
     * 抽取方式
     */
    private TriggerModeEnum triggerMode;
    /**
     * 执行频率
     */
    private TriggerFrequencyEnum triggerFrequency;
    /**
     * 执行频率具体时间配置
     * */
    private TriggerFrequencyValue triggerFrequencyValue;

    /**
     * 描述
     */
    private String description;
}
