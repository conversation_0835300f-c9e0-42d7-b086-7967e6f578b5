package com.baidu.keyue.deepsight.models.label;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ListLabelRequest extends BasePageRequest {

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签目录 ID
     */
    private Long catalogId;

}
