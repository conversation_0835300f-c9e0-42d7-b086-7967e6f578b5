package com.baidu.keyue.deepsight.models.sop;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName SopWholeRobotVersionResponse
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/5/19 19:47
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SopWholeRobotVersionResponse {
    /**
     * 机器人id
     */
    private String robotId;
    /**
     * 机器人版本名称
     */
    private String robotVersionName;
    /**
     * 机器人版本id
     */
    private String robotVersionId;

    /**
     * 机器人版本创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
