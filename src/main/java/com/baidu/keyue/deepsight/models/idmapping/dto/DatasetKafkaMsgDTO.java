package com.baidu.keyue.deepsight.models.idmapping.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * @className: DatasetAppendMsgDTO
 * @description: 数据追加kafka消息DTO
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2025/1/10 15:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class DatasetKafkaMsgDTO {

    /**
     * 表名：aiob_conversation_session
     */
    private String code;

    /**
     * 数据：{"session_id":"123456","session_id_type":0,"msg_id":"123456"}  key-为doris的列名；value-为对应的值
     */
    private Map<String, Object> data;
}
