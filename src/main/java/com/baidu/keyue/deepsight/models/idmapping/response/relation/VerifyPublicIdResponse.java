package com.baidu.keyue.deepsight.models.idmapping.response.relation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: VerifyPublicIdResponse
 * @description: 校验是否存在公共id对结果
 * @author: wangzhong<PERSON>
 * @date: 2025/3/6 17:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class VerifyPublicIdResponse {

    /**
     * 数据表id
     */
    private Boolean existPublicId;
}
