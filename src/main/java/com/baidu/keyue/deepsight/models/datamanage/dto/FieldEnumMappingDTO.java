package com.baidu.keyue.deepsight.models.datamanage.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className FieldEnumMappingDTO
 * @description
 * @date 2025/2/24 15:57
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FieldEnumMappingDTO {
    /**
     * 枚举的key
     */
    private String key;
    /**
     * 枚举的value
     */
    private String value;
    /**
     * 枚举描述
     */
    private String desc;
}
