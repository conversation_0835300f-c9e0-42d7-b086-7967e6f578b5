package com.baidu.keyue.deepsight.models.profile;

import com.baidu.keyue.deepsight.models.base.response.BasePageResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.FieldShowConfigResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.TableFieldDetailResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RecordOfCall {
    private List<TableFieldDetailResponse> fields;

    private BasePageResponse.Page<Map<String, String>> contents;

    /**
     * 显示列配置
     */
    private FieldShowConfigResponse fieldShowConfig;
}
