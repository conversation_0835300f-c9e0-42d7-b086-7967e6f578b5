package com.baidu.keyue.deepsight.models.base.response;

import com.baidu.keyue.deepsight.models.datamanage.response.FieldShowConfigResponse;
import com.baidu.keyue.deepsight.models.datamanage.response.VisibleFieldResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 通用数据集分页数据响应
 * @ClassName BaseRecordResponse
 * @Description 通用数据集分页数据响应，包含可见字段、显示列字段、分页数据
 * <AUTHOR>
 * @Date 2025/5/12 10:10 AM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseRecordResponse {

    /**
     * 数据集可见字段
     */
    private List<VisibleFieldResponse> fields;

    /**
     * 数据集分页数据
     */
    private BasePageResponse.Page<Map<String, String>> contents;

    /**
     * 显示列配置
     */
    private FieldShowConfigResponse fieldShowConfig;
}
