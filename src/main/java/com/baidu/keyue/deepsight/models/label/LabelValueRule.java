package com.baidu.keyue.deepsight.models.label;

import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 单个标签值的值和对应规则
 * 规则
 *      用户属性  从用户主数据中选择字段
 *      明细数据  从业务数据中选择业务事件
 * */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LabelValueRule {

    /**
     * 标签值
     * */
    private String labelValue;

    /**
     * 标签值描述
     */
    private String labelValueDesc;

    /**
     * 条件
     * */
    private RuleGroup ruleGroup;
}
