package com.baidu.keyue.deepsight.models.rules.dto;

import com.baidu.keyue.deepsight.enums.AggregatorEnum;
import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.enums.FuncEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @className: RuleFilter
 * @description: 规则过滤条件
 * @author: wangz<PERSON><PERSON>
 * @date: 2024/12/21 16:22
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RuleFilter {

    /**
     * 过滤类型
     */
    private FilterTypeEnum type;

    /**
     * 字段id：对于标签筛选需要特殊处理，来源并不是数据集元信息表
     */
    private Long fieldId;

    /**
     * 过滤字段
     */
    @JsonIgnore
    private String filed;

    /**
     * 字段类型：对应的doris类型
     */
    @JsonIgnore
    private String fieldDataType;

    /**
     * 调用函数
     */
    private FuncEnum function;

    /**
     * 聚合函数: avg,sum,min,max,count,distance count
     */
    private AggregatorEnum aggregator;

    /**
     * 过滤参数
     */
    private List<String> params;

}
