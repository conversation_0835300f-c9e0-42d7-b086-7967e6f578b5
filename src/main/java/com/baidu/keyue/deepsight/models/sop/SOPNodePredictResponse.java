package com.baidu.keyue.deepsight.models.sop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * *@Author: dongjiacheng01
 * *@Description: SOP节点预测Response
 * *@Date: 16:51 2025/5/28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
public class SOPNodePredictResponse {

    private Boolean isSaved;

    private String markdown;

}
