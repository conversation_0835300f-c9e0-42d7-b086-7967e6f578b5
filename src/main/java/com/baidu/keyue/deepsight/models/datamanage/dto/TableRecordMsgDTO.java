package com.baidu.keyue.deepsight.models.datamanage.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @className TableRecordMsgDTO
 * @description kafkadto
 * @date 2025/1/10 16:40
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TableRecordMsgDTO {

    /**
     * doris表名
     */
    private String code;

    /**
     * 表数据
     */
    private Object data;
}
