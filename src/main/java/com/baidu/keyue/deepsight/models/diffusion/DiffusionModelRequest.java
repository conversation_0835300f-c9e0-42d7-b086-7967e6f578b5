package com.baidu.keyue.deepsight.models.diffusion;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DiffusionModelRequest {
    /**
     * 任务 id
     */
    private String taskId;
    /**
     * 种子人群文件地址
     */
    private List<String> seedUrls = Lists.newArrayList();
    /**
     * 预测人群文件地址
     */
    private List<String> candidateUrls = Lists.newArrayList();
    /**
     * 负样本人群文件地址
     */
    private List<String> negativeUrls = Lists.newArrayList();
    /**
     * 结果文件夹 ObjectKey
     */
    private String resultUrl;
}
