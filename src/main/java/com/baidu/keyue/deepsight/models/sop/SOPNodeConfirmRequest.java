package com.baidu.keyue.deepsight.models.sop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * *@Author: dongjiacheng01
 * *@Description: SOP节点编辑请求
 * *@Date: 19:31 2025/5/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SOPNodeConfirmRequest {

    @NotBlank(message = "任务id不可为空")
    private String taskId;

    @NotBlank(message = "任务规则不可为空")
    private String rule;

    @NotBlank(message = "流程节点markdown不可为空")
    private String markdown;

    private String robotVer;

}
