package com.baidu.keyue.deepsight.models.idmapping.request.rule;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

/**
 * @className: CreateIdMappingRuleRequest
 * @description: id mapping 规则配置 添加请求
 * @author: chenwenyu03
 * @date: 2025/3/10 15:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateIdMappingRuleRequest {

    /**
     * 提取规则主键
     */
    private Long id;

    /**
     * 提取字段
     */
    @NotNull(message = "提取字段不能为空")
    private String enField;

    /**
     * 提取字段中文
     */
    @NotNull(message = "提取字段中文不能为空")
    private String cnField;

    /**
     * 描述
     */
    @JsonProperty(defaultValue = "")
    private String description;

    /**
     * 取值类型 0 单值，1 多值
     */
    @JsonProperty(defaultValue = "0")
    @Range(min =0, max = 1, message = "取值类型 必须为0或1")
    private Integer fieldType;


    /**
     * 针对多值类型的合并策略，对单值无效 0 最新，1 最早
     */
    @JsonProperty(defaultValue = "0")
    @Range(min =0, max = 1, message = "合并策略 必须为0或1")
    private Integer mergePolicy;

    /**
     * 优先级 0 -100
     */
    @Range(min =0, max = 100, message = "优先级必须在0-100之间")
    private Integer priority;

}
