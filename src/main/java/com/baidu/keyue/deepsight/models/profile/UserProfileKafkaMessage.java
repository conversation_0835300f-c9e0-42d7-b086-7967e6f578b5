package com.baidu.keyue.deepsight.models.profile;

import java.util.Map;
import java.util.Objects;

import com.baidu.keyue.deepsight.config.Constants;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserProfileKafkaMessage {
    /**
     * table name code
     */
    private String code;

    /**
     * table content data map, consist of columns and oneId field
     */
    private Map<String, Object> data;

    public String getOneId() {
        if (Objects.isNull(data) || data.isEmpty()) {
            return null;
        }
        Object oId = data.get(Constants.TABLE_USER_ONE_ID);
        if (Objects.isNull(oId)) {
            return null;
        }

        return String.valueOf(oId);
    }

    public String getTenantId() {
        return StringUtils.substringAfterLast(code, "_");
    }
}
