package com.baidu.keyue.deepsight.models.sop;

import com.baidu.keyue.deepsight.mysqldb.entity.AiobSopMeta;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * *@Author: dongjiacheng01
 * *@Description: 快捷场景Node查询Response
 * *@Date: 10:25 2025/5/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SOPQuickNodeListResponse {

    /**
     * 对话规则
     */
    private String rule;

    /**
     * step with nodes list
     */
    private List<SopQuickStepWithNodes> list;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    public static class SopQuickStepWithNodes {

        private String step;

        private List<SopQuickNodeListItem> nodeList;

    }

    /**
     * node list item inner static class
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @Builder
    public static class SopQuickNodeListItem {

        /**
         * 节点id
         */
        private String nodeId;

        /**
         * 节点名称
         */
        private String nodeName;

        public static SopQuickNodeListItem convertFromSOPMetaEntity(AiobSopMeta meta) {
            return SopQuickNodeListItem.builder()
                    .nodeId(meta.getNodeId())
                    .nodeName(meta.getNodeName())
                    .build();
        }

    }

}
