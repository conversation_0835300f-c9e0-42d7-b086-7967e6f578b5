package com.baidu.keyue.deepsight.models.dial;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BusyLocationDistributionResponse {
    /**
     * 用户忙的总次数
     */
    private Integer total;

    /**
     * 归属地相同占比
     */
    private Double sameRatio;

    /**
     * 归属地不相同占比
     */
    private Double differentRatio;

    /**
     * 归属地为空占比
     */
    private Double nullRatio;

    public BusyLocationDistributionResponse(Map<String, Object> map) {
        Object total = map.get("total");
        Object sameRatio= map.get("sameRatio");
        Object differentRatio = map.get("differentRatio");
        Object nullRatio = map.get("nullRatio");
        this.total =  (total == null) ? 0 : ((Long) total).intValue();
        this.sameRatio = (sameRatio == null) ? 0 : ((BigDecimal) sameRatio).doubleValue();
        this.differentRatio = (differentRatio == null) ? 0 : ((BigDecimal) differentRatio).doubleValue();
        this.nullRatio = (nullRatio == null) ? 0 : ((BigDecimal) nullRatio).doubleValue();
    }
}
