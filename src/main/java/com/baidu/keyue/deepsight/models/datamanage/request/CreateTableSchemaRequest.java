package com.baidu.keyue.deepsight.models.datamanage.request;

import com.baidu.keyue.deepsight.models.datamanage.dto.TableFieldInfoDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @className CreateTableSchemaRequest
 * @description 数据表创建请求
 * @date 2025/2/8, 10:52
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateTableSchemaRequest {

    /**
     * 数据表id
     */
    private Long dataTableId;

    /**
     * 数据集中文名称
     */
    @NotNull(message = "中文名称不能为空")
    private String tableName;

    /**
     * 数据集英文名称
     */
    @NotNull(message = "英文名称不能为空")
    private String enName;

    /**
     * 数据类型 0事实数据 1维度数据
     */
    @Max(1)
    @Min(0)
    @NotNull(message = "tableType不能为空")
    private Integer tableType;

    /**
     * 数据集描述
     */
    private String desc;

    /**
     * 存储引擎类型
     */
    private String dbType;


    /**
     * 数据集字段
     */
    private List<TableFieldInfoDTO> tableFieldInfos;



}
