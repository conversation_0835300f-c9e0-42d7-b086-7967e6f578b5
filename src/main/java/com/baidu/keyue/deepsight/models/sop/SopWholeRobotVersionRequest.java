package com.baidu.keyue.deepsight.models.sop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @ClassName SopWholeRobotVersionResponse
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/5/19 19:47
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SopWholeRobotVersionRequest {
    /**
     * 机器人id
     */
    private String robotId;
}
