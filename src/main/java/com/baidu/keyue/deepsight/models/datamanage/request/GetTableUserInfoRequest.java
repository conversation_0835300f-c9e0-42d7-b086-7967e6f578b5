package com.baidu.keyue.deepsight.models.datamanage.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

/**
 * @ClassName GetTableFieldReq
 * @Description 获取用户表信息
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/1/23 15:23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetTableUserInfoRequest {
    /**
     * 用户属性名称
     */
    @NotNull(message = "用户属性名称不可为空")
    private String userFieldName ;
    /**
     * 用户属性值
     */
    @NotNull(message = "用户属性值不可为空")
    private String userFieldValue;

    /**
     * 分页页码，默认 1
     */
    @Range(min = 1, message = "pageNo必须大于0")
    private Integer pageNo = 1;

    /**
     * 表类型
     */
    private String tableType;

    /**
     * 分页大小，默认 10
     */
    private Integer pageSize = 1;

}
