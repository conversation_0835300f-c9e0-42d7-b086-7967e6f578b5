package com.baidu.keyue.deepsight.models.dial;

import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @ClassName AiobTaskListRequest
 * @Description 外呼列表查询请求体
 * <AUTHOR>
 * @Date 2025/7/7 2:53 PM
 */
@Data
public class AiobTaskListRequest {
    
    /**
     * 任务id列表
     */
    private List<String> taskIdList;
    
    private Integer pn = 1;
    
    private Integer ps = 999;

    public AiobTaskListRequest(List<Map<String, Object>> taskIds) {
        this.taskIdList = taskIds.stream()
                .map(map -> String.valueOf(map.get("taskId")))
                .filter(id -> !Objects.equals("null", id))
                .toList();
    }
}
