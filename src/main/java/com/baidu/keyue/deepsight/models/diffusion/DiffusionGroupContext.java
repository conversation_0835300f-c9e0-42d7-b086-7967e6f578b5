package com.baidu.keyue.deepsight.models.diffusion;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;

import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DiffusionGroupContext {
    private List<Long> groupIds;

    private String querySql;
    private String countSql;
    private Long count;

    private String tempFilename;
    private String objectKey;

    public DiffusionGroupContext(List<Long> groupIds) {
        this.groupIds = groupIds;
    }
}
