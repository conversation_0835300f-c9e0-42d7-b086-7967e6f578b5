package com.baidu.keyue.deepsight.models.idmapping.response.relation;

import com.baidu.keyue.deepsight.models.idmapping.dto.IdMappingRelFieldDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRelation;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @className: ListIdMappingRelResponse
 * @description: id对list查询响应
 * @author: wangzhongcheng
 * @date: 2025/3/7 14:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdMappingRelItemResponse {

    /**
     * id对主键
     */
    private Long idMappingRelId;

    /**
     * 是否存在公共id
     * true 存在，false 不存在
     */
    private Boolean existPublicId;

    /**
     * 数据集Id
     */
    private Long dataTableId;

    /**
     * 抽取数据集名
     */
    private String dataTableName;

    /**
     * 是否预置
     */
    private Boolean preset;

    /**
     * 抽取字段
     */
    private List<IdMappingRelFieldDTO> tableFields;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * ID对实体对象转为前端展示对象
     */
    public static IdMappingRelItemResponse convertFrom(IdMappingRelation idMappingRel,
                                                       Map<String, Long> enFieldCountMap,
                                                       Map<Long, String> dataTableNameMap) {
        IdMappingRelItemResponse idMappingRelItemResponse = new IdMappingRelItemResponse();
        List<IdMappingRelFieldDTO> idMappingRelFieldDTOS = IdMappingRelFieldDTO.convertFrom(idMappingRel);
        idMappingRelItemResponse.setPreset(idMappingRel.getPreset());
        idMappingRelItemResponse.setIdMappingRelId(idMappingRel.getId());
        idMappingRelItemResponse.setTableFields(idMappingRelFieldDTOS);
        idMappingRelItemResponse.setCreateTime(idMappingRel.getCreateTime());
        idMappingRelItemResponse.setDataTableId(idMappingRel.getDataTableId());
        idMappingRelItemResponse.setDataTableName(dataTableNameMap.get(idMappingRel.getDataTableId()));
        idMappingRelItemResponse.setExistPublicId(isExistPublicId(enFieldCountMap, idMappingRelFieldDTOS));
        return idMappingRelItemResponse;
    }

    /**
     * 判断是否存在公共id
     * @param enFieldCountMap 字段名和字段出现次数映射
     * @param tableFields 字段列表
     * @return true 存在，false 不存在
     */
    private static boolean isExistPublicId(Map<String, Long> enFieldCountMap,
                                           List<IdMappingRelFieldDTO> tableFields) {
        for (IdMappingRelFieldDTO tableField : tableFields) {
            Long enFieldCount = enFieldCountMap.get(tableField.getTableEnField());
            if (Objects.nonNull(enFieldCount) && enFieldCount > 1) {
                return true;
            }
        }
        return false;
    }

}
