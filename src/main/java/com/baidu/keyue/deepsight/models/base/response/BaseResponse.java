package com.baidu.keyue.deepsight.models.base.response;

import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

/**
 * @className: BaseResponse
 * @description: 通用返回结构体
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BaseResponse<T> {

    /**
     * 请求 ID
     */
    private String requestId;

    /**
     * 错误码，枚举参考 ErrorCode.java
     */
    private String code;

    /**
     * 错误信息
     */
    private String message;

    /**
     * 请求返回的结构体
     */
    private T data;

    public static <T> BaseResponse<T> of(ErrorCode errorCode) {
        BaseResponse<T> response = new BaseResponse<>();
        response.setCode(errorCode.getCode());
        response.setMessage(errorCode.getMessage());
        response.setRequestId(WebContextHolder.getRequestId());
        return response;
    }

    public static <T> BaseResponse<T> of(T data) {
        BaseResponse<T> response = new BaseResponse<>();
        response.setCode(ErrorCode.SUCCESS.getCode());
        response.setMessage(ErrorCode.SUCCESS.getMessage());
        response.setRequestId(WebContextHolder.getRequestId());
        response.setData(data);
        return response;
    }

}
