package com.baidu.keyue.deepsight.models.sop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * *@Author: dongjiacheng01
 * *@Description: 快捷场景Node查询Request
 * *@Date: 10:21 2025/5/20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SOPQuickNodeListRequest {

    @NotBlank(message = "租户id不可为空")
    private String tenantId;

    @NotBlank(message = "任务id不可为空")
    private String taskId;

    private String robotVer;

}
