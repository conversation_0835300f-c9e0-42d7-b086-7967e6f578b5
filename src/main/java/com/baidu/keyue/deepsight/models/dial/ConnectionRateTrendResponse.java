package com.baidu.keyue.deepsight.models.dial;

import com.baidu.keyue.deepsight.utils.MathUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConnectionRateTrendResponse {
    /**
     * 时间点(格式根据类型不同而不同)
     * 24小时模式: "HH:mm"
     * 30天模式: "yyyy-MM-dd"
     *
     * @example "2025-05-12" 或 "14:00"
     */
    private String time;

    /**
     * 接通率(0-100)
     */
    private Integer connectedCallsRate;

    /**
     * 拨打次数
     */
    private Long totalCalls;

    public ConnectionRateTrendResponse(Map<String, Object> map) {
        Object statDate = map.get("dataTime");
        Object connectRate = map.get("connect_rate");
        Object dialCount = map.get("dial_count");
        this.time = statDate == null ? "" : statDate.toString();
        this.connectedCallsRate = connectRate == null ? 0 : MathUtils.doubleToIntPercent(Double.valueOf(connectRate.toString()));
        this.totalCalls = dialCount == null ? 0 : (Long) dialCount;
    }

    public ConnectionRateTrendResponse(String time) {
        this.time = time;
        this.connectedCallsRate = 0;
        this.totalCalls = 0L;
    }
}
