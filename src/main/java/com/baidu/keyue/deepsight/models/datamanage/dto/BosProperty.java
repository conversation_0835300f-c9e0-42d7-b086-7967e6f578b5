package com.baidu.keyue.deepsight.models.datamanage.dto;

import lombok.Data;

/**
 * @ClassName BosProperty
 * @Description BosProperty
 * <AUTHOR>
 * @Date 2025/3/12 3:15 PM
 */
@Data
public class BosProperty {

    public static final String TYPE_BOS = "bos";
    public static final String TYPE_S3 = "s3";

    private String clientType = "bos";
    private String accessKey;
    private String secret;
    private String endPoint;
    /**
     * 地域，s3客户端需要，bos随便传即可。
     */
    private String region = "test";
    private String bucket;
    /**
     * 这个是业务方自用配置，objectKey前缀
     */
    private String tmpObjectPreffix; 
    /**
     * 这个是业务方自用配置，objectKey前缀
     */
    private String allPreffix;
    /**
     * 视频mct队列，用于查封面图片
     */
    private String mediaPiplineName; 
    /**
     * 缩略图宽px
     */
    private Integer thumbnailWidth;   
    /**
     * 缩略图高px
     */
    private Integer thumbnailHeight;  
    /**
     * 是否是私有仓库，生成链接规则会有所不同
     */
    private Integer isPrivate = 1;
    /**
     * 多任务分片复制的每一块大小，默认5mb
     */
    private Integer partSize = 5 * 1024 * 1024;
    /**
     * 路径风格，0=http://{endpoint}/{bucket}, 1=http://{bucket}.{endpoint}
     */
    private Integer pathStyle = 0;
}
