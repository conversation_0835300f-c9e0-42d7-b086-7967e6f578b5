package com.baidu.keyue.deepsight.models.sop;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SopNodeDailyDistribute {
    /**
     * 日期(yyyy-MM-dd)
     */
    private String date;

    /**
     * 下游节点流量分布
     */
    private List<SopNode> nodes;
}
