package com.baidu.keyue.deepsight.models.datamanage.request;

import com.baidu.keyue.deepsight.models.datamanage.dto.TableFieldInfoDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @className UpdateTableSchemaRequest
 * @description 数据表更新请求
 * @date 2025/2/8, 10:53
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateTableSchemaRequest {

    /**
     * 数据表id
     */
    @NotNull(message = "dataTableId不能为空")
    private Long dataTableId;

    /**
     * 数据集中文名称
     */
    private String tableName;

    /**
     * 数据集描述
     */
    private String desc;

    /**
     * 数据集字段
     */
    private List<TableFieldInfoDTO> tableFieldInfos;


}
