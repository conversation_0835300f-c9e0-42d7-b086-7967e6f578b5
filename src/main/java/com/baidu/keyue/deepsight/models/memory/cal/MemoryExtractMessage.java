package com.baidu.keyue.deepsight.models.memory.cal;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class MemoryExtractMessage implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long memoryId;
    private Long datasetId;
    private String modelUrl;
    private String dorisSpace;
    private String dorisTable;

    private String userId;
    private String externalId;
    private String tenantId;
    private String prompt;
    private String text;
}
