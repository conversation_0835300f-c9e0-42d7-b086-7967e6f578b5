package com.baidu.keyue.deepsight.models.rules.response;

import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @className: DatasetInfo
 * @description: 数据集信息
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/25 19:14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DatasetInfo {

    /**
     * 数据集ID
     */
    private Long dataTableId;

    /**
     * 数据集名称
     */
    private String datasetName;

    /**
     * 数据集属性列表
     */
    private List<DatasetPropertiesResult> properties;

    /**
     * 将数据表信息转换为数据集信息
     */
    public static DatasetInfo convertFrom(DataTableInfo dataTableInfo) {
        return DatasetInfo.builder()
                .dataTableId(dataTableInfo.getId())
                .datasetName(dataTableInfo.getCnName()).build();
    }

}
