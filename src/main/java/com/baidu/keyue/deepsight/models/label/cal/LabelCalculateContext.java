package com.baidu.keyue.deepsight.models.label.cal;

import java.util.Objects;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.models.bsc.basic.BaseCalculateContext;
import com.baidu.keyue.deepsight.models.bsc.label.BscLabelTaskRequest;
import com.baidu.keyue.deepsight.models.label.LabelDistribute;
import com.baidu.keyue.deepsight.mysqldb.entity.LabelWithBLOBs;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LabelCalculateContext extends BaseCalculateContext {

    // exec info
    private Boolean labelRecalculate = false;
    private LabelWithBLOBs label;

    /**
     * bsc 信息
     * bscTaskRequest - bsc 自定义param参数
     */
    private BscLabelTaskRequest bscLabelTaskRequest;

    // exec result
    private Long totalLines;
    private LabelDistribute labelDistribute;

    public String generateFieldName(Long fieldId) {
        if (Objects.isNull(fieldId)) {
            return "";
        }
        return Constants.DORIS_LABEL_PROCESS_FIELD_NAME_PREFIX + String.valueOf(fieldId);
    }

    public String generateTemporaryTableName() {
        return Constants.DORIS_LABEL_PROCESS_TEMPORARY_TABLE_NAME_PREFIX + String.valueOf(getExecId());
    }

    public String generateTemporaryDupTableName() {
        return Constants.DORIS_LABEL_PROCESS_TEMPORARY_DUP_TABLE_NAME_PREFIX + String.valueOf(getExecId());
    }

    public String generateBscJobName() {
        return String.format("%s%d-%d", Constants.DORIS_LABEL_PROCESS_FIELD_NAME_PREFIX, label.getId(), getExecId());
    }

}
