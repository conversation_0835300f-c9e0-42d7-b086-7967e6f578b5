package com.baidu.keyue.deepsight.models.customer.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName DeleteCustomerGroupRequest
 * @Description 获取客群详情请求体
 * <AUTHOR> (<EMAIL>)
 * @Date 2024/12/30 20:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetCustomerGroupRequest {
    /**
     * 客群ID
     */
    private Long customerGroupId;
}
