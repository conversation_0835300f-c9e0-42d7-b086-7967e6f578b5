package com.baidu.keyue.deepsight.models.sop;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SopSankeyMetaResponse {

    /**
     * 步骤+节点列表
     */
    private List<SOPMetaInternal> sopMeta;

    /**
     * 是否手动确认过
     */
    private Boolean manualChecked;
}
