package com.baidu.keyue.deepsight.models.meg;

import java.util.List;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum MEGIdEnum {

    phone(19, "PHONE", "手机号"),
    userid(7, "USERID", "baidu passport 用户ID"),
    baiduid(1, "BAIDUID", "百度Cookie标识"),
    cuid(3, "CUID", "移动设备标识"),
    imei(4, "IMEI", "移动身份码"),
    mac(12, "MAC", "网卡物理地址"),
    idfa(15, "IDFA", "apple提供的广告设备唯一ID"),
    oaid(30, "OAID", "设备标识，代替imei"),

//    udwid(6, "UDWID", "idmapping 生成的ID"),
//    cproid(2, "CPROID", "网盟ID"),
//    pccode(5, "PCCODE", "PC设备标识"),
//    oaid_ori(38, "OAID_ORI", "用户获取原始oaid，填在mapping_type中"),
//    superid(17, "SUPERID", "超级cookie，稳定的baiduid"),
//    iqiyiid(10, "IQIYI_ID", "爱奇艺id"),
//    weiboid(9, "WEIBO_ID", "微博ID"),

    ;

    private int code;
    private String name;
    private String desc;

    public static List<Integer> getIdCodesExceptMobile() {
        return Lists.newArrayList(
                baiduid.getCode(),
                cuid.getCode(),
                imei.getCode(),
                mac.getCode(),
                idfa.getCode(),
                oaid.getCode()
        );
    }

    public static MEGIdEnum getIdTypeByName(String name) {
        for (MEGIdEnum idEnum : MEGIdEnum.values()) {
            if (idEnum.getName().equals(name)) {
                return idEnum;
            }
        }
        return null;
    }

}
