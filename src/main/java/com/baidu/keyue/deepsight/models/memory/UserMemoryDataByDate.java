package com.baidu.keyue.deepsight.models.memory;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserMemoryDataByDate {
    /**
     * 天
     */
    private String day;
    /**
     * 每天的记忆列表
     */
    private List<UserMemoryDetail> memoryList;
}
