package com.baidu.keyue.deepsight.models.idmapping.request.rule;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: DeleteIdMappingRuleRequest
 * @description: IDmapping规则配置 删除请求
 * @author: chenwenyu03
 * @date: 2025/3/10 15:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeleteIdMappingRuleRequest {

    /**
     * 提取字段
     */
    @NotNull(message = "提取字段不能为空")
    private String enField;

}