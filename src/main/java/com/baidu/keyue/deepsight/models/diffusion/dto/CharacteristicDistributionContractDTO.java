package com.baidu.keyue.deepsight.models.diffusion.dto;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.enums.UserFiledEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @className: CharacteristicDistributionContractDTO
 * @description: 特征分布对比DTO
 * @author: wangzhongcheng
 * @date: 2025/3/26 11:15
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CharacteristicDistributionContractDTO {

    /**
     * 特征名
     */
    private String characteristicName;

    /**
     * 预测、抽样结果分布对比
     */
    private List<DistributionContractDTO> distributionContractList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    static class DistributionContractDTO {
        /**
         * 评分分段描述：0-0.1、0.1-0.2 ... 0.9-1
         */
        private String grade;

        /**
         * 落于该分段的预测结果人数
         */
        private Integer predictCount;

        /**
         * 落于该分段的抽样结果人数
         */
        private Integer samplingCount;
    }

    /**
     * 将Doris返回的Map数据转换为CharacteristicDistributionContractDTO
     * @param attribute 特征中文名
     * @param attributeField 特征英文名
     * @param dataList Doris返回的Map数据
     * @return CharacteristicDistributionContractDTO
     */
    public static CharacteristicDistributionContractDTO convertFrom(String attribute,
                                                                    String attributeField,
                                                                    List<Map<String, Object>> dataList) {
        CharacteristicDistributionContractDTO contractDTO = new CharacteristicDistributionContractDTO();
        contractDTO.setCharacteristicName(attribute);

        List<DistributionContractDTO> contractDTOS = dataList.stream()
                .map(data -> convertFromDorisData(attributeField, data))
                .filter(Objects::nonNull).toList();
        contractDTO.setDistributionContractList(contractDTOS);
        return contractDTO;
    }

    /**
     * 将Doris返回的Map数据转换为DistributionContractDTO
     * @param grade 分段描述
     * @param data 预测数据
     * @return
     */
    private static DistributionContractDTO convertFromDorisData(String grade, Map<String, Object> data) {
        DistributionContractDTO distributionContractDTO = new DistributionContractDTO();
        distributionContractDTO.setGrade((String) data.get(grade));
        if (StringUtils.isEmpty(distributionContractDTO.getGrade())) {
            return null;
        }

        // 预测结果特征人数统计
        Object predictCount = data.get(Constants.PREDICT_CHARACTERISTIC_FIELD_NAME);
        if (Objects.isNull(predictCount)) {
            distributionContractDTO.setPredictCount(0);
        } else {
            distributionContractDTO.setPredictCount(Integer.valueOf(predictCount.toString()));
        }
        // 抽样结果特征人数统计
        Object sampleCount = data.get(Constants.SAMPLE_CHARACTERISTIC_FIELD_NAME);
        if (Objects.isNull(sampleCount)) {
            distributionContractDTO.setSamplingCount(0);
        } else {
            distributionContractDTO.setSamplingCount(Integer.valueOf(sampleCount.toString()));
        }
        return distributionContractDTO;
    }

}
