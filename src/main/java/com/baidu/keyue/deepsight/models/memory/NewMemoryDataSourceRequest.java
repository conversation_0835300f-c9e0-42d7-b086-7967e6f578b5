package com.baidu.keyue.deepsight.models.memory;

import java.util.List;

import com.baidu.keyue.deepsight.annotation.LongIdConstraint;
import com.baidu.keyue.deepsight.enums.PromptTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.YesEnum;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NewMemoryDataSourceRequest {

    /**
     * 数据集ID
     */
    @LongIdConstraint(message = "请选择抽取数据集")
    private Long datasetId;
    /**
     * 数据集名称
     */
    private String datasetName;

    /**
     * 字段ID
     */
    @LongIdConstraint(message = "请选择抽取字段")
    private Long fieldId;
    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 字段名称-en
     */
    private String fieldNameEn;

    /**
     * 指令类型
     */
    private PromptTypeEnum promptType;
    /**
     * 抽取指令
     */
    private String prompt;

    /**
     * 抽取方式
     */
    private TriggerModeEnum triggerMode;
    /**
     * 执行频率
     */
    private TriggerFrequencyEnum triggerFrequency;
    /**
     * 执行频率具体时间配置
     * */
    private TriggerFrequencyValue triggerFrequencyValue;

    /**
     * 描述
     */
    private String description;

    /**
     * 抽取数据范围规则
     */
    private List<RuleFilter> dataFilterRule;

    /**
     * 是否是默认预设数据集，前端不用传，后端初始化使用
     */
    private YesEnum isDefault;
}
