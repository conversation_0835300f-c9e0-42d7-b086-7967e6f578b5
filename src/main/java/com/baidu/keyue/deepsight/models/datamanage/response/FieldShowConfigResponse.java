package com.baidu.keyue.deepsight.models.datamanage.response;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.mysqldb.entity.FieldShowConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName FieldShowConfigResponse
 * @Description 字段展示配置响应
 * <AUTHOR>
 * @Date 2025/5/9 3:25 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FieldShowConfigResponse implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 数据集ID
     */
    private Long dataTableId;
    
    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String modifier;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 展示列，逗号分隔
     */
    private List<String> showFields;

    public FieldShowConfigResponse(FieldShowConfig fieldShowConfig) {
        BeanUtils.copyProperties(fieldShowConfig, this);
        String showField = fieldShowConfig.getShowField();
        if (StrUtil.isNotBlank(showField)) {
            this.showFields = List.of(showField.split(Constants.SEPARATOR));
        }
    }

    public FieldShowConfigResponse(Long dataTableId, List<VisibleFieldResponse> visibleFields) {
        this.dataTableId = dataTableId;
        if (CollUtil.isNotEmpty(visibleFields)) {
            showFields = visibleFields.stream().map(VisibleFieldResponse::getEnName).collect(Collectors.toList());
        }
    }

    public void subConfigSize(int maxSize) {
        if (CollUtil.isNotEmpty(this.showFields) && this.showFields.size() > maxSize) {
            this.showFields = showFields.subList(0, maxSize);
        }
    }
}
