package com.baidu.keyue.deepsight.models.bsc.basic;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BscKafkaConfig {
    private String kafkaTopic;
    private String kafkaGroupId;
    private String kafkaCerBosUrl;
    private String kafkaBootstrapServers;
    private String kafkaStart;
}
