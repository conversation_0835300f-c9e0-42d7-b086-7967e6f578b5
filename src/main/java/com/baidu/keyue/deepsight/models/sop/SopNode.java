package com.baidu.keyue.deepsight.models.sop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SopNode {

    /**
     * 节点 id
     */
    private String nodeId;

    /**
     * 到达人数
     */
    private Long uv = 0L;

    /**
     * 到达人次
     */
    private Long pv = 0L;

    /**
     * 到达人数比例
     */
    private Integer uvPercent = 0;

    /**
     * 到达人次比例
     */
    private Integer pvPercent = 0;

    /**
     * 意向标签
     */
    private String intent;
}
