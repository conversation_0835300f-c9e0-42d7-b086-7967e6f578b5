package com.baidu.keyue.deepsight.models.idmapping.response.rule;


import com.baidu.keyue.deepsight.enums.IdMappingRuleFiledTypeEnum;
import com.baidu.keyue.deepsight.enums.IdMappingRuleMergePolicyEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.IdMappingRule;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @className: IdMappingRuleItemResponse
 * @description: id mapping 规则查询响应
 * @author: chenwenyu03
 * @date: 2025/3/10 15:51
 */
@Data
@AllArgsConstructor(access = AccessLevel.PUBLIC)
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdMappingRuleItemResponse {

    /**
     * 提取规则主键
     */
    private Long id;

    /**
     * 提取字段英文名
     */
    private String enField;


    /**
     * 提取字段中文名
     */
    private String cnField;

    /**
     * 描述
     */
    private String description;

    /**
     * 取值类型 0 单值，1 多值
     */
    private Integer fieldType;

    /**
     * 针对多值类型的合并策略，对单值无效 0 最新，1 最早
     */
    private Integer mergePolicy;

    /**
     * 优先级 0 -100
     */
    private Integer priority;

    /**
     * 是否预置
     */
    private Boolean preset;

    /**
     * 创建时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    public IdMappingRuleItemResponse(IdMappingRule idMappingRule) {
        this.id = idMappingRule.getId();
        this.enField = idMappingRule.getEnField();
        this.cnField = idMappingRule.getCnField();
        this.description = idMappingRule.getDescription();
        this.fieldType = idMappingRule.getFieldType().intValue();
        this.mergePolicy = idMappingRule.getMergePolicy().intValue();
        this.priority = idMappingRule.getPriority();
        this.preset = idMappingRule.getPreset();
        this.createTime = idMappingRule.getCreateTime();
    }


    // 预置规则构造方法
    public IdMappingRuleItemResponse(String enField, String description, IdMappingRuleFiledTypeEnum fieldType, Integer priority) {
        this.id = null;
        this.enField = enField;
        this.cnField = "";
        this.description = description;
        this.fieldType = fieldType.getInteger();
        this.mergePolicy = IdMappingRuleMergePolicyEnum.SAVE_NEWEST.getInteger();
        this.priority = priority;
        this.preset = true;
        this.createTime = null;
    }

    public IdMappingRuleItemResponse(IdMappingRuleItemResponse response) {
        this.id = response.getId();
        this.enField = response.getEnField();
        this.cnField = response.getCnField();
        this.description = response.getDescription();
        this.fieldType = response.getFieldType();
        this.mergePolicy = response.getMergePolicy();
        this.priority = response.getPriority();
        this.preset = response.getPreset();
        this.createTime = response.getCreateTime();
    }
}
