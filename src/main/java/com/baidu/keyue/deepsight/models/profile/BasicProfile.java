package com.baidu.keyue.deepsight.models.profile;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BasicProfile {

    /**
     * 基本信息
     */
    private List<BasicProfileValue> userInfo;

    /**
     * 标签
     */
    private List<String> labels;

    /**
     * 客群
     */
    private List<String> groups;

    /**
     * oneId 脑图
     */
    private OneIdGraph oneIdGraph;


    /**
     * 未来日程
     */
    private List<TaskSchedule> taskSchedules;
}
