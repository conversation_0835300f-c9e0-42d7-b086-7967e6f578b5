package com.baidu.keyue.deepsight.models.bsc.datapredict;

import com.baidu.keyue.deepsight.models.bsc.basic.BscDorisConfig;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 数据预测BSC任务请求参数
 * TODO 待完善
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BscDataPredictTaskRequest {
    /**
     * 任务执行 id
     */
    private Long execId;

    private BscDorisConfig dorisConfig;

    private String modelUrl;
    private String prompt;
    private String tenantId;
    private String workerCode;
    private List<Long> datasetId;
}
