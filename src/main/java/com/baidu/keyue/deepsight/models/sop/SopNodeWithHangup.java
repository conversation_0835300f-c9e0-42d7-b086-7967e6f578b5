package com.baidu.keyue.deepsight.models.sop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SopNodeWithHangup extends SopNode {

    /**
     * 节点挂断统计
     */
    private SopHangup hangup;

    public SopNodeWithHangup(String nodeId, Long uv, Long pv, Integer uvPercent, Integer pvPercent, String intent) {
        super(nodeId, uv, pv, uvPercent, pvPercent, intent);
        this.hangup = new SopHangup();
    }
}