package com.baidu.keyue.deepsight.models.idmapping.request.relation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: DeleteIdMappingRelRequest
 * @description: id-mapping 对删除请求
 * @author: wangzhong<PERSON>
 * @date: 2025/3/6 14:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeleteIdMappingRelRequest {

    /**
     * ID对主键 id
     */
    @NotNull(message = "ID对不能为空")
    private Long idMappingRelId;

}
