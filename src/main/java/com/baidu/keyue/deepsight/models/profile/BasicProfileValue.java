package com.baidu.keyue.deepsight.models.profile;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BasicProfileValue {
    /**
     * 字段名（英文）
     */
    String key;
    /**
     * 字段名（中文）
     */
    String keyCn;
    /**
     * 字段值
     */
    String value;
    /**
     * 是否是数据增强字段
     */
    Boolean predict;
}
