package com.baidu.keyue.deepsight.models.datamanage.response;

import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;


/**
 * @ClassName DataManageListResponse
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2024/12/24 12:07
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TableDetailResponse {

    /**
     * 数据表id
     */
    private Long dataTableId;

    /**
     * 数据集名称
     */
    private String tableName;

    /**
     * 数据集英文名
     */
    private String enName;

    /**
     * 数据类型 0事实数据 1维度数据
     */
    private Integer tableType;

    /**
     * 数据集描述
     */
    private String desc;

    /**
     * 是否预置
     */
    private Boolean isPreset;

    /**
     * 数据集状态 0未提交 1创建中 2已创建 3创建失败
     */
    private Integer dataStatus;

    /**
     * 存储引擎类型
     */
    private String dbType;

    /**
     * 数据集创建时间
     */
    private Long createTime;

    public TableDetailResponse(DataTableInfo info){
        this.setDataTableId(info.getId());
        this.setTableName(info.getCnName());
        this.setEnName(info.getEnName());
        this.setTableType(info.getDataType());
        this.setDbType(info.getDbType());
        this.setDesc(info.getDescription());
        this.setIsPreset(info.getIsPreset() == 0 ? false : true);
        this.setDataStatus((int) info.getStatus());
        this.setCreateTime(info.getCreateTime().getTime());


    }
}
