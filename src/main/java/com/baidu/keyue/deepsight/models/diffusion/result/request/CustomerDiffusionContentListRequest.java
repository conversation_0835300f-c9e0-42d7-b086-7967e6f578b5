package com.baidu.keyue.deepsight.models.diffusion.result.request;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.baidu.keyue.deepsight.models.rules.dto.RuleFilter;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @className: CustomerDiffusionContentListRequest
 * @description: 客户扩散列表结果请求
 * @author: wangz<PERSON><PERSON>
 * @date: 2025/3/25 18:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CustomerDiffusionContentListRequest extends BasePageRequest {

    /**
     * 数据表id
     */
    @NotNull(message = "人群扩散任务ID不能为空")
    private Long customerDiffusionTaskId;

    /**
     * 过滤条件
     */
    private List<RuleFilter> filters;

}
