package com.baidu.keyue.deepsight.models.customer.request;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName CustomerDiffusionTaskCreateRequest
 * @Description 人群扩散重新预测请求
 * <AUTHOR>
 * @Date 2025/3/24 4:58 PM
 */
@Data
public class CustomerDiffusionTaskRetryRequest implements Serializable {

    /**
     * 预测任务名称
     */
    @NotNull(message = "任务ID不能为空")
    private Long id;
}
