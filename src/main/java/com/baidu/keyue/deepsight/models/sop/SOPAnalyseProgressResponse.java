package com.baidu.keyue.deepsight.models.sop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SOPAnalyseProgressResponse {
    /**
     * 接通人数(任务维度)
     */
    private Long totalConnectedCall;

    /**
     * 接通人数(区分版本)
     */
    private Long connectedCall;

    /**
     * 洞察已分析(区分版本)
     */
    private Long processed;
}
