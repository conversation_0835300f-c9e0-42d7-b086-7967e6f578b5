package com.baidu.keyue.deepsight.models.datamanage.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.concurrent.atomic.AtomicLong;

/**
 * @ClassName FileImportResponse
 * @Description 文件数据导入返回体
 * <AUTHOR>
 * @Date 2025/2/14 4:39 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class FileImportTaskResponse {
    
    /**
     * 导入成功条数
     */
    private AtomicLong successCount = new AtomicLong(0);
    /**
     * 导入失败条数
     */
    private AtomicLong failCount = new AtomicLong(0);

    /**
     * 错误信息
     */
    private String message;
    
    public void setMessage(String message){
        if (this.message == null) {
            this.message = message;
        } else {
            this.message = this.message + ";" + message;
        }
    }
}
