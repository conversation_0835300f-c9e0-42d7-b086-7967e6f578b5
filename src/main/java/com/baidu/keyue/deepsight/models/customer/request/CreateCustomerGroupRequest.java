package com.baidu.keyue.deepsight.models.customer.request;

import com.baidu.keyue.deepsight.enums.GroupingTypeEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.enums.UpdateModEnum;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.models.rules.dto.RuleGroup;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName CreateCustomerGroupRequest
 * @Description 客群创建请求体
 * <AUTHOR> (<EMAIL>)
 * @Date 2024/12/30 20:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateCustomerGroupRequest {

    /**
     * 客群名称
     */
    @Size(min = 1, max = 50)
    private String customerGroupName;

    /**
     * 客群描述
     */
    @Size(min = 0, max = 500)
    private String customerGroupDescription;

    /**
     * 更新取值逻辑
     */
    @NotNull(message = "更新取值逻辑模式不能为空")
    private UpdateModEnum updateMod;

    /**
     * 更新方式
     */
    @NotNull(message = "更新方式不能为空")
    private TriggerModeEnum triggerMod;

    /**
     * 执行频率
     */
    private TriggerFrequencyEnum triggerFrequency;

    /**
     * 执行频率具体时间配置
     * */
    @Valid
    private TriggerFrequencyValue triggerFrequencyValue;

    /**
     * 标签值规则
     * */
    @NotNull(message = "客群圈选规则不能为空")
    private RuleGroup customerGroupRule;

    /**
     * 是否需要计算：false-不需要，true-需要，默认true
     * 程序内部使用
     */
    private Boolean needCal = true;

    /**
     * 分群方式，内部使用
     */
    private GroupingTypeEnum groupingType;
    /**
     * 配置按钮标记：true-可操作 false-不可操作
     */
    private Boolean configTag;
}
