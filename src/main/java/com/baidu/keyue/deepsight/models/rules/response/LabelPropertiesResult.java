package com.baidu.keyue.deepsight.models.rules.response;

import com.baidu.keyue.deepsight.enums.FilterTypeEnum;
import com.baidu.keyue.deepsight.models.label.LabelBrief;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @className: LabelPropertiesResult
 * @description:
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/24 23:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LabelPropertiesResult extends BaseProperties {

    private List<FilterEnumInfo> enums;

    /**
     * 将LabelBrief转换为LabelPropertiesResult
     * @param labelBrief LabelBrief对象，业务标签
     */
    public static LabelPropertiesResult convertFrom(LabelBrief labelBrief) {
        LabelPropertiesResult labelPropertiesResult = new LabelPropertiesResult();
        labelPropertiesResult.setFieldId(labelBrief.getField());
        labelPropertiesResult.setCname(labelBrief.getLabelName());
        labelPropertiesResult.setDataType(FilterTypeEnum.STRING.getType());
        labelPropertiesResult.setIsMeasure(false);
        labelPropertiesResult.setIsIdKey(false);

        // 处理枚举信息
        List<FilterEnumInfo> enums = labelBrief.getLabelValueRange()
                .stream()
                .map(labelValue -> new FilterEnumInfo(labelValue, labelValue, labelValue))
                .toList();
        labelPropertiesResult.setEnums(enums);
        return labelPropertiesResult;
    }

}
