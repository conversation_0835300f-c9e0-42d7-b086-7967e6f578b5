package com.baidu.keyue.deepsight.models.sop;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;


@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SopUserConfigUpdateRequest extends SopBaseRequest {

    /**
     * 流量分布核心指标 0为到达人数 1为到达人次
     */
    @Range(min = 0, max = 1, message = "核心指标必须为0或者1")
    private Integer mainMetric;

    /**
     * 流量分布核心指标 0为到达人数比例 1为到达人次比例
     */
    @Range(min = 0, max = 1, message = "辅助指标必须为0或者1")
    private Integer assistMetric;

    /**
     * 挂断提醒阈值 0-5分别为 5% 10% 20% 30% 40% 50%
     */
    @Range(min = 0, max = 5, message = "挂断提醒阈值在0-5之间")
    private Integer warningThreshold;
}
