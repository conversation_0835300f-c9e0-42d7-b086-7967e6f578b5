package com.baidu.keyue.deepsight.models.catalog;

import com.baidu.keyue.deepsight.annotation.LongIdConstraint;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MoveCatalogRequest {

    /**
     * 标签目录ID
     */
    @LongIdConstraint(message = "标签目录ID无效")
    private Long catalogId;

    /**
     * 目标目录 ID，移动到哪个目录之后，0 表示同级目录最上方
     */
    @NotNull(message = "目标位置标签目录ID不能为空")
    private Long after;

}
