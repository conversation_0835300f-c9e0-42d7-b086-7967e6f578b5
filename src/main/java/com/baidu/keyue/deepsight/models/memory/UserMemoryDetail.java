package com.baidu.keyue.deepsight.models.memory;

import com.baidu.keyue.deepsight.enums.MemoryTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserMemoryDetail {
    /**
     * 记忆时间
     */
    private String datetime;
    /**
     * 记忆内容
     */
    private String content;
    /**
     * 记忆类型
     */
    private MemoryTypeEnum type;
}
