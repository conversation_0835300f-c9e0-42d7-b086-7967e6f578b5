package com.baidu.keyue.deepsight.models.dial;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LineInfoResponse {
    /**
     * 号线 num
     * @example "(021)12345678"
     */
    private String lineNum;


    public LineInfoResponse(Map<String, Object> map) {
        Object lineNum = map.get("callerNum");
        this.lineNum = (lineNum == null) ? "" : lineNum.toString();
    }
}
