package com.baidu.keyue.deepsight.models.agg;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AiobAggMetricCal {
    private String oneId;

    /**
     * 接通率
     */
    private Float connectRate;
    /**
     * 搜轮挂断率
     */
    private Float firstRoundHangupRate;
    /**
     * 平均对话轮次
     */
    private Float avgRounds;
    /**
     * 平均通话时长
     */
    private Float avgDuration;
    /**
     * 各时间段接通情况
     */
    private Map<String, AiobAggMetricTimeBucket> timeBucketStatistics;


    /**
     * 拨打总次数
     */
    private Long totalCalls = 0L;
    /**
     * 接通次数
     */
    private Long totalConnectedCalls = 0L;
    /**
     * 搜轮挂断次数(对话轮次是1 ，且挂断的)
     */
    private Long totalFirstRoundHangup = 0L;

    /**
     * 接通-对话轮次
     */
    private Long totalRounds = 0L;
    /**
     * 接通-通话总时长(秒)
     */
    private Long totalDurationTime = 0L;

    /**
     * 接通-通话时段
     */
    private Map<String, Long> timeBucketMap = new HashMap<String, Long>() {{
        this.put("0-3", 0L);
        this.put("3-6", 0L);
        this.put("6-9", 0L);
        this.put("9-12", 0L);
        this.put("12-15", 0L);
        this.put("15-18", 0L);
        this.put("18-21", 0L);
        this.put("21-24", 0L);
    }};

    public void appendTimeBucket(String timeBucket, Object calls) {
        Long num = 0L;
        if (Objects.nonNull(calls)) {
            num = (Long) calls;
        }
        if (timeBucketMap.containsKey(timeBucket)) {
            timeBucketMap.put(timeBucket, num + timeBucketMap.get(timeBucket));
        } else {
            timeBucketMap.put(timeBucket, num);
        }
    }

    public void appendTotalCalls(Object totalCalls) {
        if (Objects.nonNull(totalCalls)) {
            this.totalCalls += (Long) totalCalls;
        }
    }

    public void appendTotalConnectedCalls(Object totalConnectedCalls) {
        if (Objects.nonNull(totalConnectedCalls)) {
            this.totalConnectedCalls += (Long) totalConnectedCalls;
        }
    }

    public void appendTotalFirstRoundHangup(Object totalFirstRoundHangup) {
        if (Objects.nonNull(totalFirstRoundHangup)) {
            this.totalFirstRoundHangup += (Long) totalFirstRoundHangup;
        }
    }

    public void appendTotalRounds(Object totalRounds) {
        if (Objects.nonNull(totalRounds)) {
            this.totalRounds += (Long) totalRounds;
        }
    }

    public void appendTotalDurationTime(Object totalDurationTime) {
        if (Objects.nonNull(totalDurationTime)) {
            this.totalDurationTime += (Long) totalDurationTime;
        }
    }

    public void statistics() {
        if (totalCalls == 0) {
            this.connectRate = 0F;
            this.firstRoundHangupRate = 0F;
        } else {
            this.connectRate = totalConnectedCalls * 1.0f / totalCalls;
            this.firstRoundHangupRate = totalFirstRoundHangup * 1.0f / totalCalls;
        }

        if (totalConnectedCalls == 0) {
            this.avgRounds = 0F;
            this.avgDuration = 0F;
        } else {
            this.avgRounds = totalRounds * 1.0f / totalConnectedCalls;
            this.avgDuration = totalDurationTime * 1.0f / totalConnectedCalls;
        }

        timeBucketStatistics = new HashMap<>();
        if (totalConnectedCalls == 0) {
            for (Map.Entry<String, Long> entry : timeBucketMap.entrySet()) {
                timeBucketStatistics.put(entry.getKey(), new AiobAggMetricTimeBucket(0L, 0F));
            }
        } else {
            for (Map.Entry<String, Long> entry : timeBucketMap.entrySet()) {
                float percent = entry.getValue() * 1.0f / totalConnectedCalls;
                timeBucketStatistics.put(entry.getKey(), new AiobAggMetricTimeBucket(entry.getValue(), percent));
            }
        }
    }
}
