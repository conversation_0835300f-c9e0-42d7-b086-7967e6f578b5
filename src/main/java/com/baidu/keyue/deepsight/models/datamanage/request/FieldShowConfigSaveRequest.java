package com.baidu.keyue.deepsight.models.datamanage.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName FieldShowConfigResponse
 * @Description 字段展示配置添加或更新请求体
 * <AUTHOR>
 * @Date 2025/5/9 3:25 PM
 */
@Data
public class FieldShowConfigSaveRequest implements Serializable {

    /**
     * 数据集ID
     */
    @NotNull(message = "数据集ID不能为空")
    private Long dataTableId;
    
    /**
     * 展示列，逗号分隔
     */
    @NotEmpty(message = "显示列字段不能为空")
    private List<String> showFields;
}
