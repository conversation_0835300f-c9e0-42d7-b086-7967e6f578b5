package com.baidu.keyue.deepsight.models.catalog;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ListCatalogRequest {

    /**
     * 标签目录名称，非必填
     */
    private String catalogName;

    /**
     * 标签名称，非必填
     */
    private String labelName;

    /**
     * 标签目录 ID，非必填
     */
    private Long catalogId;

    /**
     * 租户 ID，非必填
     */
    private String tenantId;
}
