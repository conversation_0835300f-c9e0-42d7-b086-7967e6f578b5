package com.baidu.keyue.deepsight.models.idmapping.request.relation;

import com.baidu.keyue.deepsight.models.base.request.BasePageRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @className: ListIdMappingRelRequest
 * @description: id对list查询 该类字段为空，暂无查询条件
 * @author: wangz<PERSON><PERSON>
 * @date: 2025/3/7 14:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ListIdMappingRelRequest extends BasePageRequest {

}
