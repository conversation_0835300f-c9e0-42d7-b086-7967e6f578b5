package com.baidu.keyue.deepsight.models.sop.nodepredict;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * *@Author: dongjiacheng01
 * *@Description: 节点预测算子 - 规则总结接口请求体
 * *@Date: 16:37 2025/5/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class NodeProcessSummaryRequest {

    private String taskId;

    private String dialogueRule;

}
