package com.baidu.keyue.deepsight.models.bsc;


import com.baidu.keyue.deepsight.models.bsc.basic.BscBaseResponse;
import com.baidu.keyue.deepsight.models.bsc.inner.BscCommitResult;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class BscCommitJobResponse extends BscBaseResponse {

    private BscCommitResult result;

    public String getInstanceId() {
        if (result == null) {
            return null;
        }
        return result.getInstanceId();
    }
}
