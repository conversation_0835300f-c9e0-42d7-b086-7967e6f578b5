package com.baidu.keyue.deepsight.models.datamanage.request;

import com.baidu.keyue.deepsight.enums.TableRecordOpEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @className TableRecordReq
 * @description 表数据记录请求
 * @date 2024/12/27 16:45
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TableRecordRequest {

    /**
     * 数据表id
     */
    @NotNull(message = "dataTableId不能为空")
    private Long dataTableId;

    /**
     * 数据表id
     */
    @NotEmpty(message = "ids不能为空")
    private List<String> ids;

    /**
     * 数据操作 DELETE:删除
     */
    private TableRecordOpEnum op;



}
