package com.baidu.keyue.deepsight.models.datamanage.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName BosStsResponse
 * @Description BOS sts临时授权响应
 * <AUTHOR>
 * @Date 2025/3/12 2:52 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StsSessionVo implements Serializable {
    private String accessKeyId;
    private String secretAccessKey;
    /**
     * sessionToken
     */
    private String sessionToken;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 过期时间
     */
    private Date expiration;
    /**
     * userID
     */
    private String userId;
    /** 地域，oss 需要 */
    private String region;
    /**
     * 桶名
     */
    private String bucket;
    /**
     * 域名
     */
    private String endPoint;
    /**
     * 限制权限为某objectKey时该值不为空
     */
    private String objectKey;
}
