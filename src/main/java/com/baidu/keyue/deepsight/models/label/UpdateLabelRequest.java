package com.baidu.keyue.deepsight.models.label;

import com.baidu.keyue.deepsight.annotation.LongIdConstraint;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateLabelRequest extends NewLabelRequest {
    /**
     * 标签ID
     */
    @LongIdConstraint(message = "标签ID无效")
    private Long labelId;

    /**
     * 标签值变更/保存类型变更/取值逻辑变更 的时候强制做一次覆盖计算
     */
    private Boolean recalculate;
}
