package com.baidu.keyue.deepsight.models.idmapping.request.relation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName SwitchIdMappingRelRequest
 * @Description 设置idmapping开关
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/3/10 15:31
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SwitchIdMappingRelRequest {
    // 开关状态
    @NotNull(message = "status不能为空")
    private Boolean status;
}
