package com.baidu.keyue.deepsight.models.bsc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BscStartInstanceRequest {
    private Boolean isRestart = false;
    private String vpcId = "vpc-f3ndwf8e0077";
    private String vpcUuid = null;
    private String subnetId = "sbn-s72h4xsia6sr";
    private String subnetUuid = null;
    private String logicalZone = "zoneF";
    private String securityGroupId = "g-p40e1bfwhrar";
    private String cidr = "172.16.5.0/24";
    private String cuType = "CPU";
    private Integer cpu = 1;
    private Integer ram = 4;
    private Integer minCu = 2;
    private Integer maxCu = 2;
}
