package com.baidu.keyue.deepsight.models.datamanage.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className TableFieldInfoDTO
 * @description 表字段信息
 * @date 2025/2/8, 11:37
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TableFieldInfoDTO {

    /**
     * 中文名称
     */
    @NotNull(message = "中文名称不能为空")
    private String cnName;

    /**
     * 英文名称
     */
    @NotNull(message = "英文名称不能为空")
    private String enName;

    /**
     * 数据类型 string单值 strings多值 number数字类 time时间类
     */
    @NotNull(message = "fieldType不能为空")
    private String fieldType;

    /**
     * 字段描述
     */
    private String description;

    /**
     * 是否展示value值
     */
    private Boolean isShowValue;

    /**
     * 是否必填
     */
    @NotNull(message = "isRequired不能为空")
    private Boolean isRequired;

    /**
     * 字段标记 0无 1主键 2事件 3度量 4敏感 5分区 6id
     */
    @NotNull(message = "fieldTag不能为空")
    private Integer fieldTag;

    /**
     * 字段取值类型 如strings:enum/text
     */
    @NotNull(message = "valueType不能为空")
    private String valueType;

    /**
     * 数据库字段类型
     */
    @NotNull(message = "dataType不能为空")
    private String dataType;

    /**
     * 作为筛选条件
     */
    @NotNull(message = "dataType不能为空")
    private Boolean isFilterCriteria;

    /**
     * 高级配置
     */
    private List<Map<String, String>> configInfos;
}
