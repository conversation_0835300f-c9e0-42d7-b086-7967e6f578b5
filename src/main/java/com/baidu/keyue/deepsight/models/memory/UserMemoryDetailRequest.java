package com.baidu.keyue.deepsight.models.memory;


import com.baidu.keyue.deepsight.enums.MemoryTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserMemoryDetailRequest {

    /**
     * user_id
     */
    private String userId;

    /**
     * oneId
     */
    private String oneId;

    /**
     * 记忆类型
     */
    private MemoryTypeEnum type;

    /**
     * 检索文本，可为空
     */
    private String queryText;
}
