package com.baidu.keyue.deepsight.models.datamanage.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName UploadDetailDto
 * @Description 文件上传DTO
 * <AUTHOR>
 * @Date 2025/3/13 12:20 PM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UploadDetailDto implements Serializable {
    /**
     * 原始文件名
     */
    private String sourceName;
    /**
     * bos 文件KEY（objectKey）
     */
    private String bosName;
}
