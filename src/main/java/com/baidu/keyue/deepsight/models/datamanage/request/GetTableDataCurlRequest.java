package com.baidu.keyue.deepsight.models.datamanage.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @className TableDataCurlRequest
 * @description 获取数据接入cur信息
 * @date 2025/2/8 14:56
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetTableDataCurlRequest {

    /**
     * 数据表id
     */
    @NotNull(message = "dataTableId不能为空")
    private Long dataTableId;




}
