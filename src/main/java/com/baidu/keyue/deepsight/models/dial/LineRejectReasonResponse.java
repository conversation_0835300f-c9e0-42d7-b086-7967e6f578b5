package com.baidu.keyue.deepsight.models.dial;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LineRejectReasonResponse {
    /**
     * 原因编码
     * @example "LINE_BLOCKED"
     */
    private String reason;

    /**
     * 该原因占比(0-100)
     */
    private Double percentage;

    /**
     * 该原因出现次数
     */
    private Integer count;

}
