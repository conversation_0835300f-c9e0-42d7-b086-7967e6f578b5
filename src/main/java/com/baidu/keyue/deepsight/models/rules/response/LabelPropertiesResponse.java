package com.baidu.keyue.deepsight.models.rules.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @className: LabelPropertiesResponse
 * @description: 可筛选标签树
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/24 23:34
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LabelPropertiesResponse {

    /**
     * 标签目录列表
     */
    private List<LabelCatalog> list;

}
