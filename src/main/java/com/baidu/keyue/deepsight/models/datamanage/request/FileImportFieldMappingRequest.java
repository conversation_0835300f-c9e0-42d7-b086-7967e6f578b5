package com.baidu.keyue.deepsight.models.datamanage.request;

import com.baidu.keyue.deepsight.enums.ImportMappingTypeEnum;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName FileImportFieldMappingRequest
 * @Description 文件导入数据字段映射
 * <AUTHOR>
 * @Date 2025/2/21 3:27 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FileImportFieldMappingRequest {

    /**
     * 数据集ID
     */
    @NotNull(message = "数据集ID不能为空")
    public Long dataTableId;
    
    @NotNull(message = "文件ID不能为空")
    public Long fileId;
    /**
     * 字段映射模式：EQUAL_NAME-同名映射（默认），AI_SEMANTEME-AI语义映射
     */
    public ImportMappingTypeEnum mappingType = ImportMappingTypeEnum.EQUAL_NAME;
}
