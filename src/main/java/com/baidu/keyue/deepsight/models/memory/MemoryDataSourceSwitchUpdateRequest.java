package com.baidu.keyue.deepsight.models.memory;

import com.baidu.keyue.deepsight.annotation.LongIdConstraint;
import com.baidu.keyue.deepsight.enums.SwitchEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MemoryDataSourceSwitchUpdateRequest {

    @LongIdConstraint(message = "数据集ID无效")
    private Long id;

    private SwitchEnum status;
}
