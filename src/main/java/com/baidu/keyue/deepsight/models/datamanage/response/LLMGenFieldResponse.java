package com.baidu.keyue.deepsight.models.datamanage.response;

import com.baidu.keyue.deepsight.models.datamanage.dto.TableFieldInfoDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @className AiGenFieldResponse
 * @description ai生成字段结果
 * @date 2025/2/13 14:56
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LLMGenFieldResponse {

    /**
     * 数据集字段
     */
    private List<TableFieldInfoDTO> tableFieldInfos;
}
