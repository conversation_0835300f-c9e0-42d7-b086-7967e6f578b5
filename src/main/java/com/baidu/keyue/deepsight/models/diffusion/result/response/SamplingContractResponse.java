package com.baidu.keyue.deepsight.models.diffusion.result.response;

import com.baidu.keyue.deepsight.models.diffusion.DiffusionCalculateContext;
import com.baidu.keyue.deepsight.models.diffusion.dto.CharacteristicDistributionContractDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * @className: SamplingContractResponse
 * @description: 抽样对比预测结果响应
 * @author: wangzhongcheng
 * @date: 2025/3/25 19:33
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SamplingContractResponse {

    /**
     * 预测结果与抽样分布对比
     */
    private List<CharacteristicDistributionContractDTO> contractList;

    /**
     * 将DiffusionCalculateContext转换为SamplingContractResponse
     */
    public static SamplingContractResponse convertFrom(DiffusionCalculateContext diffusionCalculateContext) {
        SamplingContractResponse samplingContractResponse = new SamplingContractResponse();
        samplingContractResponse.setContractList(diffusionCalculateContext.getContractList());
        return samplingContractResponse;
    }

    /**
     * 获取默认的SamplingContractResponse
     */
    public static SamplingContractResponse getDefaultResponse() {
        SamplingContractResponse samplingContractResponse = new SamplingContractResponse();
        samplingContractResponse.setContractList(new ArrayList<>());
        return samplingContractResponse;
    }

}
