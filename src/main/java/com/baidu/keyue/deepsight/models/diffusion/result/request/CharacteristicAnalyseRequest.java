package com.baidu.keyue.deepsight.models.diffusion.result.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * @className: CharacteristicAnalyseRequest
 * @description: 特征分析请求参数
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2025/3/25 19:51
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CharacteristicAnalyseRequest {

    @NotNull(message = "人群扩散任务ID不能为空")
    private Long customerDiffusionTaskId;

}
