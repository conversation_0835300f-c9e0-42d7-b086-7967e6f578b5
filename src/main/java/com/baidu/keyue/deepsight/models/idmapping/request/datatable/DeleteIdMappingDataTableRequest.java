package com.baidu.keyue.deepsight.models.idmapping.request.datatable;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: DeleteIdMappingDataTableRequest
 * @description: id mapping 数据表规则配置 删除请求
 * @author: chenwenyu03
 * @date: 2025/3/11 10:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeleteIdMappingDataTableRequest {

    /**
     * 数据表id
     */
    @NotNull(message = "抽取数据集不能为空")
    private Long dataTableId;
}
