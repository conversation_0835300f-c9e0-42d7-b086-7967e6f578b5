package com.baidu.keyue.deepsight.models.customer.response;

import cn.hutool.json.JSONUtil;
import com.baidu.keyue.deepsight.enums.DiffusionFilterEnum;
import com.baidu.keyue.deepsight.enums.FeatureSelectEnum;
import com.baidu.keyue.deepsight.enums.JudgeCriteriaEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @ClassName CustomerDiffusionTaskRes
 * @Description 人群扩散任务详情
 * <AUTHOR>
 * @Date 2025/3/25 2:01 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDiffusionTaskRes implements Serializable {

    /**
     * 预测扩散id
     */
    private Long id;

    /**
     * 预测任务名称
     */
    private String taskName;

    /**
     * 种子人群id
     */
    private Long seedGroup;
    /**
     * 种子人群详情
     */
    private GroupDetailDto seedGroupDetail;

    /**
     * 预测人群
     */
    private List<Long> predictGroup = new ArrayList<>();

    /**
     * 预测人群详情
     */
    private List<GroupDetailDto> predictGroupDetail;

    /**
     * 过滤规则:NOT_FILTER-不过滤 REMOVE_SEED_GROUP-剔除种子人群
     */
    private DiffusionFilterEnum filterRule;

    /**
     * 特征筛选:SYSTEM_RECOMMEND-系统推荐 COVER_CUSTOMIZE-覆盖率自定义
     */
    private FeatureSelectEnum featureSelect;

    /**
     * 覆盖率阈值
     */
    private Float threshold;

    /**
     * 判定标准:SIMILARITY-根据相似度 RANKING-取前几个
     */
    private JudgeCriteriaEnum judgeCriteria;

    /**
     * 判定标准：相似度
     */
    private Float similarity;

    /**
     * 判定标准：取前几个
     */
    private Integer ranking;

    /**
     * 更新触发类型:CRON-定时更新 MANUAL-手动更新 REALTIME-实时更新
     */
    private TriggerModeEnum triggerMod;

    /**
     * 执行频率
     */
    private TriggerFrequencyEnum triggerFrequency;

    /**
     * 执行频率json
     */
    private TriggerFrequencyValue triggerFrequencyValue;

    /**
     * 计算状态:PENDING-待计算,RUNNING-计算中,SUCCESS-成功,FAILED-失败,CANCELED-已取消
     */
    private TaskExecStatusEnum calStatus;

    /**
     * 上一次执行时间
     */
    private Date lastCalDate;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 打包到客群标识,false:未进行,true:已进行
     */
    private Boolean groupPackage;

    /**
     * 删除标识,false:未删除,true:已删除
     */
    private Boolean del;

    /**
     * 创建者
     */
    private String creator;

    /**
     * 修改者
     */
    private String modifier;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人姓名
     */
    private String creatorName;

    /**
     * 更新人姓名
     */
    private String modifierName;

    /**
     * 打包客群ID
     */
    private Long customerGroupId;

    public CustomerDiffusionTaskRes(CustomerDiffusionTask task) {
        BeanUtils.copyProperties(task, this);
        for (String id : task.getPredictGroup().split(",")) {
            this.predictGroup.add(Long.parseLong(id));
        }
        this.filterRule = DiffusionFilterEnum.getByCode(task.getFilterRule());
        this.featureSelect = FeatureSelectEnum.getByCode(task.getFeatureSelect());
        this.judgeCriteria = JudgeCriteriaEnum.getByCode(task.getJudgeCriteria());
        this.triggerMod = TriggerModeEnum.getByCode(task.getTriggerMod());
        this.calStatus = TaskExecStatusEnum.getByCode(task.getCalStatus());
        this.triggerFrequency = TriggerFrequencyEnum.getByCode(task.getTriggerFrequency());
        this.triggerFrequencyValue = JSONUtil.toBean(task.getTriggerFrequencyValue(), TriggerFrequencyValue.class);
    }
}
