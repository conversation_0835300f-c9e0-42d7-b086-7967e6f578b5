package com.baidu.keyue.deepsight.models.dial;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @ClassName AiobTaskListResponse
 * @Description 外呼任务列表res
 * @<PERSON> v_<PERSON><PERSON>
 * @Date 2025/7/7 5:49 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AiobTaskListResponse {
    private String code;
    private String msg;
    private TaskDetail data;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TaskDetail {
        private Integer total;
        private Integer pn;
        private Integer ps;
        private List<TaskList> list;
        
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TaskList {
        private Long taskId;
        private String taskName;
        private String robotId;
        private String robotLongId;
        private String robotName;
        private Integer taskRobotType;
        private Integer taskType;
        private Integer status;
        private Integer robotScene;
    }
}
