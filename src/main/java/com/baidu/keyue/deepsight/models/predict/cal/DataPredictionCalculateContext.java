package com.baidu.keyue.deepsight.models.predict.cal;

import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.bsc.basic.BaseCalculateContext;
import com.baidu.keyue.deepsight.models.bsc.datapredict.BscDataPredictTaskRequest;
import com.baidu.keyue.deepsight.mysqldb.entity.DataPredictionSourceWithBLOBs;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataPredictionCalculateContext extends BaseCalculateContext {

    private DataPredictionSourceWithBLOBs dataPredictionSource;

    private BscDataPredictTaskRequest bscDataPredictTaskRequest;

    private TriggerModeEnum triggerMode;
    private String ruleSql;
    private Integer count;

    public String generateBscJobName() {
        return String.format("%s%d-%d", Constants.DORIS_DATA_PREDICT_PROCESS_FIELD_NAME_PREFIX,
                dataPredictionSource.getId(), getExecId());
    }
}
