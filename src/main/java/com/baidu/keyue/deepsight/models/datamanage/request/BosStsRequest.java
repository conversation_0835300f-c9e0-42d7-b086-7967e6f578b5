package com.baidu.keyue.deepsight.models.datamanage.request;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName BosStsRequest
 * @Description BOS sts 临时session请求
 * <AUTHOR>
 * @Date 2025/3/12 2:48 PM
 */
@Data
public class BosStsRequest implements Serializable {

    /**
     * 需要哪个bucket的权限
     */
    private String bucket;

    /**
     * url前缀
     */
    private String endPoint;

    /**
     * 协议http、https
     */
    private String protocol;

    /**
     * 文件后缀
     */
    @NotBlank(message = "文件后缀不能为空")
    private String suffix;
}
