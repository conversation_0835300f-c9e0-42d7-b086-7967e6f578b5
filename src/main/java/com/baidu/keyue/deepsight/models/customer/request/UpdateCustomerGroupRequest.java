package com.baidu.keyue.deepsight.models.customer.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @ClassName UpdateCustomerGroupRequest
 * @Description 更新客群详情请求体
 * <AUTHOR> (<EMAIL>)
 * @Date 2024/12/30 20:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateCustomerGroupRequest extends CreateCustomerGroupRequest {
    /**
     * 客群ID
     */
    private Long customerGroupId;
}
