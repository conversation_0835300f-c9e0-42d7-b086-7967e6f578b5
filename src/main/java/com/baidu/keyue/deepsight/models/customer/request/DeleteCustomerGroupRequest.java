package com.baidu.keyue.deepsight.models.customer.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @ClassName DeleteCustomerGroupRequest
 * @Description 删除客群请求体
 * <AUTHOR> (<EMAIL>)
 * @Date 2024/12/30 20:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DeleteCustomerGroupRequest {
    /**
     * 客群ID
     */
    @NotNull(message = "customerGroupId不可为空")
    private Long customerGroupId;
}
