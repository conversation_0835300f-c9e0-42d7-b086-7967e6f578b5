package com.baidu.keyue.deepsight.models.datamanage.dto;

import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @className TenantDTO
 * @description 租户升级dtl
 * @date 2025/3/10 10:48
 */
@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TenantDTO {

    private TenantInfo tenantInfo;

    private String tenantId;

    private String type;

    private UserAuthInfo authInfo;
    
    private Boolean isInit = false;
}
