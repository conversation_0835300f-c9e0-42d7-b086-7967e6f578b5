package com.baidu.keyue.deepsight.models.customer.response;

import com.baidu.keyue.deepsight.mysqldb.entity.CustomerGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName GroupDetail
 * @Description 客群简单详情
 * <AUTHOR>
 * @Date 2025/3/26 11:13 AM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GroupDetailDto implements Serializable {
    /**
     * 客群ID
     */
    private Long id;
    /**
     * 客群名
     */
    private String customerGroupName;
    /**
     * 人数
     */
    private Long count;

    public GroupDetailDto(CustomerGroup customerGroup) {
        this.id = customerGroup.getId();
        this.customerGroupName = customerGroup.getCustomerGroupName();
    }
}
