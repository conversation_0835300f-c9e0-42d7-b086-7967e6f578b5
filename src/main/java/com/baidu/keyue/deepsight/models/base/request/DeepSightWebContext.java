package com.baidu.keyue.deepsight.models.base.request;

import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

/**
 * @ClassName DeepSightWebContext
 * @Description 洞察请求上下文DTO
 * <AUTHOR>
 * @Date 2025/3/19 3:31 PM
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DeepSightWebContext extends UserAuthInfo {
    /**
     * 请求ID
     */
    private String requestId;

    public DeepSightWebContext(UserAuthInfo userAuthInfo) {
        BeanUtils.copyProperties(userAuthInfo, this);
    }
}
