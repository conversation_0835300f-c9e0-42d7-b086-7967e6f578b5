package com.baidu.keyue.deepsight.models.diffusion;

import java.util.List;

import com.baidu.keyue.deepsight.models.meg.Attribute;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DiffusionRequestItem {
    @JsonProperty("oneid")
    private String oneId;

    private List<Attribute> attribute;
}
