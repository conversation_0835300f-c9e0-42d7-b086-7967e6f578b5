package com.baidu.keyue.deepsight.models.predict;

import java.util.List;

import com.baidu.keyue.deepsight.enums.PredictUpdateModEnum;
import com.baidu.keyue.deepsight.enums.PromptTypeEnum;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class PredictDataSource {

    /**
     * 预测使用数据
     */
    private List<PredictDataSet> dataSourceList;

    /**
     * 指令类型
     */
    private PromptTypeEnum promptType;
    /**
     * 抽取指令
     */
    private String prompt;

    /**
     * 预测数据更新方式
     */
    private PredictUpdateModEnum predictionUpdateType;

    /**
     * 抽取方式
     */
    private TriggerModeEnum triggerMode;

    /**
     * 执行频率
     */
    private TriggerFrequencyEnum triggerFrequency;

    /**
     * 执行频率具体时间配置
     * */
    private TriggerFrequencyValue triggerFrequencyValue;

    /**
     * 描述
     */
    private String description;
}
