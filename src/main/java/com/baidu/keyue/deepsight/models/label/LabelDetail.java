package com.baidu.keyue.deepsight.models.label;

import com.baidu.keyue.deepsight.enums.ExecModEnum;
import com.baidu.keyue.deepsight.enums.TaskExecStatusEnum;
import com.baidu.keyue.deepsight.enums.TriggerModeEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.Label;
import com.baidu.keyue.deepsight.mysqldb.entity.TaskSchedulerWithBLOBs;
import com.baidu.keyue.deepsight.utils.DatetimeUtils;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LabelDetail {

    /** 标签ID */
    private Long labelId;

    /** 标签名称 */
    private String labelName;

    /** 标签目录 ID */
    private Long catalogId;

    /** 生产方式 */
    private String execMod;

    /** 更新方式 */
    private String triggerMod;

    /** 创建人 */
    private String creator;

    /** 创建时间, eg: "2024-12-23 12:23:00" */
    private String createTime;

    /** 状态 */
    private TaskExecStatusEnum status;

    /** 失败原因 */
    private String errorMessage;

    /** 标签更新取值逻辑 */
    private String updateMod;

    /** 标签值保存类型 */
    private String labelValueSaveMod;

    /** 执行频率 */
    private String triggerFrequency;

    /** 执行频率具体时间配置 */
    private TriggerFrequencyValue triggerFrequencyValue;

    /** 最新一次计算时间，eg：“2024-12-22” */
    private String lastTaskDate;

    /** 标签值规则 */
    private LabelRule labelRule;

    /**
     * 将标签 entity 转换为标签详情 VO
     *
     * @param label 标签 entity
     * @param taskSchedulerRecordMap key-taskId，value-taskSchedulerRecord
     * @return
     */
    public static LabelDetail convertFrom(
            Label label, Map<Long, TaskSchedulerWithBLOBs> taskSchedulerRecordMap) {
        LabelDetail labelBrief = new LabelDetail();
        labelBrief.setLabelId(label.getId());
        labelBrief.setLabelName(label.getLabelName());
        labelBrief.setExecMod(ExecModEnum.getDescByCode(label.getExecMod()));
        labelBrief.setTriggerMod(TriggerModeEnum.getDescByCode(label.getTriggerMod()));
        labelBrief.setCreator(label.getCreator());
        labelBrief.setCreateTime(DatetimeUtils.formatDate(label.getCreateTime()));

        TaskExecStatusEnum calStatus = TaskExecStatusEnum.getByCode(label.getLabelCalStatus());
        labelBrief.setStatus(calStatus);
        if (TaskExecStatusEnum.FAILED.equals(calStatus)) {
            Long taskId = label.getTask();

            if (Objects.nonNull(taskSchedulerRecordMap)
                    && Objects.nonNull(taskSchedulerRecordMap.get(taskId))) {
                labelBrief.setErrorMessage(taskSchedulerRecordMap.get(taskId).getMessage());
            } else {
                labelBrief.setErrorMessage("");
            }
        } else {
            labelBrief.setErrorMessage("");
        }
        return labelBrief;
    }
}
