package com.baidu.keyue.deepsight.models.dial;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RobotInfoResponse {
    /**
     * 机器人id
     * @example "robot-789"
     */
    private String robotId;

    /**
     * 机器人名称
     * @example "客服机器人A"
     */
    private String robotName;

    public RobotInfoResponse(Map<String, Object> map) {
        Object robotId = map.get("robotId");
        Object robotName = map.get("robotName");
        this.robotId = (robotId == null) ? "" : robotId.toString();
        this.robotName = (robotName == null) ? "" : robotName.toString();
    }
}
