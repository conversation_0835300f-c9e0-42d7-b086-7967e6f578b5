package com.baidu.keyue.deepsight.models.diffusion;

import java.util.List;

import com.baidu.keyue.deepsight.enums.OperationModeEnum;
import com.baidu.keyue.deepsight.models.bsc.basic.BaseCalculateContext;
import com.baidu.keyue.deepsight.models.diffusion.dto.CharacteristicDistributionContractDTO;
import com.baidu.keyue.deepsight.models.diffusion.dto.DiffusionCharacteristicDTO;
import com.baidu.keyue.deepsight.models.diffusion.dto.GradingDistributionDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.CustomerDiffusionTask;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class DiffusionCalculateContext extends BaseCalculateContext {

    private CustomerDiffusionTask diffusionTask;
    private String table;
    private String tenantId;
    private Float threshold;

    /**
     * 运营模式， 默认为自运营
     */
    private OperationModeEnum operationMode = OperationModeEnum.OPERATION_BY_SELF;

    /**
     * 种子人群
     */
    private DiffusionGroupContext seedGroup;
    /**
     * 预测人群
     */
    private DiffusionGroupContext predictGroup;

    /**
     * 输出文件夹 ObjectKey 前缀
     */
    private String outputDirPrefix;

    /**
     * 人群特征重要度结果
     */
    private List<DiffusionCharacteristicDTO> features;

    /**
     * 分段柱状图展示列表: 按照 score 分段统计人数
     */
    private List<GradingDistributionDTO> gradingDistributionList;

    /**
     * 预测结果与抽样分布对比
     */
    private List<CharacteristicDistributionContractDTO> contractList;
}
