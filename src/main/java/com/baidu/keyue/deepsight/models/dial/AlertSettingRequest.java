package com.baidu.keyue.deepsight.models.dial;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baidu.keyue.deepsight.enums.AlertConfigTypeEnum;
import com.baidu.keyue.deepsight.enums.AlertTimeTypeEnum;
import com.baidu.keyue.deepsight.mysqldb.entity.AlertConfig;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AlertSettingRequest {

    /**
     * 配置记录ID
     */
    private Integer id;
    /**
     * 配置对象类型：LINE(号线)/TASK(任务)/ROBOT(机器人)
     * 例如： LINE
     */
    @NotNull(message = "配置对象类型不能为空")
    private AlertConfigTypeEnum configType;

    /**
     * 配置对象的id "line-123"
     */
    @NotBlank(message = "配置对象的id不能为空")
    private String configId;

    /**
     * 是否告警开关：0(关闭)/1(开启)
     */
    private Integer alertSwitch;

    /**
     * 告警时间类型：24H(近24小时)/7D(近7天)/30D(近30天)
     * 例如：24H
     */
    @NotNull(message = "告警时间类型不能为空")
    private AlertTimeTypeEnum alertTimeType;

    /**
     * 接通率阈值(0~100)
     */
    @Range(min = 0, max = 100, message = "接通率阈值(0~100)")
    private Integer connectedRateThreshold;

    /**
     * 拨打次数阈值：10/100/1000/10000
     */
    @NotNull(message = "拨打次数阈值不能为空")
    private Integer callCountThreshold;

    /**
     * 告警频率（小时）：1/12/24/72
     */
    @NotNull(message = "告警频率（小时)不能为空")
    private Integer alertFrequency;

    public AlertSettingRequest(AlertConfig alertConfig) {
        BeanUtils.copyProperties(alertConfig, this);
        this.configType = AlertConfigTypeEnum.createByValue(alertConfig.getConfigType());
        this.alertTimeType = AlertTimeTypeEnum.createByValue(alertConfig.getAlertTime());
        this.connectedRateThreshold = alertConfig.getThresholdRate().intValue();
        this.callCountThreshold = alertConfig.getDialCount();
        this.alertFrequency = alertConfig.getAlertFreq();
        this.configId = alertConfig.getConfigTarget();
        this.alertSwitch = alertConfig.getIsActive() ? 1 : 0;
    }

    public AlertConfig toAlertConfig() {
        AlertConfig alertConfig = new AlertConfig();
        alertConfig.setConfigType(this.configType.getValue());
        alertConfig.setConfigTarget(this.configId);
        alertConfig.setIsActive(Objects.equals(this.alertSwitch, 1));
        alertConfig.setAlertTime(this.alertTimeType.getValue());
        alertConfig.setThresholdRate(new BigDecimal(this.connectedRateThreshold));
        alertConfig.setDialCount(this.callCountThreshold);
        alertConfig.setAlertFreq(this.alertFrequency);
        // 如果是激活状态，计算下次执行时间
        DateTime dateTime = DateUtil.offsetMinute(DateUtil.beginOfHour(new Date()), -1);
        alertConfig.setNextCheckTime(DateUtil.offsetHour(dateTime, this.alertFrequency));
        return alertConfig;
    }
}

