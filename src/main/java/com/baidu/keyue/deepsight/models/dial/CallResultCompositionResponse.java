package com.baidu.keyue.deepsight.models.dial;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CallResultCompositionResponse {
    /**
     * 呼通结果
     * @example "小秘书接通"
     */
    private String resultName;

    /**
     * 该结果占比(0-100)
     */
    private Double percentage;


    public static List<CallResultCompositionResponse> convert(Map<String, Object> map) {
        List<CallResultCompositionResponse> responses = new ArrayList<>();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            CallResultCompositionResponse response  = new CallResultCompositionResponse();
            response.setResultName(entry.getKey());
            Object percentage =  entry.getValue();
            response.setPercentage((percentage) == null ? 0 : ((BigDecimal) percentage).doubleValue());
            responses.add(response);
        }
        return responses;
    }

}
