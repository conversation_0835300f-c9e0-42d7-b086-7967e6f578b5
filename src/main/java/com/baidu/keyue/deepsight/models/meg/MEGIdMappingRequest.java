package com.baidu.keyue.deepsight.models.meg;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class MEGIdMappingRequest {

    @JsonProperty("request_items")
    private List<RequestItem> requestItems;

    @Data
    private static class RequestItem {
        private RequestId id;
        private Integer encrypted;
        @JsonProperty("mapping_types")
        private List<Integer> mappingTypes;
        @JsonProperty("need_cross_mapping")
        private Boolean needCrossMapping;
    }

    @Data
    private static class RequestId {
        private String value;
        private Integer type;
    }

    public static MEGIdMappingRequest buildWithMobile(String mobile) {
        MEGIdMappingRequest req = new MEGIdMappingRequest();

        RequestItem item = new RequestItem();

        RequestId id = new RequestId();
        id.setType(MEGIdEnum.phone.getCode());
        id.setValue(mobile);

        item.setId(id);
        item.setEncrypted(mobile.length() == 11 ? 0 : 1);
        item.setMappingTypes(MEGIdEnum.getIdCodesExceptMobile());
        item.setNeedCrossMapping(true);

        req.setRequestItems(Lists.newArrayList(item));
        return req;
    }

    public static MEGIdMappingRequest buildWithMobile(List<String> mobiles) {
        MEGIdMappingRequest req = new MEGIdMappingRequest();

        List<RequestItem> requestItems = Lists.newArrayList();
        for (String mobile : mobiles) {
            RequestItem item = new RequestItem();
            RequestId id = new RequestId();
            id.setType(MEGIdEnum.phone.getCode());
            id.setValue(mobile);

            item.setId(id);
            item.setEncrypted(mobile.length() == 11 ? 0 : 1);
            item.setMappingTypes(MEGIdEnum.getIdCodesExceptMobile());
            item.setNeedCrossMapping(true);
            requestItems.add(item);
        }
        req.setRequestItems(requestItems);
        return req;
    }

    public static MEGIdMappingRequest buildWithOtherId(String otherId, MEGIdEnum type) {
        MEGIdMappingRequest req = new MEGIdMappingRequest();

        RequestItem item = new RequestItem();

        RequestId id = new RequestId();
        id.setType(type.getCode());
        id.setValue(otherId);

        item.setId(id);
        item.setEncrypted(0);
        item.setMappingTypes(MEGIdEnum.getIdCodesExceptMobile());
        item.setNeedCrossMapping(true);

        req.setRequestItems(Lists.newArrayList(item));
        return req;
    }
}
