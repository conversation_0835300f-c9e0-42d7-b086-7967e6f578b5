package com.baidu.keyue.deepsight.models.idmapping.request.relation;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @className: UpdateIdMappingRelRequest
 * @description: id-mapping 对修改请求
 * @author: wangzhong<PERSON>
 * @date: 2025/3/6 14:13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class UpdateIdMappingRelRequest {

    /**
     * ID对主键 id
     */
    @NotNull(message = "ID对不能为空")
    private Long idMappingRelId;

    /**
     * ID对字段列表
     */
    @NotNull(message = "至少选择两个可以标识用户的ID")
    @Size(min = 2, message = "至少选择两个可以标识用户的ID")
    private List<String> tableEnFields;

    /**
     * 是否添加ID对到刷新OneId数据集列表
     */
    private Boolean refreshOneId = false;

    /**
     * 是否添加ID对到ID Mapping表配置中
     */
    private Boolean addToIdMapping = false;

}
