package com.baidu.keyue.deepsight.models.sop;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SopSankeyWholeResponse {

    /**
     * 步骤+节点列表
     */
    private List<SopSankeyStep> steps;
    /**
     * 边列表
     */
    private List<SopEdge> edges;
}
