package com.baidu.keyue.deepsight.models.rules.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @className: FilterEnumInfo
 * @description:
 * @author: wangzhongcheng
 * @date: 2025/1/3 14:46
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FilterEnumInfo {

    /**
     * 枚举字段 - 对应数据库中的值
     */
    private String key;
    private String value;
    private String desc;

}
