package com.baidu.keyue.deepsight.models.bsc;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BscCreateJobRequest {
    /**
     * 作业所在地域,如保定区域为bd,北京区域为bj
     */
    private String region;
    /**
     * 作业名称，只能包含大小写英文字母，数字，下划线_，横行-
     */
    private String name;
    /**
     * 作业类型  FLINK_STREAM/JAR
     */
    private String type;
    /**
     * 引擎版本
     * flink: 1.18.1-bsc
     */
    private String engineVersion;


    /**
     * 作业描述信息
     */
    private String description;
}
