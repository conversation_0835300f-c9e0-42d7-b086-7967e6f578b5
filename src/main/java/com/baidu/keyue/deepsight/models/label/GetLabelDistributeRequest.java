package com.baidu.keyue.deepsight.models.label;

import com.baidu.keyue.deepsight.annotation.LongIdConstraint;
import com.baidu.keyue.deepsight.enums.SortOrderEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetLabelDistributeRequest {

    /**
     * 标签ID
     */
    @LongIdConstraint(message = "标签ID无效")
    private Long labelId;

    /**
     * 排序方式 “ASC”/“DESC”, 默认DESC
     */
    private SortOrderEnum sort = SortOrderEnum.DESC;
}
