package com.baidu.keyue.deepsight.models.bsc;

import com.baidu.keyue.deepsight.models.bsc.basic.BscBaseResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BscStartInstanceResponse extends BscBaseResponse {
    private String instanceId;
}
