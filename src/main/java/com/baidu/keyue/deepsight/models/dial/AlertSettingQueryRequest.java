package com.baidu.keyue.deepsight.models.dial;

import com.baidu.keyue.deepsight.enums.AlertConfigTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AlertSettingQueryRequest {
    /**
     * 配置对象类型：LINE(号线)/TASK(任务)/ROBOT(机器人)
     * @example LINE
     */
    private AlertConfigTypeEnum configType;

    /**
     * 配置对象的id
     * @example "line-123"
     */
    @NotBlank(message = "配置对象的id不能为空")
    private String configId;
}
