package com.baidu.keyue.deepsight.models.diffusion.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @className: GradingDistribution
 * @description: 评分分布对象
 * @author: wangzhongcheng
 * @date: 2025/3/25 19:41
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GradingDistributionDTO {

    /**
     * 评分分段描述：0-0.1、0.1-0.2 ... 0.9-1
     */
    private String grade;

    /**
     * 落于该分段的人数
     */
    private Long count;

    /**
     * 该分段占比
     */
    private Double percentage;
}
