package com.baidu.keyue.deepsight.models.datamanage.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName StsItemDto
 * @Description StsItemDto
 * <AUTHOR>
 * @Date 2025/3/12 3:28 PM
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StsItemDto implements Serializable {
    private String eid;
    private String service;
    private String region;
    private String effect;
    private List<String> resource;
    private List<String> permission;
}
