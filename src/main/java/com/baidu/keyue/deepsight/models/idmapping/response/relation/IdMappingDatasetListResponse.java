package com.baidu.keyue.deepsight.models.idmapping.response.relation;

import com.baidu.keyue.deepsight.mysqldb.entity.DataTableInfo;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @className: IdMappingDatasetListResponse
 * @description: idMapping数据集列表响应 下拉列表展示，没有分页
 * @author: wangzhongcheng
 * @date: 2025/3/7 18:58
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class IdMappingDatasetListResponse {

    private List<IdMappingDatasetItem> list;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IdMappingDatasetItem {
        /**
         * 数据表id
         */
        private Long dataTableId;
        /**
         * 数据表名称
         */
        private String dataTableName;
        /**
         * 是否被使用 true-被使用 false-未被使用
         */
        private Boolean used;

        /**
         * 将数据集实体对象转换为ID对选择的数据集展示对象。
         *
         * @param dataTableInfo DataTableInfo对象，包含数据表的信息
         * @param used 数据表是否被使用，true表示已使用，false表示未使用
         * @return 转换后的IdMappingDatasetItem对象
         */
        public static IdMappingDatasetItem convertFrom(DataTableInfo dataTableInfo, Boolean used) {
            IdMappingDatasetItem idMappingDatasetItem = new IdMappingDatasetItem();
            idMappingDatasetItem.setUsed(used);
            idMappingDatasetItem.setDataTableId(dataTableInfo.getId());
            idMappingDatasetItem.setDataTableName(dataTableInfo.getCnName());
            return idMappingDatasetItem;
        }

    }

    /**
     * 将提供的数据表信息列表和已使用的数据表ID列表转换为ID对数据集列表展示对象。
     *
     * @param usedDataTableIds 已使用的数据表ID列表
     * @param dataTableInfos   数据表信息列表
     * @return 转换后的IdMappingDatasetListResponse对象
     */
    public static IdMappingDatasetListResponse convertFrom(List<Long> usedDataTableIds,
                                                           List<DataTableInfo> dataTableInfos) {
        IdMappingDatasetListResponse idMappingDatasetListResponse = new IdMappingDatasetListResponse();
        List<IdMappingDatasetItem> resultList = dataTableInfos.stream()
                .map(dataTableInfo ->
                        IdMappingDatasetItem
                                .convertFrom(dataTableInfo, usedDataTableIds.contains(dataTableInfo.getId()))
                )
                .toList();
        idMappingDatasetListResponse.setList(resultList);
        return idMappingDatasetListResponse;
    }

}
