package com.baidu.keyue.deepsight.models.datamanage.response;

import com.baidu.keyue.deepsight.enums.TableFieldTagEnum;
import com.baidu.keyue.deepsight.enums.TableFieldValueEnum;
import com.baidu.keyue.deepsight.models.rules.response.FilterEnumInfo;
import com.baidu.keyue.deepsight.mysqldb.entity.TableFieldMetaInfo;
import com.baidu.keyue.deepsight.utils.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.List;

/**
 * @className: VisibleFieldResp
 * @description:
 * @author: wangzhongcheng
 * @date: 2025/1/2 15:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VisibleFieldResponse {

    /**
     * 字段ID
     */
    private Long id;

    /**
     * 中文名称
     */
    private String cnName;

    /**
     * 英文名称
     */
    private String enName;

    /**
     * 字段标签
     */
    private TableFieldTagEnum tableFieldTag;

    /**
     * 字段枚举
     */
    private List<FilterEnumInfo> enums;

    /**
     * 数据库字段类型
     */
    private String dataType;
    /**
     * 表字段类型
     */
    private String fieldType;

    /**
     * 枚举是否展示值：true-展示value，false-展示业务值
     */
    private Boolean isShowValue;

    /**
     * 是否必填：true-必填，false-非必填
     */
    private Boolean isRequired;

    public VisibleFieldResponse(TableFieldMetaInfo tableFieldMetaInfo) {
        BeanUtils.copyProperties(tableFieldMetaInfo, this);
        List<FilterEnumInfo> filterEnumInfos = null;
        if (TableFieldValueEnum.getByValueType(tableFieldMetaInfo.getValueType()) == TableFieldValueEnum.ENUM
                && StringUtils.isNotEmpty(tableFieldMetaInfo.getConfigInfos())) {
            filterEnumInfos = JsonUtils.toListUnchecked(
                    tableFieldMetaInfo.getConfigInfos(), List.class, FilterEnumInfo.class);
        }
        this.enums = filterEnumInfos;
        this.enName = tableFieldMetaInfo.getEnField();
        this.cnName = tableFieldMetaInfo.getCnField();
        this.tableFieldTag = TableFieldTagEnum.getByCode(tableFieldMetaInfo.getFieldTag());
    }

    /**
     * 将TableFieldMetaInfo转换为VisibleFieldResp
     */
    public static VisibleFieldResponse convertFrom(TableFieldMetaInfo tableFieldMetaInfo) {
        List<FilterEnumInfo> filterEnumInfos = null;
        if (TableFieldValueEnum.getByValueType(tableFieldMetaInfo.getValueType()) == TableFieldValueEnum.ENUM
                && StringUtils.isNotEmpty(tableFieldMetaInfo.getConfigInfos())) {
            filterEnumInfos = JsonUtils.toListUnchecked(
                    tableFieldMetaInfo.getConfigInfos(), List.class, FilterEnumInfo.class);
        }

        return VisibleFieldResponse.builder()
                .cnName(tableFieldMetaInfo.getCnField())
                .enName(tableFieldMetaInfo.getEnField())
                .dataType(tableFieldMetaInfo.getDataType())
                .enums(filterEnumInfos)
                .isShowValue(tableFieldMetaInfo.getIsShowValue())
                .isRequired(tableFieldMetaInfo.getIsRequired())
                .tableFieldTag(TableFieldTagEnum.getByCode(tableFieldMetaInfo.getFieldTag())).build();
    }

    /**
     * 将TableFieldMetaInfo转换为VisibleFieldResp
     */
    public static VisibleFieldResponse convertFrom(String cnName,
                                                   String enName,
                                                   boolean isShowValue) {

        return VisibleFieldResponse.builder()
                .cnName(cnName)
                .enName(enName)
                .isShowValue(isShowValue).build();
    }

}
