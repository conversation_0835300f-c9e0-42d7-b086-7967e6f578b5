package com.baidu.keyue.deepsight.safesdk;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

/**
 * @ClassName SafeSdkConfig
 * @Description safe sdk 配置
 * <AUTHOR>
 * @Date 2025/6/24 3:48 PM
 */
@Configuration
@Component
public class SafeSdkConfig {

    public static String safeSdkPath;

    @Value("${safe.sdk-config-path}")
    public void setSafeSdkPath(String path) {
        safeSdkPath = path;
    }
}
