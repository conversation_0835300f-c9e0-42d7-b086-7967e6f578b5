package com.baidu.keyue.deepsight.safesdk;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URL;
import java.net.UnknownHostException;
import java.util.List;
import java.util.function.Predicate;

/**
 * 解决RestTemplate安全问题引入
 */
@Component
@Slf4j
public class SafeHttpRequestFactory extends SimpleClientHttpRequestFactory {
    @Autowired
    private SafeWhitelistConfig safeWhitelistConfig;

    /**
     * 判断是否内网地址 list
     */
    private static final List<Predicate<InetAddress>> PRIVATE_IP_RULES = List.of(
            InetAddress::isLoopback<PERSON>dd<PERSON>,
            InetAddress::isAny<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
            InetAddress::isLink<PERSON>ocal<PERSON>ddress,
            InetAddress::isSiteLocal<PERSON>dd<PERSON>,
            InetAddress::isMulticastAddress,
            InetAddress::isMCGlobal,
            InetAddress::isMCLinkLocal,
            InetAddress::isMCNodeLocal,
            InetAddress::isMCOrgLocal,
            InetAddress::isMCSiteLocal
    );

    @Override
    protected void prepareConnection(HttpURLConnection connection, String method) throws IOException {
        URL url = connection.getURL();
        String host = url.getHost();
        InetAddress address = null;
        String ip = null;
        try {
            address = InetAddress.getByName(host);
            ip = address.getHostAddress();
        } catch (UnknownHostException e) {
            log.error("Failed to resolve host: ", e);
        }

        if (StringUtils.isNotEmpty(ip) && !safeWhitelistConfig.getIpWhitelist().contains(ip)
                && StringUtils.isNotEmpty(host) && !safeWhitelistConfig.getHostWhitelist().contains(host)) {
            if (isPrivateIp(address)) {
                log.error("Blocked unsafe request to private IP [{}] from host: {}", ip, host);
                throw new SecurityException("Blocked unsafe request to private IP: " + ip);
            }
        }
        super.prepareConnection(connection, method);
    }

    /**
     * 判断给定的IP地址是否为私有IP地址。
     *
     * @param ip 需要判断的IP地址
     * @return 如果给定的IP地址为私有IP地址，则返回true；否则返回false
     */
    private boolean isPrivateIp(InetAddress ip) {
        if (ip == null) {
            log.info("isPrivateIp InetAddress is null");
            return true;
        }
        return PRIVATE_IP_RULES.stream().anyMatch(rule -> rule.test(ip));
    }
}