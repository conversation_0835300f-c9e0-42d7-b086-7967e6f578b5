package com.baidu.keyue.deepsight.safesdk;

import jakarta.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * 安全部 okHttp 链接白名单校验配置
 * <AUTHOR>
 */
@Component
public class SafeWhitelistConfig {

    @Value("${safe.whitelist.ips}")
    private String ipWhitelist;

    @Value("${safe.whitelist.hosts}")
    private String hostWhitelist;

    private Set<String> ipSet;
    private Set<String> hostSet;

    @PostConstruct
    public void init() {
        this.ipSet = new HashSet<>(Arrays.asList(ipWhitelist.split(",")));
        this.hostSet = new HashSet<>(Arrays.asList(hostWhitelist.split(",")));
    }

    public Set<String> getIpWhitelist() {
        return ipSet;
    }

    public Set<String> getHostWhitelist() {
        return hostSet;
    }
}