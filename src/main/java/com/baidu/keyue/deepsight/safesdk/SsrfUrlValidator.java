package com.baidu.keyue.deepsight.safesdk;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.URL;
import java.util.List;
import java.util.function.Predicate;

/**
 * 解决webClient ssrf引入
 */
@Slf4j
@Component
public class SsrfUrlValidator {

    @Autowired
    private SafeWhitelistConfig safeWhitelistConfig;

    private static final List<Predicate<InetAddress>> PRIVATE_IP_RULES = List.of(
            InetAddress::isLoopbackAddress,
            InetAddress::isAnyLocalAddress,
            InetAddress::isLinkLocalAddress,
            InetAddress::isSiteLocalAddress,
            InetAddress::isMulticastAddress,
            InetAddress::isMCGlobal,
            InetAddress::isMCLinkLocal,
            InetAddress::isMCNodeLocal,
            InetAddress::isMCOrgLocal,
            InetAddress::isMCSiteLocal
    );

    public void validateUrl(String rawUrl) {
        InetAddress address = null;
        String host = "";
        String ip = "";
        try {
            URL url = new URL(rawUrl);
            host = url.getHost();
            address = InetAddress.getByName(host);
            ip = address.getHostAddress();
        } catch (Exception e) {
            log.error("Error parsing URL: {}", rawUrl, e);
        }

        if (StringUtils.isNotEmpty(ip) && !safeWhitelistConfig.getIpWhitelist().contains(ip)
                && StringUtils.isNotEmpty(host) && !safeWhitelistConfig.getHostWhitelist().contains(host)) {
            if (isPrivateIp(address)) {
                log.warn("Blocked SSRF attempt to private IP: {}", ip);
                throw new SecurityException("Blocked unsafe request to IP: " + ip);
            }
        }
    }

    private boolean isPrivateIp(InetAddress ip) {
        if (ip == null) {
            log.info("isPrivateIp InetAddress is null");
            return true;
        }
        return PRIVATE_IP_RULES.stream().anyMatch(rule -> rule.test(ip));
    }
}