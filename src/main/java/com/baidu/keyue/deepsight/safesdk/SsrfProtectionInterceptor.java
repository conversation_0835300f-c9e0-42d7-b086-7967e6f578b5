package com.baidu.keyue.deepsight.safesdk;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpRequest;
import org.springframework.http.client.ClientHttpRequestExecution;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.http.client.ClientHttpResponse;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.InetAddress;
import java.net.URI;
import java.net.UnknownHostException;
import java.util.List;
import java.util.function.Predicate;

/**
 * 解决RestTemplate安全问题引入
 */
@Component
@Slf4j
public class SsrfProtectionInterceptor implements ClientHttpRequestInterceptor {

    @Autowired
    private SafeWhitelistConfig safeWhitelistConfig;

    /**
     * 判断是否内网地址 list
     */
    private static final List<Predicate<InetAddress>> PRIVATE_IP_RULES = List.of(
            InetAddress::isLoopbackAddress,
            InetAddress::isAnyLocalAddress,
            InetAddress::isLinkLocalAddress,
            InetAddress::isSiteLocalAddress,
            InetAddress::isMulticastAddress,
            InetAddress::isMCGlobal,
            InetAddress::isMCLinkLocal,
            InetAddress::isMCNodeLocal,
            InetAddress::isMCOrgLocal,
            InetAddress::isMCSiteLocal
    );

    @Override
    public ClientHttpResponse intercept(HttpRequest request, byte[] body, ClientHttpRequestExecution execution)
            throws IOException {
        URI uri = request.getURI();
        String host = uri.getHost();
        InetAddress address = null;
        String ip = null;
        try {
            address = InetAddress.getByName(host);
            ip = address.getHostAddress();
        } catch (UnknownHostException e) {
            log.error("Failed to resolve host: {}", host, e);
        }

        if (StringUtils.isNotEmpty(ip) && !safeWhitelistConfig.getIpWhitelist().contains(ip)
                && StringUtils.isNotEmpty(host) && !safeWhitelistConfig.getHostWhitelist().contains(host)) {
            if (isPrivateIp(address)) {
                log.error("Blocked unsafe request to private IP: {}", ip);
                throw new SecurityException("SSRF risk: private IP access blocked. IP=" + ip + ", host=" + host);
            }
        }

        return execution.execute(request, body);
    }

    /**
     * 判断给定的IP地址是否为私有IP地址。
     *
     * @param ip 需要判断的IP地址
     * @return 如果给定的IP地址为私有IP地址，则返回true；否则返回false
     */
    private boolean isPrivateIp(InetAddress ip) {
        if (ip == null) {
            log.info("isPrivateIp InetAddress is null");
            return true;
        }
        return PRIVATE_IP_RULES.stream().anyMatch(rule -> rule.test(ip));
    }
}