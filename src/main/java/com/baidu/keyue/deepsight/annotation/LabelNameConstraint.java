package com.baidu.keyue.deepsight.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

@Constraint(validatedBy = LabelNameValidator.class)
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface LabelNameConstraint {
    String message() default "标签名称无效";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
