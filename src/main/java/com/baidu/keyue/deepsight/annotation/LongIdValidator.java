package com.baidu.keyue.deepsight.annotation;

import java.util.Objects;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

public class LongIdValidator implements ConstraintValidator<LongIdConstraint, Long> {
    /**
     * ID不能为空 && 不能小于等于 0
     * */
    @Override
    public boolean isValid(Long s, ConstraintValidatorContext constraintValidatorContext) {
        return Objects.nonNull(s) && s > 0;
    }
}
