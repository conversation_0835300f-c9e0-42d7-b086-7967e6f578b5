package com.baidu.keyue.deepsight.utils;

import cn.hutool.core.collection.CollectionUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.config.ErrorCode;
import com.baidu.keyue.deepsight.models.echopath.EchoPathResp;
import com.baidu.keyue.deepsight.models.exception.DeepSightException;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.Map;
import java.util.Objects;

/**
 * @title: RestTemplateUtils
 * @description: RestTemplateUtils
 * @author: v_zhu<PERSON>ji
 * @date: 2025/2/18 10:18 AM
 **/
@Component
@Slf4j
public class RestTemplateUtils {
    @Autowired
    private RestTemplate restTemplate;

    /**
     * 发送post请求
     * @param url 请求地址
     * @param entity 请求体
     * @param clazz
     * @return
     * @param <T>
     */
    public <T> ResponseEntity<T> postForEntity(
            String url, HttpEntity entity, Class<T> clazz) {
        log.info("postEntity url={}", url);
        HttpHeaders headers = entity.getHeaders();
        if (Objects.isNull(headers.getContentType())) {
            // 默认 请求头为json格式
            headers.setContentType(MediaType.APPLICATION_JSON);
        }
        return restTemplate.postForEntity(url, entity, clazz);
    }

    /**
     * 发送post请求给洞察
     * @param url       请求地址
     * @param headerMap 请求头
     * @param jsonBody  请求体 json字符串
     * @return
     */
    public EchoPathResp postJsonEchoPath(String url
            , Map<String, String> headerMap, String jsonBody) {
        log.info("postJsonEchoPath url={}, headerMap={} , jsonBody={}", url
                , JsonUtils.transferToJson(headerMap), JsonUtils.transferToJson(jsonBody));
        if (Objects.nonNull(WebContextHolder.getUserAuthInfo())) {
            String userId = String.valueOf(WebContextHolder.getUserAuthInfo().getUserId());
            String tenantId = String.valueOf(WebContextHolder.getUserAuthInfo().getTenantId());
            headerMap.put(Constants.USER_ID_HEADER_KEY, userId);
            headerMap.put(Constants.TENANT_ID_HEADER_KEY, tenantId);
        }
        headerMap.put(Constants.SERVER_NAME_HEADER_KEY, Constants.SERVER_NAME);
        headerMap.put(Constants.REQUEST_ID_HEADER_KEY, MDC.get(Constants.REQUEST_ID_FIELD));
        HttpHeaders headers = new HttpHeaders();
        // 添加自定义的请求头
        if (CollectionUtil.isNotEmpty(headerMap)) {
            for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                headers.add(entry.getKey(), entry.getValue());
            }
        }
        if (Objects.isNull(headers.getContentType())) {
            // 默认 请求头为json格式
            headers.setContentType(MediaType.APPLICATION_JSON);
        }
        HttpEntity<String> entity = new HttpEntity<>(jsonBody, headers);
        try {
            ResponseEntity<EchoPathResp> response = postForEntity(url, entity, EchoPathResp.class);
            if (response != null) {
                return response.getBody();
            }
        } catch (Exception e) {
            log.error("postJsonEchoPath url={}", url, e);
            throw new DeepSightException.BusinessException(ErrorCode.INTERNAL_ERROR, "请求客户洞察异常");
        }

        return null;
    }
}
