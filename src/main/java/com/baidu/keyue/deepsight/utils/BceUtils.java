package com.baidu.keyue.deepsight.utils;


import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.baidu.keyue.deepsight.models.datamanage.dto.BosProperty;
import com.baidu.keyue.deepsight.models.datamanage.dto.StsRequestTo;
import com.baidu.keyue.deepsight.models.datamanage.request.BceRequest;
import com.baidu.keyue.deepsight.models.datamanage.response.StsSessionVo;
import com.baidu.kybase.commons.utils.HttpUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

/**
 * @ClassName BceUtils
 * @Description BCE工具类
 * <AUTHOR>
 * @Date 2025/3/12 2:24 PM
 */
public class BceUtils {

    private static final Logger LOG = LoggerFactory.getLogger(BceUtils.class);

    /**
     * 远程调用获取sts Token
     * 在token超时之前，不要用这个方法，使用缓存中的token，避免性能损失！
     */
    public static StsSessionVo getStsSession(BceRequest<StsRequestTo> request, BosProperty bosProperty)
            throws MalformedURLException, InvalidKeyException, NoSuchAlgorithmException, URISyntaxException {
        String requestId = IdUtil.randomUUID();
        LOG.debug("getStsSession: requestId={}, request={}", requestId, JSONUtil.toJsonStr(request));
        String authority = signature(request, bosProperty, requestId);
        Map<String, String> headerMap = new HashMap<>(request.getHeader());
        headerMap.put("Authorization", authority);
        String resultJson = HttpUtil.postJson(request.getUrl(), JSONUtil.toJsonStr(request.getRequestBody()), headerMap);
        LOG.debug("getStsSession: requestId={}, result={}", requestId, resultJson);
        StsSessionVo sessionVo = JsonUtils.readType(resultJson, new TypeReference<>() {
        });
        sessionVo.setBucket(bosProperty.getBucket());
        sessionVo.setEndPoint(bosProperty.getEndPoint());
        sessionVo.setRegion(bosProperty.getRegion());
        return sessionVo;
    }
    
    public static String signature(BceRequest<?> request, BosProperty bosProperty, String requestId)
            throws MalformedURLException, NoSuchAlgorithmException, InvalidKeyException, URISyntaxException {
        if (requestId == null) {
            requestId = IdUtil.randomUUID();
        }
        LOG.debug("signature: requestId={}, request={}", requestId, JSONUtil.toJsonStr(request));
        StringBuilder result = new StringBuilder();
        URL url = new URL(request.getUrl());
        String authStringPrefix = "bce-auth-v1/"
                + bosProperty.getAccessKey()
                + "/" + DatetimeUtils.gmtDateFormat(DateUtils.addHours(request.getRequestDate(), -8))
                + "/1800";
        LOG.debug("signature: requestId={}, authStringPrefix={}", requestId, authStringPrefix);
        result.append(authStringPrefix)
                .append("/").append(getSignedHeaders(request))
                .append("/").append(finalSign(request, bosProperty, authStringPrefix, requestId));
        String resultStr = result.toString();
        LOG.debug("signature: requestId={}, result={}", requestId, resultStr);
        return resultStr;
    }

    private static String finalSign(BceRequest<?> request, BosProperty bosProperty,
                                    String authStringPrefix, String requestId)
            throws InvalidKeyException, NoSuchAlgorithmException, MalformedURLException, URISyntaxException {
        if (requestId == null) {
            requestId = IdUtil.randomUUID();
        }
        LOG.debug("finalSign: requestId={}, request={}", requestId, JSONUtil.toJsonStr(request));
        String canonicalRequest = buildCanonicalRequest(request, requestId);
        LOG.debug("finalSign: requestId={}, canonicalRequest=\n{}", requestId, canonicalRequest);
        String signingKey = hmacSha256(authStringPrefix, bosProperty.getSecret());
        LOG.debug("finalSign: requestId={}, signingKey={}", requestId, signingKey);
        return hmacSha256(canonicalRequest, signingKey);
    }

    /**
     * 生成 HMACSHA256
     * @param data 待处理数据
     * @param key 密钥
     * @return 加密结果
     */
    public static String hmacSha256(String data, String key) throws NoSuchAlgorithmException, InvalidKeyException {
        Mac sha256Hmac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        sha256Hmac.init(secretKey);
        byte[] array = sha256Hmac.doFinal(data.getBytes(StandardCharsets.UTF_8));
        StringBuilder sb = new StringBuilder();
        for (byte item : array) {
            sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
        }
        return sb.toString().toLowerCase();
    }

    private static String buildCanonicalRequest(BceRequest<?> request, String requestId)
            throws MalformedURLException, URISyntaxException {
        if (requestId == null) {
            requestId = IdUtil.randomUUID();
        }
        URL url = new URL(request.getUrl());
        StringBuilder canonicalRequest = new StringBuilder();
        String method = request.getHttpMethod();
        String uri = StringUtil.uriEncode(url.toURI().getPath(), false);
        String queryStr = buildCanonicalQueryString(url);
        LOG.info("CanonicalQueryString={}", queryStr);
        String header = buildCanonicalHeaders(request);
        LOG.debug("buildCanonicalRequest: method={}, uri={}, queryStr={}, header=----------\n{}",
                method, uri, queryStr, header);
        canonicalRequest.append(method).append("\n")
                .append(uri).append("\n")
                .append(queryStr).append("\n")
                .append(header);
        return canonicalRequest.toString();
    }

    private static String buildCanonicalHeaders(BceRequest<?> request) throws MalformedURLException {
        URL url = new URL(request.getUrl());
        Set<String> sortedSet = new TreeSet<>();
        for (String key : request.getWaitSignHeader()) {
            if ("host".equalsIgnoreCase(key)) {
                String host = url.getHost();
                Integer port = url.getPort();
                if (port != null && port != 80 && port > 0) {
                    host += ":" + port;
                }
                sortedSet.add("host:" + StringUtil.uriEncode(host, true));
                continue;
            }
            String value = request.getHeader().get(key);
            if (value == null) {
                continue;
            }
            value = value.trim();
            if (value.isEmpty()) {
                continue;
            }
            sortedSet.add(StringUtil.uriEncode(key.toLowerCase(), true)
                    + ":" + StringUtil.uriEncode(value, true));
        }
        return String.join("\n", sortedSet);
    }

    private static String buildCanonicalQueryString(URL url) {
        Set<String> sortedSet = new TreeSet<>();
        String query = url.getQuery();
        if (StringUtils.isNotBlank(query)) {
            String[] split = query.split("\\&");
            for (String kv : split) {
                String[] split1 = kv.split("=");
                if (split1.length == 1) {
                    sortedSet.add(StringUtil.uriEncode(split1[0], true) + "=");
                } else {
                    sortedSet.add(StringUtil.uriEncode(split1[0], true)
                            + "=" + StringUtil.uriEncode(split1[1], true));
                }
            }
            return String.join("&", sortedSet);
        } else {
            return "";
        }
    }

    private static String getSignedHeaders(BceRequest<?> request) {
        Set<String> sortedHeader = new TreeSet<>();
        for (String key : request.getWaitSignHeader()) {
            sortedHeader.add(key.toLowerCase());
        }
        return String.join(";", sortedHeader);
    }

}
