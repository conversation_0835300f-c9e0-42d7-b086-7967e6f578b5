package com.baidu.keyue.deepsight.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * @ClassName GzipUtils
 * @Description 压缩工具类
 * <AUTHOR>
 * @Date 2025/2/17 4:42 PM
 */
@Slf4j
public class GzipUtils {

    /**
     * 压缩对象为byte数组
     * object为空则返回空
     *
     * @param data 字符串数据
     * @return 压缩数据
     */
    public static byte[] compressObj(Object data) {
        try {
            if (data == null) {
                return null;
            }
            String json = JsonUtils.toJson(data);
            return compressString(json);
        } catch (IOException e) {
            log.error("object zip to byte IOException:", e);
        }
        return null;
    }

    /**
     * 压缩字符串为byte数组
     * 如果字符串为空，则返回空
     *
     * @param data 字符串数据
     * @return 压缩数据
     */
    public static byte[] compressString(String data) {
        if (StrUtil.isBlank(data)) {
            return null;
        }
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream)) {
            gzipOutputStream.write(data.getBytes());

        } catch (IOException e) {
            log.error("string zip IOException:", e);
            return null;
        }
        return byteArrayOutputStream.toByteArray();
    }

    /**
     * 解压字符串
     *
     * @param compressedData 字符串byte压缩
     * @return 字符串
     */
    public static String decompressToString(byte[] compressedData) {
        if (compressedData == null || compressedData.length == 0) {
            return null;
        }
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(compressedData);
        try (GZIPInputStream gzipInputStream = new GZIPInputStream(byteArrayInputStream);
             InputStreamReader reader = new InputStreamReader(gzipInputStream);
             BufferedReader bufferedReader = new BufferedReader(reader)) {
            return bufferedReader.readLine();
        } catch (IOException e) {
            log.error("decompressToString error:{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
