package com.baidu.keyue.deepsight.utils;

/**
 * @ClassName MathUtils
 * @Description mathUtils
 * <AUTHOR>
 * @Date 2025/7/1 10:46 AM
 */
public class MathUtils {

    /**
     * 讲double转为int百分数
     * 0.81 --> 81,
     * 
     * @param doublePercent
     * @return
     */
    public static Integer doubleToIntPercent(Double doublePercent) {
        return (int) Math.round(doublePercent * 100);
    }
}
