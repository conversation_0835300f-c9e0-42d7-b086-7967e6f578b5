package com.baidu.keyue.deepsight.utils;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baidu.keyue.deepsight.enums.TriggerFrequencyEnum;
import com.baidu.keyue.deepsight.models.label.TriggerFrequencyValue;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

@UtilityClass
public class DatetimeUtils {
    private static final Logger LOG = LoggerFactory.getLogger(DatetimeUtils.class);

    public static final DateTimeFormatter DATE_TIME_ZONE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");
    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter PURE_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter PURE_DATETIME_FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    public static final DateTimeFormatter DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter HOUR_FORMAT = DateTimeFormatter.ofPattern("HH:mm");
    public static final DateTimeFormatter GMT_DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");

    public long getCurrentTimestampInSec() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 格式化日期 yyyy-MM-dd HH:mm:ss
     * */
    public String formatDate(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return DateUtil.formatDateTime(date);
    }

    public String formatDate(LocalDateTime dateTime) {
        return dateTime.format(DATE_TIME_FORMATTER);
    }

    /**
     * 格式化日期 yyyyMMdd
     * */
    public String pureDateFormat(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return DateUtil.format(date, PURE_DATE_FORMAT);
    }

    /**
     * 格式化日期 yyyy-MM-dd'T'HH:mm:ss'Z'
     * */
    public String gmtDateFormat(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return DateUtil.format(date, GMT_DATE_FORMAT);
    }

    /**
     * 格式化日期 yyyyMMddHHmmss
     * */
    public String pureDateTimeFormat(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }
        return DateUtil.format(date, PURE_DATETIME_FORMAT);
    }

    public String backToWithFrequency(TriggerFrequencyEnum triggerFrequency, TriggerFrequencyValue triggerFrequencyValue) {
        LocalDateTime now = LocalDateTime.now();
        if (Objects.isNull(triggerFrequencyValue)) {
            return formatDate(now);
        }

        LocalDateTime back = switch (triggerFrequency) {
            case DAY -> now.minusHours(24);
            case WEEK -> now.minusHours(24 * 7);
            case MONTH -> now.minusHours(24 * 31);
            default -> now;
        };
        return formatDate(back);
    }

    public String backToDate(int minutes) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime back = now.minusMinutes(minutes);
        return formatDate(back);
    }

    // 时间戳转成DateTime类型
    public DateTime fromTimestamp(long timestamp) {
        return DateUtil.date(timestamp);
    }


    public DateTime fromDatetimeStr(String datetimeStr, DateTimeFormatter formatter) {
        if (StringUtils.isBlank(datetimeStr)) {
            return null;
        }
        try {
            return DateUtil.parse(datetimeStr, formatter);
        } catch (Exception e) {
            return null;
        }
    }

    public DateTime fromDatetimeStr(String datetimeStr) {
        if (StringUtils.isBlank(datetimeStr)) {
            return null;
        }
        try {
            return DateUtil.parse(datetimeStr);
        } catch (Exception e) {
            LOG.error("dateTime parser error, value:{},", datetimeStr, e);
            return null;
        }
    }

    public String dateFormat(Date date) {
        return DateUtil.format(date, DATE_FORMAT);
    }

    public String hourFormat(Date date) {
        return DateUtil.format(date, HOUR_FORMAT);
    }

    /**
     * 获取昨天的日期
     * @return yyyy-MM-dd
     */
    public String yesterdayDate() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime yesterday = now.minusHours(24);
        return yesterday.format(DATE_FORMAT);
    }

    /**
     * 获取 N 天前的日期
     * @param day 天数
     * @return yyyy-MM-dd
     */
    public String passedDateByDay(int day) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime yesterday = now.minusDays(day);
        return yesterday.format(DATE_FORMAT);
    }
}
