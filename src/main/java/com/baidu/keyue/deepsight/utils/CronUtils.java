package com.baidu.keyue.deepsight.utils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import com.cronutils.model.Cron;
import com.cronutils.model.CronType;
import com.cronutils.model.definition.CronDefinitionBuilder;
import com.cronutils.model.time.ExecutionTime;
import com.cronutils.parser.CronParser;
import com.google.common.collect.Lists;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;

/**
 * UNIX 风格 Cron 表达式
 *     0                  1                  *                        *                   *
 * minute (0-59)	hour (0 - 23)	day of the month (1 - 31)	month (1 - 12)	day of the week (1 - 7)
 *
 * PS: week实际取值 0~7，1～5表示星期一到星期五, 0和7都可以表示星期日，前后端统一采用1～7
 * */
@UtilityClass
public class CronUtils {

    /**
     * 每天：可选0点到23点整数点
     * @param hour 小时数
     * @return cron expression
     */
    public String everyDay(Integer hour) {
        List<String> cronExpressions = Lists.newArrayList("0", String.valueOf(hour), "*", "*", "*");
        return StringUtils.join(cronExpressions, " ");
    }

    /**
     * 每周：可选每周一~每周日+0点到23点整数点
     * @param hour 小时数
     * @param dayOfWeek 星期几
     * @return cron expression
     */
    public String everyWeek(Integer hour, Integer dayOfWeek) {
        List<String> cronExpressions = Lists.newArrayList("0", String.valueOf(hour), "*", "*", String.valueOf(dayOfWeek));
        return StringUtils.join(cronExpressions, " ");
    }

    /**
     * 每月：可选每月1号~31号+0点到23点整数点
     * @param hour 小时数
     * @param dayOfMonth 每月几号
     * @return cron expression
     */
    public String everyMonth(Integer hour, Integer dayOfMonth) {
        List<String> cronExpressions = Lists.newArrayList("0", String.valueOf(hour), String.valueOf(dayOfMonth), "*", "*");
        return StringUtils.join(cronExpressions, " ");
    }

    public LocalDateTime nextExecLocalDateTime(String cronExpression) {
        ZonedDateTime now = ZonedDateTime.now();
        CronParser parser = new CronParser(CronDefinitionBuilder.instanceDefinitionFor(CronType.UNIX));

        try {
            Cron cron = parser.parse(cronExpression);
            ExecutionTime executionTime = ExecutionTime.forCron(cron);
            Optional<ZonedDateTime> nextExecution = executionTime.nextExecution(now);
            return nextExecution.map(ZonedDateTime::toLocalDateTime).orElse(null);
        } catch (Exception e) {
            return null;
        }
    }

    public Date nextExecDate(String cronExpression) {
        LocalDateTime localDateTime = nextExecLocalDateTime(cronExpression);
        if (localDateTime == null) {
            return null;
        }
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

}
