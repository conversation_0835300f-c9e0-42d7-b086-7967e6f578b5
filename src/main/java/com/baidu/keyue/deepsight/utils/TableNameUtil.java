package com.baidu.keyue.deepsight.utils;

import com.baidu.keyue.deepsight.config.Constants;
import org.apache.commons.lang.StringEscapeUtils;

/**
 * @className: TableNameUtil
 * @description:
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2025/3/26 20:38
 */
public class TableNameUtil {

    /**
     * 生成人群扩散结果临时表
     *
     * @param diffusionId 人群扩展任务id
     * @return
     */
    public static String generateDiffusionTemporaryTableName(long diffusionId) {
        return StringEscapeUtils.escapeSql(Constants.DORIS_DIFFUSION_PROCESS_TEMPORARY_TABLE_NAME_PREFIX + diffusionId);
    }

}
