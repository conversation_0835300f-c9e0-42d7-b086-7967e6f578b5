package com.baidu.keyue.deepsight.utils;

import java.net.URLEncoder;

/**
 * @ClassName StringUtil
 * @Description 字符串工具类
 * <AUTHOR>
 * @Date 2025/3/12 2:24 PM
 */
public class StringUtil {

    public static String null2str(Object obj) {
        return obj == null ? "" : obj.toString();
    }


    /**
     * uri编码
     * @param input 入参字符串
     * @param encodeSlash 是否需要编码'/'字符
     * @return 编码后的字符串
     */
    public static String uriEncode(CharSequence input, boolean encodeSlash) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char ch = input.charAt(i);
            if ((ch >= 'A' && ch <= 'Z') || (ch >= 'a' && ch <= 'z') || (ch >= '0' && ch <= '9')
                    || ch == '_' || ch == '-' || ch == '~' || ch == '.') {
                result.append(ch);
            } else if (ch == '/') {
                result.append(encodeSlash ? "%2F" : ch);
            } else {
                try {
                    result.append(URLEncoder.encode(String.valueOf(ch), "utf-8"));
                } catch (Exception e) {
                    return null;
                }
            }
        }
        return result.toString();
    }

    /**
     * 获取SQL 模糊查询
     * @param name
     * @return
     */
    public static String getSqlLike(String name){
        return "%" + name + "%";
    }

}
