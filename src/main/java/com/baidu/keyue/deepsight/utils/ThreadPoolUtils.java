package com.baidu.keyue.deepsight.utils;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.concurrent.BasicThreadFactory;

public class ThreadPoolUtils {

    private static volatile ThreadPoolExecutor threadPoolExecutor = null;

    public static ThreadPoolExecutor getSingleThreadPool() {
        if (threadPoolExecutor == null) {
            synchronized (ThreadPoolUtils.class) {
                if (threadPoolExecutor == null) {
                    threadPoolExecutor = new ThreadPoolExecutor(8, 16,
                            5, TimeUnit.SECONDS, new LinkedBlockingQueue<>(),
                            new BasicThreadFactory.Builder().namingPattern("SingleThreadPool-%d").build(),
                            new ThreadPoolExecutor.DiscardPolicy()
                    );
                }
            }
        }
        return threadPoolExecutor;
    }
}
