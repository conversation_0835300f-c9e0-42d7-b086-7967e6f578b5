package com.baidu.keyue.deepsight.utils;

import java.util.List;

import com.google.common.collect.Lists;
import lombok.experimental.UtilityClass;

@UtilityClass
public class AliasUtils {

    public static List<String> getAlias(int count) {
        List<String> alias = Lists.newArrayList();

        for (int i = 0; i < count; i++) {
            alias.add(getAliasName(i));
        }
        return alias;
    }

    public static String getAliasName(int num) {
        return String.valueOf((char) ('A' + num));
    }
}
