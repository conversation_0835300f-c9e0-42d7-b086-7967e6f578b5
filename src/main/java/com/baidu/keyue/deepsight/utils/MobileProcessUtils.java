package com.baidu.keyue.deepsight.utils;

import java.util.Map;
import java.util.Objects;

import com.baidu.keyue.deepsight.config.Constants;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
@UtilityClass
public class MobileProcessUtils {

    public String maskMobileNumber(String mobile) {
        if (mobile == null || mobile.length() != 11) {
            return "****";
        }
        return mobile.substring(0, 3) + "****" + mobile.substring(7);
    }


    public String decryptMobile(String mobile, Map<String, String> fieldEncryptInfo) {
        if (StringUtils.isBlank(mobile)) {
            return mobile;
        } else if (mobile.length() == 11) {
            return mobile;
        }

        try {
            return AESUtils.decrypt(mobile, Constants.MOBILE_DECRYPT_KEY);
        } catch (Exception e) {
            log.error("decrypt mobile with default key error", e);
        }

        if (Objects.nonNull(fieldEncryptInfo) && fieldEncryptInfo.containsKey("mobile")) {
            try {
                return AESUtils.decrypt(mobile, fieldEncryptInfo.get("mobile"));
            } catch (Exception e) {
                log.error("decrypt mobile error", e);
            }
        }
        return mobile;
    }

    public String decryptMobile(String decryptKey, String mobile) {
        if (mobile.length() == 11) {
            return mobile;
        }
        try {
            return AESUtils.decrypt(mobile, Constants.MOBILE_DECRYPT_KEY);
        } catch (Exception e) {
            // ignore
        }
        // 使用默认解密key失败，则查询该表对应的解密key
        if (StringUtils.isBlank(decryptKey)) {
            return null;
        }
        try {
            return AESUtils.decrypt(mobile, decryptKey);
        } catch (Exception e) {
            return null;
        }
    }
}
