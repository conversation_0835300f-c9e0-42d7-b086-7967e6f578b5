package com.baidu.keyue.deepsight.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @className FileUtil
 * @description 文件工具类
 * @date 2025/2/17 16:33
 */
@Slf4j
public class FileUtil {

    /**
     * @Description: 从指定的文件中读取字符串内容，并返回该字符串。如果读取过程中出现任何IO异常或文件不存在，则抛出相应的异常。
     * @Param fileName string - 需要读取的文件名，包含路径信息，例如："config/application.properties"
     * @Return String - 返回读取到的字符串内容，如果读取失败则返回null
     * @Throw IOException - 当读取过程中出现IO异常时抛出此异常
     * @Throw FileNotFoundException - 当指定的文件不存在时抛出此异常
     */
    public static String readFileAsString(String fileName) throws IOException {
        String content = null;
        InputStream inputStream = null;
        byte[] fileBytes;
        try {
            // 读取配置文件
            ClassPathResource classPathResource = new ClassPathResource(fileName);
            inputStream = classPathResource.getInputStream();
            fileBytes = IOUtils.toByteArray(inputStream);
            content = new String(fileBytes, StandardCharsets.UTF_8);
        } catch (FileNotFoundException e) {
            log.error("文件 {} 未找到", fileName, e);
            throw new FileNotFoundException();
        } catch (IOException e) {
            log.error("读取文件 {} 失败", fileName, e);
            throw new IOException();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("读取文件 {} 发生异常", fileName, e);
                }
            }
        }
        return content;
    }
}
