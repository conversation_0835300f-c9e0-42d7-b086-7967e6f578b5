package com.baidu.keyue.deepsight.utils;

import freemarker.cache.StringTemplateLoader;
import freemarker.template.Configuration;
import freemarker.template.Template;

import java.io.StringWriter;
import java.util.Map;

/**
 * <AUTHOR>
 * @className FreeMarkUtil
 * @description 模版引擎工具
 * @date 2025/2/17 16:28
 */
public class FreeMarkUtil {

    public static String stringReplace(String source, Map<String, Object> replaceMap) throws Exception {
        Configuration configuration = new Configuration(Configuration.getVersion());
        StringTemplateLoader stringTemplateLoader = new StringTemplateLoader();
        stringTemplateLoader.putTemplate("myTemplate", source);
        configuration.setTemplateLoader(stringTemplateLoader);
        Template template = configuration.getTemplate("myTemplate", "utf-8");
        StringWriter writer = new StringWriter();
        template.process(replaceMap, writer);
        return writer.toString();
    }

}
