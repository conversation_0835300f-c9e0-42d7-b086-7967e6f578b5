package com.baidu.keyue.deepsight.web.interceptors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


/**
 * <AUTHOR>
 * @className InterceptorConfig
 * @description 拦截器配置
 * @date 2025/3/10 12:07
 */
@Configuration
public class InterceptorConfig implements WebMvcConfigurer {



    @Autowired
    private OpenApiAuthInterceptor openApiAuthInterceptor;



    /**
     * {@inheritDoc}
     * 添加拦截器，将 authInterceptor 添加到所有路径上。
     *
     * @param registry 拦截器注册器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 拦截器注册
        registry.addInterceptor(openApiAuthInterceptor)
                .addPathPatterns("/deepsight/v1/table/content/sync")
                .order(1);

    }
}
