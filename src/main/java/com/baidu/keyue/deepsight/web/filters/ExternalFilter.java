package com.baidu.keyue.deepsight.web.filters;

import cn.hutool.core.util.StrUtil;
import com.baidu.keyue.deepsight.config.Constants;
import com.baidu.keyue.deepsight.models.base.request.DeepSightWebContext;
import com.baidu.keyue.deepsight.models.datamanage.dto.TenantDTO;
import com.baidu.keyue.deepsight.mysqldb.entity.TenantInfo;
import com.baidu.keyue.deepsight.service.tenant.TenantInfoService;
import com.baidu.keyue.deepsight.web.WebContextHolder;
import com.baidu.kybase.sdk.user.dto.UserAuthInfo;
import jakarta.annotation.Resource;
import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Objects;

/**
 * @className: ExternalFilter
 * @description: 服务间调用设置随路信息，服务只能通过 blb ip 请求
 * 外网需要通过基座转发给洞察，基座只会匹配 /deepsith/v1 开头的url，即 /external 没有对外暴露不会存在外部流量
 * @author: wangzhongcheng
 * @date: 2025/2/21 16:46
 */
@Slf4j
@Component
public class ExternalFilter implements Filter {

    @Resource
    private TenantInfoService tenantInfoService;

    @Value("${app-version}")
    private Integer apiVersion;
    
    private static final String EXTERNAL_PREFIX = "/external";

    @Override
    public void doFilter(ServletRequest servletRequest,
                         ServletResponse servletResponse,
                         FilterChain filterChain) throws IOException, ServletException {
        if (servletRequest instanceof HttpServletRequest request) {
            // 获取请求头中的信息
            String userId = request.getHeader(Constants.USER_ID_HEADER_KEY);
            userId = StringUtils.isEmpty(userId) ? request.getHeader(Constants.REQUEST_USER_FIELD) : userId;
            String tenantId = request.getHeader(Constants.TENANT_ID_HEADER_KEY);
            tenantId = StringUtils.isEmpty(tenantId) ? request.getHeader(Constants.REQUEST_TENANT_ID_FIELD) : tenantId;
            DeepSightWebContext info = WebContextHolder.getDeepSightWebContext();
            if (info == null) {
                info = new DeepSightWebContext();
            }
            if (StringUtils.isNotEmpty(userId)) {
                info.setUserId(Long.valueOf(userId));
            }
            if (StringUtils.isNotEmpty(tenantId)) {
                info.setTenantId(Long.valueOf(tenantId));
            }
            WebContextHolder.setDeepSightWebContext(info);
            // 检查租户，不存在则进行初始化
            checkTenant(tenantId, userId, request.getRequestURI());
        }
        try {
            filterChain.doFilter(servletRequest, servletResponse);
        } finally {
            // 应用方的清理逻辑
            WebContextHolder.clean();
        }

    }

    /**
     * 检查租户
     * 只检查/external开头的接口
     * 不存在则初始化
     * 未更新则更新
     *
     * @param tenantId   租户ID
     * @param userId     用户ID
     * @param requestURI
     */
    public void checkTenant(String tenantId, String userId, String requestURI) {
        if (!requestURI.startsWith(EXTERNAL_PREFIX) || StrUtil.isBlank(tenantId)) {
            return;
        }
        // 设置全局租户信息
        TenantInfo tenantInfo = tenantInfoService.queryTenantInfo(tenantId);
        // 租户信息查不到，则执行初始化租户逻辑
        if (ObjectUtils.isEmpty(tenantInfo) || !Objects.equals(tenantInfo.getVersion(), apiVersion)) {
            UserAuthInfo userAuthInfo = new UserAuthInfo();
            if (StrUtil.isNotBlank(userId)) {
                try {
                    userAuthInfo.setUserId(Long.parseLong(userId));
                } catch (Exception e) {
                    log.error("内部接口调用租户初始化userId解析异常,userId:{}, msg:{}", userId, e.getMessage());
                }
            }
            TenantDTO tenantDTO = new TenantDTO();
            tenantDTO.setTenantId(tenantId);
            tenantDTO.setAuthInfo(userAuthInfo);
            tenantDTO.setType(Constants.TENANT_LOGIN_TYPE);
            tenantDTO.setTenantInfo(tenantInfo);
            try {
                tenantInfoService.initOrUpgradeTenant(tenantDTO);
            } catch (Exception exception) {
                log.error("租户{}初始化或升级失败, exception:", tenantId, exception);
            }
        }
    }
}
