/*
 * Copyright (c) 2018 Baidu.com, Inc. All Rights Reserved
 */
package com.baidu.keyue.deepsight;

import com.baidu.scan.safesdk.ConfigManager;
import com.baidu.scan.safesdk.exceptions.InvalidParamException;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.io.IOException;

import static com.baidu.keyue.deepsight.safesdk.SafeSdkConfig.safeSdkPath;

/**
 * Hello world!
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 1.0.0
 */
@EnableAsync
@EnableCaching
@EnableScheduling
@EnableConfigurationProperties
@SpringBootApplication
public class App {
    public static void main(String[] args) {
        SpringApplication.run(App.class, args);
        // 加载safeSDk配置
        try {
            ConfigManager.loadConfig(safeSdkPath);
        } catch (InvalidParamException | IOException e) {
            e.printStackTrace();
        }
    }
}
