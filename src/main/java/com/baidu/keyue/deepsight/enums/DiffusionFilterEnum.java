package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @ClassName DiffusionFilterEnum
 * @Description 人群扩散过滤规则枚举
 * <AUTHOR>
 * @Date 2025/3/24 5:47 PM
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum DiffusionFilterEnum {
    // 0不过滤 1剔除种子人群
    // NOT_FILTER-不过滤 REMOVE_SEED_GROUP-剔除种子人群
    NOT_FILTER((byte) 0, "不过滤"),

    REMOVE_SEED_GROUP((byte) 1, "剔除种子人群");

    private Byte code;

    private String desc;

    public Boolean getBoolean() {
        return this.getCode() != 0;
    }

    public static DiffusionFilterEnum getByCode(Byte code){
        for (DiffusionFilterEnum value : DiffusionFilterEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
