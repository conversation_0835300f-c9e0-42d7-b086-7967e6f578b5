package com.baidu.keyue.deepsight.enums;

import com.baidu.keyue.deepsight.config.Constants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @className ESFieldTypeEnum
 * @description es字段类型
 * @date 2025/3/6 15:25
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ESToJavaTypeMapping {

    INT("int", Constants.CONSTANT_NUMBER),
    FLOAT("float", Constants.CONSTANT_NUMBER),
    BOOLEAN("boolean", Constants.CONSTANT_BOOLEAN),
    STING("string", Constants.CONSTANT_STRING),
    STINGS("strings", Constants.CONSTANT_LIST_STRING),
    DATE("date", Constants.CONSTANT_NUMBER),
    ;

    private String dataType;

    private String type;

    private static final Map<String, ESToJavaTypeMapping> TYPE_MAP = new HashMap<>() {{
        for (ESToJavaTypeMapping type : ESToJavaTypeMapping.values()) {
            put(type.getDataType(), type);
        }
    }};

    public static ESToJavaTypeMapping getJavaType(String dataType) {
        return TYPE_MAP.get(dataType);
    }


}
