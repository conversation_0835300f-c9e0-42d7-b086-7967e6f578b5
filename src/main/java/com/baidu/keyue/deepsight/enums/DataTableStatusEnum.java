package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className DataTableStatusEunm
 * @description 数据表状态枚举
 * @date 2024/12/26 19:58
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum DataTableStatusEnum {

    /**
     * 未提交
     */
    NOT_SUBMIT(0, "未提交"),

    /**
     * 创建中
     */
    CREATING(1, "创建中"),

    /**
     * 已创建
     */
    CREATED(2, "已创建"),

    /**
     * 创建失败
     */
    FAILURE(3, "创建失败"),
    ;

    private Integer status;

    private String name;


}
