package com.baidu.keyue.deepsight.enums;

/**
 * @ClassName TableRecordOpEnum
 * @Description TODO
 * <AUTHOR> (<EMAIL>)
 * @Date 2025/1/23 16:25
 */

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TableRecordOpEnum {
    // 目前仅支持删除
    DELETE("DELETE", "删除")

    ;
    /**
     * 操作code
     */
    private String code;

    /**
     * 描述
     */
    private String desc;
}
