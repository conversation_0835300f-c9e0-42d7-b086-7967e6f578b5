package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @ClassName ImportStatusEnum
 * @Description 文件导入数据状态枚举
 * <AUTHOR>
 * @Date 2025/2/14 2:29 PM
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ImportStatusEnum {
 
    UPLOAD(0, "已上传"),
    IMPORTING(1, "导入中"),
    IMPORT_SUCCESS(2, "导入完成"),
    IMPORT_FAIL(3, "导入失败");

    /**
     * 状态值
     */
    private Integer status;
    
    /**
     * 状态描述
     */
    private String desc;
}
