package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @ClassName FeatureSelectEnum
 * @Description 特征筛选枚举
 * <AUTHOR>
 * @Date 2025/3/24 5:50 PM
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum FeatureSelectEnum {
    // 特征筛选:0系统推荐 1覆盖率自定义
    // 特征筛选:SYSTEM_RECOMMEND-系统推荐 COVER_CUSTOMIZE-覆盖率自定义
    SYSTEM_RECOMMEND((byte) 0, "系统推荐"),

    COVER_CUSTOMIZE((byte) 1, "覆盖率自定义");

    private Byte code;

    private String desc;

    public Boolean getBoolean() {
        return this.getCode() != 0;
    }
    
    public static FeatureSelectEnum getByCode(Byte code){
        for (FeatureSelectEnum value : FeatureSelectEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
