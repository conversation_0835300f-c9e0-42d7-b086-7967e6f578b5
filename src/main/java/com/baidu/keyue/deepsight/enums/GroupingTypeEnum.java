package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @ClassName GroupingTypeEnum
 * @Description 客群-分群方式枚举
 * <AUTHOR>
 * @Date 2025/3/27 2:18 PM
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum GroupingTypeEnum {
    // 分群方式：0:规则圈选 1:文件导入 2:模型预测
    // RULE_CIRCLE-规则圈选 FILE_IMPORT-文件导入 MODEL_PREDICTION-模型预测
    RULE_CIRCLE((byte) 0, "规则圈选"),
    FILE_IMPORT((byte) 1, "文件导入"),
    MODEL_PREDICTION((byte) 2, "模型预测");

    private Byte code;

    private String desc;

    public Boolean getBoolean() {
        return this.getCode() != 0;
    }

    public static GroupingTypeEnum getByCode(Byte code){
        for (GroupingTypeEnum value : GroupingTypeEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
