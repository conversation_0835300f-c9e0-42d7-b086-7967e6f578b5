package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: LabelValueUpdateModEnum
 * @description: 标签更新取值逻辑模式
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum UpdateModEnum {

    REPLACE((byte) 0, "每次更新重新计算"),
    MERGE((byte) 1, "每次更新合并历史数据");

    private Byte code;
    private String desc;

    public static String getDescByCode(Byte code) {
        for (UpdateModEnum updateMod : UpdateModEnum.values()) {
            if (updateMod.getCode().equals(code)) {
                return updateMod.getDesc();
            }
        }
        return null;
    }

    public static UpdateModEnum getByCode(Byte code) {
        for (UpdateModEnum updateMod : UpdateModEnum.values()) {
            if (updateMod.getCode().equals(code)) {
                return updateMod;
            }
        }
        return null;
    }
}
