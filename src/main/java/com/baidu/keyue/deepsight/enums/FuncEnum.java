package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: FuncEnum
 * @description:
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/21 16:19
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum FuncEnum {

    CONTAIN("contain", "包含"),
    NOT_CONTAIN("notContain", "不包含"),
    LIKE("like", "模糊匹配"),
    IS_NULL("isNull", "为空"),
    IS_NOT_NULL("isNotNull", "不为空"),
    GREATER_EQUALS("ge", "大于等于"),
    GREATER_THAN("gt", "大于"),
    LESS_EQUALS("le", "小于等于"),
    LESS_THAN("lt", "小于"),
    EQUALS("eq", "等于"),
    NOT_EQUALS("ne", "不等于"),
    LEAST("least", "距离当前时间"),
    BETWEEN("between", "区间");

    @JsonValue
    private String code;
    private String desc;

}
