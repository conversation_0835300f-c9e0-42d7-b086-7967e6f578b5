package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 任务类型枚举
 *
 * @className: TaskTypeEnum
 * @description: 任务类型
 * @author: lvtao03
 * @date: 2025/01/25 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TaskTypeEnum {

    /**
     * 贴源层数据任务
     */
    ODS_TASK((byte) 0, "贴源层数据任务"),
    /**
     * 标签生产-衍生指标数据任务
     */
    LABEL_DWS_TASK((byte) 1, "标签生产-衍生指标数据任务"),

    CUSTOMER_DWS_TASK((byte) 2, "客群分析计算任务"),

    MEMORY_EXTRACT((byte) 3, "记忆提取计算任务"),

    DATA_PREDICT((byte) 4, "数据增强任务"),

    ID_MAPPING_RESET((byte) 5, "ID-Mapping重置任务"),

    CUSTOMER_DIFFUSION((byte) 6, "客群扩散任务"),
    ;

    private Byte code;
    private String desc;

    public static TaskTypeEnum getDescByCode(Byte code) {
        for (TaskTypeEnum taskExecStatus : TaskTypeEnum.values()) {
            if (taskExecStatus.getCode().equals(code)) {
                return taskExecStatus;
            }
        }
        return null;
    }

}
