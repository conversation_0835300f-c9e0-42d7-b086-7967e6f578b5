package com.baidu.keyue.deepsight.enums;

import com.google.common.collect.ImmutableMap;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className TableFieldTagEnum
 * @description 字段标记
 * @date 2024/12/27 11:03
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TableFieldTagEnum {
    NULL(0, "无", "字段无标识"),

    PRIMARY(1, "主键", "用于唯一标识表中的每一行记录"),

    EVENT(2,"事件", "（维度表无该选项）：在事实表中标识行为动作的字段，用于判断不同表中行为的识别"),

    MEASURE(3, "度量", "（维度表无该选项，且为数字类型字段）：" +
            "度量字段具有可加性，即可以在不同的维度上进行聚合计算，如求和、平均值等"),

    SENSITIVE(4, "敏感", "如标记为敏感字段，数据记录页面该字段值要脱敏打星显示，该字段值全部掩码，显示三个***"),

    PARTITION(5, "分区", "根据该字段的值将数据存储在不同的分区中。"),

    ID(6, "id", "标识id类的字段"),
    SEARCH(7, "搜索字段", "es搜索字段"),

    ;

    private Integer code;

    private String name;

    private String desc;

    private static ImmutableMap.Builder<Integer, TableFieldTagEnum> builder = ImmutableMap.builder();
    private static ImmutableMap<Integer, TableFieldTagEnum> map = null;

    static {
        for (TableFieldTagEnum tagEnum : values()) {
            builder.put(tagEnum.getCode(), tagEnum);
        }
        map = builder.build();
    }

    public static TableFieldTagEnum getByCode(int code) {
        return map.get(code);
    }







}
