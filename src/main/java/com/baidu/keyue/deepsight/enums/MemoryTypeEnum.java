package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 长期记忆类型枚举
 *
 * @className: MemoryTypeEnum
 * @description: 长期记忆类型枚举
 * @author: lvtao03
 * @date: 2025/01/25 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum MemoryTypeEnum {

    ATTRIBUTE("attribute", "基础属性"),
    INTEREST("interest", "兴趣爱好"),
    EVENT("event", "重要事件"),

    ;

    private String code;
    private String desc;

    public static MemoryTypeEnum getDescByCode(String code) {
        for (MemoryTypeEnum taskExecStatus : MemoryTypeEnum.values()) {
            if (taskExecStatus.getCode().equals(code)) {
                return taskExecStatus;
            }
        }
        return null;
    }

}
