package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * @ClassName AiobLineStatusEnum
 * @Description 外呼号线状态
 * <AUTHOR>
 * @Date 2025/7/2 4:11 PM
 */
@Getter
public enum AiobLineStatusEnum {
    // 号线状态：ALL(全部状态)/ENABLED(启用中)/BANNED(已禁止)/ARREARS(已欠费)/DISABLED(已停用)
    ALL("ALL","全部状态"),
    ENABLED("ENABLED","启用中"),
    BANNED("BANNED","已禁止"),
    ARREARS("ARREARS","已欠费"),
    DISABLED("DISABLED","已停用"),
    ;
    private static final Logger LOGGER = LoggerFactory.getLogger(AiobLineStatusEnum.class);

    @JsonValue
    private final String value;
    private final String desc;

    AiobLineStatusEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @JsonCreator
    public static AiobLineStatusEnum createByValue(String value) {
        for (AiobLineStatusEnum configTypeEnum : values()) {
            if (Objects.equals(value, configTypeEnum.getValue())) {
                return configTypeEnum;
            }
        }
        throw new IllegalArgumentException("line status Invalid name: " + value);
    }

    public static AiobLineStatusEnum createByObj(Object lineStatus) {
        if (lineStatus == null) {
            return null;
        }
        try {
            return createByValue(lineStatus.toString());
        } catch (Exception e) {
            LOGGER.error("error line status enum:{}", lineStatus);
        }
        return null;
    }
}
