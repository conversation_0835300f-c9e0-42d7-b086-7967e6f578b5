package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName AiobSortFieldEnum
 * @Description 呼通率排行榜排序字段
 * <AUTHOR>
 * @Date 2025/7/2 4:11 PM
 */
@Getter
public enum AiobSortFieldEnum {
    // 排序字段：CALL_COUNT(拨打次数)/CONNECTED_COUNT(呼通次数)/CONNECTED_RATE(接通率)
    // /ALARM_DAYS(告警天数)/UNCONNECTED_COUNT(未接通次数)/LINE_REASON_RATE(号线原因未接通占比)/PLATFORM_REASON_RATE(平台原因未接通占比)
    CALL_COUNT("CALL_COUNT","拨打次数"),
    CONNECTED_COUNT("CONNECTED_COUNT","呼通次数"),
    CONNECTED_RATE("CONNECTED_RATE","接通率"),
    ALARM_DAYS("ALARM_DAYS","告警天数"),
    UNCONNECTED_COUNT("UNCONNECTED_COUNT","未接通次数"),
    LINE_REASON_RATE("LINE_REASON_RATE","号线原因未接通占比"),
    PLATFORM_REASON_RATE("PLATFORM_REASON_RATE","平台原因未接通占比"),
    ;
    @JsonValue
    private final String value;
    private final String desc;

    AiobSortFieldEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @JsonCreator
    public static AiobSortFieldEnum createByValue(String value) {
        for (AiobSortFieldEnum configTypeEnum : values()) {
            if (Objects.equals(value, configTypeEnum.getValue())) {
                return configTypeEnum;
            }
        }
        throw new IllegalArgumentException("sort filed Invalid name: " + value);
    }
}
