package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: YesEnum
 * @description: 是/否状态
 * @author: lvtao03
 * @date: 2025/02/10 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum YesEnum {

    NO((byte) 0, "否"),
    YES((byte) 1, "是");

    private Byte code;

    private String desc;

    public Boolean getBoolean() {
        return this.getCode() != 0;
    }

    public static YesEnum getByBoolean(Boolean code) {
        if (code) {
            return YesEnum.YES;
        }
        return YesEnum.NO;
    }
}
