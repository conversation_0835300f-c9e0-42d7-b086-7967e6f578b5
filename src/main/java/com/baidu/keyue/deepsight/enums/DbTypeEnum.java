package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className DbTypeEnum
 * @description
 * @date 2025/3/7 11:16
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum DbTypeEnum {

    ES_TYPE("es", "esConfServiceImpl"),
    DORIS_TYPE("doris", "dorisConfServiceImpl");

    private String dbType;
    private String className;

    public static DbTypeEnum getDbEnum(String dbType) {
        for (DbTypeEnum dbTypeEnum : DbTypeEnum.values()) {
            if (dbTypeEnum.getDbType().equals(dbType)) {
                return dbTypeEnum;
            }
        }
        return DORIS_TYPE;
    }
}
