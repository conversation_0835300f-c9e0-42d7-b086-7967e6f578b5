package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: PresetEnum
 * @description: 预置枚举
 * @author: wang<PERSON><PERSON>cheng
 * @date: 2025/3/6 16:16
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PresetEnum {

    NOT_PRESET((byte) 0, "非预置"),
    PRESET((byte) 1, "预置");

    private Byte code;

    private String desc;

    public Boolean getBoolean() {
        return this.getCode() != 0;
    }

}
