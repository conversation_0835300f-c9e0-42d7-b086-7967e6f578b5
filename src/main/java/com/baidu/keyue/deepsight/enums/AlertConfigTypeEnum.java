package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName AlertConfigTypeEnum
 * @Description 呼通率告警配置类型枚举
 * <AUTHOR>
 * @Date 2025/7/1 10:58 AM
 */
@Getter
public enum AlertConfigTypeEnum {
    // 配置对象类型：LINE(号线)/TASK(任务)/ROBOT(机器人)
    LINE("LINE", "号线", "callerNum"),
    TASK("TASK", "任务", "taskId"),
    ROBOT("ROBOT", "机器人", "robotId"),
    ;
    @JsonValue
    private final String value;
    private final String desc;
    /**
     * 对应session表类型的ID字段名：号线ID字段名、任务ID字段名、机器人ID字段名
     */
    private final String idFieldName;

    AlertConfigTypeEnum(String value, String desc, String idFieldName) {
        this.value = value;
        this.desc = desc;
        this.idFieldName = idFieldName;
    }

    @JsonCreator
    public static AlertConfigTypeEnum createByValue(String value) {
        for (AlertConfigTypeEnum configTypeEnum : values()) {
            if (Objects.equals(value, configTypeEnum.getValue())) {
                return configTypeEnum;
            }
        }
        throw new IllegalArgumentException("Invalid name: " + value);
    }
    
}
