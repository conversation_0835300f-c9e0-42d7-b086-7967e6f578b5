package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: ExecModEnum
 * @description: 生产方式
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ExecModEnum {

    RULE((byte) 0, "业务规则"),

    SQL((byte) 1, "SQL"),

    MODEL((byte) 2, "业务模式"),

    ;

    private Byte code;
    private String desc;

    public static String getDescByCode(Byte code) {
        for (ExecModEnum execMod : ExecModEnum.values()) {
            if (execMod.getCode().equals(code)) {
                return execMod.getDesc();
            }
        }
        return null;
    }

    public static ExecModEnum getByCode(Byte code) {
        for (ExecModEnum execMod : ExecModEnum.values()) {
            if (execMod.getCode().equals(code)) {
                return execMod;
            }
        }
        return null;
    }
}
