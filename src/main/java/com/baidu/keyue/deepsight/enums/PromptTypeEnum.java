package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: PromptTypeEnum
 * @description: prompt 类型
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PromptTypeEnum {

    DEFAULT((byte) 0, "系统预置"),
    CUSTOM((byte) 1, "自定义");

    private Byte code;
    private String desc;

    public static String getDescByCode(Byte code) {
        for (PromptTypeEnum typeEnum : PromptTypeEnum.values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum.getDesc();
            }
        }
        return null;
    }

    public static PromptTypeEnum getByCode(Byte code) {
        for (PromptTypeEnum typeMode : PromptTypeEnum.values()) {
            if (typeMode.getCode().equals(code)) {
                return typeMode;
            }
        }
        return null;
    }
}
