package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @className: LogicEnum
 * @description: 逻辑枚举
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/21 16:06
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum LogicEnum {

    AND("and", "且"),
    OR("or", "或");

    @JsonValue
    private String code;
    private String name;

    private static final Map<String, LogicEnum> CodeMap = Arrays.stream(LogicEnum.values())
            .collect(Collectors.toMap(LogicEnum::getCode, e -> e));

    public static LogicEnum valueOfCode(String code) {
        return CodeMap.get(code);
    }

}
