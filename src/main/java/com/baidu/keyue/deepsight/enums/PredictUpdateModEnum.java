package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: PredictUpdateModEnum
 * @description: 数据预测更新方式
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PredictUpdateModEnum {

    REPLACE((byte) 0, "预测到结果后定时更新"),
    HOLD((byte) 1, "预测到结果停止预测更新");

    private Byte code;
    private String desc;

    public static String getDescByCode(Byte code) {
        for (PredictUpdateModEnum updateMod : PredictUpdateModEnum.values()) {
            if (updateMod.getCode().equals(code)) {
                return updateMod.getDesc();
            }
        }
        return null;
    }

    public static PredictUpdateModEnum getByCode(Byte code) {
        for (PredictUpdateModEnum updateMod : PredictUpdateModEnum.values()) {
            if (updateMod.getCode().equals(code)) {
                return updateMod;
            }
        }
        return null;
    }
}
