package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className SopUserConfigMetric
 * @description SOP分析设置指标
 * @date 2025/5/20 11:03
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SopUserConfigMainMetricEnum {
    ARRIVAL_USER_NUM(0, "到达人数"),

    ARRIVAL_NUM(1, "到达人次"),

    ;
    private Integer code;

    private String desc;

    public static SopUserConfigMainMetricEnum codeOf(Integer code) {
        if (code == null) {
            return null;
        }
        for (SopUserConfigMainMetricEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
