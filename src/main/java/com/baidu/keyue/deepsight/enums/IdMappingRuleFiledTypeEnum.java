package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @className: IdMappingRuleFiledTypeEnum
 * @description: id映射规则字段类型枚举
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2025/3/11 17:18
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum IdMappingRuleFiledTypeEnum {

    SINGLE_VALUE((byte) 0, "单值"),

    MULTI_VALUE((byte) 1, "多值");

    private Byte code;

    private String desc;

    public Boolean getBoolean() {
        return this.getCode() != 0;
    }

    private static final Map<Byte, IdMappingRuleFiledTypeEnum> CATCH_ENUM = new HashMap<>() {{
       this.put(SINGLE_VALUE.getCode(), SINGLE_VALUE);
       this.put(MULTI_VALUE.getCode(), MULTI_VALUE);
    }};

    public static IdMappingRuleFiledTypeEnum valueOfCode(Byte code) {
        return CATCH_ENUM.getOrDefault(code, IdMappingRuleFiledTypeEnum.SINGLE_VALUE);
    }

    public Integer getInteger() {
        return this.getCode().intValue();
    }

}
