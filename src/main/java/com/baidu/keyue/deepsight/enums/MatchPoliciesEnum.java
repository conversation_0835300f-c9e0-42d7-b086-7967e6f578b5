package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @ClassName MatchPoliciesEnum
 * @Description 导入客群-匹配策略枚举
 * <AUTHOR>
 * @Date 2025/3/25 2:36 PM
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum MatchPoliciesEnum {

    // 0-保留全部用户 1-仅保留匹配用户
    // ALL-保留全部用户 ONLY_MATCH-仅保留匹配用户
    ALL(0, "保留全部用户"),

    ONLY_MATCH(1, "仅保留匹配用户");

    private Integer code;

    private String desc;

    public Boolean getBoolean() {
        return this.getCode() != 0;
    }
}
