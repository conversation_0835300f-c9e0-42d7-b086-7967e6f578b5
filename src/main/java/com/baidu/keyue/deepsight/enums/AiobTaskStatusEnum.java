package com.baidu.keyue.deepsight.enums;

import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * @ClassName AiobTaskStatusEnum
 * @Description 外呼任务状态
 * <AUTHOR>
 * @Date 2025/7/2 4:11 PM
 */
@Getter
public enum AiobTaskStatusEnum {
    // 任务状态：ALL(全部状态)/RUNNING(执行中)/PAUSED(已暂停)/COMPLETED(已完成)
    // 1-待启动/2-执行中/3-已暂停/4-已完成/5-已终止
    WAITING_RUN(1, "待启动"),
    RUNNING(2, "执行中"),
    PAUSED(3, "已暂停"),
    COMPLETED(4, "已完成"),
    // 手动终止任务 暂时没用
    DROP(5, "已终止");;
    private static final Logger LOGGER = LoggerFactory.getLogger(AiobTaskStatusEnum.class);
    private final Integer code;
    private final String desc;

    AiobTaskStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AiobTaskStatusEnum createByValue(Integer value) {
        for (AiobTaskStatusEnum configTypeEnum : values()) {
            if (Objects.equals(value, configTypeEnum.getCode())) {
                return configTypeEnum;
            }
        }
        throw new IllegalArgumentException("task status Invalid name: " + value);
    }

    public static AiobTaskStatusEnum createByObj(Object taskStatus) {
        if (taskStatus == null) {
            return null;
        }
        try {
            return createByValue((int) taskStatus);
        } catch (Exception e) {
            LOGGER.error("error task status enum:{}", taskStatus);
        }
        return null;
    }
}
