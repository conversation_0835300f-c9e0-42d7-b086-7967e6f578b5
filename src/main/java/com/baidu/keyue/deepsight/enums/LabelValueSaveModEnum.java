package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: LabelValueSaveModEnum
 * @description: 标签值保存类型
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum LabelValueSaveModEnum {

    SINGLE((byte) 0, "单值"),
    MULTI((byte) 1, "多值");

    private Byte code;
    private String desc;

    public static String getDescByCode(Byte code) {
        for (LabelValueSaveModEnum labelValueSaveModEnum : LabelValueSaveModEnum.values()) {
            if (labelValueSaveModEnum.getCode().equals(code)) {
                return labelValueSaveModEnum.getDesc();
            }
        }
        return null;
    }

    public static LabelValueSaveModEnum getByCode(Byte code) {
        for (LabelValueSaveModEnum labelValueSaveModEnum : LabelValueSaveModEnum.values()) {
            if (labelValueSaveModEnum.getCode().equals(code)) {
                return labelValueSaveModEnum;
            }
        }
        return null;
    }

}
