package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: TableTypeEnum
 * @description: 数据表分层规范
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TableTypeEnum {

    /**
     * 贴源层数据表
     */
    ODS(0, "ods_", "贴源层数据表"),

    /**
     * 基础指标表
     */
    DWD(1, "dwd_", "基础指标表"),

    /**
     * 衍生指标表
     */
    DWS(2, "dws_", "衍生指标表"),

    /**
     * 维度表
     */
    DIM(3, "dim_", "维度表"),

    /**
     * 主题表
     */
    DM(4, "dm_", "主题表"),

    /**
     * 应用层表
     * */
    ADS(5, "ads_", "应用报表");

    private Integer code;
    private String tablePrefix;
    private String desc;

}
