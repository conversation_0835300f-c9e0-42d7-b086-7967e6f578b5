package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * @className: IdMappingRuleMergePolicyEnum
 * @description: id映射规则合并策略
 * @author: wangz<PERSON><PERSON>
 * @date: 2025/3/11 17:26
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum IdMappingRuleMergePolicyEnum {

    SAVE_NEWEST((byte) 0, "保存最新数据"),

    SAVE_OLDEST((byte) 1, "保存最老数据");

    private Byte code;

    private String desc;

    public Boolean getBoolean() {
        return this.getCode() != 0;
    }

    private static final Map<Byte, IdMappingRuleMergePolicyEnum> CATCH_ENUM = new HashMap<>() {{
        this.put(SAVE_NEWEST.getCode(), SAVE_NEWEST);
        this.put(SAVE_OLDEST.getCode(), SAVE_OLDEST);
    }};

    public static IdMappingRuleMergePolicyEnum valueOfCode(Byte code) {
        return CATCH_ENUM.getOrDefault(code, IdMappingRuleMergePolicyEnum.SAVE_NEWEST);
    }

    public Integer getInteger() {
        return this.getCode().intValue();
    }
}
