package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: TriggerFrequencyEnum
 * @description: 执行频率
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TriggerFrequencyEnum {
    // DAY-每日,WEEK-每周,MONTH-每月
    DAY((byte) 0, "每日"),
    WEEK((byte) 1, "每周"),
    MONTH((byte) 2, "每月"),
    ;

    private Byte code;
    private String desc;

    public static String getDescByCode(Byte code) {
        for (TriggerFrequencyEnum triggerFrequency : TriggerFrequencyEnum.values()) {
            if (triggerFrequency.getCode().equals(code)) {
                return triggerFrequency.getDesc();
            }
        }
        return null;
    }

    public static TriggerFrequencyEnum getByCode(Byte code) {
        for (TriggerFrequencyEnum triggerFrequency : TriggerFrequencyEnum.values()) {
            if (triggerFrequency.getCode().equals(code)) {
                return triggerFrequency;
            }
        }
        return null;
    }
}
