package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className TableFieldType
 * @description 表字段类型
 * @date 2024/12/27 10:43
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TableFieldTypeEnum {
    STRING("string", "单值字符类型"),
    STRINGS("strings", "多值字符类型"),
    NUMBER("number", "数字类型"),
    TIME("time", "时间类型"),
    BOOLEAN("boolean", "时间类型"),;
    private String value;
    private String desc;

    private static final Map<String, List<TableFieldTagEnum>> fieldTagMap = new HashMap<>();

    private static final Map<String, List<TableFieldValueEnum>> fieldValueMap = new HashMap<>();


    static {
        // 初始化字段标记和字段类型的映射关系
        fieldTagMap.put(STRING.value, new ArrayList<>(List.of(TableFieldTagEnum.PRIMARY,
                TableFieldTagEnum.NULL,
                TableFieldTagEnum.EVENT,
                TableFieldTagEnum.SENSITIVE,
                TableFieldTagEnum.ID,
                TableFieldTagEnum.SEARCH)));
        fieldTagMap.put(STRINGS.value, new ArrayList<>(List.of(TableFieldTagEnum.NULL
                , TableFieldTagEnum.SENSITIVE, TableFieldTagEnum.PARTITION)));
        fieldTagMap.put(NUMBER.value, new ArrayList<>(List.of(TableFieldTagEnum.NULL,
                TableFieldTagEnum.PRIMARY, TableFieldTagEnum.MEASURE, TableFieldTagEnum.SENSITIVE, TableFieldTagEnum.ID)));
        fieldTagMap.put(TIME.value, new ArrayList<>(List.of(TableFieldTagEnum.NULL,
                TableFieldTagEnum.SENSITIVE, TableFieldTagEnum.PARTITION)));
        fieldTagMap.put(BOOLEAN.value, new ArrayList<>(List.of(TableFieldTagEnum.NULL)));



        // 初始化字段标记和字段类型的映射关系
        fieldValueMap.put(STRING.value, new ArrayList<>(List.of(TableFieldValueEnum.TEXT,
                TableFieldValueEnum.ENUM)));
        fieldValueMap.put(STRINGS.value, new ArrayList<>(List.of(TableFieldValueEnum.ENUM)));
        fieldValueMap.put(NUMBER.value, new ArrayList<>(List.of(TableFieldValueEnum.NUMBER)));
        fieldValueMap.put(BOOLEAN.value, new ArrayList<>(List.of(TableFieldValueEnum.BOOLEAN)));

    }

    private static final Map<String, TableFieldTypeEnum> VALUE_ENUM_MAP = new HashMap<>() {{
        for (TableFieldTypeEnum type : TableFieldTypeEnum.values()) {
            put(type.value, type);
        }
    }};

    public static List<TableFieldTagEnum> getFieldTag(String value) {
        return fieldTagMap.get(value);
    }

    public static List<TableFieldValueEnum> getFieldValueType(String value) {
        return fieldValueMap.get(value);
    }

    public static TableFieldTypeEnum fromValue(String value) {
        return VALUE_ENUM_MAP.get(value);
    }

}
