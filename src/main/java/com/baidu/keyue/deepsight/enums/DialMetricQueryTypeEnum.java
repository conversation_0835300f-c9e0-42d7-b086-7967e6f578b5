package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum DialMetricQueryTypeEnum {

    LINE("LINE"),
    TASK("TASK"),
    ROBOT("ROBOT");

    private String queryType;

    public static DialMetricQueryTypeEnum queryType(String type) {
        for (DialMetricQueryTypeEnum queryType : DialMetricQueryTypeEnum.values()) {
            if (queryType.getQueryType().equals(type)) {
                return queryType;
            }
        }
        return TASK;
    }

    public static String getQueryField(DialMetricQueryTypeEnum queryType) {
        return switch (queryType) {
            case LINE -> "callerNum";
            case TASK -> "taskId";
            case ROBOT -> "robotId";
        };
    }
}
