package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName DateLineTypeEnum
 * @Description 时间线类型枚举
 * <AUTHOR>
 * @Date 2025/7/4 8:40 PM
 */
@Getter
public enum DateLineTypeEnum {
    HOUR("hour","yyyy-MM-dd HH:00:00"),
    DAY("day","yyyy-MM-dd"),
    ;

    @JsonValue
    private final String value;
    private final String dateFormat;

    DateLineTypeEnum(String value, String dateFormat) {
        this.value = value;
        this.dateFormat = dateFormat;
    }

    @JsonCreator
    public static DateLineTypeEnum createByValue(String value) {
        for (DateLineTypeEnum typeEnum : values()) {
            if (Objects.equals(value, typeEnum.getValue())) {
                return typeEnum;
            }
        }
        throw new IllegalArgumentException("data line Invalid name: " + value);
    }
}
