package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @ClassName ImportMappingTypeEnum
 * @Description 数据导入字段映射枚举
 * <AUTHOR>
 * @Date 2025/2/17 4:52 PM
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ImportMappingTypeEnum {

    EQUAL_NAME("EQUAL_NAME", "同名映射"),
    AI_SEMANTEME("AI_SEMANTEME", "AI语义映射");
    private String value;
    private String desc;
}
