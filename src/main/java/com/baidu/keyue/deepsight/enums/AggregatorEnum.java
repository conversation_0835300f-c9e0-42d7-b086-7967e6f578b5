package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: AggregatorEnum
 * @description:
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/21 18:47
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AggregatorEnum {

    COUNT("count", "计数聚合"),
    DISTANCE_COUNT("distanceCount", "去重计数聚合"),
    SUM("sum", "求和聚合"),
    AVG("avg", "均值聚合"),
    MAX("max", "最大值聚合"),
    MIN("min", "最小值聚合");

    @JsonValue
    private String code;
    private String desc;

}
