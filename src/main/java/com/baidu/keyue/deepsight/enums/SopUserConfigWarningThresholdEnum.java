package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className SopUserConfigWarningThresholdEnum
 * @description SOP分析设置告警阈值
 * @date 2025/5/20 11:03
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SopUserConfigWarningThresholdEnum {
    PERCENT_5(0),
    PERCENT_10(1),
    PERCENT_20(2),
    PERCENT_30(3),
    PERCENT_40(4),
    PERCENT_50(5)
    ;
    private Integer code;

    public static SopUserConfigWarningThresholdEnum codeOf(Integer code) {
        if (code == null) {
            return null;
        }
        for (SopUserConfigWarningThresholdEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
