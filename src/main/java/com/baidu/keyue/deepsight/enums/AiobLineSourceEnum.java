package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * @ClassName AiobTaskStatusEnum
 * @Description 外呼号线来源
 * <AUTHOR>
 * @Date 2025/7/2 4:11 PM
 */
@Getter
public enum AiobLineSourceEnum {
    // 来源：ALL(全部来源)/PLATFORM(来自平台)/CUSTOMER(客户自有)
    ALL("ALL", "全部来源", -100),
    PLATFORM("PLATFORM", "来自平台", 0),
    CUSTOMER("CUSTOMER", "客户自有", 1),
    ;
    private static final Logger LOGGER = LoggerFactory.getLogger(AiobLineSourceEnum.class);

    @JsonValue
    private final String value;
    private final Integer code;
    private final String desc;

    AiobLineSourceEnum(String value, String desc, Integer code) {
        this.value = value;
        this.desc = desc;
        this.code = code;
    }

    @JsonCreator
    public static AiobLineSourceEnum createByValue(String value) {
        for (AiobLineSourceEnum configTypeEnum : values()) {
            if (Objects.equals(value, configTypeEnum.getValue())) {
                return configTypeEnum;
            }
        }
        throw new IllegalArgumentException("line source Invalid name: " + value);
    }

    public static AiobLineSourceEnum createByObj(Object fromSource) {
        if (fromSource == null) {
            return null;
        }
        try {
            int code = Integer.parseInt(fromSource.toString());
            for (AiobLineSourceEnum sourceEnum : values()) {
                if (Objects.equals(sourceEnum.getCode(), code)) {
                    return sourceEnum;
                }
            }
        } catch (Exception e) {
            LOGGER.error("error line source enum:{}", fromSource);
        }
        return null;
    }
}
