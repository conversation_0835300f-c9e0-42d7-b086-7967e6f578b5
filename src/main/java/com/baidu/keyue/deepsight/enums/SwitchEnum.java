package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: SwitchEnum
 * @description: 启用状态
 * @author: lvtao03
 * @date: 2025/02/10 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SwitchEnum {

    ON((byte) 0, "启用"),

    OFF((byte) 1, "关闭");

    private Byte code;

    private String desc;

    public Boolean getBoolean() {
        return this.getCode() != 0;
    }

    public static SwitchEnum getByBoolean(Boolean code) {
        if (code) {
            return SwitchEnum.OFF;
        }
        return SwitchEnum.ON;
    }
}
