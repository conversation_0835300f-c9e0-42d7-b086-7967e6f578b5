package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: PredictTypeEnum
 * @description: 数据增强预测内容类型
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum PredictTypeEnum {
    GENDER(0, "性别", "bd_gender"),
    AGE_GROUP(1, "年龄段", "bd_age_group"),
    EDUCATION_LEVEL(2, "教育水平", "bd_education_level"),
    OCCUPATION(3, "职业类别", "bd_occupation"),
    INDUSTRY(4, "所在行业", "bd_industry"),
    LIFE_STAGE(5, "人生阶段", "bd_life_stage"),
    MARITAL_STATUS(6, "婚姻状况", "bd_marriage_status"),
    CONSUMPTION_LEVEL(7, "消费水平", "bd_consume_level"),
    SPENDING_WILLINGNESS(8, "消费意愿", "bd_consume_intent"),
    LOCATION(9, "地理位置", "bd_geographic_location"),
    INTERESTS(10, "兴趣爱好", "bd_interests"),

    ;

    private Integer code;
    private String desc;
    private String baiduField;

    public static PredictTypeEnum getByCode(Integer code) {
        for (PredictTypeEnum type : PredictTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static PredictTypeEnum getByDesc(String desc) {
        for (PredictTypeEnum type : PredictTypeEnum.values()) {
            if (type.getDesc().equals(desc)) {
                return type;
            }
        }
        if ("年龄".equals(desc)) {
            return AGE_GROUP;
        }
        if ("兴趣关注".equals(desc)) {
            return INTERESTS;
        }
        return null;
    }
}
