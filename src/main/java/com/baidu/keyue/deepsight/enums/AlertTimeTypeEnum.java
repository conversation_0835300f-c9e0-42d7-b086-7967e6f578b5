package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName AlertConfigTypeEnum
 * @Description 呼通率告警时间枚举
 * <AUTHOR>
 * @Date 2025/7/1 10:58 AM
 */
@Getter
public enum AlertTimeTypeEnum {
    // 告警时间类型：24H(近24小时)/7D(近7天)/30D(近30天)
    HOUR_24("24H","近24小时"),
    DAY_7("7D","近7天"),
    DAY_30("30D","近30天"),
    ;
    @JsonValue
    private final String value;
    private final String desc;

    AlertTimeTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @JsonCreator
    public static AlertTimeTypeEnum createByValue(String value){
        for (AlertTimeTypeEnum configTypeEnum : values()) {
            if (Objects.equals(value, configTypeEnum.getValue())) {
                return configTypeEnum;
            }
        }
        throw new IllegalArgumentException("Invalid name: " + value);
    }
    
}
