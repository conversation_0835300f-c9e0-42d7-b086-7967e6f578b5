package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className SopUserConfigMetric
 * @description SOP分析设置指标
 * @date 2025/5/20 11:03
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SopUserConfigAssistMetricEnum {
    ARRIVAL_USER_NUM_RATIO(0, "到达人数比例"),

    ARRIVAL_NUM_RATIO(1, "到达人次比例"),

    ;
    private Integer code;

    private String desc;

    public static SopUserConfigAssistMetricEnum codeOf(Integer code) {
        if (code == null) {
            return null;
        }
        for (SopUserConfigAssistMetricEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }
}
