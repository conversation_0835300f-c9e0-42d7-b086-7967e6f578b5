package com.baidu.keyue.deepsight.enums;

import com.baidu.keyue.deepsight.config.Constants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @className TableDataTypeMapping
 * @description 表数据类型和java类型映射
 * @date 2025/1/9 19:52
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum MysqlToJavaTypeMapping {


    TINYINT("tinyint", Constants.CONSTANT_NUMBER),
    SMALLINT("smallint", Constants.CONSTANT_NUMBER),
    MEDIUMINT("mediumint", Constants.CONSTANT_NUMBER),
    INT("int", Constants.CONSTANT_NUMBER),
    LARGE_INT("largeint", Constants.CONSTANT_NUMBER),
    BIGINT("bigint", Constants.CONSTANT_NUMBER),
    FLOAT("float", Constants.CONSTANT_NUMBER),
    DOUBLE("double", Constants.CONSTANT_NUMBER),
    DECIMAL("decimal", Constants.CONSTANT_NUMBER),
    CHAR("char", Constants.CONSTANT_STRING),
    VARCHAR("varchar", Constants.CONSTANT_STRING),
    TEXT("text", Constants.CONSTANT_STRING),
    BLOB("blob", Constants.CONSTANT_STRING),
    ENUM_TYPE("enum", Constants.CONSTANT_STRING),
    SET_TYPE("set", Constants.CONSTANT_STRING),
    DATE("date", Constants.CONSTANT_STRING),
    TIME("time", Constants.CONSTANT_STRING),
    DATETIME("datetime", Constants.CONSTANT_STRING),
    TIMESTAMP("timestamp", Constants.CONSTANT_STRING),
    YEAR("year", Constants.CONSTANT_NUMBER),
    BINARY("binary", Constants.CONSTANT_STRING),
    VARBINARY("varbinary",Constants.CONSTANT_STRING),
    JSON("json", Constants.CONSTANT_JSON),
    ARRAY("array", Constants.CONSTANT_ARRAY),
    BOOLEAN("boolean", Constants.CONSTANT_BOOLEAN);

    private String dataType;

    private String type;

    private static final Map<String, MysqlToJavaTypeMapping> TYPE_MAP = new HashMap<>() {{
        for (MysqlToJavaTypeMapping type : MysqlToJavaTypeMapping.values()) {
            put(type.getDataType(), type);
        }
    }};

    public static MysqlToJavaTypeMapping getJavaType(String dataType) {
        return TYPE_MAP.get(dataType);
    }





}
