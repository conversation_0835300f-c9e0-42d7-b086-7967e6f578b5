package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className SOPStatusEnum
 * @description SOPStatusEnum
 * @date 2025/6/16 14:02
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SOPStatusEnum {

    INIT(0, "初始化"),

    NOT_START(1, "待启动"),

    IN_PROCESS(2, "执行中"),

    PAUSED(3, "已暂停"),

    COMPLETED(4, "已完成"),;

    private Integer code;

    private String desc;




}
