package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: RuleTypeEnum
 * @description: 规则类型枚举
 * @author: wang<PERSON><PERSON><PERSON>
 * @date: 2024/12/21 16:12
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum RuleTypeEnum {

    LABEL("labelProperties", "标签规则"),
    USER("userProperties", "用户属性规则"),
    DATASET("datasetProperties", "数据明细规则");

    @JsonValue
    private String code;
    private String desc;

}
