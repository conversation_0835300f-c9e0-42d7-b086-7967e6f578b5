package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: TaskExecStatusEnum
 * @description: 任务执行状态枚举
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TaskExecStatusEnum {

    // PENDING-待计算,RUNNING-计算中,SUCCESS-成功,FAILED-失败,CANCELED-已取消
    /**
     * 待执行
     */
    PENDING((byte) 0, "PENDING", "待计算"),
    /**
     * 执行中
     */
    RUNNING((byte) 1, "RUNNING", "计算中"),
    /**
     * 执行成功
     */
    SUCCESS((byte) 2, "SUCCESS", "成功"),
    /**
     * 执行失败
     */
    FAILED((byte) 3, "FAILED", "失败"),
    /**
     * 取消
     */
    CANCELED((byte) 4, "CANCELED", "已取消"),

    ;

    private Byte code;
    private String name;
    private String desc;

    public static TaskExecStatusEnum getByCode(Byte code) {
        for (TaskExecStatusEnum taskExecStatus : TaskExecStatusEnum.values()) {
            if (taskExecStatus.getCode().equals(code)) {
                return taskExecStatus;
            }
        }
        return null;
    }

    public static String getDescByCode(Byte code) {
        for (TaskExecStatusEnum taskExecStatus : TaskExecStatusEnum.values()) {
            if (taskExecStatus.getCode().equals(code)) {
                return taskExecStatus.getDesc();
            }
        }
        return null;
    }

}
