package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName AiobFailTypeEnum
 * @Description 外呼session未接通一级分类枚举
 * <AUTHOR>
 * @Date 2025/6/27 3:18 PM
 */
@Getter
public enum AiobFailTypeEnum {

    LINE("号线原因未接通"),
    PLATFORM_RULE("平台规则限制未接通"),
    CALLED_UP("被叫原因未接通"),
    OTHER("其他原因"),
    ;
    
    @JsonValue
    private final String name;
    
    AiobFailTypeEnum(String name) {
        this.name = name;
    }
    
    @JsonCreator
    public static AiobFailTypeEnum createByName(String name){
        for (AiobFailTypeEnum value : values()) {
            if (Objects.equals(name, value.getName())) {
                return value;
            }
        }
        throw new IllegalArgumentException("Invalid name: " + name);
    }
}
