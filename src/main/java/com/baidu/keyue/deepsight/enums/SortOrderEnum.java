package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: SortOrderEnum
 * @description: 排序枚举
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SortOrderEnum {

    ASC("ASC", "升序"),

    DESC("DESC", "降序")

    ;

    private String code;

    private String desc;

    public static SortOrderEnum codeOf(String code) {
        if (code == null) {
            return null;
        }
        for (SortOrderEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static boolean validate(String code) {
        return codeOf(code) != null;
    }
}
