package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: TriggerModeEnum
 * @description: 更新类型
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TriggerModeEnum {
    // CRON-定时更新 MANUAL-手动更新 REALTIME-实时更新
    CRON((byte) 0, "定时更新"),
    MANUAL((byte) 1, "手动更新"),
    REALTIME((byte) 2, "实时更新"),

    ;

    private Byte code;
    private String desc;

    public static String getDescByCode(Byte code) {
        for (TriggerModeEnum triggerMode : TriggerModeEnum.values()) {
            if (triggerMode.getCode().equals(code)) {
                return triggerMode.getDesc();
            }
        }
        return null;
    }

    public static TriggerModeEnum getByCode(Byte code) {
        for (TriggerModeEnum triggerMode : TriggerModeEnum.values()) {
            if (triggerMode.getCode().equals(code)) {
                return triggerMode;
            }
        }
        return null;
    }
}
