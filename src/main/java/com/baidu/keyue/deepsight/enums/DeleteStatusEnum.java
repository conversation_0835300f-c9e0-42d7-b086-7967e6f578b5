package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @className DeleteStatus
 * @description 删除状态
 * @date 2024/12/27 10:32
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum DeleteStatusEnum {

    /**
     * 正常状态
     */
    NORMAL(0),

    /**
     * 删除状态
     */
    DELETE(1);

    private Integer status;


}
