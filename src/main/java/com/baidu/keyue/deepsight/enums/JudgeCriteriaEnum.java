package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * @ClassName JudgeCriteriaEnum
 * @Description 人群扩散判定标准枚举
 * <AUTHOR>
 * @Date 2025/3/24 6:12 PM
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum JudgeCriteriaEnum {
    // 判定标准:0根据相似度 1取前几个
    // 判定标准:SIMILARITY-根据相似度 RANKING-取前几个
    SIMILARITY((byte) 0, "根据相似度"),

    RANKING((byte) 1, "取前几个");

    private Byte code;

    private String desc;

    public Boolean getBoolean() {
        return this.getCode() != 0;
    }

    public static JudgeCriteriaEnum getByCode(Byte code){
        for (JudgeCriteriaEnum value : JudgeCriteriaEnum.values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value;
            }
        }
        return null;
    }
}
