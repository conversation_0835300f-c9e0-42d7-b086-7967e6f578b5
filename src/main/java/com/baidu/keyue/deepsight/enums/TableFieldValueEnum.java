package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @className TableFieldValueEnum
 * @description 数据表字段取值类型
 * @date 2025/1/2 15:47
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum TableFieldValueEnum {

    TEXT("text", "文本类"),

    ENUM("enum", "枚举类"),

    NUMBER("number", "数值类"),

    TIME("time", "时间类"),

    BOOLEAN("boolean", "布尔值");


    private String valueType;

    private String desc;

    private static final Map<String, TableFieldValueEnum> VALUE_MAP = new HashMap<>() {{
        for (TableFieldValueEnum type : TableFieldValueEnum.values()) {
            put(type.getValueType(), type);
        }
    }};

    public static TableFieldValueEnum getByValueType(String valueType) {
        return VALUE_MAP.get(valueType);
    }

}
