package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @ClassName ImportTypeEnum
 * @Description 数据导入类型枚举
 * <AUTHOR>
 * @Date 2025/2/12 3:43 PM
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum ImportTypeEnum {
    /**
     * 完全替换掉原有的数据,更新整个数据集
     */
    COVER("COVER", "覆盖式写入"),
    /**
     * 在保留原有数据的基础上添加新数据，历史数据完全相同的不更新，如果数据变化则更新为最新数据
     */
    INCREMENT("INCREMENT", "增量式写入");
    private String value;
    private String desc;
}
