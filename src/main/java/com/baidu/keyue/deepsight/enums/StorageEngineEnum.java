package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: StorageEngineEnum
 * @description: 元数据表存储引擎枚举
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum StorageEngineEnum {

    /**
     * Doris
     */
    DORIS(0);


    private Integer code;

    public static StorageEngineEnum codeOf(Integer code) {
        if (code == null) {
            return null;
        }
        for (StorageEngineEnum e : values()) {
            if (e.getCode().equals(code)) {
                return e;
            }
        }
        return null;
    }

    public static boolean validate(Integer code) {
        return codeOf(code) != null;
    }
}
