package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @className: DelEnum
 * @description: 删除状态枚举
 * @author: lvtao03
 * @date: 2024/12/24 14:24
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum DelEnum {

    NOT_DELETED((byte) 0, "未删除"),

    DELETED((byte) 1, "已删除");

    private Byte code;

    private String desc;

    public Boolean getBoolean() {
        return this.getCode() != 0;
    }
}
