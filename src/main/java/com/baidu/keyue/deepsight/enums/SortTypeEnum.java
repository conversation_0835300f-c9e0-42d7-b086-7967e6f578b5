package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

import java.util.Objects;

/**
 * @ClassName SortTypeEnum
 * @Description 排序类型枚举
 * <AUTHOR>
 * @Date 2025/7/2 4:27 PM
 */
@Getter
public enum SortTypeEnum {
    ASC("ASC", "升序"),
    DESC("DESC", "降序");
    @JsonValue
    private final String value;
    private final String desc;

    SortTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @JsonCreator
    public static SortTypeEnum createByValue(String value) {
        for (SortTypeEnum sortTypeEnum : values()) {
            if (Objects.equals(value, sortTypeEnum.getValue())) {
                return sortTypeEnum;
            }
        }
        throw new IllegalArgumentException("SortTypeEnum Invalid name: " + value);
    }
}
