package com.baidu.keyue.deepsight.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 租户运营模式枚举
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum OperationModeEnum {

    OPERATION_BY_SELF((byte) 1, "自运营"),
    OPERATION_BY_BAIDU_OP((byte) 2, "百度代运营");

    private Byte code;
    private String desc;

    private static Map<Integer, OperationModeEnum> INTEGER_ENUM_MAP = new HashMap<>() {{
        this.put(1, OPERATION_BY_SELF);
        this.put(2, OPERATION_BY_BAIDU_OP);
    }};

    public static OperationModeEnum getByCode(Byte code) {
        for (OperationModeEnum operationModeEnum : OperationModeEnum.values()) {
            if (operationModeEnum.getCode().equals(code)) {
                return operationModeEnum;
            }
        }
        return null;
    }

    public static OperationModeEnum getByCode(Integer code) {
        return INTEGER_ENUM_MAP.get(code);
    }
}
