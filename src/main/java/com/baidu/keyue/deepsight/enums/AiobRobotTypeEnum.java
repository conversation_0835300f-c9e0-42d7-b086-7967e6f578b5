package com.baidu.keyue.deepsight.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Objects;

/**
 * @ClassName AiobRobotTypeEnum
 * @Description 外呼机器人类型
 * <AUTHOR>
 * @Date 2025/7/2 4:11 PM
 */
@Getter
public enum AiobRobotTypeEnum {
    // 机器人类型：ALL(全部类型)/QUICK_SCENES(快捷场景)/FLEX_CANVAS(灵活画布)
    ALL("ALL",0, "全部状态"),
    QUICK_SCENES("QUICK_SCENES", 5,"快捷场景"),
    FLEX_CANVAS("FLEX_CANVAS",6, "灵活画布"),
    ;
    private static final Logger LOGGER = LoggerFactory.getLogger(AiobTaskStatusEnum.class);

    @JsonValue
    private final String value;
    private final Integer code;
    private final String desc;

    AiobRobotTypeEnum(String value, Integer code, String desc) {
        this.value = value;
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static AiobRobotTypeEnum createByValue(String value) {
        for (AiobRobotTypeEnum configTypeEnum : values()) {
            if (Objects.equals(value, configTypeEnum.getValue())) {
                return configTypeEnum;
            }
        }
        throw new IllegalArgumentException("robot type Invalid name: " + value);
    }

    public static AiobRobotTypeEnum createByObj(Object robotScene) {
        if (robotScene == null) {
            return null;
        }
        try {
            int code = Integer.parseInt(robotScene.toString());
            for (AiobRobotTypeEnum typeEnum : values()) {
                if (Objects.equals(typeEnum.getCode(), code)) {
                    return typeEnum;
                }
            }
            LOGGER.warn("task status enum not find:{}", robotScene);
            return null;
        } catch (Exception e) {
            LOGGER.error("error task status enum:{}", robotScene);
        }
        return null;
    }
}
