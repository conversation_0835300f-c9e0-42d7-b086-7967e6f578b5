package com.baidu.keyue.deepsight.client;

import java.util.HashMap;

import com.baidu.keyue.deepsight.models.bsc.basic.BscBaseResponse;
import com.baidu.keyue.deepsight.models.bsc.BscCommitJobRequest;
import com.baidu.keyue.deepsight.models.bsc.BscCommitJobResponse;
import com.baidu.keyue.deepsight.models.bsc.BscCreateJobRequest;
import com.baidu.keyue.deepsight.models.bsc.BscCreateJobResponse;
import com.baidu.keyue.deepsight.models.bsc.BscGetInstanceResponse;
import com.baidu.keyue.deepsight.models.bsc.BscJobIdRequest;
import com.baidu.keyue.deepsight.models.bsc.BscListJobsRequest;
import com.baidu.keyue.deepsight.models.bsc.BscListJobsResponse;
import com.baidu.keyue.deepsight.models.bsc.BscStartInstanceRequest;
import com.baidu.keyue.deepsight.models.bsc.BscUpdateJobRequest;
import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Path;

/**
 *
 * https://cloud.baidu.com/doc/BSC/s/Jkol5gs3d#%E5%90%AF%E5%8A%A8%E4%BD%9C%E4%B8%9A%E5%AE%9E%E4%BE%8B
 *
 * */
public interface BSCClient {

    @POST("/api/v3/seniorbsc/job/list")
    @Headers({"Content-Type: application/json; charset=utf-8", "x-Region: bj"})
    Call<BscListJobsResponse> getWorkList(@Body BscListJobsRequest request);

    @POST("/api/v3/seniorbsc/job/create")
    @Headers({"Content-Type: application/json; charset=utf-8", "x-Region: bj"})
    Call<BscCreateJobResponse> createWork(@Body BscCreateJobRequest request);

    @POST("/api/v3/seniorbsc/job/update/{jobId}")
    @Headers({"Content-Type: application/json; charset=utf-8", "x-Region: bj"})
    Call<BscBaseResponse> updateWork(@Body BscUpdateJobRequest request, @Path("jobId") String jobId);

    @POST("/api/v3/seniorbsc/job/delete/{jobId}")
    @Headers({"Content-Type: application/json; charset=utf-8", "x-Region: bj"})
    Call<BscBaseResponse> deleteWork(@Path("jobId") String jobId, @Body HashMap request);

    @POST("/api/v3/seniorbsc/resource/reference/{resId}")
    @Headers({"Content-Type: application/json; charset=utf-8", "x-Region: bj"})
    Call<BscBaseResponse> updateWorkResource(@Path("resId") String resId, @Body BscJobIdRequest request);

    @POST("api/v3/seniorbsc/job/commit/{jobId}")
    @Headers({"Content-Type: application/json; charset=utf-8", "x-Region: bj"})
    Call<BscCommitJobResponse> commitWork(@Path("jobId") String jobId, @Body BscCommitJobRequest request);

    @POST("/api/v3/seniorbsc/instance/start/{instanceId}")
    @Headers({"Content-Type: application/json; charset=utf-8", "x-Region: bj"})
    Call<BscCommitJobResponse> startWorkInstance(@Path("instanceId") String instanceId, @Body BscStartInstanceRequest request);



    @POST("/api/v3/seniorbsc/instance/stop/{instanceId}")
    @Headers({"Content-Type: application/json; charset=utf-8", "x-Region: bj"})
    Call<BscBaseResponse> stopWorkInstance(@Path("instanceId") String instanceId);

    @GET("/api/v3/seniorbsc/instance/detail/{instanceId}")
    @Headers({"Content-Type: application/json; charset=utf-8", "x-Region: bj"})
    Call<BscGetInstanceResponse> getWorkInstanceDetail(@Path("instanceId") String instanceId);


}
