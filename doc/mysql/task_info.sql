CREATE TABLE
    `task_info` (
        `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '任务ID',
        `task_type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '任务类型, 0:贴源层集成任务, 1:中间层指标加工任务',
        `task_desc` VARCHAR(256) DEFAULT NULL COMMENT '任务描述',
        `task_conf` text COMMENT '任务配置信息, JSON 格式',
        `trigger_cron` VARCHAR(64) DEFAULT '' COMMENT '定时任务 cron 表达式',
        `next_exec_date` TIMESTAMP NULL DEFAULT NULL COMMENT '下一次执行时间',
        `trigger_mod` tinyint(4) NULL DEFAULT '0' COMMENT '触发类型:0:定时触发,1:手动触发,2:实时触发',

        `del` tinyint(1) NOT NULL COMMENT '删除标识, 0:未删除, 1:已删除',
        `creator` VARCHAR(128) NOT NULL COMMENT '创建者',
        `modifier` VARCHAR(128) NOT NULL COMMENT '修改者',
        `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `update_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY(`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=179 DEFAULT CHARSET=utf8mb4 COMMENT='任务表';