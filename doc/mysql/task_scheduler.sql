CREATE TABLE
    `task_scheduler` (
        `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '任务执行ID',
        `task_id` BIGINT(20) NOT NULL COMMENT '任务ID,全局唯一',
        `external_id` VARCHAR(256) DEFAULT NULL COMMENT '外部执行ID',
        `body` text COMMENT '额外信息',
        `message` text COMMENT '结果信息',
        `status` tinyint(4) NOT NULL COMMENT '任务执行状态',
        `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
        `creator` VARCHAR(128) NOT NULL COMMENT '创建者',
        `modifier` VARCHAR(128) NOT NULL COMMENT '修改者',
        `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `update_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY(`id`),
        KEY `idx_task_id_id` (`task_id`,`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1417 DEFAULT CHARSET=utf8mb4 COMMENT='任务调度执行表';