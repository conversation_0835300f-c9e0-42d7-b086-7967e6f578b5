CREATE TABLE
    `label_catalog` (
        `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '标签目录id',
        `parent_id` BIGINT(20) NOT NULL COMMENT '父级标签目录id',
        `catalog_name` VARCHAR(128) NOT NULL COMMENT '标签目录名称',
        `sort` BIGINT(20) NOT NULL COMMENT '排序值',
        `user_id` VARCHAR(128) NOT NULL COMMENT '用户 ID',
        `label_count` INT(11) NOT NULL DEFAULT '0' COMMENT '标签数量',
        `tenant_id` VARCHAR(128) NOT NULL COMMENT '租户 ID',

        `del` tinyint(1) NOT NULL COMMENT '删除标识, 0:未删除, 1: 已删除',
        `creator` VARCHAR(128) NOT NULL COMMENT '创建者',
        `modifier` VARCHAR(128) NOT NULL COMMENT '修改者',
        `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `update_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
        PRIMARY KEY(`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=457 DEFAULT CHARSET=utf8mb4 COMMENT='标签目录表';