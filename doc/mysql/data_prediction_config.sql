CREATE TABLE `data_prediction_config` (
        `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
        `tenant_id` VARCHAR(128) NOT NULL COMMENT '租户 ID',
        `prediction_type` int NOT NULL COMMENT '预测内容类型',
        `status` tinyint(1) NOT NULL COMMENT '状态, 0:启用, 1:关闭',
        `description` VARCHAR(255) NULL DEFAULT '' COMMENT '描述',

        `del` tinyint(1) NOT NULL COMMENT '删除标识, 0:未删除, 1:已删除',
        `creator` VARCHAR(128) NOT NULL COMMENT '创建者',
        `modifier` VARCHAR(128) NOT NULL COMMENT '修改者',
        `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `update_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY(`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='数据增强内容配置表'