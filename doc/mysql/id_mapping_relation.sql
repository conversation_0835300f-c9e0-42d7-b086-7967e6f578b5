CREATE TABLE `id_mapping_relation` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id对主键',
    `preset` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0: 非预置 1：预置',
    `data_table_id` bigint(20) NOT NULL COMMENT '数据表唯一id',
    `en_fields` json DEFAULT NULL COMMENT 'IDmapping id对',
    `cn_fields` json DEFAULT NULL COMMENT 'IDmapping id对中文展示，en_field 不会修改可缓存中文名',
    `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
    `creator` varchar(128) NOT NULL COMMENT '创建者',
    `modifier` varchar(128) NOT NULL COMMENT '修改者',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `tenant_id` varchar(128) NOT NULL COMMENT '租户id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8 COMMENT='IDmapping 关系表'