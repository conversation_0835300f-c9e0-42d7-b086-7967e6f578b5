CREATE TABLE
    `label` (
        `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '标签id',
        `user_id` VARCHAR(128) NOT NULL COMMENT '用户 ID',
        `catalog_id` BIGINT(20) NOT NULL COMMENT '标签目录id',
        `label_name` VARCHAR(128) NOT NULL COMMENT '标签名称',
        `label_value_update_mod` tinyint(4) NOT NULL COMMENT '标签更新取值逻辑:0:每次重新计算,1:合并历史值',
        `label_value_save_mod` tinyint(4) NOT NULL COMMENT '标签值保存类型:0:单值,1:多值',
        `trigger_mod` tinyint(4) NOT NULL COMMENT '标签更新触发类型:0:定时触发,1:手动触发',
        `trigger_frequency` tinyint(4) DEFAULT NULL COMMENT '执行频率:0:每天,1:每周,2:每月',
        `trigger_frequency_value` VARCHAR(256) DEFAULT NULL COMMENT '执行频率json',
        `label_rule` text NOT NULL COMMENT '标签值规则json',
        `exec_mod` tinyint(4) DEFAULT '0' COMMENT '生产方式:0:业务规则,1:SQL,2:业务模式',
        `field` BIGINT(20) DEFAULT NULL COMMENT '字段',
        `distribution` text COMMENT '标签分布统计结果json',
        `label_cal_status` tinyint(4) DEFAULT '0' COMMENT '标签计算状态: 0:待计算,1:计算中,2:计算成功,3:计算失败,4:计算取消',
        `last_cal_date` TIMESTAMP NULL DEFAULT NULL COMMENT '上一次执行时间',
        `task` BIGINT(20) DEFAULT NULL COMMENT '任务ID',
        `tenant_id` VARCHAR(128) NOT NULL COMMENT '租户 ID',
        `recalculate` tinyint(1) DEFAULT '0' COMMENT '是否需要覆盖更新',

        `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
        `creator` VARCHAR(128) NOT NULL COMMENT '创建者',
        `modifier` VARCHAR(128) NOT NULL COMMENT '修改者',
        `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `update_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY(`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=114 DEFAULT CHARSET=utf8mb4 COMMENT='标签表';