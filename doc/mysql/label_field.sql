CREATE TABLE
    `label_field` (
        `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '字段 ID',
        `field_type` VARCHAR(32) NOT NULL COMMENT '字段类型, STRING/INT/ARRAY...等',
        `field_desc` VARCHAR(256) DEFAULT NULL COMMENT '字段描述',
        `label_table` VARCHAR(128) NOT NULL COMMENT '宽表名',
        `table_space` VARCHAR(128) NOT NULL COMMENT '宽表空间',
        `del` tinyint(1) NOT NULL COMMENT '删除标识,0:未删除,1:已删除',
        `creator` VARCHAR(128) NOT NULL COMMENT '创建者',
        `modifier` VARCHAR(128) NOT NULL COMMENT '修改者',
        `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        `update_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        PRIMARY KEY(`id`)
    ) ENGINE=InnoDB AUTO_INCREMENT=110 DEFAULT CHARSET=utf8mb4 COMMENT='标签字段表';