CREATE TABLE `data_prediction_source` (
      `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
      `tenant_id` VARCHAR(128) NOT NULL COMMENT '租户 ID',
      `data_source_list` text NULL COMMENT '数据增强数据集 json',
      `prompt_type` tinyint(4) DEFAULT NULL COMMENT '指令类型:0:系统预置,1:自定义',
      `prompt` text NOT NULL COMMENT '抽取指令',
      `prediction_update_type` tinyint(1) NOT NULL COMMENT '数据预测更新方式, 0:预测到结果后定时更新, 1:预测到结果停止预测更新',
      `trigger_mod` tinyint(4) NOT NULL COMMENT '标签更新触发类型:0:定时触发,1:手动触发,2:实时触发',
      `trigger_frequency` tinyint(4) DEFAULT NULL COMMENT '执行频率:0:每天,1:每周,2:每月',
      `trigger_frequency_value` VARCHAR(255) NULL DEFAULT '' COMMENT '执行频率json',
      `description` VARCHAR(255) NULL DEFAULT '' COMMENT '描述',
      `task` bigint(20) DEFAULT NULL COMMENT '任务ID',
      `cal_status` tinyint(4) NULL DEFAULT '0' COMMENT '标签计算状态: 0:待计算,1:计算中,2:计算成功,3:计算失败,4:计算取消',

      `del` tinyint(1) NOT NULL COMMENT '删除标识, 0:未删除, 1:已删除',
      `creator` VARCHAR(128) NOT NULL COMMENT '创建者',
      `modifier` VARCHAR(128) NOT NULL COMMENT '修改者',
      `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `update_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
      PRIMARY KEY(`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='数据增强数据源配置表'