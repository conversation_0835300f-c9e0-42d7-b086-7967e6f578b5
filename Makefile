HOMEDIR := $(shell pwd)
OUTDIR := $(HOMEDIR)/output

all: clean iApi jacoco
iApi:
	mvn com.baidu.bapi:maven-parse:1.0.5-SNAPSHOT:generate -DoutputPath=iApiDoc

jacoco:
	mvn org.jacoco:jacoco-maven-plugin:0.8.11:prepare-agent test -DfailIfNoTests=false -Dmaven.test.failure.ignore=true
	mvn jacoco:report

clean:
	mvn clean
	rm -rf iApiDoc
	rm -rf log/*
	rm -rf logs/*

test: clean jacoco

test-report: test
	open output/site/index.html

rds:
	mvn mybatis-generator:generate

build: clean
	mvn clean install -Dmaven.test.skip=true -Dmaven.javadoc.skip=true -Dmaven.source.skip=true -Dmaven.license.skip=true -B -V

.PHONY: all clean test jacoco iApi test-report rds build
