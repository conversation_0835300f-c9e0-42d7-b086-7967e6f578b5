FROM iregistry.baidu-int.com/acg-voc/voc-base-jdk:17.0.11-fix
RUN yum install dejavu-sans-fonts fontconfig -y
ARG module="deep-sight-platform"

RUN mkdir -p /home/<USER>/${module}

COPY output/deep-sight-platform.jar /home/<USER>/${module}/
COPY output/classes/application.yml /home/<USER>/${module}/
COPY output/classes/start.sh /home/<USER>/${module}/
COPY output/classes/logback-spring.xml /home/<USER>/${module}/

# rasp
ADD 'http://filecenter.matrix.baidu.com/api/v1/file/rasp/rasp-agent-17.tar.gz/@latest/download' /home/<USER>/${module}/rasp-agent.tar.gz
RUN cd /home/<USER>/${module}/ && tar -zxf ./rasp-agent.tar.gz && rm -rf rasp-agent.tar.gz && chmod -R 777 ./rasp

#USER root
RUN chmod 777 /home/<USER>/${module}/start.sh && chown -R work:work /home/<USER>
    mkdir -p /home/<USER>
    chown -R 1000:1000 /home/<USER>
    sed -i '/^work:\|:x:1000:/d' /etc/group && \
    echo >> /etc/group && echo 'work:x:1000:' >> /etc/group && \
    sed -i '/^work:\|:x:1000:/d' /etc/passwd  && \
    echo >> /etc/passwd && \
    echo 'work:x:1000:1000::/home/<USER>/bin/sh' >> /etc/passwd
WORKDIR /home/<USER>/${module}/
CMD ["/bin/bash", "-c", "./start.sh"]
